diff --git a/1Step/1Step/Coordinators/TaskFormCoordinator.swift b/1Step/1Step/Coordinators/TaskFormCoordinator.swift
index 9f51e63..63fa7b1 100644
--- a/1Step/1Step/Coordinators/TaskFormCoordinator.swift
+++ b/1Step/1Step/Coordinators/TaskFormCoordinator.swift
@@ -6,6 +6,7 @@ import Combine
 class TaskFormCoordinator: ObservableObject {
     // MARK: - Published 属性
     @Published private(set) var isShowing: Bool = false
+    @Published private(set) var formId: UUID = UUID()
     
     // MARK: - 内部状态
     private var cancellables = Set<AnyCancellable>()
@@ -24,6 +25,7 @@ class TaskFormCoordinator: ObservableObject {
     func showForm() {
         print("[TaskFormCoordinator] 显示表单")
         DispatchQueue.main.async {
+            self.formId = UUID()
             withAnimation(.spring()) {
                 self.isShowing = true
             }
diff --git a/1Step/1Step/Extensions/Array+SafeIndex.swift b/1Step/1Step/Extensions/Array+SafeIndex.swift
new file mode 100644
index 0000000..96543d4
--- /dev/null
+++ b/1Step/1Step/Extensions/Array+SafeIndex.swift
@@ -0,0 +1,8 @@
+import Foundation
+
+extension Array {
+    /// 安全访问数组元素，避免索引越界
+    subscript(safe index: Int) -> Element? {
+        return index >= 0 && index < count ? self[index] : nil
+    }
+} 
\ No newline at end of file
diff --git a/1Step/1Step/Models/Node.swift b/1Step/1Step/Models/Node.swift
new file mode 100644
index 0000000..c3cfccc
--- /dev/null
+++ b/1Step/1Step/Models/Node.swift
@@ -0,0 +1,99 @@
+import Foundation
+
+// MARK: - Node Protocol
+
+/// 统一的节点协议，支持Task、ChecklistItem、SubStep的统一操作
+protocol Node {
+    var id: UUID { get }
+    var title: String { get set }
+    var isCompleted: Bool { get set }
+    var children: [Node] { get }
+    var canHaveChildren: Bool { get }
+}
+
+// MARK: - Task Node Extension
+
+extension Task: Node {
+    var isCompleted: Bool {
+        get { statusEnum == .done }
+        set { 
+            if newValue {
+                status = TaskStatus.done.rawValue
+                completedAt = Date()
+            } else {
+                status = TaskStatus.doing.rawValue
+                completedAt = nil
+            }
+        }
+    }
+    
+    var children: [Node] {
+        return (checklist ?? []).map { $0 as Node }
+    }
+    
+    var canHaveChildren: Bool { true }
+}
+
+// MARK: - ChecklistItem Node Extension
+
+extension ChecklistItem: Node {
+    var children: [Node] {
+        return subStepsList.map { $0 as Node }
+    }
+    
+    var canHaveChildren: Bool { true }
+}
+
+// MARK: - SubStep Node Extension
+
+extension SubStep: Node {
+    var children: [Node] {
+        return subSteps.map { $0 as Node }
+    }
+    
+    var canHaveChildren: Bool { true }
+}
+
+// MARK: - Node Utilities
+
+extension Node {
+    /// 递归查找指定ID的节点
+    func findNode(withId id: UUID) -> Node? {
+        if self.id == id {
+            return self
+        }
+        
+        for child in children {
+            if let found = child.findNode(withId: id) {
+                return found
+            }
+        }
+        
+        return nil
+    }
+    
+    /// 获取所有子节点（平铺）
+    var allChildren: [Node] {
+        var result: [Node] = []
+        
+        func collectChildren(_ node: Node) {
+            result.append(node)
+            for child in node.children {
+                collectChildren(child)
+            }
+        }
+        
+        for child in children {
+            collectChildren(child)
+        }
+        
+        return result
+    }
+    
+    /// 计算完成进度
+    var completionProgress: (completed: Int, total: Int) {
+        let allNodes = [self] + allChildren
+        let completed = allNodes.filter { $0.isCompleted }.count
+        return (completed: completed, total: allNodes.count)
+    }
+} 
\ No newline at end of file
diff --git a/1Step/1Step/Models/NodeStyle.swift b/1Step/1Step/Models/NodeStyle.swift
new file mode 100644
index 0000000..b02046a
--- /dev/null
+++ b/1Step/1Step/Models/NodeStyle.swift
@@ -0,0 +1,169 @@
+import Foundation
+import SwiftUI
+
+// MARK: - 显示上下文
+
+/// 节点的显示上下文，决定整体的显示模式
+enum DisplayContext {
+    case taskList       // 任务列表模式（未聚焦）
+    case focused        // 任务聚焦模式
+    case actionFocus    // 小行动聚焦模式
+}
+
+// MARK: - 节点样式
+
+/// 节点的样式级别，基于在聚焦层级中的相对位置
+enum NodeStyle {
+    case primary        // 主要节点（当前聚焦的节点，大勾选框、粗体）
+    case secondary      // 次要节点（子节点，中等大小、target图标）
+    case tertiary       // 三级节点（深层子节点，小巧精致、缩进显示）
+}
+
+// MARK: - 样式计算器
+
+struct NodeStyleCalculator {
+    
+    /// 根据节点在聚焦路径中的位置计算样式
+    static func calculateStyle(
+        nodeId: UUID,
+        focusPath: [UUID],
+        context: DisplayContext
+    ) -> NodeStyle {
+        
+        switch context {
+        case .taskList:
+            // 任务列表模式：所有任务都是primary样式
+            return .primary
+            
+        case .focused:
+            if focusPath.first == nodeId {
+                // 当前聚焦的任务
+                return .primary
+            } else {
+                // 任务下的小行动
+                return .secondary
+            }
+            
+        case .actionFocus:
+            if focusPath.count >= 2 && focusPath[1] == nodeId {
+                // 当前聚焦的小行动
+                return .primary
+            } else if focusPath.contains(nodeId) {
+                // 聚焦路径中的父级节点
+                return .secondary
+            } else {
+                // 子步骤
+                return .tertiary
+            }
+        }
+    }
+    
+    /// 根据节点层级深度计算样式
+    static func calculateStyleByDepth(
+        depth: Int,
+        isInFocusPath: Bool,
+        context: DisplayContext
+    ) -> NodeStyle {
+        
+        switch context {
+        case .taskList:
+            return .primary
+            
+        case .focused, .actionFocus:
+            if isInFocusPath {
+                return depth == 0 ? .primary : .secondary
+            } else {
+                return depth <= 1 ? .secondary : .tertiary
+            }
+        }
+    }
+}
+
+// MARK: - 样式配置
+
+extension NodeStyle {
+    
+    /// 勾选框大小
+    var checkboxSize: CGFloat {
+        switch self {
+        case .primary: return 24
+        case .secondary: return 20
+        case .tertiary: return 16
+        }
+    }
+    
+    /// 字体大小
+    var fontSize: CGFloat {
+        switch self {
+        case .primary: return 16
+        case .secondary: return 15
+        case .tertiary: return 14
+        }
+    }
+    
+    /// 字体粗细
+    var fontWeight: Font.Weight {
+        switch self {
+        case .primary: return .medium
+        case .secondary: return .regular
+        case .tertiary: return .regular
+        }
+    }
+    
+    /// 左侧缩进
+    var leadingPadding: CGFloat {
+        switch self {
+        case .primary: return 0
+        case .secondary: return 10
+        case .tertiary: return 20
+        }
+    }
+    
+    /// 行间距
+    var spacing: CGFloat {
+        switch self {
+        case .primary: return 12
+        case .secondary: return 10
+        case .tertiary: return 8
+        }
+    }
+    
+    /// 操作图标大小
+    var actionIconSize: CGFloat {
+        switch self {
+        case .primary: return 20
+        case .secondary: return 18
+        case .tertiary: return 16
+        }
+    }
+}
+
+// MARK: - 显示上下文配置
+
+extension DisplayContext {
+    
+    /// 是否显示面包屑
+    var showsBreadcrumb: Bool {
+        switch self {
+        case .taskList: return false
+        case .focused, .actionFocus: return true
+        }
+    }
+    
+    /// 默认展开状态
+    var defaultExpanded: Bool {
+        switch self {
+        case .taskList: return false
+        case .focused: return true
+        case .actionFocus: return true
+        }
+    }
+    
+    /// 背景模糊效果
+    var backgroundBlur: Bool {
+        switch self {
+        case .taskList: return false
+        case .focused, .actionFocus: return true
+        }
+    }
+} 
\ No newline at end of file
diff --git a/1Step/1Step/Services/TaskManager.swift b/1Step/1Step/Services/TaskManager.swift
index 48b255f..0103e8f 100644
--- a/1Step/1Step/Services/TaskManager.swift
+++ b/1Step/1Step/Services/TaskManager.swift
@@ -256,6 +256,78 @@ class TaskManager {
         taskRepository.updateTask(task)
     }
     
+    /// 切换任务完成状态
+    public func toggleTaskCompletion(_ task: Task) {
+        let currentStatus = TaskStatus(rawValue: task.status) ?? .inbox
+        let newStatus = (currentStatus == .done) ? TaskStatus.doing : TaskStatus.done
+        taskRepository.updateTaskStatus(task, newStatus: newStatus)
+    }
+    
+    /// 更新任务标题
+    public func updateTaskTitle(_ task: Task, title: String) {
+        task.title = title
+        taskRepository.updateTask(task)
+    }
+    
+    /// 切换小行动完成状态
+    public func toggleChecklistItemCompletion(_ task: Task, itemId: UUID) {
+        taskRepository.toggleChecklistItemCompletion(task, itemId: itemId)
+    }
+    
+    /// 更新小行动标题
+    public func updateChecklistItemTitle(_ task: Task, itemId: UUID, newTitle: String) {
+        taskRepository.updateChecklistItemTitle(task, itemId: itemId, newTitle: newTitle)
+    }
+    
+    /// 切换子步骤完成状态
+    public func toggleSubStepCompletion(_ task: Task, checklistItemId: UUID, subStepId: UUID) {
+        if let checklist = task.checklist,
+           let itemIndex = checklist.firstIndex(where: { $0.id == checklistItemId }) {
+            toggleSubStepCompletionRecursive(in: &task.checklist![itemIndex].subStepsList, subStepId: subStepId)
+            taskRepository.updateTask(task)
+        }
+    }
+    
+    /// 递归切换子步骤完成状态
+    private func toggleSubStepCompletionRecursive(in subSteps: inout [SubStep], subStepId: UUID) -> Bool {
+        for index in subSteps.indices {
+            if subSteps[index].id == subStepId {
+                subSteps[index].isCompleted.toggle()
+                subSteps[index].completedAt = subSteps[index].isCompleted ? Date() : nil
+                return true
+            }
+            
+            if toggleSubStepCompletionRecursive(in: &subSteps[index].subSteps, subStepId: subStepId) {
+                return true
+            }
+        }
+        return false
+    }
+    
+    /// 更新子步骤标题
+    public func updateSubStepTitle(_ task: Task, checklistItemId: UUID, subStepId: UUID, title: String) {
+        if let checklist = task.checklist,
+           let itemIndex = checklist.firstIndex(where: { $0.id == checklistItemId }) {
+            updateSubStepTitleRecursive(in: &task.checklist![itemIndex].subStepsList, subStepId: subStepId, title: title)
+            taskRepository.updateTask(task)
+        }
+    }
+    
+    /// 递归更新子步骤标题
+    private func updateSubStepTitleRecursive(in subSteps: inout [SubStep], subStepId: UUID, title: String) -> Bool {
+        for index in subSteps.indices {
+            if subSteps[index].id == subStepId {
+                subSteps[index].title = title
+                return true
+            }
+            
+            if updateSubStepTitleRecursive(in: &subSteps[index].subSteps, subStepId: subStepId, title: title) {
+                return true
+            }
+        }
+        return false
+    }
+    
     /// 添加笔记
     public func addNote(title: String, content: String, project: UUID?, tags: [String] = [], isFocused: Bool = false) -> Task {
         return taskRepository.addNote(
@@ -333,16 +405,6 @@ class TaskManager {
         taskRepository.removeChecklistItemFromTask(task, itemId: itemId)
     }
     
-    /// 切换检查清单项的完成状态
-    public func toggleChecklistItemCompletion(_ task: Task, itemId: UUID) {
-        taskRepository.toggleChecklistItemCompletion(task, itemId: itemId)
-    }
-    
-    /// 更新检查清单项标题
-    public func updateChecklistItemTitle(_ task: Task, itemId: UUID, newTitle: String) {
-        taskRepository.updateChecklistItemTitle(task, itemId: itemId, newTitle: newTitle)
-    }
-    
     /// 保存任务
     public func saveTask(_ task: Task) {
         taskRepository.updateTask(task)
diff --git a/1Step/1Step/ViewModels/BaseTaskViewModel.swift b/1Step/1Step/ViewModels/BaseTaskViewModel.swift
index 5fe3dce..73177ba 100644
--- a/1Step/1Step/ViewModels/BaseTaskViewModel.swift
+++ b/1Step/1Step/ViewModels/BaseTaskViewModel.swift
@@ -3,6 +3,7 @@ import SwiftUI
 import Combine
 
 /// 基础任务视图模型，封装任务操作的共享逻辑
+@MainActor
 class BaseTaskViewModel: ObservableObject {
     // MARK: - 服务依赖
     /// 任务管理器
diff --git a/1Step/1Step/ViewModels/FocusViewModel.swift b/1Step/1Step/ViewModels/FocusViewModel.swift
index 8817825..895d6b9 100644
--- a/1Step/1Step/ViewModels/FocusViewModel.swift
+++ b/1Step/1Step/ViewModels/FocusViewModel.swift
@@ -2,90 +2,34 @@ import Foundation
 import SwiftUI
 import SwiftData
 import Combine
-import AVFoundation
 
-/// 一步视图模型
+/// 一步视图模型 - 彻底简化版本
 class FocusViewModel: BaseTaskViewModel {
-    // MARK: - 发布属性
+    // MARK: - 核心数据
     @Published var doingTasks: [Task] = []
-    @Published var currentQuote: String = ""
-    @Published var pendingCompletionTaskID: UUID? = nil
-    @Published var focusedTaskId: UUID? = nil
-    @Published var draggedTask: Task? = nil
-    @Published var initiallyFocusedSubtaskID: UUID? = nil
-    @Published var isInActionFocusMode: Bool = false
     
-    // ActionFocus 相关状态
-    @Published var focusedChecklistItemId: UUID? = nil
-    @Published var focusedSubStepPath: [UUID] = []
-    @Published var animatingSubSteps: [UUID: Date] = [:]
-    @Published var recentlyAddedItems: Set<UUID> = []
-    @Published var autoExpandStepId: UUID? = nil // 需要自动展开的步骤ID
-    @Published var shouldExpandTaskAndSteps: Bool = false // 需要展开任务和所有小行动的标记
-    
-    // 回声Drop相关状态
-    @Published var showingEchoDrop = false
-    @Published var echoText = ""
-    
-    // MARK: - 私有属性
-    private let motivationalQuotes = [
-        "一步 · 你正在专注的事，不需要太多 ",
-        "先走这一步，剩下的事情之后再说",
-        "你不是要做很多事，只是现在这一步",
-        "此刻你只需关注这些",
-        "这些行动不为完成，只为靠近",
-        "如果太难开始，就只做一个小行动",
-        "没关系，小行动不大，但已经很勇敢",
-        "你正在的，就是你该在的这一步",
-        "不需要计划太远，走好这一步就好",
-        "你不是要变得更好，而是开始走路",
-        "没有计划的日子，也可以往前走一点",
-        "被打断也没关系，你还可以再迈出一小步",
-        "专注不是做完全部，而是愿意留时间给这一件",
-        "你没有掉队，只是以自己的速度在走",
-        "有些时候，只是开始，也已经是一种完整",
-        "你不需要向谁证明，只要和自己一起往前走",
-        "今天不做也没关系，你还有你自己",
-        "有时候，不动也是一种节奏",
-        "你可以回来，也可以暂停，这里都会等你"
-    ]
-    private var audioPlayer: AVAudioPlayer?
+    // MARK: - 状态管理器（只需要两个）
+    let focusManager: FocusManager
+    let uiStateManager: UIStateManager
     
     // MARK: - 计算属性
-    
-    /// 是否处于聚焦模式
-    var isFocusMode: Bool {
-        return focusedTaskId != nil
-    }
-    
-    /// 获取当前聚焦的任务
-    func getFocusedTask() -> Task? {
-        guard let focusedId = focusedTaskId else { return nil }
-        return doingTasks.first { $0.id == focusedId }
-    }
+    var isFocusMode: Bool { focusManager.isFocused }
+    var isInActionFocusMode: Bool { focusManager.isInActionFocusMode }
     
     // MARK: - 初始化
     override init(taskManager: TaskManager = DependencyContainer.taskManager()) {
+        self.focusManager = FocusManager(taskManager: taskManager)
+        self.uiStateManager = UIStateManager(taskManager: taskManager)
+        
         super.init(taskManager: taskManager)
         
-        // 在主线程上执行UI更新操作
         DispatchQueue.main.async { [weak self] in
-            guard let self = self else { return }
-            // 加载初始数据
-            self.loadTasks()
-            self.selectQuoteForToday()
-            self.setupAudioPlayer()
-            
-            // 恢复上次聚焦的任务（如果有）
-            self.restoreFocusState()
+            self?.loadTasks()
         }
     }
     
-    // MARK: - 重写父类方法
-    
-    /// 加载一步任务
+    // MARK: - 数据加载
     override func loadTasks() {
-        // 在主线程上修改UI绑定的属性
         DispatchQueue.main.async { [weak self] in
             guard let self = self else { return }
             
@@ -96,902 +40,127 @@ class FocusViewModel: BaseTaskViewModel {
                     !self.pendingCompletionTaskIDs.contains(task.id)
                 }
             
-            // 检查聚焦的任务是否仍然存在
-            self.checkFocusedTaskExists()
-        }
-    }
-    
-    // MARK: - 聚焦管理
-    
-    /// 聚焦于某个任务
-    func focusOnTask(_ task: Task, initiallyFocusingSubtaskID: UUID? = nil) {
-        // 允许对已聚焦任务再次操作（主要为了传递子任务ID），或聚焦新的doing任务
-        guard task.status == TaskStatus.doing.rawValue || task.id == self.focusedTaskId else { 
-            print("FocusViewModel: Task to focus is not 'doing' or already focused without subtask change.")
-            return 
-        }
-        
-        // 在主线程上修改UI绑定的属性
-        DispatchQueue.main.async { [weak self] in
-            guard let self = self else { return }
-            let oldFocusedTaskId = self.focusedTaskId
-            self.focusedTaskId = task.id
-            self.initiallyFocusedSubtaskID = initiallyFocusingSubtaskID // <-- 设置子任务ID
-            self.saveFocusState() // 保存 focusedTaskId 的持久化
-
-            // 如果主聚焦任务改变了，确保列表刷新
-            // 如果只是传递子任务ID给已聚焦的主任务，可能不需要完全刷新列表，
-            // 但 FocusView 的 body 会因为 focusedTaskId (即使值不变，但initiallyFocusedSubtaskID变了) 的变化而重绘
-            if oldFocusedTaskId != task.id {
-                AnalyticsService.shared.trackEvent(AnalyticsService.EventNames.focusModeUsed)
-                self.loadTasks() // 确保 doingTasks 更新，影响列表显示
-                
-                // 尝试恢复ActionFocus状态（只有在没有指定子任务ID时才恢复）
-                if initiallyFocusingSubtaskID == nil {
-                    _ = self.tryRestoreActionFocusState(for: task.id)
-                }
+            // 验证聚焦状态
+            if let taskId = self.focusManager.focusPath.first,
+               !self.doingTasks.contains(where: { $0.id == taskId }) {
+                self.focusManager.exitFocus()
             }
         }
     }
     
-    /// 退出聚焦模式
-    func exitFocusMode() {
-        // 在主线程上修改UI绑定的属性
-        DispatchQueue.main.async { [weak self] in
-            guard let self = self else { return }
-            self.focusedTaskId = nil
-            self.initiallyFocusedSubtaskID = nil // <-- 退出主聚焦时也清除
-            self.saveFocusState()
-            self.loadTasks() // 刷新列表
-        }
-    }
+    // MARK: - 统一操作接口
     
-    /// 由 TaskRowView 在 onAppear 中调用，以清除一次性的初始聚焦小行动ID
-    func clearInitiallyFocusedSubtaskID() {
-        // 确保只在主线程上修改发布的属性
-        DispatchQueue.main.async { [weak self] in
-            if self?.initiallyFocusedSubtaskID != nil {
-                self?.initiallyFocusedSubtaskID = nil
-                 print("FocusViewModel: Cleared initiallyFocusedSubtaskID")
-            }
-        }
+    /// 聚焦到任意节点（统一方法）
+    func focusTo(_ nodeId: UUID) {
+        focusManager.focusTo(nodeId)
+        loadTasks()
     }
     
-    /// 检查当前聚焦的任务是否存在
-    private func checkFocusedTaskExists() {
-        if let focusedId = focusedTaskId,
-           !doingTasks.contains(where: { $0.id == focusedId }) {
-            // 如果聚焦的任务不再是doing状态或已删除，退出聚焦模式
-            exitFocusMode()
-        }
+    /// 加深聚焦
+    func focusDeeper(to nodeId: UUID) {
+        focusManager.focusDeeper(to: nodeId)
     }
     
-    /// 保存聚焦状态到持久化存储
-    private func saveFocusState() {
-        if let focusedId = focusedTaskId {
-            UserDefaults.standard.set(focusedId.uuidString, forKey: "FocusedTaskID")
-        } else {
-            UserDefaults.standard.removeObject(forKey: "FocusedTaskID")
-        }
+    /// 退出聚焦
+    func exitFocus() {
+        focusManager.exitFocus()
+        loadTasks()
     }
     
-    /// 从持久化存储恢复聚焦状态
-    private func restoreFocusState() {
-        if let focusedIdString = UserDefaults.standard.string(forKey: "FocusedTaskID"),
-           let focusedId = UUID(uuidString: focusedIdString) {
-            // 仅当任务存在于doing列表中时才恢复聚焦状态
-            if doingTasks.contains(where: { $0.id == focusedId }) {
-                focusedTaskId = focusedId
-            } else {
-                // 清除无效的聚焦状态
-                UserDefaults.standard.removeObject(forKey: "FocusedTaskID")
-            }
-        }
+    /// 退出到指定层级
+    func exitToLevel(_ level: Int) {
+        focusManager.exitToLevel(level)
     }
     
-    // 任务完成和删除后的处理
-    // 提供给子类调用的扩展方法，处理完成任务的聚焦状态
-    func handleTaskCompletionForFocus(_ task: Task) {
-        // 清除ActionFocus状态
-        clearActionFocusState(for: task.id)
-        
-        // 如果完成的是聚焦任务，退出聚焦模式
-        if task.id == focusedTaskId {
-            exitFocusMode()
-        }
+    /// 切换任意节点完成状态（统一方法）
+    func toggleCompletion(_ nodeId: UUID) {
+        focusManager.toggleNodeCompletion(nodeId)
+        loadTasks()
     }
     
-    // 提供给子类调用的扩展方法，处理删除任务的聚焦状态
-    func handleTaskDeletionForFocus(_ task: Task) {
-        // 清除ActionFocus状态
-        clearActionFocusState(for: task.id)
-        
-        // 如果删除的是聚焦任务，退出聚焦模式
-        if task.id == focusedTaskId {
-            exitFocusMode()
-        }
+    /// 切换节点展开状态
+    func toggleExpansion(_ nodeId: UUID) {
+        focusManager.toggleNodeExpansion(nodeId)
     }
     
-    // MARK: - 公共方法
-    
-    /// 播放完成音效
-    func playCompletionSound() {
-        audioPlayer?.currentTime = 0
-        audioPlayer?.play()
-    }
+    // MARK: - 任务操作
     
     /// 移动任务到下一步
     func moveTaskToNextActions(task: Task) {
-        // 修改数据模型
         task.status = TaskStatus.na.rawValue
         task.updatedAt = Date()
         taskManager.updateTask(task)
         
-        // 如果移动的是聚焦任务，退出聚焦模式
-        if task.id == focusedTaskId {
-            exitFocusMode()
+        if focusManager.focusPath.first == task.id {
+            exitFocus()
         }
         
-        // 刷新UI
         loadTasks()
     }
     
-    /// 清空所有一步任务（移回下一步）
-    func clearAllDoingTasks() {
-        // 修改数据模型
-        let tasks = taskManager.getTasksByStatus(TaskStatus.doing.rawValue)
-        for task in tasks {
-            task.status = TaskStatus.na.rawValue
-            task.updatedAt = Date()
-            taskManager.updateTask(task)
-        }
-        
-        // 清空时退出聚焦模式
-        exitFocusMode()
-        
-        // 刷新UI
-        loadTasks()
-    }
+
     
     /// 更新任务排序
     func updateTaskOrder(tasks: [Task]) {
-        // 修改数据模型
         for (index, task) in tasks.enumerated() {
             task.sortOrder = Int16(index)
             taskManager.updateTask(task)
         }
-        
-        // 刷新UI
         loadTasks()
     }
     
-    // MARK: - 拖放相关方法
+    // MARK: - 拖放处理
     
-    /// 处理任务拖放操作
     func handleTaskDrop(draggedTask: Task?, toTask: Task) {
-        guard let draggedTask = draggedTask else { return }
+        guard let draggedTask = draggedTask,
+              let fromIndex = doingTasks.firstIndex(where: { $0.id == draggedTask.id }),
+              let toIndex = doingTasks.firstIndex(where: { $0.id == toTask.id }),
+              fromIndex != toIndex else { return }
         
-        // 获取源任务和目标任务的索引
-        guard let fromIndex = doingTasks.firstIndex(where: { $0.id == draggedTask.id }),
-              let toIndex = doingTasks.firstIndex(where: { $0.id == toTask.id }) else {
-            return
-        }
-        
-        // 如果源和目标相同，不执行操作
-        if fromIndex == toIndex { return }
-        
-        // 创建任务数组的副本
         var updatedTasks = doingTasks
-        
-        // 移动任务
         let task = updatedTasks.remove(at: fromIndex)
         updatedTasks.insert(task, at: toIndex)
-        
-        // 更新排序
         updateTaskOrder(tasks: updatedTasks)
     }
     
-    // MARK: - 回声Drop功能
+    // MARK: - 回声Drop（委托给UIStateManager）
     
-    /// 显示回声Drop
-    func showEchoDrop() {
-        withAnimation {
-            showingEchoDrop = true
-        }
-    }
-    
-    /// 隐藏回声Drop
-    func hideEchoDrop() {
-        withAnimation {
-            showingEchoDrop = false
-            echoText = ""
-        }
-    }
-    
-    /// 处理回声文本，将其转化为任务
+    func showEchoDrop() { uiStateManager.showEchoDrop() }
+    func hideEchoDrop() { uiStateManager.hideEchoDrop() }
     func handleEchoDropText() {
-        guard !echoText.isEmpty else { return }
-        
-        // 创建新任务，状态改为 inbox
-        let task = taskManager.addTask(
-            title: echoText,
-            status: TaskStatus.inbox.rawValue
-        )
-        
-        // 清空文本
-        echoText = ""
-        
-        // 重新加载任务列表
+        uiStateManager.handleEchoDropText()
         loadTasks()
-        
-        // 更新 Toast 提示
-        DependencyContainer.toastManager().showSuperLightInfo("已添加到收集箱")
-        
-        // 隐藏回声Drop
-        showingEchoDrop = false
-        
-        // 触发触觉反馈
-        let generator = UIImpactFeedbackGenerator(style: .medium)
-        generator.impactOccurred()
     }
+    func releaseEcho() { uiStateManager.releaseEcho() }
+    func playCompletionSound() { uiStateManager.playCompletionSound() }
     
-    /// 释放回声文本（不保存，只清空）
-    func releaseEcho() {
-        // 清空文本
-        echoText = ""
-        
-        // 隐藏回声Drop
-        showingEchoDrop = false
-    }
-    
-    // MARK: - 私有方法
+    // MARK: - 导航和获取数据
     
-    /// 选择今天的激励语
-    private func selectQuoteForToday() {
-        let defaults = UserDefaults.standard
-        let today = Calendar.current.startOfDay(for: Date())
-        
-        // 获取上次显示第一条引言的日期
-        if let lastDate = defaults.object(forKey: "LastFirstQuoteDate") as? Date,
-           !Calendar.current.isDate(today, inSameDayAs: lastDate) {
-            // 如果是新的一天，显示第一条引言
-            currentQuote = motivationalQuotes[0]
-            
-            // 保存今天的日期
-            defaults.set(today, forKey: "LastFirstQuoteDate")
-        } else {
-            // 如果是同一天内再次打开，随机显示任意一条
-            currentQuote = motivationalQuotes.randomElement() ?? ""
-        }
-    }
-    
-    /// 设置音频播放器
-    private func setupAudioPlayer() {
-        guard let soundURL = Bundle.main.url(forResource: "task_completed", withExtension: "wav") else {
-            print("无法找到音频文件")
-            return
-        }
-        
-        do {
-            // 配置音频会话为混音模式，允许与其他应用的音频共存
-            // 使用 .ambient 类别，这样不会中断其他应用的音频播放
-            try AVAudioSession.sharedInstance().setCategory(.ambient, mode: .default)
-            try AVAudioSession.sharedInstance().setActive(true, options: .notifyOthersOnDeactivation)
-            
-            audioPlayer = try AVAudioPlayer(contentsOf: soundURL)
-            audioPlayer?.volume = 0.3 // 设置音量为 30%
-            audioPlayer?.prepareToPlay()
-        } catch {
-            print("音频播放器初始化失败: \(error.localizedDescription)")
-        }
-    }
-    
-    // MARK: - ActionFocus 相关方法
-    
-    /// 聚焦到小行动，进入 ActionFocus 模式
-    func focusOnChecklistItem(_ item: ChecklistItem, in task: Task) {
-        DispatchQueue.main.async { [weak self] in
-            guard let self = self else { return }
-            
-            // 设置聚焦的任务和小行动
-            self.focusedTaskId = task.id
-            self.focusedChecklistItemId = item.id
-            self.focusedSubStepPath = []
-            
-            // 进入 ActionFocus 模式
-            self.isInActionFocusMode = true
-            
-            // 设置自动展开当前聚焦的小行动
-            self.autoExpandStepId = item.id
-            
-            print("[FocusViewModel] 进入 ActionFocus 模式 - 任务: \(task.title), 小行动: \(item.title)")
-        }
+    func getFocusedTask() -> Task? {
+        guard let taskId = focusManager.focusPath.first else { return nil }
+        return doingTasks.first { $0.id == taskId }
     }
     
-    /// 获取面包屑标题
     func getBreadcrumbTitles() -> [String] {
-        var titles: [String] = []
-        
-        // 添加项目标题（第一个面包屑）
-        if let focusedTask = getFocusedTask() {
-            if let projectId = focusedTask.project,
-               let project = taskManager.getProjectById(projectId) {
-                titles.append(project.name)
-            } else {
-                titles.append("收集箱") // 如果没有项目，显示收集箱
-            }
-            
-            // 在 ActionFocus 模式下，添加任务名作为第二个面包屑
-            if isInActionFocusMode {
-                titles.append(focusedTask.title)
-                
-                // 只有在有子步骤路径时才添加小行动名，避免与下方大标题重复
-                if !focusedSubStepPath.isEmpty, let checklistItem = getFocusedChecklistItem() {
-                    titles.append(checklistItem.title)
-                }
-            }
-        }
-        
-        // 如果有子步骤路径，添加相关标题
-        if !focusedSubStepPath.isEmpty {
-            // 添加子步骤标题（除了最后一个，因为最后一个是当前页面）
-            for (index, stepId) in focusedSubStepPath.enumerated() {
-                if index < focusedSubStepPath.count - 1 { // 不包含最后一个
-                    if let stepTitle = getSubStepTitle(stepId) {
-                        titles.append(stepTitle)
+        return focusManager.getBreadcrumbTitles()
                     }
-                }
-            }
-        }
-        
-        return titles
-    }
     
-    /// 导航到面包屑层级
     func navigateToBreadcrumbLevel(_ level: Int) {
-        DispatchQueue.main.async { [weak self] in
-            guard let self = self else { return }
-            
-            if level == 0 {
-                // 点击项目名，回到一步列表（完全退出聚焦模式）
-                self.saveCurrentActionFocusState()
-                self.focusedChecklistItemId = nil
-                self.focusedSubStepPath = []
-                self.isInActionFocusMode = false
-                self.focusedTaskId = nil
-                self.saveFocusState()
-                self.loadTasks()
-            } else if level == 1 {
-                // 点击任务名，回到任务展开状态（退出 ActionFocus 模式，但保持任务聚焦）
-                self.saveCurrentActionFocusState()
-                self.focusedChecklistItemId = nil
-                self.focusedSubStepPath = []
-                self.isInActionFocusMode = false
-                
-                // 设置需要展开任务和所有小行动的标记
-                self.shouldExpandTaskAndSteps = true
-            } else if level == 2 {
-                // 点击小行动名，回到小行动层级（清除子步骤路径）
-                self.focusedSubStepPath = []
-            } else {
-                // 点击子步骤，回到特定子步骤层级
-                // 子步骤从level 3开始（当有子步骤路径时，小行动名才会出现在面包屑中）
-                let targetPath = Array(self.focusedSubStepPath.prefix(level - 2))
-                self.focusedSubStepPath = targetPath
-            }
-        }
-    }
-    
-    /// 获取当前聚焦的小行动
-    func getFocusedChecklistItem() -> ChecklistItem? {
-        // 确保获取最新的任务数据
-        guard let focusedId = focusedTaskId,
-              let freshTask = taskManager.getTaskById(focusedId),
-              let itemId = focusedChecklistItemId,
-              let checklist = freshTask.checklist else { 
-            print("[FocusViewModel] getFocusedChecklistItem: 无法获取基础数据")
-            return nil 
-        }
-        
-        let item = checklist.first { $0.id == itemId }
-        if let item = item {
-            print("[FocusViewModel] getFocusedChecklistItem: 找到小行动 '\(item.title)', 状态: \(item.isCompleted)")
-        } else {
-            print("[FocusViewModel] getFocusedChecklistItem: 未找到指定的小行动")
-        }
-        
-        return item
-    }
-    
-    /// 获取子步骤标题
-    private func getSubStepTitle(_ stepId: UUID) -> String? {
-        guard let checklistItem = getFocusedChecklistItem() else { return nil }
-        
-        // 在子步骤列表中查找
-        let steps = checklistItem.subStepsList
-        if let step = steps.first(where: { $0.id == stepId }) {
-            return step.title
-        }
-        
-        // 如果在第一层没找到，递归查找嵌套的子步骤
-        for step in steps {
-            if let title = findSubStepTitle(stepId, in: step.subSteps) {
-                return title
-            }
-        }
-        
-        return nil
-    }
-    
-    /// 递归查找子步骤标题
-    private func findSubStepTitle(_ stepId: UUID, in subSteps: [SubStep]) -> String? {
-        for step in subSteps {
-            if step.id == stepId {
-                return step.title
-            }
-            if let title = findSubStepTitle(stepId, in: step.subSteps) {
-                return title
-            }
-        }
-        return nil
-    }
-    
-    /// 切换小行动完成状态
-    func toggleChecklistItemCompletion(_ item: ChecklistItem) {
-        guard let focusedTask = getFocusedTask() else { 
-            print("[FocusViewModel] 错误：无法找到聚焦的任务")
-            return 
-        }
-        
-        print("[FocusViewModel] 开始切换小行动完成状态")
-        print("[FocusViewModel] 当前小行动: \(item.title), 当前状态: \(item.isCompleted)")
-        
-        // 记录切换前的状态
-        let wasCompleted = item.isCompleted
-        print("[FocusViewModel] 切换前状态: wasCompleted = \(wasCompleted)")
-        
-        // 使用 taskManager 来处理小行动完成状态切换
-        taskManager.toggleChecklistItemCompletion(focusedTask, itemId: item.id)
-        
-        // 强制刷新任务数据，确保获取最新状态
-        loadTasks()
-        
-        // 如果从未完成变为完成，添加视觉反馈延迟
-        if !wasCompleted {
-            // 添加到动画状态，显示完成效果
-            DispatchQueue.main.async { [weak self] in
-                self?.animatingSubSteps[item.id] = Date()
-            }
-            
-            // 延迟3.5秒后退出 ActionFocus 模式
-            DispatchQueue.main.asyncAfter(deadline: .now() + 3.5) { [weak self] in
-                guard let self = self else { return }
-                
-                // 移除动画状态
-                self.animatingSubSteps.removeValue(forKey: item.id)
-                
-                // 重新获取最新的小行动数据确认状态
-                if let updatedItem = self.getFocusedChecklistItem() {
-                    print("[FocusViewModel] 延迟检查后状态: \(updatedItem.isCompleted)")
-                    
-                    if updatedItem.isCompleted {
-                        print("[FocusViewModel] 小行动已完成，准备退出 ActionFocus 模式")
-                        
-                        // 小行动完成，清除状态而不是保存
-                        if let taskId = self.focusedTaskId {
-                            self.clearActionFocusState(for: taskId)
-                        }
-                        
-                        // 退出 ActionFocus 模式
-                        self.isInActionFocusMode = false
-                        self.focusedChecklistItemId = nil
-                        self.focusedSubStepPath = []
-                        
-                        print("[FocusViewModel] 已退出 ActionFocus 模式")
-                    }
-                } else {
-                    print("[FocusViewModel] 错误：无法获取更新后的小行动数据")
-                }
-            }
-        } else {
-            // 如果是从完成变为未完成，立即处理
-            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
-                guard let self = self else { return }
-                
-                if let updatedItem = self.getFocusedChecklistItem() {
-                    print("[FocusViewModel] 切换后状态: \(updatedItem.isCompleted)")
-                    print("[FocusViewModel] 状态切换完成，但不需要退出 ActionFocus 模式")
-                }
-            }
-        }
-    }
-    
-    /// 切换子步骤完成状态
-    func toggleSubStepCompletion(_ stepId: UUID) {
-        guard let focusedTask = getFocusedTask(),
-              let checklistItem = getFocusedChecklistItem(),
-              let taskChecklist = focusedTask.checklist,
-              let itemIndex = taskChecklist.firstIndex(where: { $0.id == checklistItem.id }) else {
-            print("[FocusViewModel] 无法找到聚焦的任务或小行动")
-            return
-        }
-        
-        print("[FocusViewModel] 开始切换子步骤完成状态，stepId: \(stepId)")
-        
-        // 获取子步骤列表的可变副本
-        var steps = focusedTask.checklist![itemIndex].subStepsList
-        
-        // 递归查找并获取当前状态
-        var wasCompleted = false
-        var stepTitle = ""
-        let success = getSubStepStatus(stepId: stepId, in: steps, wasCompleted: &wasCompleted, stepTitle: &stepTitle)
-        
-        if success {
-            print("[FocusViewModel] 子步骤当前状态: \(stepTitle) -> \(wasCompleted)")
-            
-            // 立即执行状态切换（立即勾选、变灰、划线）
-            performSubStepToggle(stepId: stepId, wasCompleted: wasCompleted)
-            
-            // 如果从未完成变为完成，添加动画状态防止立即移动位置
-            if !wasCompleted {
-                    DispatchQueue.main.async { [weak self] in
-                        self?.animatingSubSteps[stepId] = Date()
-                    }
-                    
-                // 延迟3.5秒后移除动画状态，让项目沉下去
-                    DispatchQueue.main.asyncAfter(deadline: .now() + 3.5) { [weak self] in
-                        guard let self = self else { return }
-                        
-                    // 移除动画状态，允许项目移动到已完成区域
-                        self.animatingSubSteps.removeValue(forKey: stepId)
-                        
-                    // 如果这是当前聚焦的子步骤，延迟回退到上一级
-                    if let lastStepId = self.focusedSubStepPath.last, lastStepId == stepId {
-                        // 再延迟1秒后回退到上一级
-                        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
-                            guard let self = self else { return }
-                        self.focusedSubStepPath.removeLast()
-                            print("[FocusViewModel] 子步骤完成，回退到上一级")
-                        }
-                    }
-                }
-            }
-        } else {
-            print("[FocusViewModel] 错误：无法找到指定的子步骤")
-        }
-    }
-    
-    /// 获取子步骤状态（不修改状态）
-    private func getSubStepStatus(stepId: UUID, in steps: [SubStep], wasCompleted: inout Bool, stepTitle: inout String) -> Bool {
-        // 在当前层级查找
-        for step in steps {
-            if step.id == stepId {
-                wasCompleted = step.isCompleted
-                stepTitle = step.title
-                return true
-            }
-            
-            // 递归查找子步骤
-            if getSubStepStatus(stepId: stepId, in: step.subSteps, wasCompleted: &wasCompleted, stepTitle: &stepTitle) {
-                return true
-            }
-        }
-        
-        return false
-    }
-    
-    /// 执行实际的子步骤状态切换
-    private func performSubStepToggle(stepId: UUID, wasCompleted: Bool) {
-        guard let focusedTask = getFocusedTask(),
-              let checklistItem = getFocusedChecklistItem(),
-              let taskChecklist = focusedTask.checklist,
-              let itemIndex = taskChecklist.firstIndex(where: { $0.id == checklistItem.id }) else {
-            print("[FocusViewModel] 无法找到聚焦的任务或小行动")
-            return
-        }
-        
-        // 获取子步骤列表的可变副本
-        var steps = focusedTask.checklist![itemIndex].subStepsList
-        
-        // 递归查找并切换子步骤状态
-        var actualWasCompleted = false
-        var stepTitle = ""
-        let success = toggleSubStepRecursively(stepId: stepId, in: &steps, wasCompleted: &actualWasCompleted, stepTitle: &stepTitle)
-        
-        if success {
-            print("[FocusViewModel] 执行子步骤切换: \(stepTitle) -> \(actualWasCompleted) 变为 \(!actualWasCompleted)")
-            
-            // 更新小行动的子步骤列表
-            focusedTask.checklist![itemIndex].subStepsList = steps
-            
-            // 保存到数据库
-            taskManager.updateTask(focusedTask)
-            
-            // 刷新任务数据
-            loadTasks()
-        }
-    }
-    
-    /// 递归查找并切换子步骤状态
-    private func toggleSubStepRecursively(stepId: UUID, in steps: inout [SubStep], wasCompleted: inout Bool, stepTitle: inout String) -> Bool {
-        // 在当前层级查找
-        for i in 0..<steps.count {
-            if steps[i].id == stepId {
-                wasCompleted = steps[i].isCompleted
-                stepTitle = steps[i].title
-                steps[i].toggleCompletion()
-                return true
-            }
-            
-            // 递归查找子步骤
-            var subSteps = steps[i].subSteps
-            if toggleSubStepRecursively(stepId: stepId, in: &subSteps, wasCompleted: &wasCompleted, stepTitle: &stepTitle) {
-                steps[i].subSteps = subSteps
-                return true
-            }
-        }
-        
-        return false
-    }
-    
-    /// 添加子步骤到聚焦的小行动
-    func addSubStepToFocusedItem(title: String) {
-        guard let focusedTask = getFocusedTask(),
-              let checklistItem = getFocusedChecklistItem(),
-              let taskChecklist = focusedTask.checklist,
-              let itemIndex = taskChecklist.firstIndex(where: { $0.id == checklistItem.id }) else { 
-            print("[FocusViewModel] 无法找到聚焦的任务或小行动")
-            return 
-        }
-        
-        // 创建新的子步骤
-        let newSubStep = SubStep(title: title)
-        
-        // 添加到小行动的子步骤数组
-        focusedTask.checklist?[itemIndex].addSubStep(newSubStep)
-        
-        // 保存到数据库
-        taskManager.updateTask(focusedTask)
-        
-        print("[FocusViewModel] 添加子步骤成功: \(title) 到小行动: \(checklistItem.title)")
-        
-        // 刷新任务数据
+        focusManager.navigateToBreadcrumb(level)
         loadTasks()
     }
     
-    /// 从聚焦的小行动中移除子步骤
-    func removeSubStepFromFocusedItem(_ stepId: UUID) {
-        guard let focusedTask = getFocusedTask(),
-              let checklistItem = getFocusedChecklistItem(),
-              let taskChecklist = focusedTask.checklist,
-              let itemIndex = taskChecklist.firstIndex(where: { $0.id == checklistItem.id }) else {
-            print("[FocusViewModel] 无法找到聚焦的任务或小行动")
-            return
-        }
-        
-        print("[FocusViewModel] 开始删除子步骤，stepId: \(stepId)")
-        
-        // 获取子步骤列表的可变副本
-        var steps = focusedTask.checklist![itemIndex].subStepsList
-        
-        // 递归查找并删除子步骤
-        var removedStepTitle = ""
-        let success = removeSubStepRecursively(stepId: stepId, from: &steps, removedTitle: &removedStepTitle)
-        
-        if success {
-            print("[FocusViewModel] 成功删除子步骤: \(removedStepTitle)")
-            
-            // 更新小行动的子步骤列表
-            focusedTask.checklist![itemIndex].subStepsList = steps
-            
-            // 保存到数据库
-            taskManager.updateTask(focusedTask)
-            
-            // 如果删除的是当前聚焦的子步骤，回退到上一级
-            if focusedSubStepPath.contains(stepId) {
-                DispatchQueue.main.async { [weak self] in
-                    if let lastStepId = self?.focusedSubStepPath.last, lastStepId == stepId {
-                        self?.focusedSubStepPath.removeLast()
-                    }
-                }
-            }
-            
-            // 刷新任务数据
-            loadTasks()
-        } else {
-            print("[FocusViewModel] 错误：无法找到要删除的子步骤")
-        }
-    }
-    
-    /// 递归查找并删除子步骤
-    private func removeSubStepRecursively(stepId: UUID, from steps: inout [SubStep], removedTitle: inout String) -> Bool {
-        // 在当前层级查找
-        for i in 0..<steps.count {
-            if steps[i].id == stepId {
-                removedTitle = steps[i].title
-                steps.remove(at: i)
-                return true
-            }
-            
-            // 递归查找子步骤
-            var subSteps = steps[i].subSteps
-            if removeSubStepRecursively(stepId: stepId, from: &subSteps, removedTitle: &removedTitle) {
-                steps[i].subSteps = subSteps
-                return true
-            }
-        }
-        
-        return false
-    }
-    
-    /// 聚焦到子步骤
-    func focusOnSubStep(_ stepId: UUID) {
-        DispatchQueue.main.async { [weak self] in
-            guard let self = self else { return }
-            
-            // 添加到聚焦路径
-            if !self.focusedSubStepPath.contains(stepId) {
-                self.focusedSubStepPath.append(stepId)
-            }
-            
-            // 进入 ActionFocus 模式
-            self.isInActionFocusMode = true
-            
-            // 保存当前状态
-            self.saveCurrentActionFocusState()
-        }
-    }
-    
-    // MARK: - ActionFocus 状态持久化
-    
-    private let actionFocusStatesKey = "ActionFocusStates"
-    
-    /// 保存当前ActionFocus状态
-    func saveCurrentActionFocusState() {
-        guard let taskId = focusedTaskId,
-              let itemId = focusedChecklistItemId else { 
-            print("[FocusViewModel] 无法保存ActionFocus状态：缺少必要信息")
-            return 
-        }
-        
-        var manager = loadActionFocusStateManager()
-        manager.saveState(
-            taskId: taskId,
-            checklistItemId: itemId,
-            subStepPath: focusedSubStepPath
-        )
-        saveActionFocusStateManager(manager)
-        
-        print("[FocusViewModel] 保存ActionFocus状态 - 任务: \(taskId), 小行动: \(itemId), 路径: \(focusedSubStepPath)")
-    }
-    
-    /// 直接恢复ActionFocus状态（用于外部调用）
-    func restoreActionFocusState(taskId: UUID, checklistItemId: UUID, subStepPath: [UUID]) {
-        DispatchQueue.main.async { [weak self] in
-            guard let self = self else { return }
-            
-            self.focusedTaskId = taskId
-            self.focusedChecklistItemId = checklistItemId
-            self.focusedSubStepPath = subStepPath
-            self.isInActionFocusMode = true
-            
-            // 设置自动展开当前聚焦的小行动
-            self.autoExpandStepId = checklistItemId
-            
-            // 保存聚焦状态
-            self.saveFocusState()
-            
-            // 显示恢复提示
-            DependencyContainer.toastManager().showSuperLightInfo("已恢复到上次位置")
-            
-            print("[FocusViewModel] 直接恢复ActionFocus状态 - 任务: \(taskId), 小行动: \(checklistItemId), 路径: \(subStepPath)")
-        }
-    }
-    
-    /// 尝试恢复指定任务的ActionFocus状态
-    func tryRestoreActionFocusState(for taskId: UUID) -> Bool {
-        let manager = loadActionFocusStateManager()
-        
-        guard let state = manager.getState(for: taskId) else {
-            print("[FocusViewModel] 没有找到任务 \(taskId) 的ActionFocus状态")
-            return false
-        }
-        
-        // 验证数据是否仍然有效
-        guard let task = taskManager.getTaskById(taskId),
-              let checklistItem = task.checklist?.first(where: { $0.id == state.checklistItemId }) else {
-            print("[FocusViewModel] ActionFocus状态数据已失效 - 任务或小行动不存在")
-            clearActionFocusState(for: taskId) // 清理失效状态
-            return false
-        }
-        
-        // 验证子步骤路径是否仍然有效
-        if !validateSubStepPath(state.subStepPath, in: checklistItem) {
-            print("[FocusViewModel] 子步骤路径已失效")
-            clearActionFocusState(for: taskId) // 清理失效状态
-            return false
-        }
-        
-        // 恢复状态
-        DispatchQueue.main.async { [weak self] in
-            guard let self = self else { return }
-            
-            self.focusedTaskId = taskId
-            self.focusedChecklistItemId = state.checklistItemId
-            self.focusedSubStepPath = state.subStepPath
-            self.isInActionFocusMode = true
-            
-            // 显示恢复提示
-            DependencyContainer.toastManager().showSuperLightInfo("已恢复到上次位置")
-        }
-        
-        print("[FocusViewModel] 成功恢复ActionFocus状态 - 小行动: \(state.checklistItemId), 路径: \(state.subStepPath)")
-        return true
-    }
-    
-    /// 清除指定任务的ActionFocus状态
-    func clearActionFocusState(for taskId: UUID) {
-        var manager = loadActionFocusStateManager()
-        manager.removeState(for: taskId)
-        saveActionFocusStateManager(manager)
-        print("[FocusViewModel] 清除任务 \(taskId) 的ActionFocus状态")
-    }
-    
-    /// 退出ActionFocus模式并保存状态
-    func exitActionFocusModeWithSave() {
-        // 先保存当前状态
-        saveCurrentActionFocusState()
-        
-        // 然后退出模式，回到一步列表
-        DispatchQueue.main.async { [weak self] in
-            guard let self = self else { return }
-            
-            // 退出ActionFocus模式
-            self.isInActionFocusMode = false
-            self.focusedChecklistItemId = nil
-            self.focusedSubStepPath = []
-            
-            // 同时退出任务聚焦模式，回到一步列表
-            self.focusedTaskId = nil
-            self.initiallyFocusedSubtaskID = nil
-            self.saveFocusState()
-            
-            // 刷新任务列表
-            self.loadTasks()
-        }
-        
-        print("[FocusViewModel] 退出ActionFocus模式并保存状态，回到一步列表")
-    }
+    // MARK: - 任务状态处理
     
-    /// 验证子步骤路径是否仍然有效
-    private func validateSubStepPath(_ path: [UUID], in checklistItem: ChecklistItem) -> Bool {
-        var currentSteps = checklistItem.subStepsList
-        
-        for stepId in path {
-            guard let step = currentSteps.first(where: { $0.id == stepId }) else {
-                return false
-            }
-            currentSteps = step.subSteps
-        }
-        
-        return true
-    }
-    
-    /// 从UserDefaults加载状态管理器
-    private func loadActionFocusStateManager() -> ActionFocusStateManager {
-        guard let data = UserDefaults.standard.data(forKey: actionFocusStatesKey),
-              let manager = try? JSONDecoder().decode(ActionFocusStateManager.self, from: data) else {
-            return ActionFocusStateManager()
+    func handleTaskCompletionForFocus(_ task: Task) {
+        if focusManager.focusPath.first == task.id {
+            exitFocus()
         }
-        return manager
     }
     
-    /// 保存状态管理器到UserDefaults
-    private func saveActionFocusStateManager(_ manager: ActionFocusStateManager) {
-        guard let data = try? JSONEncoder().encode(manager) else { 
-            print("[FocusViewModel] 编码ActionFocusStateManager失败")
-            return 
+    func handleTaskDeletionForFocus(_ task: Task) {
+        if focusManager.focusPath.first == task.id {
+            exitFocus()
         }
-        UserDefaults.standard.set(data, forKey: actionFocusStatesKey)
     }
 } 
\ No newline at end of file
diff --git a/1Step/1Step/ViewModels/FocusViewModel.swift.backup b/1Step/1Step/ViewModels/FocusViewModel.swift.backup
new file mode 100644
index 0000000..d4146fd
--- /dev/null
+++ b/1Step/1Step/ViewModels/FocusViewModel.swift.backup
@@ -0,0 +1,742 @@
+import Foundation
+import SwiftUI
+import SwiftData
+import Combine
+
+/// 一步视图模型 - 重构后的轻量级版本
+class FocusViewModel: BaseTaskViewModel {
+    // MARK: - 核心数据
+    @Published var doingTasks: [Task] = []
+    
+    // MARK: - 状态管理器
+    let focusManager: FocusManager
+    let uiStateManager: UIStateManager
+    
+    // MARK: - 计算属性
+    
+    /// 是否处于聚焦模式
+    var isFocusMode: Bool {
+        return focusManager.isFocused
+    }
+    
+    /// 获取当前聚焦的任务
+    func getFocusedTask() -> Task? {
+        guard let taskId = focusManager.focusPath.first else { return nil }
+        return doingTasks.first { $0.id == taskId }
+    }
+    
+    // MARK: - 初始化
+    override init(taskManager: TaskManager = DependencyContainer.taskManager()) {
+        // 初始化状态管理器
+        self.focusManager = FocusManager(taskManager: taskManager)
+        self.uiStateManager = UIStateManager(taskManager: taskManager)
+        
+        super.init(taskManager: taskManager)
+        
+        // 在主线程上执行UI更新操作
+        DispatchQueue.main.async { [weak self] in
+            guard let self = self else { return }
+            // 加载初始数据
+            self.loadTasks()
+        }
+    }
+    
+    // MARK: - 重写父类方法
+    
+    /// 加载一步任务
+    override func loadTasks() {
+        // 在主线程上修改UI绑定的属性
+        DispatchQueue.main.async { [weak self] in
+            guard let self = self else { return }
+            
+            self.doingTasks = self.taskManager.getTasksByStatus(TaskStatus.doing.rawValue)
+                .sorted { $0.sortOrder < $1.sortOrder }
+                .filter { task in
+                    !self.pendingDeletionTaskIDs.contains(task.id) && 
+                    !self.pendingCompletionTaskIDs.contains(task.id)
+                }
+            
+            // 验证聚焦状态是否仍然有效
+            if let taskId = self.focusManager.focusPath.first,
+               !self.doingTasks.contains(where: { $0.id == taskId }) {
+                // 聚焦的任务不存在了，清除聚焦状态
+                self.focusManager.exitFocus()
+            }
+        }
+    }
+    
+    // MARK: - 聚焦管理
+    
+    /// 聚焦于某个任务
+    func focusOnTask(_ task: Task, initialChecklistItemId: UUID? = nil) {
+        DispatchQueue.main.async { [weak self] in
+            guard let self = self else { return }
+            
+            var path = [task.id]
+            if let itemId = initialChecklistItemId {
+                path.append(itemId)
+            }
+            
+            self.focusManager.focus(to: path)
+            self.loadTasks()
+        }
+    }
+    
+    /// 聚焦到小行动级别
+    func focusOnChecklistItem(_ item: ChecklistItem, in task: Task) {
+        DispatchQueue.main.async { [weak self] in
+            guard let self = self else { return }
+            
+            let path = [task.id, item.id]
+            self.focusManager.focus(to: path)
+            
+            print("[FocusViewModel] 聚焦到小行动 - 任务: \(task.title), 小行动: \(item.title)")
+        }
+    }
+    
+    /// 退出聚焦模式
+    func exitFocusMode() {
+        DispatchQueue.main.async { [weak self] in
+            guard let self = self else { return }
+            
+            self.focusManager.exitFocus()
+            self.loadTasks()
+        }
+    }
+    
+    /// 退出到指定层级
+    func exitToLevel(_ level: Int) {
+        DispatchQueue.main.async { [weak self] in
+            guard let self = self else { return }
+            
+            self.focusManager.focusTo(level: level)
+        }
+    }
+    
+    // 任务完成和删除后的处理
+    func handleTaskCompletionForFocus(_ task: Task) {
+        // 如果完成的是聚焦任务，退出聚焦
+        if focusManager.focusPath.first == task.id {
+            exitFocusMode()
+        }
+    }
+    
+    func handleTaskDeletionForFocus(_ task: Task) {
+        // 如果删除的是聚焦任务，退出聚焦
+        if focusManager.focusPath.first == task.id {
+            exitFocusMode()
+        }
+    }
+    
+    // MARK: - 公共方法
+    
+    /// 播放完成音效（委托给 UIStateManager）
+    func playCompletionSound() {
+        uiStateManager.playCompletionSound()
+    }
+    
+    /// 移动任务到下一步
+    func moveTaskToNextActions(task: Task) {
+        // 修改数据模型
+        task.status = TaskStatus.na.rawValue
+        task.updatedAt = Date()
+        taskManager.updateTask(task)
+        
+        // 如果移动的是聚焦任务，退出聚焦模式
+        if task.id == focusStateManager.focusedTaskId {
+            exitFocusMode()
+        }
+        
+        // 刷新UI
+        loadTasks()
+    }
+    
+    /// 清空所有一步任务（移回下一步）
+    func clearAllDoingTasks() {
+        // 修改数据模型
+        let tasks = taskManager.getTasksByStatus(TaskStatus.doing.rawValue)
+        for task in tasks {
+            task.status = TaskStatus.na.rawValue
+            task.updatedAt = Date()
+            taskManager.updateTask(task)
+        }
+        
+        // 清空时退出聚焦模式
+        exitFocusMode()
+        
+        // 刷新UI
+        loadTasks()
+    }
+    
+    /// 更新任务排序
+    func updateTaskOrder(tasks: [Task]) {
+        // 修改数据模型
+        for (index, task) in tasks.enumerated() {
+            task.sortOrder = Int16(index)
+            taskManager.updateTask(task)
+        }
+        
+        // 刷新UI
+        loadTasks()
+    }
+    
+    // MARK: - 拖放相关方法
+    
+    /// 处理任务拖放操作
+    func handleTaskDrop(draggedTask: Task?, toTask: Task) {
+        guard let draggedTask = draggedTask else { return }
+        
+        // 获取源任务和目标任务的索引
+        guard let fromIndex = doingTasks.firstIndex(where: { $0.id == draggedTask.id }),
+              let toIndex = doingTasks.firstIndex(where: { $0.id == toTask.id }) else {
+            return
+        }
+        
+        // 如果源和目标相同，不执行操作
+        if fromIndex == toIndex { return }
+        
+        // 创建任务数组的副本
+        var updatedTasks = doingTasks
+        
+        // 移动任务
+        let task = updatedTasks.remove(at: fromIndex)
+        updatedTasks.insert(task, at: toIndex)
+        
+        // 更新排序
+        updateTaskOrder(tasks: updatedTasks)
+    }
+    
+    // MARK: - 回声Drop功能（委托给 UIStateManager）
+    
+    /// 显示回声Drop
+    func showEchoDrop() {
+        uiStateManager.showEchoDrop()
+    }
+    
+    /// 隐藏回声Drop
+    func hideEchoDrop() {
+        uiStateManager.hideEchoDrop()
+    }
+    
+    /// 处理回声文本，将其转化为任务
+    func handleEchoDropText() {
+        uiStateManager.handleEchoDropText()
+        // 重新加载任务列表以反映变化
+        loadTasks()
+    }
+    
+    /// 释放回声文本（不保存，只清空）
+    func releaseEcho() {
+        uiStateManager.releaseEcho()
+    }
+    
+    // MARK: - 私有方法（已移动到对应管理器）
+    // 注：selectQuoteForToday 和 setupAudioPlayer 已移动到 UIStateManager
+    
+    // MARK: - ActionFocus 相关方法（委托给 ActionFocusManager）
+    
+    /// 聚焦到小行动，进入 ActionFocus 模式
+    func focusOnChecklistItem(_ item: ChecklistItem, in task: Task) {
+        DispatchQueue.main.async { [weak self] in
+            guard let self = self else { return }
+            
+            // 设置聚焦的任务
+            self.focusStateManager.focusOnTask(task)
+            
+            // 委托给 ActionFocusManager 进入 ActionFocus 模式
+            self.actionFocusManager.enterActionFocusMode(taskId: task.id, checklistItemId: item.id)
+            
+            print("[FocusViewModel] 进入 ActionFocus 模式 - 任务: \(task.title), 小行动: \(item.title)")
+        }
+    }
+    
+    /// 获取面包屑标题
+    func getBreadcrumbTitles() -> [String] {
+        return focusManager.getBreadcrumbTitles()
+    }
+    
+    /// 导航到面包屑层级
+    func navigateToBreadcrumbLevel(_ level: Int) {
+        DispatchQueue.main.async { [weak self] in
+            guard let self = self else { return }
+            
+            if level == 0 {
+                // 点击项目名，回到一步列表（完全退出聚焦模式）
+                self.exitFocusMode()
+            } else {
+                // 其他层级，退出到对应层级
+                self.focusManager.focusTo(level: level - 1) // 减1因为面包屑从项目名开始
+            }
+        }
+    }
+    
+    /// 获取当前聚焦的小行动
+    func getFocusedChecklistItem() -> ChecklistItem? {
+        guard focusManager.focusLevel >= 2,
+              let taskId = focusManager.focusPath.first,
+              let itemId = focusManager.focusPath[safe: 1],
+              let task = taskManager.getTaskById(taskId),
+              let checklist = task.checklist else {
+            return nil
+        }
+        
+        return checklist.first { $0.id == itemId }
+    }
+    
+    /// 切换小行动完成状态（使用统一的完成处理逻辑）
+    func toggleChecklistItemCompletion(_ item: ChecklistItem) {
+        guard let focusedTask = getFocusedTask() else { 
+            print("[FocusViewModel] 错误：无法找到聚焦的任务")
+            return 
+        }
+        
+        let wasCompleted = item.isCompleted
+        
+        // 使用 taskManager 来处理小行动完成状态切换
+        taskManager.toggleChecklistItemCompletion(focusedTask, itemId: item.id)
+        
+        // 使用统一的完成处理逻辑
+        focusManager.handleNodeCompletion(item.id, wasCompleted: wasCompleted)
+        
+        // 强制刷新任务数据，确保获取最新状态
+        loadTasks()
+        
+        // 如果从未完成变为完成，添加视觉反馈延迟
+        if !wasCompleted {
+            // 添加到动画状态，显示完成效果
+            DispatchQueue.main.async { [weak self] in
+                self?.animatingSubSteps[item.id] = Date()
+            }
+            
+            // 延迟3.5秒后退出 ActionFocus 模式
+            DispatchQueue.main.asyncAfter(deadline: .now() + 3.5) { [weak self] in
+                guard let self = self else { return }
+                
+                // 移除动画状态
+                self.animatingSubSteps.removeValue(forKey: item.id)
+                
+                // 重新获取最新的小行动数据确认状态
+                if let updatedItem = self.getFocusedChecklistItem() {
+                    print("[FocusViewModel] 延迟检查后状态: \(updatedItem.isCompleted)")
+                    
+                    if updatedItem.isCompleted {
+                        print("[FocusViewModel] 小行动已完成，准备退出 ActionFocus 模式")
+                        
+                        // 小行动完成，清除状态而不是保存
+                        if let taskId = self.focusedTaskId {
+                            self.clearActionFocusState(for: taskId)
+                        }
+                        
+                        // 退出 ActionFocus 模式
+                        self.isInActionFocusMode = false
+                        self.focusedChecklistItemId = nil
+                        self.focusedSubStepPath = []
+                        
+                        print("[FocusViewModel] 已退出 ActionFocus 模式")
+                    }
+                } else {
+                    print("[FocusViewModel] 错误：无法获取更新后的小行动数据")
+                }
+            }
+        } else {
+            // 如果是从完成变为未完成，立即处理
+            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
+                guard let self = self else { return }
+                
+                if let updatedItem = self.getFocusedChecklistItem() {
+                    print("[FocusViewModel] 切换后状态: \(updatedItem.isCompleted)")
+                    print("[FocusViewModel] 状态切换完成，但不需要退出 ActionFocus 模式")
+                }
+            }
+        }
+    }
+    
+    /// 切换子步骤完成状态
+    func toggleSubStepCompletion(_ stepId: UUID) {
+        guard let focusedTask = getFocusedTask(),
+              let checklistItem = getFocusedChecklistItem(),
+              let taskChecklist = focusedTask.checklist,
+              let itemIndex = taskChecklist.firstIndex(where: { $0.id == checklistItem.id }) else {
+            print("[FocusViewModel] 无法找到聚焦的任务或小行动")
+            return
+        }
+        
+        print("[FocusViewModel] 开始切换子步骤完成状态，stepId: \(stepId)")
+        
+        // 获取子步骤列表的可变副本
+        var steps = focusedTask.checklist![itemIndex].subStepsList
+        
+        // 递归查找并获取当前状态
+        var wasCompleted = false
+        var stepTitle = ""
+        let success = getSubStepStatus(stepId: stepId, in: steps, wasCompleted: &wasCompleted, stepTitle: &stepTitle)
+        
+        if success {
+            print("[FocusViewModel] 子步骤当前状态: \(stepTitle) -> \(wasCompleted)")
+            
+            // 立即执行状态切换（立即勾选、变灰、划线）
+            performSubStepToggle(stepId: stepId, wasCompleted: wasCompleted)
+            
+            // 如果从未完成变为完成，添加动画状态防止立即移动位置
+            if !wasCompleted {
+                    DispatchQueue.main.async { [weak self] in
+                        self?.animatingSubSteps[stepId] = Date()
+                    }
+                    
+                // 延迟3.5秒后移除动画状态，让项目沉下去
+                    DispatchQueue.main.asyncAfter(deadline: .now() + 3.5) { [weak self] in
+                        guard let self = self else { return }
+                        
+                    // 移除动画状态，允许项目移动到已完成区域
+                        self.animatingSubSteps.removeValue(forKey: stepId)
+                        
+                    // 如果这是当前聚焦的子步骤，延迟回退到上一级
+                    if let lastStepId = self.focusedSubStepPath.last, lastStepId == stepId {
+                        // 再延迟1秒后回退到上一级
+                        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
+                            guard let self = self else { return }
+                        self.focusedSubStepPath.removeLast()
+                            print("[FocusViewModel] 子步骤完成，回退到上一级")
+                        }
+                    }
+                }
+            }
+        } else {
+            print("[FocusViewModel] 错误：无法找到指定的子步骤")
+        }
+    }
+    
+    /// 获取子步骤状态（不修改状态）
+    private func getSubStepStatus(stepId: UUID, in steps: [SubStep], wasCompleted: inout Bool, stepTitle: inout String) -> Bool {
+        // 在当前层级查找
+        for step in steps {
+            if step.id == stepId {
+                wasCompleted = step.isCompleted
+                stepTitle = step.title
+                return true
+            }
+            
+            // 递归查找子步骤
+            if getSubStepStatus(stepId: stepId, in: step.subSteps, wasCompleted: &wasCompleted, stepTitle: &stepTitle) {
+                return true
+            }
+        }
+        
+        return false
+    }
+    
+    /// 执行实际的子步骤状态切换
+    private func performSubStepToggle(stepId: UUID, wasCompleted: Bool) {
+        guard let focusedTask = getFocusedTask(),
+              let checklistItem = getFocusedChecklistItem(),
+              let taskChecklist = focusedTask.checklist,
+              let itemIndex = taskChecklist.firstIndex(where: { $0.id == checklistItem.id }) else {
+            print("[FocusViewModel] 无法找到聚焦的任务或小行动")
+            return
+        }
+        
+        // 获取子步骤列表的可变副本
+        var steps = focusedTask.checklist![itemIndex].subStepsList
+        
+        // 递归查找并切换子步骤状态
+        var actualWasCompleted = false
+        var stepTitle = ""
+        let success = toggleSubStepRecursively(stepId: stepId, in: &steps, wasCompleted: &actualWasCompleted, stepTitle: &stepTitle)
+        
+        if success {
+            print("[FocusViewModel] 执行子步骤切换: \(stepTitle) -> \(actualWasCompleted) 变为 \(!actualWasCompleted)")
+            
+            // 更新小行动的子步骤列表
+            focusedTask.checklist![itemIndex].subStepsList = steps
+            
+            // 保存到数据库
+            taskManager.updateTask(focusedTask)
+            
+            // 刷新任务数据
+            loadTasks()
+        }
+    }
+    
+    /// 递归查找并切换子步骤状态
+    private func toggleSubStepRecursively(stepId: UUID, in steps: inout [SubStep], wasCompleted: inout Bool, stepTitle: inout String) -> Bool {
+        // 在当前层级查找
+        for i in 0..<steps.count {
+            if steps[i].id == stepId {
+                wasCompleted = steps[i].isCompleted
+                stepTitle = steps[i].title
+                steps[i].toggleCompletion()
+                return true
+            }
+            
+            // 递归查找子步骤
+            var subSteps = steps[i].subSteps
+            if toggleSubStepRecursively(stepId: stepId, in: &subSteps, wasCompleted: &wasCompleted, stepTitle: &stepTitle) {
+                steps[i].subSteps = subSteps
+                return true
+            }
+        }
+        
+        return false
+    }
+    
+    /// 添加子步骤到聚焦的小行动
+    func addSubStepToFocusedItem(title: String) {
+        guard let focusedTask = getFocusedTask(),
+              let checklistItem = getFocusedChecklistItem(),
+              let taskChecklist = focusedTask.checklist,
+              let itemIndex = taskChecklist.firstIndex(where: { $0.id == checklistItem.id }) else { 
+            print("[FocusViewModel] 无法找到聚焦的任务或小行动")
+            return 
+        }
+        
+        // 创建新的子步骤
+        let newSubStep = SubStep(title: title)
+        
+        // 添加到小行动的子步骤数组
+        focusedTask.checklist?[itemIndex].addSubStep(newSubStep)
+        
+        // 保存到数据库
+        taskManager.updateTask(focusedTask)
+        
+        print("[FocusViewModel] 添加子步骤成功: \(title) 到小行动: \(checklistItem.title)")
+        
+        // 刷新任务数据
+        loadTasks()
+    }
+    
+    /// 从聚焦的小行动中移除子步骤
+    func removeSubStepFromFocusedItem(_ stepId: UUID) {
+        guard let focusedTask = getFocusedTask(),
+              let checklistItem = getFocusedChecklistItem(),
+              let taskChecklist = focusedTask.checklist,
+              let itemIndex = taskChecklist.firstIndex(where: { $0.id == checklistItem.id }) else {
+            print("[FocusViewModel] 无法找到聚焦的任务或小行动")
+            return
+        }
+        
+        print("[FocusViewModel] 开始删除子步骤，stepId: \(stepId)")
+        
+        // 获取子步骤列表的可变副本
+        var steps = focusedTask.checklist![itemIndex].subStepsList
+        
+        // 递归查找并删除子步骤
+        var removedStepTitle = ""
+        let success = removeSubStepRecursively(stepId: stepId, from: &steps, removedTitle: &removedStepTitle)
+        
+        if success {
+            print("[FocusViewModel] 成功删除子步骤: \(removedStepTitle)")
+            
+            // 更新小行动的子步骤列表
+            focusedTask.checklist![itemIndex].subStepsList = steps
+            
+            // 保存到数据库
+            taskManager.updateTask(focusedTask)
+            
+            // 如果删除的是当前聚焦的子步骤，回退到上一级
+            if focusedSubStepPath.contains(stepId) {
+                DispatchQueue.main.async { [weak self] in
+                    if let lastStepId = self?.focusedSubStepPath.last, lastStepId == stepId {
+                        self?.focusedSubStepPath.removeLast()
+                    }
+                }
+            }
+            
+            // 刷新任务数据
+            loadTasks()
+        } else {
+            print("[FocusViewModel] 错误：无法找到要删除的子步骤")
+        }
+    }
+    
+    /// 递归查找并删除子步骤
+    private func removeSubStepRecursively(stepId: UUID, from steps: inout [SubStep], removedTitle: inout String) -> Bool {
+        // 在当前层级查找
+        for i in 0..<steps.count {
+            if steps[i].id == stepId {
+                removedTitle = steps[i].title
+                steps.remove(at: i)
+                return true
+            }
+            
+            // 递归查找子步骤
+            var subSteps = steps[i].subSteps
+            if removeSubStepRecursively(stepId: stepId, from: &subSteps, removedTitle: &removedTitle) {
+                steps[i].subSteps = subSteps
+                return true
+            }
+        }
+        
+        return false
+    }
+    
+    /// 聚焦到子步骤
+    func focusOnSubStep(_ stepId: UUID) {
+        DispatchQueue.main.async { [weak self] in
+            guard let self = self else { return }
+            
+            // 添加到聚焦路径
+            if !self.focusedSubStepPath.contains(stepId) {
+                self.focusedSubStepPath.append(stepId)
+            }
+            
+            // 进入 ActionFocus 模式
+            self.isInActionFocusMode = true
+            
+            // 保存当前状态
+            self.saveCurrentActionFocusState()
+        }
+    }
+    
+    // MARK: - ActionFocus 状态持久化
+    
+    private let actionFocusStatesKey = "ActionFocusStates"
+    
+    /// 保存当前ActionFocus状态
+    func saveCurrentActionFocusState() {
+        guard let taskId = focusedTaskId,
+              let itemId = focusedChecklistItemId else { 
+            print("[FocusViewModel] 无法保存ActionFocus状态：缺少必要信息")
+            return 
+        }
+        
+        var manager = loadActionFocusStateManager()
+        manager.saveState(
+            taskId: taskId,
+            checklistItemId: itemId,
+            subStepPath: focusedSubStepPath
+        )
+        saveActionFocusStateManager(manager)
+        
+        print("[FocusViewModel] 保存ActionFocus状态 - 任务: \(taskId), 小行动: \(itemId), 路径: \(focusedSubStepPath)")
+    }
+    
+    /// 直接恢复ActionFocus状态（用于外部调用）
+    func restoreActionFocusState(taskId: UUID, checklistItemId: UUID, subStepPath: [UUID]) {
+        DispatchQueue.main.async { [weak self] in
+            guard let self = self else { return }
+            
+            self.focusedTaskId = taskId
+            self.focusedChecklistItemId = checklistItemId
+            self.focusedSubStepPath = subStepPath
+            self.isInActionFocusMode = true
+            
+            // 设置自动展开当前聚焦的小行动
+            self.autoExpandStepId = checklistItemId
+            
+            // 保存聚焦状态
+            self.saveFocusState()
+            
+            // 显示恢复提示
+            DependencyContainer.toastManager().showSuperLightInfo("已恢复到上次位置")
+            
+            print("[FocusViewModel] 直接恢复ActionFocus状态 - 任务: \(taskId), 小行动: \(checklistItemId), 路径: \(subStepPath)")
+        }
+    }
+    
+    /// 尝试恢复指定任务的ActionFocus状态
+    func tryRestoreActionFocusState(for taskId: UUID) -> Bool {
+        let manager = loadActionFocusStateManager()
+        
+        guard let state = manager.getState(for: taskId) else {
+            print("[FocusViewModel] 没有找到任务 \(taskId) 的ActionFocus状态")
+            return false
+        }
+        
+        // 验证数据是否仍然有效
+        guard let task = taskManager.getTaskById(taskId),
+              let checklistItem = task.checklist?.first(where: { $0.id == state.checklistItemId }) else {
+            print("[FocusViewModel] ActionFocus状态数据已失效 - 任务或小行动不存在")
+            clearActionFocusState(for: taskId) // 清理失效状态
+            return false
+        }
+        
+        // 验证子步骤路径是否仍然有效
+        if !validateSubStepPath(state.subStepPath, in: checklistItem) {
+            print("[FocusViewModel] 子步骤路径已失效")
+            clearActionFocusState(for: taskId) // 清理失效状态
+            return false
+        }
+        
+        // 恢复状态
+        DispatchQueue.main.async { [weak self] in
+            guard let self = self else { return }
+            
+            self.focusedTaskId = taskId
+            self.focusedChecklistItemId = state.checklistItemId
+            self.focusedSubStepPath = state.subStepPath
+            self.isInActionFocusMode = true
+            
+            // 显示恢复提示
+            DependencyContainer.toastManager().showSuperLightInfo("已恢复到上次位置")
+        }
+        
+        print("[FocusViewModel] 成功恢复ActionFocus状态 - 小行动: \(state.checklistItemId), 路径: \(state.subStepPath)")
+        return true
+    }
+    
+    /// 清除指定任务的ActionFocus状态
+    func clearActionFocusState(for taskId: UUID) {
+        var manager = loadActionFocusStateManager()
+        manager.removeState(for: taskId)
+        saveActionFocusStateManager(manager)
+        print("[FocusViewModel] 清除任务 \(taskId) 的ActionFocus状态")
+    }
+    
+    /// 退出ActionFocus模式并保存状态
+    func exitActionFocusModeWithSave() {
+        // 先保存当前状态
+        saveCurrentActionFocusState()
+        
+        // 然后退出模式，回到一步列表
+        DispatchQueue.main.async { [weak self] in
+            guard let self = self else { return }
+            
+            // 退出ActionFocus模式
+            self.isInActionFocusMode = false
+            self.focusedChecklistItemId = nil
+            self.focusedSubStepPath = []
+            
+            // 同时退出任务聚焦模式，回到一步列表
+            self.focusedTaskId = nil
+            self.initiallyFocusedSubtaskID = nil
+            self.saveFocusState()
+            
+            // 刷新任务列表
+            self.loadTasks()
+        }
+        
+        print("[FocusViewModel] 退出ActionFocus模式并保存状态，回到一步列表")
+    }
+    
+    /// 验证子步骤路径是否仍然有效
+    private func validateSubStepPath(_ path: [UUID], in checklistItem: ChecklistItem) -> Bool {
+        var currentSteps = checklistItem.subStepsList
+        
+        for stepId in path {
+            guard let step = currentSteps.first(where: { $0.id == stepId }) else {
+                return false
+            }
+            currentSteps = step.subSteps
+        }
+        
+        return true
+    }
+    
+    /// 从UserDefaults加载状态管理器
+    private func loadActionFocusStateManager() -> ActionFocusStateManager {
+        guard let data = UserDefaults.standard.data(forKey: actionFocusStatesKey),
+              let manager = try? JSONDecoder().decode(ActionFocusStateManager.self, from: data) else {
+            return ActionFocusStateManager()
+        }
+        return manager
+    }
+    
+    /// 保存状态管理器到UserDefaults
+    private func saveActionFocusStateManager(_ manager: ActionFocusStateManager) {
+        guard let data = try? JSONEncoder().encode(manager) else { 
+            print("[FocusViewModel] 编码ActionFocusStateManager失败")
+            return 
+        }
+        UserDefaults.standard.set(data, forKey: actionFocusStatesKey)
+    }
+} 
\ No newline at end of file
diff --git a/1Step/1Step/ViewModels/Managers/FocusManager.swift b/1Step/1Step/ViewModels/Managers/FocusManager.swift
new file mode 100644
index 0000000..4f11546
--- /dev/null
+++ b/1Step/1Step/ViewModels/Managers/FocusManager.swift
@@ -0,0 +1,290 @@
+import Foundation
+import SwiftUI
+
+/// 通用树形节点管理器 - 统一处理所有类型的节点操作
+@MainActor
+class FocusManager: ObservableObject {
+    // MARK: - 核心状态
+    @Published var focusPath: [UUID] = []           // 聚焦路径，表示当前层级
+    @Published var expandedNodes: Set<UUID> = []    // 展开的节点
+    @Published var animatingNodes: [UUID: Date] = [:]  // 正在动画的节点
+    
+    private let taskManager: TaskManager
+    
+    // MARK: - 计算属性
+    var isFocused: Bool { !focusPath.isEmpty }
+    var focusLevel: Int { focusPath.count }
+    var isInActionFocusMode: Bool { focusLevel >= 2 }
+    
+    // MARK: - 初始化
+    init(taskManager: TaskManager) {
+        self.taskManager = taskManager
+    }
+    
+    // MARK: - 统一节点操作
+    
+    /// 聚焦到任意节点
+    func focusTo(_ nodeId: UUID) {
+        let newPath = buildPathTo(nodeId)
+        focusPath = newPath
+        print("[FocusManager] 聚焦到节点: \(nodeId), 路径: \(newPath)")
+    }
+    
+    /// 加深聚焦（在当前路径基础上添加）
+    func focusDeeper(to nodeId: UUID) {
+        focusPath.append(nodeId)
+        print("[FocusManager] 加深聚焦到: \(nodeId)")
+    }
+    
+    /// 退出聚焦
+    func exitFocus() {
+        focusPath = []
+        expandedNodes.removeAll()
+        print("[FocusManager] 退出所有聚焦")
+    }
+    
+    /// 退出到指定层级
+    func exitToLevel(_ level: Int) {
+        guard level >= 0 else { 
+            exitFocus()
+            return 
+        }
+        
+        if level < focusPath.count {
+            focusPath = Array(focusPath.prefix(level + 1))
+            print("[FocusManager] 退出到层级: \(level)")
+        }
+    }
+    
+    /// 切换任意节点的完成状态
+    func toggleNodeCompletion(_ nodeId: UUID) {
+        let nodeType = identifyNodeType(nodeId)
+        
+        switch nodeType {
+        case .task(let task):
+            let wasCompleted = task.isCompleted
+            taskManager.toggleTaskCompletion(task)
+            handleCompletionAnimation(nodeId, wasCompleted: wasCompleted)
+            
+        case .checklistItem(let task, let item):
+            let wasCompleted = item.isCompleted
+            taskManager.toggleChecklistItemCompletion(task, itemId: item.id)
+            handleCompletionAnimation(nodeId, wasCompleted: wasCompleted)
+            
+        case .subStep(let task, let checklistItem, _):
+            let wasCompleted = findSubStepCompleted(nodeId, in: checklistItem.subStepsList)
+            taskManager.toggleSubStepCompletion(task, checklistItemId: checklistItem.id, subStepId: nodeId)
+            handleCompletionAnimation(nodeId, wasCompleted: wasCompleted)
+            
+        case .unknown:
+            print("[FocusManager] 无法识别节点类型: \(nodeId)")
+        }
+    }
+    
+    /// 展开/折叠节点
+    func toggleNodeExpansion(_ nodeId: UUID) {
+        if expandedNodes.contains(nodeId) {
+            expandedNodes.remove(nodeId)
+        } else {
+            expandedNodes.insert(nodeId)
+        }
+    }
+    
+    /// 检查节点是否展开
+    func isNodeExpanded(_ nodeId: UUID) -> Bool {
+        expandedNodes.contains(nodeId)
+    }
+    
+    /// 检查节点是否在动画中
+    func isNodeAnimating(_ nodeId: UUID) -> Bool {
+        animatingNodes[nodeId] != nil
+    }
+    
+    // MARK: - 导航和面包屑
+    
+    /// 获取面包屑标题
+    func getBreadcrumbTitles() -> [String] {
+        var titles = ["一步"]
+        
+        for nodeId in focusPath {
+            if let title = getNodeTitle(nodeId) {
+                titles.append(title)
+            }
+        }
+        
+        return titles
+    }
+    
+    /// 导航到面包屑层级
+    func navigateToBreadcrumb(_ level: Int) {
+        if level == 0 {
+            exitFocus()
+        } else {
+            exitToLevel(level - 1)
+        }
+    }
+    
+    // MARK: - 私有辅助方法
+    
+    /// 识别节点类型
+    private func identifyNodeType(_ nodeId: UUID) -> NodeType {
+        // 先查找任务
+        let allTasks = taskManager.getTasksByStatus(TaskStatus.doing.rawValue)
+        
+        for task in allTasks {
+            if task.id == nodeId {
+                return .task(task)
+            }
+            
+            // 查找小行动
+            if let checklist = task.checklist {
+                for item in checklist {
+                    if item.id == nodeId {
+                        return .checklistItem(task, item)
+                    }
+                    
+                    // 查找子步骤
+                    if findSubStepExists(nodeId, in: item.subStepsList) {
+                        return .subStep(task, item, nodeId)
+                    }
+                }
+            }
+        }
+        
+        return .unknown
+    }
+    
+    /// 构建到指定节点的路径
+    private func buildPathTo(_ nodeId: UUID) -> [UUID] {
+        let nodeType = identifyNodeType(nodeId)
+        
+        switch nodeType {
+        case .task(_):
+            return [nodeId]
+            
+        case .checklistItem(let task, _):
+            return [task.id, nodeId]
+            
+        case .subStep(let task, let checklistItem, _):
+            var path = [task.id, checklistItem.id]
+            if let subPath = buildSubStepPath(to: nodeId, in: checklistItem.subStepsList) {
+                path.append(contentsOf: subPath)
+            }
+            return path
+            
+        case .unknown:
+            return []
+        }
+    }
+    
+    /// 构建子步骤路径
+    private func buildSubStepPath(to targetId: UUID, in steps: [SubStep]) -> [UUID]? {
+        for step in steps {
+            if step.id == targetId {
+                return [targetId]
+            }
+            
+            if let subPath = buildSubStepPath(to: targetId, in: step.subSteps) {
+                return [step.id] + subPath
+            }
+        }
+        return nil
+    }
+    
+    /// 获取节点标题
+    private func getNodeTitle(_ nodeId: UUID) -> String? {
+        let nodeType = identifyNodeType(nodeId)
+        
+        switch nodeType {
+        case .task(let task):
+            return task.title
+        case .checklistItem(_, let item):
+            return item.title
+        case .subStep(_, _, let stepId):
+            return findSubStepTitle(stepId)
+        case .unknown:
+            return nil
+        }
+    }
+    
+    /// 查找子步骤标题
+    private func findSubStepTitle(_ stepId: UUID) -> String? {
+        let allTasks = taskManager.getTasksByStatus(TaskStatus.doing.rawValue)
+        
+        for task in allTasks {
+            if let checklist = task.checklist {
+                for item in checklist {
+                    if let title = findSubStepTitleRecursive(stepId, in: item.subStepsList) {
+                        return title
+                    }
+                }
+            }
+        }
+        return nil
+    }
+    
+    private func findSubStepTitleRecursive(_ stepId: UUID, in steps: [SubStep]) -> String? {
+        for step in steps {
+            if step.id == stepId {
+                return step.title
+            }
+            if let title = findSubStepTitleRecursive(stepId, in: step.subSteps) {
+                return title
+            }
+        }
+        return nil
+    }
+    
+    /// 检查子步骤是否存在
+    private func findSubStepExists(_ stepId: UUID, in steps: [SubStep]) -> Bool {
+        for step in steps {
+            if step.id == stepId {
+                return true
+            }
+            if findSubStepExists(stepId, in: step.subSteps) {
+                return true
+            }
+        }
+        return false
+    }
+    
+    /// 查找子步骤完成状态
+    private func findSubStepCompleted(_ stepId: UUID, in steps: [SubStep]) -> Bool {
+        for step in steps {
+            if step.id == stepId {
+                return step.isCompleted
+            }
+            if findSubStepExists(stepId, in: step.subSteps) {
+                return findSubStepCompleted(stepId, in: step.subSteps)
+            }
+        }
+        return false
+    }
+    
+    /// 处理完成动画
+    private func handleCompletionAnimation(_ nodeId: UUID, wasCompleted: Bool) {
+        if !wasCompleted {
+            // 从未完成变为完成，添加动画状态
+            animatingNodes[nodeId] = Date()
+            
+            // 延迟移除动画状态
+            DispatchQueue.main.asyncAfter(deadline: .now() + 3.5) { [weak self] in
+                self?.animatingNodes.removeValue(forKey: nodeId)
+                
+                // 如果是当前聚焦的节点，回退一级
+                if self?.focusPath.last == nodeId {
+                    self?.exitToLevel((self?.focusLevel ?? 1) - 2)
+                }
+            }
+        }
+    }
+}
+
+// MARK: - 节点类型定义
+
+private enum NodeType {
+    case task(Task)
+    case checklistItem(Task, ChecklistItem)  
+    case subStep(Task, ChecklistItem, UUID)
+    case unknown
+} 
\ No newline at end of file
diff --git a/1Step/1Step/ViewModels/Managers/UIStateManager.swift b/1Step/1Step/ViewModels/Managers/UIStateManager.swift
new file mode 100644
index 0000000..6073252
--- /dev/null
+++ b/1Step/1Step/ViewModels/Managers/UIStateManager.swift
@@ -0,0 +1,205 @@
+import Foundation
+import SwiftUI
+import AVFoundation
+
+/// UI 状态管理器 - 专门处理界面相关状态
+@MainActor
+class UIStateManager: ObservableObject {
+    // MARK: - 回声Drop相关状态
+    @Published var showingEchoDrop = false
+    @Published var echoText = ""
+    
+    // MARK: - 拖拽相关状态
+    @Published var draggedTask: Task? = nil
+    @Published var pendingCompletionTaskID: UUID? = nil
+    
+    // MARK: - 激励语相关
+    @Published var currentQuote: String = ""
+    
+    // MARK: - 最近添加项目集合
+    @Published var recentlyAddedItems: Set<UUID> = []
+    
+    // MARK: - 音频播放器
+    private var audioPlayer: AVAudioPlayer?
+    
+    // MARK: - 激励语数据
+    private let motivationalQuotes = [
+        "一步 · 你正在专注的事，不需要太多 ",
+        "先走这一步，剩下的事情之后再说",
+        "你不是要做很多事，只是现在这一步",
+        "此刻你只需关注这些",
+        "这些行动不为完成，只为靠近",
+        "如果太难开始，就只做一个小行动",
+        "没关系，小行动不大，但已经很勇敢",
+        "你正在的，就是你该在的这一步",
+        "不需要计划太远，走好这一步就好",
+        "你不是要变得更好，而是开始走路",
+        "没有计划的日子，也可以往前走一点",
+        "被打断也没关系，你还可以再迈出一小步",
+        "专注不是做完全部，而是愿意留时间给这一件",
+        "你没有掉队，只是以自己的速度在走",
+        "有些时候，只是开始，也已经是一种完整",
+        "你不需要向谁证明，只要和自己一起往前走",
+        "今天不做也没关系，你还有你自己",
+        "有时候，不动也是一种节奏",
+        "你可以回来，也可以暂停，这里都会等你"
+    ]
+    
+    private let taskManager: TaskManager
+    
+    init(taskManager: TaskManager) {
+        self.taskManager = taskManager
+        setupAudioPlayer()
+        selectQuoteForToday()
+    }
+    
+    // MARK: - 回声Drop功能
+    
+    /// 显示回声Drop
+    func showEchoDrop() {
+        withAnimation {
+            showingEchoDrop = true
+        }
+    }
+    
+    /// 隐藏回声Drop
+    func hideEchoDrop() {
+        withAnimation {
+            showingEchoDrop = false
+            echoText = ""
+        }
+    }
+    
+    /// 处理回声文本，将其转化为任务
+    func handleEchoDropText() {
+        guard !echoText.isEmpty else { return }
+        
+        // 创建新任务，状态改为 inbox
+        let task = taskManager.addTask(
+            title: echoText,
+            status: TaskStatus.inbox.rawValue
+        )
+        
+        // 清空文本
+        echoText = ""
+        
+        // 更新 Toast 提示
+        DependencyContainer.toastManager().showSuperLightInfo("已添加到收集箱")
+        
+        // 隐藏回声Drop
+        showingEchoDrop = false
+        
+        // 触发触觉反馈
+        let generator = UIImpactFeedbackGenerator(style: .medium)
+        generator.impactOccurred()
+    }
+    
+    /// 释放回声文本（不保存，只清空）
+    func releaseEcho() {
+        // 清空文本
+        echoText = ""
+        
+        // 隐藏回声Drop
+        showingEchoDrop = false
+    }
+    
+    // MARK: - 音频播放
+    
+    /// 播放完成音效
+    func playCompletionSound() {
+        audioPlayer?.currentTime = 0
+        audioPlayer?.play()
+    }
+    
+    /// 设置音频播放器
+    private func setupAudioPlayer() {
+        guard let soundURL = Bundle.main.url(forResource: "task_completed", withExtension: "wav") else {
+            print("无法找到音频文件")
+            return
+        }
+        
+        do {
+            // 配置音频会话为混音模式，允许与其他应用的音频共存
+            // 使用 .ambient 类别，这样不会中断其他应用的音频播放
+            try AVAudioSession.sharedInstance().setCategory(.ambient, mode: .default)
+            try AVAudioSession.sharedInstance().setActive(true, options: .notifyOthersOnDeactivation)
+            
+            audioPlayer = try AVAudioPlayer(contentsOf: soundURL)
+            audioPlayer?.volume = 0.3 // 设置音量为 30%
+            audioPlayer?.prepareToPlay()
+        } catch {
+            print("音频播放器初始化失败: \(error.localizedDescription)")
+        }
+    }
+    
+    // MARK: - 激励语管理
+    
+    /// 选择今天的激励语
+    private func selectQuoteForToday() {
+        let defaults = UserDefaults.standard
+        let today = Calendar.current.startOfDay(for: Date())
+        
+        // 获取上次显示第一条引言的日期
+        if let lastDate = defaults.object(forKey: "LastFirstQuoteDate") as? Date,
+           !Calendar.current.isDate(today, inSameDayAs: lastDate) {
+            // 如果是新的一天，显示第一条引言
+            currentQuote = motivationalQuotes[0]
+            
+            // 保存今天的日期
+            defaults.set(today, forKey: "LastFirstQuoteDate")
+        } else {
+            // 如果是同一天内再次打开，随机显示任意一条
+            currentQuote = motivationalQuotes.randomElement() ?? ""
+        }
+    }
+    
+    /// 刷新激励语（手动调用）
+    func refreshQuote() {
+        currentQuote = motivationalQuotes.randomElement() ?? ""
+    }
+    
+    // MARK: - 拖拽状态管理
+    
+    /// 设置拖拽任务
+    func setDraggedTask(_ task: Task?) {
+        draggedTask = task
+    }
+    
+    /// 设置待完成任务ID
+    func setPendingCompletionTaskID(_ taskID: UUID?) {
+        pendingCompletionTaskID = taskID
+    }
+    
+    /// 清除拖拽状态
+    func clearDragState() {
+        draggedTask = nil
+        pendingCompletionTaskID = nil
+    }
+    
+    // MARK: - 最近添加项目管理
+    
+    /// 添加最近添加的项目
+    func addRecentlyAddedItem(_ itemId: UUID) {
+        recentlyAddedItems.insert(itemId)
+        
+        // 3秒后自动移除
+        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) { [weak self] in
+            self?.recentlyAddedItems.remove(itemId)
+        }
+    }
+    
+    /// 移除最近添加的项目
+    func removeRecentlyAddedItem(_ itemId: UUID) {
+        recentlyAddedItems.remove(itemId)
+    }
+    
+    /// 清除所有最近添加的项目
+    func clearRecentlyAddedItems() {
+        recentlyAddedItems.removeAll()
+    }
+    
+    /// 检查是否是最近添加的项目
+    func isRecentlyAdded(_ itemId: UUID) -> Bool {
+        return recentlyAddedItems.contains(itemId)
+    }
+} 
\ No newline at end of file
diff --git a/1Step/1Step/Views/FocusView/Components/BreadcrumbView.swift b/1Step/1Step/Views/FocusView/Components/BreadcrumbView.swift
new file mode 100644
index 0000000..05af2d6
--- /dev/null
+++ b/1Step/1Step/Views/FocusView/Components/BreadcrumbView.swift
@@ -0,0 +1,89 @@
+import SwiftUI
+
+/// 面包屑导航视图
+struct BreadcrumbView: View {
+    @ObservedObject var focusManager: FocusManager
+    @ObservedObject var viewModel: FocusViewModel
+    @Binding var showingTaskDetailFromBreadcrumb: Bool
+    
+    var body: some View {
+        let breadcrumbTitles = focusManager.getBreadcrumbTitles()
+        
+        VStack(alignment: .leading, spacing: 8) {
+            // 当前聚焦的标题
+            if let currentTitle = breadcrumbTitles.last {
+                Text(currentTitle)
+                    .font(.title2)
+                    .fontWeight(.semibold)
+                    .foregroundColor(.primary)
+                    .multilineTextAlignment(.leading)
+            }
+            
+            // 面包屑路径
+            if breadcrumbTitles.count > 1 {
+                breadcrumbPath(titles: breadcrumbTitles)
+            }
+        }
+        .padding(.horizontal, 16)
+        .padding(.vertical, 12)
+    }
+    
+    private func breadcrumbPath(titles: [String]) -> some View {
+        HStack(spacing: 4) {
+            ForEach(Array(titles.dropLast().enumerated()), id: \.offset) { index, title in
+                Button {
+                    handleBreadcrumbTap(level: index)
+                } label: {
+                    Text(title)
+                        .font(.caption)
+                        .foregroundColor(.blue)
+                        .lineLimit(1)
+                }
+                .buttonStyle(PlainButtonStyle())
+                
+                if index < titles.count - 2 {
+                    Text("/")
+                        .font(.caption)
+                        .foregroundColor(.gray)
+                }
+            }
+            
+            Spacer()
+            
+            // 任务详情按钮
+            if focusManager.focusLevel > 0 {
+                Button {
+                    showingTaskDetailFromBreadcrumb = true
+                } label: {
+                    Image(systemName: "info.circle")
+                        .font(.caption)
+                        .foregroundColor(.blue)
+                }
+                .buttonStyle(PlainButtonStyle())
+            }
+        }
+    }
+    
+    private func handleBreadcrumbTap(level: Int) {
+        focusManager.navigateToBreadcrumb(level)
+    }
+}
+
+#Preview {
+    let taskManager = TaskManager(
+        taskRepository: DependencyContainer.taskRepository(),
+        projectRepository: DependencyContainer.projectRepository(),
+        tagRepository: DependencyContainer.tagRepository()
+    )
+    let viewModel = FocusViewModel(taskManager: taskManager)
+    
+    // 模拟聚焦状态
+    viewModel.focusManager.focusPath = [UUID(), UUID(), UUID()]
+    
+    return BreadcrumbView(
+        focusManager: viewModel.focusManager,
+        viewModel: viewModel,
+        showingTaskDetailFromBreadcrumb: .constant(false)
+    )
+    .padding()
+} 
\ No newline at end of file
diff --git a/1Step/1Step/Views/FocusView/Components/NodeListView.swift b/1Step/1Step/Views/FocusView/Components/NodeListView.swift
new file mode 100644
index 0000000..6f19581
--- /dev/null
+++ b/1Step/1Step/Views/FocusView/Components/NodeListView.swift
@@ -0,0 +1,242 @@
+import SwiftUI
+
+/// 通用节点列表视图 - 支持递归显示和交互
+struct NodeListView: View {
+    let nodes: [Node]
+    let context: DisplayContext
+    @ObservedObject var focusManager: FocusManager
+    @ObservedObject var viewModel: FocusViewModel
+    
+    private let maxDepth: Int
+    private let currentDepth: Int
+    
+    init(
+        nodes: [Node],
+        context: DisplayContext,
+        focusManager: FocusManager,
+        viewModel: FocusViewModel,
+        maxDepth: Int = 10,
+        currentDepth: Int = 0
+    ) {
+        self.nodes = nodes
+        self.context = context
+        self.focusManager = focusManager
+        self.viewModel = viewModel
+        self.maxDepth = maxDepth
+        self.currentDepth = currentDepth
+    }
+    
+    var body: some View {
+        LazyVStack(spacing: 0) {
+            ForEach(nodes, id: \.id) { node in
+                VStack(spacing: 0) {
+                    // 节点本身
+                    NodeView(
+                        node: node,
+                        style: getNodeStyle(for: node),
+                        context: context,
+                        focusManager: focusManager,
+                        viewModel: viewModel
+                    )
+                    .id(node.id)
+                    
+                    // 递归显示子节点
+                    if shouldShowChildren(for: node) {
+                        childrenView(for: node)
+                            .transition(.asymmetric(
+                                insertion: .scale(scale: 0.95).combined(with: .opacity),
+                                removal: .scale(scale: 0.95).combined(with: .opacity)
+                            ))
+                    }
+                }
+                .animation(.easeInOut(duration: 0.2), value: focusManager.isNodeExpanded(node.id))
+            }
+        }
+    }
+    
+    // MARK: - 子节点视图
+    
+    private func childrenView(for node: Node) -> some View {
+        NodeListView(
+            nodes: node.children,
+            context: context,
+            focusManager: focusManager,
+            viewModel: viewModel,
+            maxDepth: maxDepth,
+            currentDepth: currentDepth + 1
+        )
+        .padding(.top, 4)
+    }
+    
+    // MARK: - 辅助方法
+    
+    /// 是否应该显示子节点
+    private func shouldShowChildren(for node: Node) -> Bool {
+        guard !node.children.isEmpty else { return false }
+        guard currentDepth < maxDepth else { return false }
+        
+        switch context {
+        case .taskList:
+            return false // 任务列表模式不展开子节点
+            
+        case .focused:
+            // 聚焦模式：展开所有小行动
+            return focusManager.isInFocusPath(node.id) || 
+                   focusManager.isNodeExpanded(node.id) ||
+                   context.defaultExpanded
+            
+        case .actionFocus:
+            // ActionFocus模式：展开所有子步骤
+            return focusManager.isInFocusPath(node.id) || 
+                   focusManager.isNodeExpanded(node.id) ||
+                   context.defaultExpanded
+        }
+    }
+    
+    /// 获取节点样式
+    private func getNodeStyle(for node: Node) -> NodeStyle {
+        // 使用样式计算器
+        let calculatedStyle = NodeStyleCalculator.calculateStyle(
+            nodeId: node.id,
+            focusPath: focusManager.focusPath,
+            context: context
+        )
+        
+        // 根据深度微调
+        switch currentDepth {
+        case 0:
+            return calculatedStyle
+        case 1:
+            return calculatedStyle == .primary ? .secondary : calculatedStyle
+        default:
+            return .tertiary
+        }
+    }
+}
+
+// MARK: - 专用列表视图
+
+/// 任务列表视图
+struct TaskListView: View {
+    let tasks: [Task]
+    @ObservedObject var focusManager: FocusManager
+    @ObservedObject var viewModel: FocusViewModel
+    
+    var body: some View {
+        NodeListView(
+            nodes: tasks.map { $0 as Node },
+            context: .taskList,
+            focusManager: focusManager,
+            viewModel: viewModel
+        )
+    }
+}
+
+/// 聚焦节点树视图
+struct FocusedNodeTreeView: View {
+    let rootNodeId: UUID
+    @ObservedObject var focusManager: FocusManager
+    @ObservedObject var viewModel: FocusViewModel
+    
+    var body: some View {
+        VStack(spacing: 0) {
+            if let rootNode = findRootNode() {
+                NodeListView(
+                    nodes: [rootNode],
+                    context: focusManager.getCurrentDisplayContext(),
+                    focusManager: focusManager,
+                    viewModel: viewModel
+                )
+            } else {
+                EmptyStateView()
+            }
+        }
+    }
+    
+    private func findRootNode() -> Node? {
+        // 从ViewModel的任务中查找根节点
+        let allTasks = viewModel.doingTasks
+        
+        for task in allTasks {
+            if task.id == rootNodeId {
+                return task
+            }
+            
+            // 在小行动中查找
+            if let checklist = task.checklist {
+                for item in checklist {
+                    if item.id == rootNodeId {
+                        return item
+                    }
+                    
+                    // 在子步骤中查找
+                    if let found = findNodeInSubSteps(rootNodeId, in: item.subStepsList) {
+                        return found
+                    }
+                }
+            }
+        }
+        
+        return nil
+    }
+    
+    private func findNodeInSubSteps(_ nodeId: UUID, in steps: [SubStep]) -> Node? {
+        for step in steps {
+            if step.id == nodeId {
+                return step
+            }
+            
+            if let found = findNodeInSubSteps(nodeId, in: step.subSteps) {
+                return found
+            }
+        }
+        return nil
+    }
+}
+
+/// 空状态视图
+struct EmptyStateView: View {
+    var body: some View {
+        VStack(spacing: 16) {
+            Image(systemName: "checkmark.circle.badge.questionmark")
+                .font(.system(size: 48))
+                .foregroundColor(.gray.opacity(0.6))
+            
+            Text("没有一步任务")
+                .font(.title3)
+                .fontWeight(.medium)
+                .foregroundColor(.gray)
+            
+            Text("点击右上角的 + 开始添加任务")
+                .font(.body)
+                .foregroundColor(.gray.opacity(0.8))
+                .multilineTextAlignment(.center)
+        }
+        .padding(.top, 60)
+    }
+}
+
+// MARK: - 预览
+
+#Preview("任务列表") {
+    let taskManager = TaskManager()
+    let focusManager = FocusManager(taskManager: taskManager)
+    let viewModel = FocusViewModel(taskManager: taskManager, focusManager: focusManager)
+    
+    let testTasks = [
+        Task(title: "完成项目报告", status: TaskStatus.doing.rawValue),
+        Task(title: "学习SwiftUI", status: TaskStatus.doing.rawValue),
+        Task(title: "准备会议材料", status: TaskStatus.doing.rawValue)
+    ]
+    
+    return TaskListView(
+        tasks: testTasks,
+        focusManager: focusManager,
+        viewModel: viewModel
+    )
+    .padding()
+}
+#Preview("空状态") {
+    EmptyStateView()
+        .padding()
+} 
diff --git a/1Step/1Step/Views/FocusView/Components/NodeView.swift b/1Step/1Step/Views/FocusView/Components/NodeView.swift
new file mode 100644
index 0000000..f78802d
--- /dev/null
+++ b/1Step/1Step/Views/FocusView/Components/NodeView.swift
@@ -0,0 +1,315 @@
+import SwiftUI
+import SwipeActions
+
+/// 通用节点视图组件 - 统一处理Task、ChecklistItem、SubStep的显示
+struct NodeView: View {
+    let node: Node
+    let style: NodeStyle
+    let context: DisplayContext
+    @ObservedObject var focusManager: FocusManager
+    @ObservedObject var viewModel: FocusViewModel
+    
+    @State private var editText: String = ""
+    
+    var body: some View {
+        SwipeView {
+            nodeContentView
+        } leadingActions: { swipeContext in
+            leadingSwipeActions(swipeContext)
+        } trailingActions: { swipeContext in
+            trailingSwipeActions(swipeContext)
+        }
+        .onAppear {
+            editText = node.title
+        }
+    }
+    
+    // MARK: - 主内容区
+    
+    private var nodeContentView: some View {
+        HStack(spacing: style.spacing) {
+            // 勾选框
+            completionButton
+            
+            // 内容区
+            contentArea
+            
+            Spacer()
+            
+            // 操作区
+            actionArea
+        }
+        .padding(.leading, style.leadingPadding)
+        .contentShape(Rectangle())
+        .onTapGesture {
+            handleTap()
+        }
+        .onLongPressGesture {
+            handleLongPress()
+        }
+    }
+    
+    // MARK: - 勾选框
+    
+    private var completionButton: some View {
+        Button {
+            focusManager.toggleNodeCompletion(node.id)
+        } label: {
+            ZStack {
+                Circle()
+                    .strokeBorder(
+                        node.isCompleted ? Color.green : Color.gray.opacity(0.3),
+                        lineWidth: 2
+                    )
+                    .frame(width: style.checkboxSize, height: style.checkboxSize)
+                
+                if node.isCompleted {
+                    Image(systemName: "checkmark")
+                        .font(.system(size: style.checkboxSize * 0.6, weight: .medium))
+                        .foregroundColor(.green)
+                }
+                
+                // 动画效果
+                if focusManager.isNodeAnimating(node.id) {
+                    Circle()
+                        .fill(Color.green.opacity(0.3))
+                        .frame(width: style.checkboxSize, height: style.checkboxSize)
+                        .scaleEffect(focusManager.isNodeAnimating(node.id) ? 1.5 : 1.0)
+                        .opacity(focusManager.isNodeAnimating(node.id) ? 0 : 1)
+                        .animation(.easeOut(duration: 1.5), value: focusManager.isNodeAnimating(node.id))
+                }
+            }
+        }
+        .buttonStyle(PlainButtonStyle())
+    }
+    
+    // MARK: - 内容区
+    
+    private var contentArea: some View {
+        VStack(alignment: .leading, spacing: 4) {
+            // 标题
+            titleView
+            
+            // 子节点信息
+            if !node.children.isEmpty && style != .tertiary {
+                childrenInfoView
+            }
+        }
+    }
+    
+    private var titleView: some View {
+        Group {
+            if focusManager.isEditing(node.id) {
+                editingTextField
+            } else {
+                displayTitle
+            }
+        }
+    }
+    
+    private var editingTextField: some View {
+        TextField("输入标题", text: $editText)
+            .font(.system(size: style.fontSize, weight: style.fontWeight))
+            .textFieldStyle(RoundedBorderTextFieldStyle())
+            .onSubmit {
+                saveEdit()
+            }
+            .onAppear {
+                editText = node.title
+            }
+    }
+    
+    private var displayTitle: some View {
+        Text(node.title)
+            .font(.system(size: style.fontSize, weight: style.fontWeight))
+            .foregroundColor(node.isCompleted ? .gray : .primary)
+            .strikethrough(node.isCompleted)
+            .multilineTextAlignment(.leading)
+    }
+    
+    private var childrenInfoView: some View {
+        HStack(spacing: 4) {
+            let progress = node.completionProgress
+            
+            if progress.total > 0 {
+                Text("\(progress.completed)/\(progress.total)")
+                    .font(.caption2)
+                    .foregroundColor(.gray)
+                
+                if progress.completed > 0 {
+                    Circle()
+                        .fill(Color.green)
+                        .frame(width: 4, height: 4)
+                }
+            }
+        }
+    }
+    
+    // MARK: - 操作区
+    
+    private var actionArea: some View {
+        HStack(spacing: 8) {
+            // 展开按钮
+            if node.canHaveChildren && !node.children.isEmpty {
+                expandButton
+            }
+            
+            // 聚焦按钮
+            if canFocus {
+                focusButton
+            }
+        }
+    }
+    
+    private var expandButton: some View {
+        Button {
+            focusManager.toggleNodeExpansion(node.id)
+        } label: {
+            Image(systemName: focusManager.isNodeExpanded(node.id) ? "chevron.down" : "chevron.right")
+                .font(.system(size: style.actionIconSize * 0.8))
+                .foregroundColor(.gray)
+        }
+        .buttonStyle(PlainButtonStyle())
+    }
+    
+    private var focusButton: some View {
+        Button {
+            focusManager.focusTo(node.id)
+        } label: {
+            Image(systemName: "target")
+                .font(.system(size: style.actionIconSize * 0.9))
+                .foregroundColor(.blue)
+        }
+        .buttonStyle(PlainButtonStyle())
+    }
+    
+    // MARK: - 滑动操作
+    
+    private func leadingSwipeActions(_ swipeContext: SwipeContext) -> some View {
+        HStack {
+            // 快速完成
+            Button {
+                focusManager.toggleNodeCompletion(node.id)
+                swipeContext.close()
+            } label: {
+                Label("完成", systemImage: "checkmark.circle.fill")
+                    .foregroundColor(.white)
+            }
+            .frame(width: 60, height: 50)
+            .background(Color.green)
+        }
+    }
+    
+    private func trailingSwipeActions(_ swipeContext: SwipeContext) -> some View {
+        HStack {
+            // 编辑
+            Button {
+                focusManager.startEditing(node.id)
+                swipeContext.close()
+            } label: {
+                Label("编辑", systemImage: "pencil")
+                    .foregroundColor(.white)
+            }
+            .frame(width: 60, height: 50)
+            .background(Color.blue)
+            
+            // 删除
+            Button {
+                deleteNode()
+                swipeContext.close()
+            } label: {
+                Label("删除", systemImage: "trash")
+                    .foregroundColor(.white)
+            }
+            .frame(width: 60, height: 50)
+            .background(Color.red)
+        }
+    }
+    
+    // MARK: - 交互处理
+    
+    private func handleTap() {
+        // 根据上下文决定点击行为
+        switch context {
+        case .taskList:
+            if node.canHaveChildren && !node.children.isEmpty {
+                focusManager.focusTo(node.id)
+            }
+        case .focused, .actionFocus:
+            if node.canHaveChildren && !node.children.isEmpty {
+                focusManager.toggleNodeExpansion(node.id)
+            }
+        }
+    }
+    
+    private func handleLongPress() {
+        focusManager.selectNode(node.id)
+        focusManager.startEditing(node.id)
+    }
+    
+    private func saveEdit() {
+        guard !editText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
+            editText = node.title
+            focusManager.stopEditing()
+            return
+        }
+        
+        focusManager.updateNodeTitle(node.id, newTitle: editText.trimmingCharacters(in: .whitespacesAndNewlines))
+    }
+    
+    private func deleteNode() {
+        // TODO: 实现删除逻辑
+        print("[NodeView] 删除节点: \(node.id)")
+    }
+    
+    // MARK: - 计算属性
+    
+    private var canFocus: Bool {
+        switch context {
+        case .taskList:
+            return node.canHaveChildren && !node.children.isEmpty
+        case .focused:
+            return node.canHaveChildren && !node.children.isEmpty && style == .secondary
+        case .actionFocus:
+            return false // ActionFocus模式下不显示聚焦按钮
+        }
+    }
+}
+
+// MARK: - 预览
+
+#Preview {
+    let taskManager = TaskManager()
+    let focusManager = FocusManager(taskManager: taskManager)
+    let viewModel = FocusViewModel(taskManager: taskManager, focusManager: focusManager)
+    
+    // 创建测试任务
+    let testTask = Task(title: "测试任务", status: TaskStatus.doing.rawValue)
+    
+    return VStack(spacing: 16) {
+        NodeView(
+            node: testTask,
+            style: .primary,
+            context: .taskList,
+            focusManager: focusManager,
+            viewModel: viewModel
+        )
+        
+        NodeView(
+            node: testTask,
+            style: .secondary,
+            context: .focused,
+            focusManager: focusManager,
+            viewModel: viewModel
+        )
+        
+        NodeView(
+            node: testTask,
+            style: .tertiary,
+            context: .actionFocus,
+            focusManager: focusManager,
+            viewModel: viewModel
+        )
+    }
+    .padding()
+} 
\ No newline at end of file
diff --git a/1Step/1Step/Views/FocusView/FocusEchoDropView.swift b/1Step/1Step/Views/FocusView/FocusEchoDropView.swift
index d5801ba..d3b1c73 100644
--- a/1Step/1Step/Views/FocusView/FocusEchoDropView.swift
+++ b/1Step/1Step/Views/FocusView/FocusEchoDropView.swift
@@ -137,7 +137,7 @@ struct FocusEchoDropView: View {
             
             // 文本编辑器
             ZStack {
-                TextEditor(text: $viewModel.echoText)
+                TextEditor(text: viewModel.echoTextBinding)
                     .focused($isTextFieldFocused)
                     .scrollContentBackground(.hidden)
                     .background(Color.clear)
@@ -151,7 +151,7 @@ struct FocusEchoDropView: View {
                 // 释放效果视图
                 if isReleasing {
                     FocusReleaseEffectView(
-                        text: viewModel.echoText,
+                        text: viewModel.uiStateManager.echoText,
                         textOpacity: textOpacity,
                         colorScheme: colorScheme,
                         ripples: ripples
diff --git a/1Step/1Step/Views/FocusView/FocusView+ActionFocus.swift b/1Step/1Step/Views/FocusView/FocusView+ActionFocus.swift
deleted file mode 100644
index 32570b0..0000000
--- a/1Step/1Step/Views/FocusView/FocusView+ActionFocus.swift
+++ /dev/null
@@ -1,292 +0,0 @@
-import SwiftUI
-import SwiftData
-import SwipeActions
-
-// MARK: - ActionFocus 相关视图
-extension FocusView {
-    
-    /// ActionFocus 视图 - 显示面包屑和当前聚焦的内容
-    var actionFocusView: some View {
-        ScrollView {
-            VStack(alignment: .leading, spacing: 0) {
-                // 面包屑导航
-                breadcrumbView
-                    .padding(.horizontal, 16)
-                    .padding(.vertical, 8)
-                
-                // 当前聚焦的内容 - 使用卡片样式
-                ZStack {
-                    currentFocusedContentView
-                        .padding(.vertical, 8)
-                        // 移除额外的水平间距，保持与小行动展开内容一致
-                    
-                    // 添加底部分隔线，与聚焦任务样式保持一致
-                    VStack {
-                        Spacer()
-                        HStack {
-                            Spacer().frame(width: 62) // 与勾选框宽度对齐
-                            Rectangle()
-                                .frame(height: 0.5)
-                                .foregroundColor(Color.gray.opacity(0.25))
-                        }
-                        .padding(.bottom, 0)
-                    }
-                }
-                .frame(maxWidth: .infinity, alignment: .leading)
-                .padding(.horizontal, 16)
-            }
-        }
-        .scrollDisabled(true)
-    }
-    
-    /// 面包屑视图
-    var breadcrumbView: some View {
-        let titles = viewModel.getBreadcrumbTitles()
-        
-        return HStack(spacing: 8) {
-            ForEach(Array(titles.enumerated()), id: \.offset) { index, title in
-                Button(action: {
-                    print("[FocusView] 面包屑点击 - index: \(index), title: \(title)")
-                    
-                    // 所有面包屑点击都执行导航回退
-                    print("[FocusView] 执行面包屑导航到层级: \(index)")
-                    viewModel.navigateToBreadcrumbLevel(index)
-                }) {
-                    Text(title)
-                        .font(.system(size: 14, weight: .medium))
-                        .foregroundColor(.secondary)
-                        .lineLimit(1)
-                }
-                
-                if index < titles.count - 1 {
-                    Image(systemName: "chevron.right")
-                        .font(.system(size: 12))
-                        .foregroundColor(.secondary)
-                }
-            }
-            
-            Spacer()
-        }
-        .padding(.horizontal, 12)
-        .padding(.vertical, 8)
-        .background(Color.gray.opacity(0.05))
-        .cornerRadius(8)
-    }
-    
-    /// 当前聚焦的内容视图
-    var currentFocusedContentView: some View {
-        // 使用SwipeView包装整个内容，就像TaskRowView一样
-        SwipeView {
-        VStack(alignment: .leading, spacing: 16) {
-            if !viewModel.focusedSubStepPath.isEmpty {
-                // 显示子步骤内容
-                subStepContentView
-            } else if viewModel.focusedChecklistItemId != nil {
-                // 显示小行动内容
-                checklistItemContentView
-            }
-        }
-        } leadingActions: { context in
-            // 左滑操作：完成当前聚焦的内容
-            SwipeAction(
-                action: {
-                    // 先关闭滑动状态
-                    context.state.wrappedValue = .closed
-                    
-                    if !viewModel.focusedSubStepPath.isEmpty {
-                        // 如果在子步骤层级，完成当前子步骤
-                        if let lastStepId = viewModel.focusedSubStepPath.last {
-                            // 播放完成音效
-                            viewModel.playCompletionSound()
-                            
-                            // 触发烟花效果
-                            let screenSize = UIScreen.main.bounds.size
-                            let position = CGPoint(x: screenSize.width / 2, y: screenSize.height / 2)
-                            confettiManager.trigger(at: position)
-                            
-                            // 触发触感反馈
-                            UIImpactFeedbackGenerator(style: .medium).impactOccurred()
-                            
-                            // 完成子步骤
-                            viewModel.toggleSubStepCompletion(lastStepId)
-                        }
-                    } else if let item = viewModel.getFocusedChecklistItem() {
-                        // 如果在小行动层级，完成小行动
-                        // 播放完成音效
-                        viewModel.playCompletionSound()
-                        
-                        // 触发烟花效果
-                        let screenSize = UIScreen.main.bounds.size
-                        let position = CGPoint(x: screenSize.width / 2, y: screenSize.height / 2)
-                        confettiManager.trigger(at: position)
-                        
-                        // 触发触感反馈
-                        UIImpactFeedbackGenerator(style: .medium).impactOccurred()
-                        
-                        // 完成小行动
-                        viewModel.toggleChecklistItemCompletion(item)
-                    }
-                },
-                label: { highlighted in
-                    VStack(spacing: 4) {
-                        Image(systemName: "checkmark.circle.fill")
-                            .font(.system(size: 18, weight: .semibold))
-                            .foregroundColor(Color.green)
-                        Text("完成")
-                            .font(.caption)
-                            .fontWeight(.medium)
-                            .foregroundColor(Color.green)
-                    }
-                    .padding(.vertical, 4)
-                },
-                background: { _ in Color.clear }
-            )
-        } trailingActions: { context in
-            // 右滑操作：直接退出ActionFocus模式
-            SwipeAction(
-                action: {
-                    // 先关闭滑动状态
-                    context.state.wrappedValue = .closed
-                    
-                    // 直接退出ActionFocus模式
-                    viewModel.exitActionFocusModeWithSave()
-                },
-                label: { highlighted in
-                    VStack(spacing: 4) {
-                        Image(systemName: "arrow.left")
-                            .font(.system(size: 18, weight: .semibold))
-                            .foregroundColor(Color.gray)
-                        Text("退出一步模式")
-                            .font(.caption)
-                            .fontWeight(.medium)
-                            .foregroundColor(Color.gray)
-                    }
-                    .padding(.vertical, 4)
-                },
-                background: { _ in Color.clear }
-            )
-        }
-        .swipeActionsMaskCornerRadius(0)
-        .swipeActionCornerRadius(0)
-        .swipeActionsStyle(.equalWidths)
-        .swipeMinimumDistance(40)
-        .swipeActionsVisibleStartPoint(max(0, 40))
-        .swipeActionsVisibleEndPoint(max(0, 60))
-        .swipeSpacing(0)
-        .swipeOffsetCloseAnimation(stiffness: max(1, 90), damping: max(1, 35))
-        .swipeActionWidth(max(0, 90))
-    }
-    
-    /// 小行动内容视图
-    var checklistItemContentView: some View {
-        VStack(spacing: 0) {
-            // 每次都获取最新的小行动数据
-            if let item = viewModel.getFocusedChecklistItem() {
-                // 检查当前小行动是否正在动画
-                let isAnimating = viewModel.animatingSubSteps[item.id] != nil
-                let shouldShowAsCompleted = item.isCompleted && !isAnimating
-                
-                // 小行动卡片主体 - 保持与TaskRowView一致的结构
-                VStack(alignment: .leading, spacing: 0) {
-                    HStack(alignment: .center, spacing: 10) {
-                                            // 勾选框 - 点击完成小行动，保持与TaskRowView一致的样式
-                    Button(action: {
-                        // 播放完成音效
-                        viewModel.playCompletionSound()
-                        
-                        // 触发烟花效果（大反馈）
-                        let screenSize = UIScreen.main.bounds.size
-                        let position = CGPoint(x: screenSize.width / 2, y: screenSize.height / 2)
-                        confettiManager.trigger(at: position)
-                        
-                        // 触发触感反馈（大反馈）
-                        UIImpactFeedbackGenerator(style: .medium).impactOccurred()
-                        
-                        // 完成小行动并处理回退
-                        viewModel.toggleChecklistItemCompletion(item)
-                    }) {
-                        // 直接使用完成状态，不受动画状态影响
-                        Image(systemName: item.isCompleted ? "checkmark.circle.fill" : "circle")
-                                .font(.system(size: 22)) // 与TaskRowView保持一致
-                            .foregroundColor(item.isCompleted ? 
-                                           Color.green : 
-                                           Color.gray)
-                                .frame(width: 44, height: 44) // 与TaskRowView保持一致的尺寸
-                                .contentShape(Rectangle())
-                    }
-                        .padding(.leading, 4) // 与TaskRowView保持一致
-                        .buttonStyle(BorderlessButtonStyle())
-                    
-                                            // 小行动内容区域 - 保持与TaskRowView一致的结构
-                    ZStack(alignment: .trailing) {
-                            // 主要内容
-                        VStack(alignment: .leading, spacing: 4) {
-                                // 标题行 - 限制最多为2行
-                            Text(item.title)
-                                    .font(.system(size: 16))
-                                .foregroundColor(shouldShowAsCompleted ? 
-                                               Color.secondary : 
-                                                   Color.primary.opacity(0.8))
-                                    .multilineTextAlignment(.leading)
-                                    .lineLimit(2)
-                                .strikethrough(item.isCompleted, color: Color.secondary)
-                                .onTapGesture {
-                                    // 点击小行动标题显示任务详情
-                                    showingTaskDetailFromBreadcrumb = true
-                                }
-                            }
-                            .frame(maxWidth: .infinity, alignment: .leading)
-                            .contentShape(Rectangle())
-                            
-                            // 展开图标放在右侧中间位置
-                        Button(action: {
-                            toggleStepExpansion(item.id)
-                        }) {
-                            Image(systemName: isStepExpanded(item.id) ? "chevron.up" : "chevron.down")
-                                .font(.system(size: 13))
-                                .foregroundColor(Color.secondary.opacity(0.5))
-                                .frame(width: 24, height: 24)
-                                .background(Color.clear)
-                                    .contentShape(Rectangle())
-                                    .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isStepExpanded(item.id))
-                            }
-                            .buttonStyle(PlainButtonStyle())
-                            .frame(width: 30, height: 30)
-                                .contentShape(Rectangle())
-                        }
-                        .frame(maxWidth: .infinity)
-                    }
-                    .frame(maxWidth: .infinity)
-                    .opacity(shouldShowAsCompleted ? 0.5 : 1.0) // 添加透明度动画
-                    .animation(.easeInOut(duration: 0.8), value: isAnimating) // 添加动画
-                }
-                
-                // 展开区域
-                if isStepExpanded(item.id) {
-                    VStack(alignment: .leading, spacing: 0) {
-                    // 添加分隔线
-                    Rectangle()
-                        .frame(height: 0.3)
-                        .foregroundColor(Color.gray.opacity(0.2))
-                        .padding(.horizontal, 8)
-                            .padding(.top, 8)
-                        .padding(.bottom, 4)
-                    
-                    // 如果有子步骤，显示列表
-                    if item.hasSubSteps {
-                        subStepsListView(for: item)
-                    }
-                    
-                    // 添加子步骤输入框
-                    addSubStepInputView(for: item)
-                    }
-                    .padding(.horizontal, 16)
-                    .padding(.bottom, 12)
-                    .transition(.opacity)
-                    .contentShape(Rectangle())
-                }
-            }
-        }
-    }
-
-} 
\ No newline at end of file
diff --git a/1Step/1Step/Views/FocusView/FocusView+StepComponents.swift b/1Step/1Step/Views/FocusView/FocusView+StepComponents.swift
deleted file mode 100644
index b0d784f..0000000
--- a/1Step/1Step/Views/FocusView/FocusView+StepComponents.swift
+++ /dev/null
@@ -1,61 +0,0 @@
-import SwiftUI
-import SwiftData
-import SwipeActions
-
-// MARK: - 通用步骤组件
-extension FocusView {
-    
-    // MARK: - 展开状态管理
-    
-    /// 检查步骤是否展开
-    func isStepExpanded(_ stepId: UUID) -> Bool {
-        return expandedStepIds.contains(stepId)
-    }
-    
-    /// 切换步骤展开状态
-    func toggleStepExpansion(_ stepId: UUID) {
-        if expandedStepIds.contains(stepId) {
-            expandedStepIds.remove(stepId)
-        } else {
-            expandedStepIds.insert(stepId)
-        }
-    }
-    
-    // MARK: - 编辑状态管理
-    
-    /// 开始编辑步骤
-    func startEditingStep(stepId: UUID, currentTitle: String) {
-        editingStepId = stepId
-        editingText = currentTitle
-        isEditingFocused = true
-    }
-    
-    /// 保存步骤编辑
-    func saveStepEdit() {
-        let newTitle = editingText.trimmingCharacters(in: .whitespacesAndNewlines)
-        guard !newTitle.isEmpty, let stepId = editingStepId else { return }
-        
-        // 根据当前聚焦状态决定更新哪种类型的步骤
-        if let item = viewModel.getFocusedChecklistItem() {
-            // 更新小行动的子步骤
-            item.updateSubStepTitle(at: stepId, to: newTitle)
-            viewModel.taskManager.updateTask(viewModel.getFocusedTask()!)
-        }
-        
-        // 重置编辑状态
-        cancelStepEdit()
-        
-        // 触发轻微触感反馈
-        UIImpactFeedbackGenerator(style: .light).impactOccurred()
-        
-        // 刷新数据
-        viewModel.loadTasks()
-    }
-    
-    /// 取消步骤编辑
-    func cancelStepEdit() {
-        editingStepId = nil
-        editingText = ""
-        isEditingFocused = false
-    }
-} 
\ No newline at end of file
diff --git a/1Step/1Step/Views/FocusView/FocusView+SubSteps.swift b/1Step/1Step/Views/FocusView/FocusView+SubSteps.swift
deleted file mode 100644
index eaaeede..0000000
--- a/1Step/1Step/Views/FocusView/FocusView+SubSteps.swift
+++ /dev/null
@@ -1,651 +0,0 @@
-import SwiftUI
-import SwiftData
-import SwipeActions
-
-// MARK: - 子步骤相关视图
-extension FocusView {
-    
-    /// 子步骤内容视图
-    var subStepContentView: some View {
-        VStack(alignment: .leading, spacing: 12) {
-            if let currentSubStep = getCurrentFocusedSubStep() {
-                // 检查当前子步骤是否正在动画
-                let isAnimating = viewModel.animatingSubSteps[currentSubStep.id] != nil
-                let shouldShowAsCompleted = currentSubStep.isCompleted && !isAnimating
-                
-                // 当前子步骤标题区域 - 使用与任务标题相似的样式
-                HStack(spacing: 10) {
-                    Button(action: {
-                        // 播放完成音效
-                        viewModel.playCompletionSound()
-                        
-                        // 触发烟花效果（大反馈）
-                        let screenSize = UIScreen.main.bounds.size
-                        let position = CGPoint(x: screenSize.width / 2, y: screenSize.height / 2)
-                        confettiManager.trigger(at: position)
-                        
-                        // 触发触感反馈（大反馈）
-                        UIImpactFeedbackGenerator(style: .medium).impactOccurred()
-                        
-                        // 完成当前子步骤并处理回退
-                        if let lastStepId = viewModel.focusedSubStepPath.last {
-                            print("[FocusView] 点击大标题完成子步骤，ID: \(lastStepId)")
-                            viewModel.toggleSubStepCompletion(lastStepId)
-                        }
-                    }) {
-                        // 直接使用完成状态，不受动画状态影响
-                        Image(systemName: currentSubStep.isCompleted ? "checkmark.circle.fill" : "circle")
-                            .font(.system(size: 24)) // 增大到与任务勾选框相同大小
-                            .foregroundColor(currentSubStep.isCompleted ? 
-                                           Color.green : 
-                                           Color.gray)
-                    }
-                    
-                    // 使用ZStack来实现标题和展开图标的布局
-                    ZStack(alignment: .trailing) {
-                        // 标题内容
-                        VStack(alignment: .leading, spacing: 4) {
-                            // 根据编辑状态显示不同内容
-                            if editingStepId == currentSubStep.id {
-                                // 编辑模式：显示文本输入框
-                                TextField("子步骤内容", text: $editingText)
-                                    .font(.system(size: 15))
-                                    .foregroundColor(.primary)
-                                    .focused($isEditingFocused)
-                                    .onSubmit {
-                                        saveStepEdit()
-                                    }
-                                    .onAppear {
-                                        editingText = currentSubStep.title
-                                        isEditingFocused = true
-                                    }
-                            } else {
-                                // 显示模式：显示文本 - 使用动画状态
-                                Text(currentSubStep.title)
-                                    .font(.system(size: 16)) // 与行动标题保持一致
-                                    .foregroundColor(shouldShowAsCompleted ? 
-                                                   Color.secondary : 
-                                                   Color.primary.opacity(0.8)) // 与行动标题颜色保持一致
-                                    .strikethrough(currentSubStep.isCompleted, color: Color.secondary)
-                                    .frame(maxWidth: .infinity, alignment: .leading)
-                                    .contentShape(Rectangle())
-                                    .onTapGesture {
-                                        // 点击子步骤标题显示任务详情
-                                        showingTaskDetailFromBreadcrumb = true
-                                    }
-                                    .onAppear {
-                                        print("[FocusView] 子步骤大标题显示 - 标题: \(currentSubStep.title), 完成状态: \(currentSubStep.isCompleted)")
-                                    }
-                                    .padding(.trailing, 30) // 始终给展开按钮留出空间
-                            }
-                            
-                            // 编辑模式下显示保存和取消按钮
-                            if editingStepId == currentSubStep.id {
-                                HStack(spacing: 8) {
-                                    Button("取消") {
-                                        cancelStepEdit()
-                                    }
-                                    .font(.system(size: 14))
-                                    .foregroundColor(.secondary)
-                                    
-                                    Button("保存") {
-                                        saveStepEdit()
-                                    }
-                                    .font(.system(size: 14))
-                                    .foregroundColor(.accentColor)
-                                }
-                            }
-                        }
-                        
-                        // 展开图标（始终显示，即使没有子步骤也可以添加）
-                        if editingStepId != currentSubStep.id {
-                            Button(action: {
-                                toggleStepExpansion(currentSubStep.id)
-                            }) {
-                                Image(systemName: isStepExpanded(currentSubStep.id) ? "chevron.up" : "chevron.down")
-                                    .font(.system(size: 13))
-                                    .foregroundColor(Color.secondary.opacity(0.5))
-                                    .frame(width: 24, height: 24)
-                                    .background(Color.clear)
-                                    .contentShape(Rectangle())
-                            }
-                            .buttonStyle(PlainButtonStyle())
-                        }
-                    }
-                }
-                .padding(.horizontal, 18) // 与任务标题相同的内边距
-                .opacity(shouldShowAsCompleted ? 0.5 : 1.0) // 添加透明度动画
-                .animation(.easeInOut(duration: 0.8), value: isAnimating) // 添加动画
-                
-                // 子子步骤列表区域（只在展开时显示）
-                if isStepExpanded(currentSubStep.id) {
-                    // 添加分隔线
-                    Rectangle()
-                        .frame(height: 0.3)
-                        .foregroundColor(Color.gray.opacity(0.2))
-                        .padding(.horizontal, 8)
-                        .padding(.top, 2)
-                        .padding(.bottom, 4)
-                    
-                    // 如果有子子步骤，显示列表
-                    if currentSubStep.hasSubSteps {
-                        subSubStepsListView(for: currentSubStep)
-                    }
-                    
-                    // 添加子子步骤输入框
-                    addSubSubStepInputView(for: currentSubStep)
-                }
-            }
-        }
-    }
-    
-    /// 获取当前聚焦的子步骤
-    func getCurrentFocusedSubStep() -> SubStep? {
-        // 确保从最新的任务数据中获取
-        guard let focusedId = viewModel.focusedTaskId,
-              let freshTask = viewModel.taskManager.getTaskById(focusedId),
-              let freshItem = freshTask.checklist?.first(where: { $0.id == viewModel.focusedChecklistItemId }),
-              !viewModel.focusedSubStepPath.isEmpty else { return nil }
-        
-        var currentSteps = freshItem.subStepsList
-        var currentStep: SubStep?
-        
-        for stepId in viewModel.focusedSubStepPath {
-            if let step = currentSteps.first(where: { $0.id == stepId }) {
-                currentStep = step
-                currentSteps = step.subSteps
-            } else {
-                return nil
-            }
-        }
-        
-        return currentStep
-    }
-    
-    /// 子步骤列表视图（用于小行动聚焦模式）
-    func subStepsListView(for item: ChecklistItem) -> some View {
-        let sortedSteps = getSortedSubSteps(item.subStepsList)
-        
-        return VStack(alignment: .leading, spacing: 6) {
-            SwipeViewGroup {
-                ForEach(sortedSteps, id: \.id) { subStep in
-                    subStepRowView(subStep, item: item)
-                }
-            }
-        }
-    }
-    
-    /// 单个子步骤行视图
-    private func subStepRowView(_ subStep: SubStep, item: ChecklistItem) -> some View {
-        let isAnimating = viewModel.animatingSubSteps[subStep.id] != nil
-        let shouldShowAsCompleted = subStep.isCompleted && !isAnimating
-        
-        return SwipeView {
-            subStepContent(subStep, shouldShowAsCompleted: shouldShowAsCompleted, isAnimating: isAnimating, item: item)
-        } leadingActions: { _ in
-            // 空实现，禁用从左往右滑动
-        } trailingActions: { context in
-            subStepTrailingActions(subStep, item: item)
-        }
-        .swipeActionsMaskCornerRadius(0)
-        .swipeActionCornerRadius(0)
-        .swipeActionsStyle(.equalWidths)
-        .swipeMinimumDistance(60)
-        .swipeActionsVisibleStartPoint(max(0, 60))
-        .swipeActionsVisibleEndPoint(max(0, 90))
-        .swipeSpacing(max(0, 1))
-        .swipeOffsetCloseAnimation(stiffness: max(1, 120), damping: max(1, 50))
-        .swipeActionWidth(max(0, 85))
-    }
-    
-    /// 子步骤内容视图
-    private func subStepContent(_ subStep: SubStep, shouldShowAsCompleted: Bool, isAnimating: Bool, item: ChecklistItem) -> some View {
-        ZStack {
-            HStack(spacing: 10) {
-                subStepCheckbox(subStep)
-                subStepTextContainer(subStep, shouldShowAsCompleted: shouldShowAsCompleted, item: item)
-            }
-            .padding(.horizontal, 22)
-            .contentShape(Rectangle())
-            .opacity(shouldShowAsCompleted ? 0.5 : 1.0)
-            .animation(.easeInOut(duration: 0.8), value: isAnimating)
-            
-            // 完成标记
-            if isAnimating && subStep.isCompleted {
-                HStack {
-                    Spacer()
-                    Image(systemName: "checkmark.circle.fill")
-                        .font(.system(size: 24))
-                        .foregroundColor(.green)
-                        .scaleEffect(isAnimating ? 1.2 : 1.0)
-                        .animation(.spring(response: 0.4, dampingFraction: 0.6), value: isAnimating)
-                    Spacer()
-                }
-                .padding(.horizontal, 22)
-            }
-        }
-    }
-    
-    /// 子步骤勾选框
-    private func subStepCheckbox(_ subStep: SubStep) -> some View {
-        Button(action: {
-            // 如果是未完成变为完成，添加成功反馈
-            if !subStep.isCompleted {
-                // 播放完成音效
-                viewModel.playCompletionSound()
-                
-                // 触发成功触感反馈（中等强度）
-                UINotificationFeedbackGenerator().notificationOccurred(.success)
-            } else {
-                // 触发轻微触感反馈
-                UIImpactFeedbackGenerator(style: .light).impactOccurred()
-            }
-            
-            // 切换完成状态
-            viewModel.toggleSubStepCompletion(subStep.id)
-        }) {
-            Image(systemName: subStep.isCompleted ? "checkmark.circle.fill" : "circle")
-                .font(.system(size: 15))
-                .foregroundColor(subStep.isCompleted ? 
-                               Color.green.opacity(0.8) : 
-                               Color.gray.opacity(0.7))
-                .frame(width: 22, height: 22)
-        }
-        .buttonStyle(PlainButtonStyle())
-    }
-    
-    /// 子步骤文本容器
-    private func subStepTextContainer(_ subStep: SubStep, shouldShowAsCompleted: Bool, item: ChecklistItem) -> some View {
-        HStack(spacing: 6) {
-            subStepTextContent(subStep, shouldShowAsCompleted: shouldShowAsCompleted)
-            subStepActions(subStep, item: item)
-        }
-        .padding(.horizontal, 8)
-        .background(
-            subStep.isCompleted ? Color.clear : Color.gray.opacity(0.03)
-        )
-        .cornerRadius(4)
-    }
-    
-    /// 子步骤文本内容
-    private func subStepTextContent(_ subStep: SubStep, shouldShowAsCompleted: Bool) -> some View {
-        Group {
-            if editingStepId == subStep.id {
-                // 编辑模式：显示文本输入框
-                TextField("子步骤内容", text: $editingText)
-                    .font(.system(size: 15))
-                    .foregroundColor(.primary)
-                    .focused($isEditingFocused)
-                    .onSubmit {
-                        saveStepEdit()
-                    }
-                    .onAppear {
-                        editingText = subStep.title
-                        isEditingFocused = true
-                    }
-            } else {
-                // 显示模式：显示文本
-                Text(subStep.title)
-                    .font(.system(size: shouldShowAsCompleted ? 13 : 15))
-                    .foregroundColor(subStep.isCompleted ? 
-                                   .secondary.opacity(shouldShowAsCompleted ? 0.6 : 0.8) : 
-                                   .primary.opacity(0.75))
-                    .strikethrough(subStep.isCompleted, color: Color.secondary)
-                    .lineLimit(2)
-                    .truncationMode(.tail)
-                    .frame(maxWidth: .infinity, alignment: .leading)
-                    .contentShape(Rectangle())
-                    .onTapGesture {
-                        // 单击进入编辑模式（仅未完成的项目）
-                        if !subStep.isCompleted {
-                            startEditingStep(stepId: subStep.id, currentTitle: subStep.title)
-                        }
-                    }
-            }
-        }
-    }
-    
-    /// 子步骤操作按钮
-    @ViewBuilder
-    private func subStepActions(_ subStep: SubStep, item: ChecklistItem) -> some View {
-        if editingStepId == subStep.id {
-            // 编辑模式下显示保存和取消按钮
-            HStack(spacing: 8) {
-                Button("取消") {
-                    cancelStepEdit()
-                }
-                .font(.system(size: 14))
-                .foregroundColor(.secondary)
-                
-                Button("保存") {
-                    saveStepEdit()
-                }
-                .font(.system(size: 14))
-                .foregroundColor(.accentColor)
-            }
-        } else if !subStep.isCompleted {
-            // 正常状态下，未完成的子步骤显示 target 图标
-            Button(action: {
-                // 聚焦到这个子步骤
-                viewModel.focusedSubStepPath.append(subStep.id)
-            }) {
-                Image(systemName: "target")
-                    .font(.system(size: 14))
-                    .foregroundColor(.secondary.opacity(0.6))
-                    .frame(width: 20, height: 20)
-            }
-            .buttonStyle(PlainButtonStyle())
-        }
-    }
-    
-    /// 子步骤滑动删除操作
-    @ViewBuilder
-    private func subStepTrailingActions(_ subStep: SubStep, item: ChecklistItem) -> some View {
-        // 只为未完成的项目显示删除操作
-        if !subStep.isCompleted {
-            SwipeAction(
-                action: { 
-                    // 删除子步骤
-                    deleteSubStep(subStep.id, from: item)
-                },
-                label: { _ in
-                    Image(systemName: "xmark")
-                        .font(.system(size: 16))
-                        .foregroundColor(.secondary)
-                },
-                background: { _ in
-                    Color.clear
-                }
-            )
-        }
-    }
-    
-    /// 子子步骤列表视图（用于子步骤聚焦模式）
-    func subSubStepsListView(for parentStep: SubStep) -> some View {
-        let sortedSteps = getSortedSubSteps(parentStep.subSteps)
-        
-        return VStack(alignment: .leading, spacing: 6) {
-            SwipeViewGroup {
-                ForEach(sortedSteps, id: \.id) { subSubStep in
-                    subSubStepRowView(subSubStep, parentStep: parentStep)
-                }
-            }
-        }
-    }
-    
-    /// 单个子子步骤行视图
-    private func subSubStepRowView(_ subSubStep: SubStep, parentStep: SubStep) -> some View {
-        let isAnimating = viewModel.animatingSubSteps[subSubStep.id] != nil
-        let shouldShowAsCompleted = subSubStep.isCompleted && !isAnimating
-        
-        return SwipeView {
-            subSubStepContent(subSubStep, shouldShowAsCompleted: shouldShowAsCompleted, isAnimating: isAnimating, parentStep: parentStep)
-        } leadingActions: { _ in
-            // 空实现，禁用从左往右滑动
-        } trailingActions: { context in
-            subSubStepTrailingActions(subSubStep, parentStep: parentStep)
-        }
-        .swipeActionsMaskCornerRadius(0)
-        .swipeActionCornerRadius(0)
-        .swipeActionsStyle(.equalWidths)
-        .swipeMinimumDistance(60)
-        .swipeActionsVisibleStartPoint(max(0, 60))
-        .swipeActionsVisibleEndPoint(max(0, 90))
-        .swipeSpacing(max(0, 1))
-        .swipeOffsetCloseAnimation(stiffness: max(1, 120), damping: max(1, 50))
-        .swipeActionWidth(max(0, 85))
-    }
-    
-    /// 子子步骤内容视图
-    private func subSubStepContent(_ subSubStep: SubStep, shouldShowAsCompleted: Bool, isAnimating: Bool, parentStep: SubStep) -> some View {
-        ZStack {
-            HStack(spacing: 10) {
-                subSubStepCheckbox(subSubStep, parentStep: parentStep)
-                subSubStepTextContainer(subSubStep, shouldShowAsCompleted: shouldShowAsCompleted, parentStep: parentStep)
-            }
-            .padding(.horizontal, 22)
-            .contentShape(Rectangle())
-            .opacity(shouldShowAsCompleted ? 0.5 : 1.0)
-            .animation(.easeInOut(duration: 0.8), value: isAnimating)
-            
-            // 完成标记
-            if isAnimating && subSubStep.isCompleted {
-                HStack {
-                    Spacer()
-                    Image(systemName: "checkmark.circle.fill")
-                        .font(.system(size: 24))
-                        .foregroundColor(.green)
-                        .scaleEffect(isAnimating ? 1.2 : 1.0)
-                        .animation(.spring(response: 0.4, dampingFraction: 0.6), value: isAnimating)
-                    Spacer()
-                }
-                .padding(.horizontal, 22)
-            }
-        }
-    }
-    
-    /// 子子步骤勾选框
-    private func subSubStepCheckbox(_ subSubStep: SubStep, parentStep: SubStep) -> some View {
-        Button(action: {
-            // 如果是未完成变为完成，添加成功反馈
-            if !subSubStep.isCompleted {
-                // 播放完成音效
-                viewModel.playCompletionSound()
-                
-                // 触发成功触感反馈（中等强度）
-                UINotificationFeedbackGenerator().notificationOccurred(.success)
-            } else {
-                // 触发轻微触感反馈
-                UIImpactFeedbackGenerator(style: .light).impactOccurred()
-            }
-            
-            // 切换完成状态
-            viewModel.toggleSubStepCompletion(subSubStep.id)
-        }) {
-            Image(systemName: subSubStep.isCompleted ? "checkmark.circle.fill" : "circle")
-                .font(.system(size: 15))
-                .foregroundColor(subSubStep.isCompleted ? 
-                               Color.green.opacity(0.8) : 
-                               Color.gray.opacity(0.7))
-                .frame(width: 22, height: 22)
-        }
-        .buttonStyle(PlainButtonStyle())
-    }
-    
-    /// 子子步骤文本容器
-    private func subSubStepTextContainer(_ subSubStep: SubStep, shouldShowAsCompleted: Bool, parentStep: SubStep) -> some View {
-        HStack(spacing: 6) {
-            subSubStepTextContent(subSubStep, shouldShowAsCompleted: shouldShowAsCompleted)
-            subSubStepActions(subSubStep, parentStep: parentStep)
-        }
-        .padding(.horizontal, 8)
-        .background(
-            subSubStep.isCompleted ? Color.clear : Color.gray.opacity(0.03)
-        )
-        .cornerRadius(4)
-    }
-    
-    /// 子子步骤文本内容
-    private func subSubStepTextContent(_ subSubStep: SubStep, shouldShowAsCompleted: Bool) -> some View {
-        Group {
-            if editingStepId == subSubStep.id {
-                // 编辑模式：显示文本输入框
-                TextField("子步骤内容", text: $editingText)
-                    .font(.system(size: 15))
-                    .foregroundColor(.primary)
-                    .focused($isEditingFocused)
-                    .onSubmit {
-                        saveStepEdit()
-                    }
-                    .onAppear {
-                        editingText = subSubStep.title
-                        isEditingFocused = true
-                    }
-            } else {
-                // 显示模式：显示文本
-                Text(subSubStep.title)
-                    .font(.system(size: shouldShowAsCompleted ? 13 : 15))
-                    .foregroundColor(subSubStep.isCompleted ? 
-                                   .secondary.opacity(shouldShowAsCompleted ? 0.6 : 0.8) : 
-                                   .primary.opacity(0.75))
-                    .strikethrough(subSubStep.isCompleted, color: Color.secondary)
-                    .lineLimit(2)
-                    .truncationMode(.tail)
-                    .frame(maxWidth: .infinity, alignment: .leading)
-                    .contentShape(Rectangle())
-                    .onTapGesture {
-                        // 单击进入编辑模式（仅未完成的项目）
-                        if !subSubStep.isCompleted {
-                            startEditingStep(stepId: subSubStep.id, currentTitle: subSubStep.title)
-                        }
-                    }
-            }
-        }
-    }
-    
-    /// 子子步骤操作按钮
-    @ViewBuilder
-    private func subSubStepActions(_ subSubStep: SubStep, parentStep: SubStep) -> some View {
-        if editingStepId == subSubStep.id {
-            // 编辑模式下显示保存和取消按钮
-            HStack(spacing: 8) {
-                Button("取消") {
-                    cancelStepEdit()
-                }
-                .font(.system(size: 14))
-                .foregroundColor(.secondary)
-                
-                Button("保存") {
-                    saveStepEdit()
-                }
-                .font(.system(size: 14))
-                .foregroundColor(.accentColor)
-            }
-        } else if !subSubStep.isCompleted {
-            // 正常状态下，未完成的子子步骤显示 target 图标
-            Button(action: {
-                // 聚焦到这个子子步骤
-                viewModel.focusedSubStepPath.append(subSubStep.id)
-            }) {
-                Image(systemName: "target")
-                    .font(.system(size: 14))
-                    .foregroundColor(.secondary.opacity(0.6))
-                    .frame(width: 20, height: 20)
-            }
-            .buttonStyle(PlainButtonStyle())
-        }
-    }
-    
-    /// 子子步骤滑动删除操作
-    @ViewBuilder
-    private func subSubStepTrailingActions(_ subSubStep: SubStep, parentStep: SubStep) -> some View {
-        // 只为未完成的项目显示删除操作
-        if !subSubStep.isCompleted {
-            SwipeAction(
-                action: { 
-                    // 删除子子步骤
-                    deleteSubSubStep(subSubStep.id, from: parentStep)
-                },
-                label: { _ in
-                    Image(systemName: "xmark")
-                        .font(.system(size: 16))
-                        .foregroundColor(.secondary)
-                },
-                background: { _ in
-                    Color.clear
-                }
-            )
-        }
-    }
-    
-    /// 删除子子步骤
-    func deleteSubSubStep(_ stepId: UUID, from parentStep: SubStep) {
-        viewModel.removeSubStepFromFocusedItem(stepId)
-        viewModel.loadTasks()
-    }
-    
-    /// 获取排序后的子步骤列表
-    func getSortedSubSteps(_ steps: [SubStep]) -> [SubStep] {
-        // 定义临时列表，根据实际状态和动画状态进行分类
-        var incomplete: [SubStep] = []
-        var completed: [SubStep] = []
-        
-        // 遍历所有子步骤，根据动画状态决定它们应该在哪个分组中
-        for step in steps {
-            // 检查子步骤是否正在动画
-            if viewModel.animatingSubSteps[step.id] != nil {
-                // 如果正在动画，保持在未完成列表中（动画期间不移动）
-                incomplete.append(step)
-            } else {
-                // 如果没有动画，正常分类
-                if step.isCompleted {
-                    completed.append(step)
-                } else {
-                    incomplete.append(step)
-                }
-            }
-        }
-        
-        // 在未完成项目中，将新增项目放在最后
-        let oldIncompleteSteps = incomplete.filter { !viewModel.recentlyAddedItems.contains($0.id) }
-        let newIncompleteSteps = incomplete.filter { viewModel.recentlyAddedItems.contains($0.id) }
-        
-        // 排序：旧的未完成 + 新增的未完成 + 已完成
-        return oldIncompleteSteps + newIncompleteSteps + completed
-    }
-    
-
-    
-
-    
-
-    
-    /// 删除子步骤
-    func deleteSubStep(_ stepId: UUID, from item: ChecklistItem) {
-        viewModel.removeSubStepFromFocusedItem(stepId)
-        viewModel.loadTasks()
-    }
-    
-    /// 添加子子步骤输入框（用于子步骤聚焦模式）
-    func addSubSubStepInputView(for parentStep: SubStep) -> some View {
-        HStack(spacing: 10) {
-            // 勾选框图标
-            Image(systemName: "circle")
-                .font(.system(size: 15))
-                .foregroundColor(Color.gray.opacity(0.7))
-                .frame(width: 22, height: 22)
-            
-            // 文本字段
-            TextField("添加一步...", text: $newSubStepTitle)
-                .font(.system(size: 14))
-                .foregroundColor(.primary.opacity(0.7))
-                .submitLabel(.done)
-                .focused($isAddingSubStepFocused)
-                .onSubmit {
-                    addSubSubStep(to: parentStep)
-                }
-                .padding(.vertical, 5)
-        }
-        .padding(.horizontal, 22)  // 与子步骤内容保持一致的缩进
-        .padding(.top, 4)
-    }
-    
-    /// 添加子子步骤（用于子步骤聚焦模式）
-    func addSubSubStep(to parentStep: SubStep) {
-        let title = newSubStepTitle.trimmingCharacters(in: .whitespacesAndNewlines)
-        guard !title.isEmpty else { return }
-        
-        // 添加子子步骤到指定的父步骤
-        viewModel.addSubStepToFocusedItem(title: title)
-        newSubStepTitle = ""
-        
-        // 触发轻微触感反馈
-        UIImpactFeedbackGenerator(style: .light).impactOccurred()
-        
-        // 重新获得焦点，以便连续添加
-        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
-            isAddingSubStepFocused = true
-        }
-    }
-
-} 
\ No newline at end of file
diff --git a/1Step/1Step/Views/FocusView/FocusView+TaskList.swift b/1Step/1Step/Views/FocusView/FocusView+TaskList.swift
deleted file mode 100644
index be3b28f..0000000
--- a/1Step/1Step/Views/FocusView/FocusView+TaskList.swift
+++ /dev/null
@@ -1,329 +0,0 @@
-import SwiftUI
-import SwiftData
-import SwipeActions
-
-// MARK: - 任务列表相关视图和逻辑
-extension FocusView {
-    
-    /// 空状态视图
-    var emptyStateView: some View {
-        // 使用ScrollView代替List，但保持类似的布局行为
-        ScrollView {
-            // 使用VStack模拟类似List的空间结构
-            VStack(spacing: 0) {
-                // 设置气球图片与文字在屏幕中间靠上的位置
-                Spacer(minLength: UIScreen.main.bounds.height * 0.15)
-                
-                // 气球图片
-                Image("empty")
-                    .resizable()
-                    .scaledToFit()
-                    .frame(width: 240, height: 240)
-                
-                // 文字说明
-                VStack(spacing: 10) {
-                    Text("你现在没有行动")
-                        .font(.system(size: 17, weight: .medium))
-                        .foregroundColor(AppColors.UI.primaryText(for: colorScheme))
-                    
-                    Text("放松一下吧")
-                        .font(.system(size: 15, weight: .regular))
-                        .foregroundColor(AppColors.UI.secondaryText(for: colorScheme))
-                }
-                .padding(.top, 8)
-                
-                // 当任务表单显示时预留足够空间
-                Spacer(minLength: formCoordinator.isShowing ? 
-                       UIScreen.main.bounds.height * 0.35 : // 表单显示时留出约1/3屏幕空间
-                       UIScreen.main.bounds.height * 0.1)   // 正常状态只留少量空间
-            }
-            .frame(minHeight: UIScreen.main.bounds.height - 180) // 确保总高度合适
-        }
-        .scrollIndicators(.hidden) // 隐藏滚动指示器
-        .scrollDisabled(true) // 禁用滚动但保持布局行为
-        .animation(.easeInOut, value: formCoordinator.isShowing) // 添加动画平滑过渡
-    }
-    
-    /// 任务列表视图
-    var taskListView: some View {
-        List {
-            SwipeViewGroup {
-                ForEach(viewModel.doingTasks) { task in
-                    // 分离复杂表达式的各个部分
-                    let isPending = (viewModel.pendingCompletionTaskID == task.id)
-                    let isExpanded = expandedTaskId == task.id
-                    
-                    // 创建绑定
-                    let isExpandedBinding = Binding(
-                        get: { isExpanded },
-                        set: { newValue in
-                            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
-                                expandedTaskId = newValue ? task.id : nil
-                            }
-                        }
-                    )
-                    
-                    // 使用单独的方法创建任务行视图
-                    taskRowWithEffects(
-                        task: task,
-                        isPending: isPending,
-                        isExpanded: isExpanded,
-                        isExpandedBinding: isExpandedBinding
-                    )
-                    .id(task.id) // 提供稳定的标识符
-                    .listRowInsets(EdgeInsets(top: 4, leading: 16, bottom: 4, trailing: 16))
-                    .listRowBackground(Color.clear)
-                    .listRowSeparator(.hidden)
-                    // 添加拖拽功能
-                    .onDrag {
-                        // 设置当前拖拽的任务
-                        viewModel.draggedTask = task
-                        // 返回拖拽的NSItemProvider
-                        return NSItemProvider(object: task.id.uuidString as NSString)
-                    }
-                    .onDrop(of: [.text], delegate: TaskDropDelegate(
-                        item: task,
-                        items: viewModel.doingTasks,
-                        draggedItem: Binding(
-                            get: { viewModel.draggedTask },
-                            set: { viewModel.draggedTask = $0 }
-                        ),
-                        viewModel: viewModel
-                    ))
-                }
-            }
-        }
-        .listStyle(PlainListStyle())
-        .background(Color.clear) // 确保列表背景透明
-    }
-    
-    /// 聚焦任务视图 - 显示单个聚焦的任务
-    var focusedTaskView: some View {
-        ScrollView {
-            VStack(alignment: .leading, spacing: 0) { 
-                if let focusedTask = viewModel.getFocusedTask() {
-                    let initialSubtaskID = viewModel.initiallyFocusedSubtaskID // 读取初始聚焦的小行动ID
-                    
-                    SwipeViewGroup {
-                        // 为TaskRowView添加ZStack来支持自定义分隔线
-                        ZStack {
-                            TaskRowView(
-                                task: focusedTask,
-                                isPendingCompletion: (viewModel.pendingCompletionTaskID == focusedTask.id),
-                                isDragging: viewModel.draggedTask != nil, // 理论上聚焦模式不拖拽，但保持参数
-                                isExpanded: Binding<Bool>( // 确保 isExpanded 绑定对聚焦任务有效
-                                    get: { expandedTaskId == focusedTask.id },
-                                    set: { newValue in
-                                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
-                                            expandedTaskId = newValue ? focusedTask.id : nil
-                                        }
-                                    }
-                                ),
-                                isFocusMode: true,
-                                taskManager: taskManager,
-                                onTaskCompleted: { initiatingTask in
-                                    handleTaskCompletion(initiatingTask)
-                                },
-                                onTaskStatusChanged: { changedTask in
-                                    viewModel.loadTasks()
-                                },
-                                onTaskDeleted: { deletedTask in
-                                    viewModel.handleTaskDelete(deletedTask)
-                                    viewModel.handleTaskDeletionForFocus(deletedTask)
-                                    viewModel.loadTasks()
-                                },
-                                onFocusRequest: { task in
-                                    // 在聚焦模式下，再次请求聚焦同一个主任务，可能只是为了切换内部小行动焦点
-                                    // 或者切换到另一个小行动，此时也需要重新设置 initiallyFocusingSubtaskID
-                                    // 但 onFocusRequest 目前不传递小行动，所以这里主要用于主任务间的切换（如果允许的话）
-                                    // 或者，如果 TaskRowView 内部聚焦操作也通过 onFocusRequest 回调（不推荐），则需要额外参数
-                                    // 目前行为：在聚焦模式下，此回调不应改变主任务焦点，除非设计如此。
-                                },
-                                onSubtaskFocused: { taskUnchanged, subtaskToFocusNext in
-                                    // 当在已聚焦的主任务内部，切换小行动焦点时，进入 ActionFocus 模式
-                                    viewModel.focusOnChecklistItem(subtaskToFocusNext, in: taskUnchanged)
-                                },
-                                onExitFocus: {
-                                    viewModel.exitFocusMode()
-                                },
-                                onRestoreActionFocus: { taskId, checklistItemId, subStepPath in
-                                    // 使用新的恢复方法
-                                    viewModel.restoreActionFocusState(
-                                        taskId: taskId,
-                                        checklistItemId: checklistItemId,
-                                        subStepPath: subStepPath
-                                    )
-                                },
-                                initiallyToFocusSubtaskID: initialSubtaskID // <-- 传递初始小行动ID
-                            )
-                            .onAppear { // 当这个 TaskRowView (代表主焦点任务) 出现时
-                                if initialSubtaskID != nil { // 如果当时有一个初始小行动ID被传递了
-                                    viewModel.clearInitiallyFocusedSubtaskID() // 通知 ViewModel 清除它，因为它已经被消费了
-                                }
-                            }
-                            
-                            // 在底部添加一条自定义分隔线，只在勾选框之后显示
-                            VStack {
-                                Spacer()
-                                HStack {
-                                    Spacer().frame(width: 62) // 与勾选框宽度对齐
-                                    Rectangle()
-                                        .frame(height: 0.5) // 从0.3改为0.5，使线稍微粗一点
-                                        .foregroundColor(Color.gray.opacity(0.25)) // 从0.15改为0.25，使颜色更明显
-                                }
-                                .padding(.bottom, 0)
-                            }
-                        }
-                    }
-                    
-                    // 移除这里的Spacer，防止内容被推到屏幕中间
-                }
-            }
-            .frame(maxWidth: .infinity, alignment: .leading) // 确保内容左对齐
-            .padding(.horizontal, 16)
-            .padding(.vertical, 8)
-        }
-        .scrollDisabled(true) // 禁用滚动，因为聚焦模式下只有一个任务
-    }
-    
-    // 提取的方法：创建带视觉效果的任务行
-    func taskRowWithEffects(
-        task: Task,
-        isPending: Bool,
-        isExpanded: Bool,
-        isExpandedBinding: Binding<Bool>
-    ) -> some View {
-        ZStack {
-            TaskRowView(
-                task: task,
-                isPendingCompletion: isPending,
-                isDragging: viewModel.draggedTask != nil,
-                isExpanded: isExpandedBinding,
-                isFocusMode: false,
-                taskManager: taskManager,
-                onTaskCompleted: { initiatingTask in
-                    handleTaskCompletion(initiatingTask)
-                },
-                onTaskStatusChanged: { changedTask in
-                    viewModel.loadTasks()
-                },
-                onTaskDeleted: { deletedTask in
-                    viewModel.handleTaskDelete(deletedTask)
-                    viewModel.handleTaskDeletionForFocus(deletedTask)
-                    viewModel.loadTasks()
-                },
-                onFocusRequest: { task in
-                    // 聚焦到选中的任务
-                    viewModel.focusOnTask(task)
-                    
-                    // 展开聚焦的任务
-                    expandedTaskId = task.id
-                },
-                onSubtaskFocused: { taskToBecomeMainFocus, subtaskToInitiallyFocus in
-                    // 当 TaskRowView 内部的小行动被聚焦时，进入 ActionFocus 模式
-                    viewModel.focusOnChecklistItem(subtaskToInitiallyFocus, in: taskToBecomeMainFocus)
-                },
-                onExitFocus: {
-                    viewModel.exitFocusMode()
-                },
-                onRestoreActionFocus: { taskId, checklistItemId, subStepPath in
-                    // 使用新的恢复方法
-                    viewModel.restoreActionFocusState(
-                        taskId: taskId,
-                        checklistItemId: checklistItemId,
-                        subStepPath: subStepPath
-                    )
-                },
-                initiallyToFocusSubtaskID: viewModel.initiallyFocusedSubtaskID // <-- 传递初始小行动ID
-            )
-            .padding(.vertical, 8) // 减少垂直内边距
-            .padding(.horizontal, 4) // 减少水平内边距，增加卡片宽度
-            
-            // 在底部添加一条自定义分隔线，只在勾选框之后显示
-            VStack {
-                Spacer()
-                HStack {
-                    Spacer().frame(width: 62) // 与勾选框宽度对齐
-                    Rectangle()
-                        .frame(height: 0.6) // 从0.3改为0.5，使线稍微粗一点
-                        .foregroundColor(Color.gray.opacity(0.3)) // 从0.15改为0.7，使颜色更明显
-                }
-                .padding(.bottom, 0)
-            }
-        }
-        // 添加视觉重点效果：当有展开的任务时，降低其他任务的不透明度
-        .opacity(expandedTaskId != nil && expandedTaskId != task.id ? 0.7 : 1.0)
-        // 为展开的任务添加轻微背景色和圆角
-        .background(
-            isExpanded ? 
-            Color.gray.opacity(0.03) : 
-            Color.clear
-        )
-        .cornerRadius(isExpanded ? 8 : 0) // 圆角应用于View，而不是Color
-        // 添加平滑过渡动画
-        .animation(.easeInOut(duration: 0.2), value: expandedTaskId)
-    }
-    
-    /// 处理任务完成的通用逻辑
-    func handleTaskCompletion(_ initiatingTask: Task) {
-        // 设置 ViewModel 中的过渡状态
-        viewModel.pendingCompletionTaskID = initiatingTask.id
-        
-        // 播放完成音效
-        viewModel.playCompletionSound()
-        
-        // 触发烟花效果
-        let screenSize = UIScreen.main.bounds.size
-        let position = CGPoint(x: screenSize.width / 2, y: screenSize.height / 2)
-        confettiManager.trigger(at: position)
-        
-        // 构建完成提示和操作
-        let taskTitle = initiatingTask.title
-        let undoAction = ToastAction(title: "撤销", action: {
-            // 1. 清除 ViewModel 中的过渡状态
-            viewModel.pendingCompletionTaskID = nil
-            
-            // 2. 更新数据库
-            initiatingTask.status = TaskStatus.doing.rawValue
-            initiatingTask.completedAt = nil
-            taskManager.saveTask(initiatingTask)
-            
-            // 3. 刷新 ViewModel 数据
-            viewModel.loadTasks()
-            
-            // 4. 显示撤销成功提示
-            toastManager.hideCurrentAndThenShow {
-                toastManager.showUndoDeletionSuccess("已撤销完成")
-            }
-        })
-        
-        // 显示完成提示
-        toastManager.showTaskCompleted("\"\(taskTitle)\" 已完成", 
-                                      action: undoAction, 
-                                      onDismiss: {
-            // 1. 清除 ViewModel 中的过渡状态
-            let completingTaskID = viewModel.pendingCompletionTaskID
-            viewModel.pendingCompletionTaskID = nil
-            
-            // 2. 只有当确实是这个任务在等待确认时才更新数据库
-            if initiatingTask.id == completingTaskID {
-                 initiatingTask.status = TaskStatus.done.rawValue
-                 initiatingTask.completedAt = Date()
-                 taskManager.saveTask(initiatingTask)
-                 
-                 // 处理聚焦状态
-                 viewModel.handleTaskCompletionForFocus(initiatingTask)
-            }
-            
-            // 3. 刷新 ViewModel 数据
-            viewModel.loadTasks()
-        })
-    }
-    
-    /// 移动任务的私有方法
-    func moveTask(from source: IndexSet, to destination: Int) {
-        var updatedTasks = viewModel.doingTasks
-        updatedTasks.move(fromOffsets: source, toOffset: destination)
-        viewModel.updateTaskOrder(tasks: updatedTasks)
-    }
-} 
\ No newline at end of file
diff --git a/1Step/1Step/Views/FocusView/FocusView.swift b/1Step/1Step/Views/FocusView/FocusView.swift
index d58ddba..e74c95f 100644
--- a/1Step/1Step/Views/FocusView/FocusView.swift
+++ b/1Step/1Step/Views/FocusView/FocusView.swift
@@ -46,11 +46,12 @@ struct FocusView: View {
     @State var showingTaskSelector = false
     @State var showingGuideSheet = false
     @State var editMode = EditMode.inactive
-    @State var expandedTaskId: UUID? = nil
-    @State var expandedStepIds: Set<UUID> = []
-    @State var newSubStepTitle: String = ""
-    @State var editingStepId: UUID? = nil
-    @State var editingText: String = ""
+    // 这些状态现在由FocusManager统一管理
+    // @State var expandedTaskId: UUID? = nil  // -> focusManager.expandedNodes
+    // @State var expandedStepIds: Set<UUID> = []  // -> focusManager.expandedNodes
+    // @State var newSubStepTitle: String = ""  // -> focusManager.newItemText
+    // @State var editingStepId: UUID? = nil  // -> focusManager.editingNodeId
+    // @State var editingText: String = ""  // -> 在NodeView中本地管理
     @StateObject var formCoordinator = TaskFormCoordinator.shared
     @StateObject var taskFormViewModel: TaskFormViewModel
     @Environment(\.toastManager) var toastManager
@@ -97,7 +98,7 @@ struct FocusView: View {
         NavigationStack(path: $focusNavigationPath) { 
             mainContentView
                 .navigationBarTitleDisplayMode(.inline)
-                .navigationBarBackButtonHidden(viewModel.isInActionFocusMode)
+                .navigationBarBackButtonHidden(viewModel.focusManager.isInActionFocusMode)
             .onAppear {
                 viewModel.loadTasks()
                 print("[FocusView] Main ON APPEAR. focusNavigationPath count: \(focusNavigationPath.count)")
@@ -113,7 +114,7 @@ struct FocusView: View {
             .alert("清空一步", isPresented: $showingClearConfirmation) {
                 Button("取消", role: .cancel) { }
                 Button("确定", role: .destructive) {
-                    viewModel.clearAllDoingTasks()
+                    // viewModel.clearAllDoingTasks() // TODO: Re-evaluate this function in new architecture
                 }
             } message: {
                 Text("确定要将所有行动移回下一步吗？")
@@ -132,7 +133,7 @@ struct FocusView: View {
                     taskDetailSheet
                 }
         }
-        .onChange(of: viewModel.showingEchoDrop) { oldValue, newValue in
+        .onChange(of: viewModel.uiStateManager.showingEchoDrop) { oldValue, newValue in
             if !newValue && oldValue {
                 withAnimation {
                     isNavBarHiddenForEchoDrop = false
@@ -140,44 +141,24 @@ struct FocusView: View {
             }
         }
         .onChange(of: isRestModeActive) { _, newValue in
-            if !newValue && !viewModel.showingEchoDrop {
+            if !newValue && !viewModel.uiStateManager.showingEchoDrop {
                 isNavBarHiddenForEchoDrop = false
             }
         }
-        .onChange(of: viewModel.isFocusMode) { _, newValue in
+        .onChange(of: viewModel.focusManager.isFocused) { _, newValue in
             withAnimation {
                 isNavBarHiddenForFocus = newValue
             }
         }
-        .onChange(of: viewModel.autoExpandStepId) { _, newStepId in
-            if let stepId = newStepId {
-                // 自动展开指定的步骤
-                expandedStepIds.insert(stepId)
-                
-                // 清除自动展开标记
-                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
-                    viewModel.autoExpandStepId = nil
-                }
-            }
-        }
-        .onChange(of: viewModel.shouldExpandTaskAndSteps) { _, shouldExpand in
-            if shouldExpand {
-                // 展开任务
-                if let taskId = viewModel.focusedTaskId {
-                    expandedTaskId = taskId
-                }
-                
-                // 展开所有小行动
-                if let task = viewModel.getFocusedTask() {
-                    for item in task.checklist ?? [] {
-                        expandedStepIds.insert(item.id)
-                    }
-                }
-                
-                // 清除标记
-                viewModel.shouldExpandTaskAndSteps = false
-            }
-        }
+        // TODO: 这些自动展开逻辑需要适配到FocusManager
+        // .onChange(of: viewModel.autoExpandStepId) { _, newStepId in
+        //     if let stepId = newStepId {
+        //         viewModel.focusManager.expandedNodes.insert(stepId)
+        //         DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
+        //             viewModel.autoExpandStepId = nil
+        //         }
+        //     }
+        // }
     }
     
     // MARK: - 主要内容视图
@@ -193,7 +174,7 @@ struct FocusView: View {
             overlayViews
         }
         .animation(.easeInOut(duration: 0.2), value: keyboardObserver.isKeyboardVisible)
-        .animation(.easeInOut(duration: 0.2), value: expandedTaskId)
+        .animation(.easeInOut(duration: 0.2), value: viewModel.focusManager.expandedNodes)
         .animation(.easeInOut(duration: 0.5), value: isRestModeActive)
         .animation(.easeInOut(duration: 0.3), value: isNavBarHiddenForFocus)
     }
@@ -201,23 +182,37 @@ struct FocusView: View {
     // MARK: - 根据模式显示内容
     @ViewBuilder
     private var contentBasedOnMode: some View {
-        if viewModel.isInActionFocusMode {
-            // ActionFocus 模式：显示面包屑 + 当前聚焦的内容
-            actionFocusView
-        } else if viewModel.isFocusMode, let focusedTask = viewModel.getFocusedTask() {
-            // 传统聚焦模式：只显示聚焦的任务
-            if viewModel.doingTasks.isEmpty {
-                emptyStateView
-            } else {
-                focusedTaskView
+        if viewModel.doingTasks.isEmpty {
+            // 空状态
+            EmptyStateView()
+        } else if viewModel.focusManager.isFocused {
+            // 聚焦模式：显示面包屑 + 节点树
+            VStack(spacing: 0) {
+                // 面包屑导航
+                BreadcrumbView(
+                    focusManager: viewModel.focusManager,
+                    viewModel: viewModel,
+                    showingTaskDetailFromBreadcrumb: $showingTaskDetailFromBreadcrumb
+                )
+                
+                // 节点树内容
+                if let rootNodeId = viewModel.focusManager.focusPath.first {
+                    FocusedNodeTreeView(
+                        rootNodeId: rootNodeId,
+                        focusManager: viewModel.focusManager,
+                        viewModel: viewModel
+                    )
+                } else {
+                    EmptyStateView()
+                }
             }
         } else {
-            // 非聚焦模式：显示任务列表或空状态
-            if viewModel.doingTasks.isEmpty {
-                emptyStateView
-            } else {
-                taskListView
-            }
+            // 列表模式：显示所有任务
+            TaskListView(
+                tasks: viewModel.doingTasks,
+                focusManager: viewModel.focusManager,
+                viewModel: viewModel
+            )
         }
     }
     
@@ -225,14 +220,14 @@ struct FocusView: View {
     @ViewBuilder
     private var overlayViews: some View {
         // 底部按钮
-        if !(keyboardObserver.isKeyboardVisible && expandedTaskId != nil) && !isRestModeActive && !formCoordinator.isShowing {
+        if !(keyboardObserver.isKeyboardVisible && !viewModel.focusManager.expandedNodes.isEmpty) && !isRestModeActive && !formCoordinator.isShowing {
             bottomButtonsView
                 .transition(.opacity.combined(with: .move(edge: .bottom)))
-                .animation(.easeInOut(duration: 0.3), value: keyboardObserver.isKeyboardVisible || expandedTaskId != nil || formCoordinator.isShowing)
+                .animation(.easeInOut(duration: 0.3), value: keyboardObserver.isKeyboardVisible || !viewModel.focusManager.expandedNodes.isEmpty || formCoordinator.isShowing)
         }
         
         // 回声视图
-        if viewModel.showingEchoDrop && !isRestModeActive {
+        if viewModel.uiStateManager.showingEchoDrop && !isRestModeActive {
             FocusEchoDropView(viewModel: viewModel)
         }
         
@@ -254,14 +249,13 @@ struct FocusView: View {
         ToolbarItem(placement: .navigationBarLeading) {
             if !isRestModeActive && !isNavBarHiddenForEchoDrop && !isNavBarHiddenForFocus {
                 Button {
-                    focusNavigationPath.append(SearchNavigationTarget())
+                    // viewModel.restoreActionFocusState() // TODO: Re-evaluate this function in new architecture
                 } label: {
-                    Image("search")
-                        .resizable()
-                        .scaledToFit()
-                        .frame(width: 22, height: 22)
-                        .foregroundColor(Color.primary)
+                    Image(systemName: "arrow.uturn.backward")
                 }
+                .disabled(!viewModel.focusManager.isInActionFocusMode)
+                .opacity(viewModel.focusManager.isInActionFocusMode ? 1 : 0)
+                .animation(.easeInOut, value: viewModel.focusManager.isInActionFocusMode)
             }
         }
         
@@ -279,11 +273,26 @@ struct FocusView: View {
         
         ToolbarItem(placement: .navigationBarTrailing) {
             if !isRestModeActive && !isNavBarHiddenForEchoDrop && !isNavBarHiddenForFocus {
-                Button {
-                    focusNavigationPath.append(FocusBrowseNavigationTarget())
-                } label: {
-                    Image(systemName: "square.grid.2x2")
-                        .foregroundColor(Color.primary)
+                HStack(spacing: 16) {
+                    Button {
+                        focusNavigationPath.append(SearchNavigationTarget())
+                    } label: {
+                        Image("search")
+                            .resizable()
+                            .scaledToFit()
+                            .frame(width: 22, height: 22)
+                            .foregroundColor(Color.primary)
+                    }
+                    
+                    Button {
+                        formCoordinator.showForm()
+                    } label: {
+                        Image(systemName: "plus.circle.fill")
+                            .resizable()
+                            .scaledToFit()
+                            .frame(width: 24, height: 24)
+                            .foregroundColor(Color.blue)
+                    }
                 }
             }
         }
@@ -292,42 +301,32 @@ struct FocusView: View {
     // MARK: - 任务详情弹窗
     @ViewBuilder
     private var taskDetailSheet: some View {
-        if let task = viewModel.getFocusedTask() {
-            NavigationStack {
+        Group {
+            if let focusedTask = viewModel.getFocusedTask() {
                 TaskDetailView(
-                    task: task,
-                    sourcePageType: .doing,
+                    task: focusedTask,
+                    sourcePageType: .doing, // Explicitly set for FocusView context
                     onDeleteIntent: { taskToDelete in
                         viewModel.handleTaskDelete(taskToDelete)
-                        viewModel.handleTaskDeletionForFocus(taskToDelete)
-                        viewModel.loadTasks()
-                        showingTaskDetailFromBreadcrumb = false
+                        showingTaskDetailFromBreadcrumb = false // 关闭自身
                     },
                     onRestoreActionFocus: { taskId, checklistItemId, subStepPath in
-                        // 使用新的恢复方法
-                        viewModel.restoreActionFocusState(
-                            taskId: taskId,
-                            checklistItemId: checklistItemId,
-                            subStepPath: subStepPath
-                        )
-                        
-                        // 关闭详情页
+                        // viewModel.restoreActionFocusState( // TODO: Re-evaluate this function
+                        //     taskId: taskId,
+                        //     checklistItemId: checklistItemId,
+                        //     subStepPath: subStepPath
+                        // )
                         showingTaskDetailFromBreadcrumb = false
                     }
                 )
-            }
-            .presentationDetents([.medium, .large])
-            .presentationDragIndicator(.visible)
-            .onAppear {
-                print("[FocusView] Sheet 显示，任务: \(task.title)")
-            }
-        } else {
-            VStack {
-                Text("任务数据为空")
-                    .foregroundColor(.red)
-            }
-            .onAppear {
-                print("[FocusView] Sheet 显示，但 getFocusedTask() 返回 nil")
+                // Add onDismiss to handle actions previously in onComplete/onChangeStatus
+                .onAppear {
+                    // Optional: Any setup needed when TaskDetailView appears
+                }
+                // We might need to trigger a refresh or specific logic when the sheet is dismissed
+                // For now, let's assume TaskDetailView handles its own state changes and FocusView will react to data changes.
+            } else {
+                Text("无法加载任务详情")
             }
         }
     }
@@ -335,86 +334,27 @@ struct FocusView: View {
     // MARK: - 底部按钮视图
     var bottomButtonsView: some View {
         BottomButtonsView(
-            showingGuideSheet: $showingGuideSheet,
+            showingGuideSheet: $showingGuideSheet, // Pass FocusView's state
             showingTaskSelector: $showingTaskSelector,
             showingClearConfirmation: $showingClearConfirmation,
-            editMode: $editMode,
-            doingTasksCount: viewModel.doingTasks.count,
-            isFocusMode: viewModel.isFocusMode || viewModel.isInActionFocusMode,
-            onExitFocus: {
-                if viewModel.isInActionFocusMode {
-                    viewModel.exitActionFocusModeWithSave()
-                } else {
-                    viewModel.exitFocusMode()
-                }
-            },
-            onShowEchoDrop: {
-                viewModel.showEchoDrop()
-                isNavBarHiddenForEchoDrop = true
-            }
+            editMode: $editMode, // Pass FocusView's state
+            doingTasksCount: viewModel.doingTasks.count, // Get from viewModel
+            isFocusMode: viewModel.focusManager.isFocused, // Get from focusManager
+            onExitFocus: viewModel.exitFocus, // Pass viewModel method
+            onShowEchoDrop: viewModel.showEchoDrop // Pass viewModel method
         )
     }
     
     // MARK: - 任务表单浮层
     private var taskFormOverlay: some View {
-        taskFormViewModel.onTaskAdded = { task, message in
-            UIImpactFeedbackGenerator(style: .medium).impactOccurred()
-            viewModel.loadTasks()
-            DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
-                NotificationCenter.default.post(name: .requestKeyboardFocus, object: nil)
-            }
-        }
-        
-        taskFormViewModel.onDismiss = {
-            formCoordinator.hideForm()
-        }
-        
-        return AddTaskFormView(viewModel: taskFormViewModel)
-    }
-    
-    // MARK: - 子步骤相关方法
-    
-    /// 添加子步骤输入框（用于小行动聚焦模式）
-    func addSubStepInputView(for item: ChecklistItem) -> some View {
-        HStack(spacing: 10) {
-            // 勾选框图标
-            Image(systemName: "circle")
-                .font(.system(size: 15))
-                .foregroundColor(Color.gray.opacity(0.7))
-                .frame(width: 22, height: 22)
-            
-            // 文本字段
-            TextField("添加一步...", text: $newSubStepTitle)
-                .font(.system(size: 14))
-                .foregroundColor(.primary.opacity(0.7))
-                .submitLabel(.done)
-                .focused($isAddingSubStepFocused)
-                .onSubmit {
-                    addSubStep()
-                }
-                .padding(.vertical, 5)
-        }
-        .padding(.horizontal, 22)  // 与子步骤内容保持一致的缩进
-        .padding(.top, 4)
+        AddTaskFormView(
+            viewModel: taskFormViewModel
+        )
+        .id(formCoordinator.formId) // 重要：确保表单在重用时能正确重置
     }
     
-    /// 添加子步骤（用于小行动聚焦模式）
-    func addSubStep() {
-        let title = newSubStepTitle.trimmingCharacters(in: .whitespacesAndNewlines)
-        guard !title.isEmpty else { return }
-        
-        // 添加子步骤
-        viewModel.addSubStepToFocusedItem(title: title)
-        newSubStepTitle = ""
-        
-        // 触发轻微触感反馈
-        UIImpactFeedbackGenerator(style: .light).impactOccurred()
-        
-        // 重新获得焦点，以便连续添加
-        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
-            isAddingSubStepFocused = true
-        }
-    }
+    // MARK: - TODO: 子步骤添加功能迁移到NodeView中统一处理
+    // 这些方法将在NodeView中重新实现
     
 
     
diff --git a/1Step/1Step/Views/FocusView/TaskRowView+ChecklistItem.swift b/1Step/1Step/Views/FocusView/TaskRowView+ChecklistItem.swift
deleted file mode 100644
index ad7ec15..0000000
--- a/1Step/1Step/Views/FocusView/TaskRowView+ChecklistItem.swift
+++ /dev/null
@@ -1,461 +0,0 @@
-import SwiftUI
-import SwiftData
-import SwipeActions
-
-// 扩展TaskRowView以处理小行动相关的功能
-extension TaskRowView {
-    
-    // MARK: - 小行动视图
-    
-    /// 单个小行动行视图
-    func checklistItemRow(_ item: ChecklistItem) -> some View {
-        HStack(spacing: 10) {
-            // 勾选框部分，不包含背景色
-            Button(action: {
-                toggleChecklistItem(item)
-            }) {
-                Image(systemName: item.isCompleted ? "checkmark.circle.fill" : "circle")
-                    .font(.system(size: 15))
-                    .foregroundColor(item.isCompleted ? 
-                                    AppColors.UI.success(for: colorScheme).opacity(0.8) : 
-                                    AppColors.UI.gray(for: colorScheme).opacity(0.7))
-                    .frame(width: 22, height: 22)
-            }
-            .buttonStyle(PlainButtonStyle())
-            
-            // 文本和图标的容器，应用背景色
-            HStack(spacing: 6) {
-                // 根据编辑状态显示不同内容
-                if editingChecklistItemId == item.id {
-                    // 编辑模式：显示文本输入框
-                    TextField("小行动内容", text: $editingText)
-                        .font(.system(size: 15))
-                        .foregroundColor(.primary)
-                        .focused($isEditingFocused)
-                        .submitLabel(.done)  // 设置键盘为完成按钮
-                        .onSubmit {
-                            saveChecklistItemEdit()
-                        }
-                        .onAppear {
-                            editingText = item.title
-                            isEditingFocused = true
-                        }
-                } else {
-                    // 显示模式：显示文本
-                Text(item.title)
-                    .font(.system(size: 15))
-                    .foregroundColor(item.isCompleted ? 
-                                   .secondary.opacity(0.6) : 
-                                   .primary.opacity(0.75))
-                    .strikethrough(item.isCompleted, color: AppColors.UI.secondaryText(for: colorScheme))
-                    .lineLimit(2)
-                    .truncationMode(.tail)
-                    .frame(maxWidth: .infinity, alignment: .leading)
-                        .contentShape(Rectangle())
-                        .onTapGesture {
-                            // 单击进入编辑模式（仅未完成的项目）
-                            if !item.isCompleted {
-                                startEditingChecklistItem(itemId: item.id, currentTitle: item.title)
-                            }
-                        }
-                }
-                
-                // 编辑模式下显示保存、取消和删除按钮
-                if editingChecklistItemId == item.id {
-                    HStack(spacing: 8) {
-                        Button(action: {
-                            cancelChecklistItemEdit()
-                        }) {
-                            Image(systemName: "xmark")
-                                .font(.system(size: 12))
-                                .foregroundColor(.secondary)
-                        }
-                        .buttonStyle(PlainButtonStyle())
-                        
-                        Button(action: {
-                            saveChecklistItemEdit()
-                        }) {
-                            Image(systemName: "checkmark")
-                                .font(.system(size: 12))
-                                .foregroundColor(.accentColor)
-                        }
-                        .buttonStyle(PlainButtonStyle())
-                        
-                        // 删除按钮 - 只在编辑状态下显示
-                        Button(action: {
-                            deleteChecklistItem(item)
-                        }) {
-                            Image(systemName: "trash")
-                                .font(.system(size: 12))
-                                .foregroundColor(.red)
-                        }
-                        .buttonStyle(PlainButtonStyle())
-                    }
-                } else if !item.isCompleted && isFocusMode {
-                    // 只在聚焦模式下，未完成的项目显示target按钮
-                    Image(systemName: "target")
-                        .font(.system(size: 14))
-                        .foregroundColor(AppColors.UI.gray(for: colorScheme).opacity(0.7))
-                        .onTapGesture {
-                            focusOnChecklistItem(item)
-                        }
-                }
-            }
-            .padding(.vertical, 5)
-            .padding(.horizontal, 8)
-            .background(
-                item.isCompleted ? Color.clear : Color.gray.opacity(0.03)
-            )
-            .cornerRadius(4)
-        }
-        .padding(.horizontal, 22)
-        .contentShape(Rectangle())
-        .id("checklist_item_\(item.id)") // 添加 ID 用于滚动定位
-    }
-    
-    /// 聚焦的小行动视图
-    @ViewBuilder
-    func focusedChecklistItemView(_ item: ChecklistItem) -> some View {
-        // 显示是否已完成
-        let isCompleted = item.isCompleted || pendingChecklistItemCompletionID == item.id
-        
-        HStack(spacing: 10) {
-            // 勾选框部分
-            Button(action: {
-                handleCompleteFocusedChecklistItem(item)
-            }) {
-                Image(systemName: isCompleted ? "checkmark.circle.fill" : "circle")
-                    .font(.system(size: 18))
-                    .foregroundColor(isCompleted ? AppColors.UI.success(for: colorScheme).opacity(0.8) : AppColors.UI.gray(for: colorScheme).opacity(0.75))
-                    .frame(width: 24, height: 24)
-            }
-            .buttonStyle(PlainButtonStyle())
-            
-            // 文本容器，应用背景色
-            Text(item.title)
-                .font(.system(size: 15.5))
-                .strikethrough(isCompleted, color: AppColors.UI.secondaryText(for: colorScheme))
-                .foregroundColor(isCompleted ? AppColors.UI.secondaryText(for: colorScheme) : Color.primary.opacity(0.85))
-                .lineLimit(2)
-                .frame(maxWidth: .infinity, alignment: .leading)
-                .padding(.vertical, 6)
-                .padding(.horizontal, 10)
-                .background(
-                    RoundedRectangle(cornerRadius: 4)
-                        .fill(Color.blue.opacity(0.04))
-                )
-                .onTapGesture {
-                    // 可以在此处添加逻辑，例如显示更多细节
-                    print("点击了聚焦小行动标题: \(item.title)")
-                }
-        }
-        .padding(.horizontal, 18)
-        // 添加下面这行，防止点击事件传递到父视图
-        .contentShape(Rectangle())
-    }
-    
-    /// 展开内容视图（小行动列表和输入框）
-    var expandedContent: some View {
-        VStack(alignment: .leading, spacing: 6) {
-            if let checklistItems = sortedChecklistItems, !checklistItems.isEmpty {
-                // 添加一条极细的分隔线，提供主任务和小行动之间的视觉边界
-                Rectangle()
-                    .frame(height: 0.3)
-                    .foregroundColor(Color.gray.opacity(0.2))
-                    .padding(.horizontal, 8)
-                    .padding(.top, 2)
-                    .padding(.bottom, 4)
-                
-                // 显示小行动列表
-                ForEach(checklistItems, id: \.id) { item in
-                    checklistItemRow(item)
-                }
-            }
-            
-            // 添加小行动输入框
-            HStack(spacing: 10) {
-                // 勾选框图标
-                Image(systemName: "circle")
-                    .font(.system(size: 15))
-                    .foregroundColor(AppColors.UI.gray(for: colorScheme).opacity(0.7))
-                    .frame(width: 22, height: 22)
-                
-                // 文本字段
-                TextField("添加小行动...", text: $newChecklistItemTitle)
-                    .font(.system(size: 14))
-                    .foregroundColor(.primary.opacity(0.7))
-                    .focused($isInputFocused)
-                    .submitLabel(.done)
-                    .onSubmit {
-                        addChecklistItem()
-                    }
-                    .padding(.vertical, 5)
-            }
-            .padding(.horizontal, 20)
-            .padding(.top, 4)
-        }
-        // 添加下面这三行，防止点击事件传递到父视图
-        .contentShape(Rectangle())
-        .onTapGesture {} // 空手势拦截点击事件
-        .allowsHitTesting(true) // 确保子视图可以接收点击事件
-    }
-    
-    // MARK: - 小行动处理方法
-    
-    /// 添加小行动
-    func addChecklistItem() {
-        let title = newChecklistItemTitle.trimmingCharacters(in: CharacterSet.whitespacesAndNewlines)
-        guard !title.isEmpty else { return }
-        
-        // 添加小行动
-        taskManager.addChecklistItemToTask(task, title: title, createdAt: Date())
-        
-        // 清空输入框并保持焦点以便连续添加
-        newChecklistItemTitle = ""
-        
-        // 触发轻微触感反馈
-        UIImpactFeedbackGenerator(style: .light).impactOccurred()
-        
-        // 延迟重新聚焦
-        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
-            isInputFocused = true
-        }
-    }
-    
-    /// 开始编辑小行动
-    func startEditingChecklistItem(itemId: UUID, currentTitle: String) {
-        editingChecklistItemId = itemId
-        editingText = currentTitle
-        // 焦点会在 onAppear 中设置
-        
-        // 触感反馈提示用户进入编辑模式
-        UIImpactFeedbackGenerator(style: .light).impactOccurred()
-    }
-    
-    /// 保存小行动编辑
-    func saveChecklistItemEdit() {
-        guard let itemId = editingChecklistItemId else { return }
-        
-        let newTitle = editingText.trimmingCharacters(in: .whitespacesAndNewlines)
-        guard !newTitle.isEmpty else {
-            cancelChecklistItemEdit()
-            return
-        }
-        
-        // 更新小行动标题
-        taskManager.updateChecklistItemTitle(task, itemId: itemId, newTitle: newTitle)
-        
-        // 清除编辑状态
-        editingChecklistItemId = nil
-        editingText = ""
-        isEditingFocused = false
-    }
-    
-    /// 取消小行动编辑
-    func cancelChecklistItemEdit() {
-        editingChecklistItemId = nil
-        editingText = ""
-        isEditingFocused = false
-    }
-    
-    /// 删除小行动
-    func deleteChecklistItem(_ item: ChecklistItem) {
-        taskManager.removeChecklistItemFromTask(task, itemId: item.id)
-        
-        // 如果正在编辑这个项目，取消编辑状态
-        if editingChecklistItemId == item.id {
-            cancelChecklistItemEdit()
-        }
-    }
-    
-    /// 切换小行动完成状态
-    func toggleChecklistItem(_ item: ChecklistItem) {
-        if let index = task.checklist?.firstIndex(where: { $0.id == item.id }) {
-            let isCompleting = !task.checklist![index].isCompleted
-            if isCompleting {
-                // 触发触感反馈
-                UIImpactFeedbackGenerator(style: .light).impactOccurred()
-                
-                // 播放完成音效
-                audioPlayer?.volume = 0.3
-                audioPlayer?.play()
-                audioPlayer?.volume = 0.5
-                
-                // 触发小型烟花效果 - 更精确的位置
-                DispatchQueue.main.async {
-                    // 使用相对于主窗口的实际位置
-                    // 注意：由于勾选框是在视图左侧，我们将x值设为50左右更准确
-                    let position: CGPoint
-                    
-                    if let itemPosition = getItemPosition(for: item.id) {
-                        // 如果能获取到实际位置，使用实际位置
-                        position = CGPoint(
-                            x: itemPosition.x + 50, // 小行动勾选框附近
-                            y: itemPosition.y + 20  // 垂直居中偏移
-                        )
-                    } else {
-                        // 回退到估计位置
-                        position = CGPoint(
-                            x: 50,
-                            y: cardFrame.minY + CGFloat((index % 10) * 30) + 50 // 根据索引估算位置
-                        )
-                    }
-                    
-                    confettiManager.trigger(at: position)
-                }
-                
-                // 更新视图状态，但保持在原来位置
-                task.checklist?[index].isCompleted = true
-                task.checklist?[index].completedAt = Date()
-                
-                // 保存到仓库
-                taskManager.saveTask(task)
-                
-                // 记录动画状态，用于排序
-                animatingItemIds[item.id] = Date()
-                
-                // 2秒后移除动画状态
-                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
-                    self.animatingItemIds.removeValue(forKey: item.id)
-                }
-            } else {
-                // 设置为未完成
-                task.checklist?[index].isCompleted = false
-                task.checklist?[index].completedAt = nil
-                taskManager.saveTask(task)
-                
-                // 移除动画状态
-                animatingItemIds.removeValue(forKey: item.id)
-                
-                // 触发轻微触感反馈
-                UIImpactFeedbackGenerator(style: .light).impactOccurred()
-            }
-        }
-    }
-    
-    /// 获取小行动在屏幕上的位置（如果能获取到的话）
-    private func getItemPosition(for itemId: UUID) -> CGPoint? {
-        // 目前我们使用任务卡片位置进行估算
-        // 未来可改进为获取具体小行动的实际位置
-        guard isTracking, cardFrame != .zero else { return nil }
-        return CGPoint(x: cardFrame.minX, y: cardFrame.minY)
-    }
-    
-    /// 设置小行动为聚焦状态
-    func focusOnChecklistItem(_ item: ChecklistItem) {
-        if self.focusedChecklistItem?.id == item.id {
-            // 如果点击的是当前已聚焦的小行动，则取消聚焦
-            self.focusedChecklistItem = nil
-        } else {
-            // 否则，聚焦到新的小行动
-            self.focusedChecklistItem = item
-            self.isExpanded = false // 自动折叠展开区域
-            // 当小行动被聚焦时，调用回调，通知父视图此Task也应该成为主焦点，并传递小行动本身
-            onSubtaskFocused?(task, item)
-        }
-        // 添加轻微的触感反馈
-        UIImpactFeedbackGenerator(style: .light).impactOccurred()
-    }
-    
-    /// 完成聚焦的小行动
-    func handleCompleteFocusedChecklistItem(_ item: ChecklistItem) {
-        guard !item.isCompleted else { return } // 如果已经完成，则不执行任何操作
-        guard pendingChecklistItemCompletionID == nil else { return } // 如果已有待处理的完成，则不重复触发
-
-        // 设置 pending 状态
-        self.pendingChecklistItemCompletionID = item.id
-
-        // 触发轻微触感反馈
-        UIImpactFeedbackGenerator(style: .light).impactOccurred()
-        
-        // 播放完成音效（音量较小）
-        audioPlayer?.volume = 0.3
-        audioPlayer?.play()
-        audioPlayer?.volume = 0.5
-        
-        // 触发小型烟花效果 - 更精确的位置
-        DispatchQueue.main.async {
-            // 使用相对于主窗口的实际位置
-            let position: CGPoint
-            
-            if isTracking && cardFrame != .zero {
-                // 由于聚焦小行动在卡片下方，我们需要相应调整y坐标
-                position = CGPoint(
-                    x: cardFrame.minX + 50, // 聚焦小行动勾选框附近
-                    y: cardFrame.minY + 100 // 聚焦小行动大约在卡片下方100像素左右
-                )
-            } else {
-                // 回退到估计位置
-                position = CGPoint(
-                    x: 50,
-                    y: UIScreen.main.bounds.height / 3 // 屏幕上方1/3处
-                )
-            }
-            
-            confettiManager.trigger(at: position)
-        }
-
-        // 显示带撤销的 Toast
-        toastManager.showTaskCompleted("\"\(item.title)\" 已完成", action: ToastAction(title: "撤销", action: {
-            if self.pendingChecklistItemCompletionID == item.id {
-                self.pendingChecklistItemCompletionID = nil
-                toastManager.hideCurrentAndThenShow {
-                    toastManager.showUndoDeletionSuccess("已撤销完成")
-                }
-            }
-        }), onDismiss: {
-            guard self.pendingChecklistItemCompletionID == item.id else { return }
-
-            // 更新数据模型
-            if let index = self.task.checklist?.firstIndex(where: { $0.id == item.id }) {
-                self.task.checklist?[index].isCompleted = true
-                self.task.checklist?[index].completedAt = Date()
-                self.taskManager.saveTask(self.task)
-            }
-
-            // 清除 pending 状态
-            self.pendingChecklistItemCompletionID = nil
-            
-            // 退出小行动聚焦状态
-            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
-                if self.focusedChecklistItem?.id == item.id {
-                    withAnimation {
-                        self.focusedChecklistItem = nil
-                    }
-                }
-            }
-        })
-    }
-    
-    // MARK: - 小行动计算属性
-    
-    /// 排序后的小行动列表 (未完成的按创建时间倒序, 2分钟内完成的按完成时间倒序)
-    var sortedChecklistItems: [ChecklistItem]? {
-        guard let checklist = task.checklist else { return nil }
-        
-        // 过滤显示未完成和2分钟内完成的小行动
-        let twoMinutesAgo = Date().addingTimeInterval(-120) // 2分钟前
-        
-        // 未完成项按创建时间倒序，包括正在动画的完成项目
-        let incomplete = checklist.filter { !$0.isCompleted || animatingItemIds.keys.contains($0.id) }
-                                  .sorted { $0.createdAt > $1.createdAt }
-        
-        // 2分钟内完成的项目按完成时间倒序，不包括正在动画的项目
-        let recentlyCompleted = checklist.filter { 
-            $0.isCompleted && 
-            !animatingItemIds.keys.contains($0.id) && 
-            ($0.completedAt ?? .distantPast) > twoMinutesAgo
-        }
-        .sorted { ($0.completedAt ?? .distantPast) > ($1.completedAt ?? .distantPast) }
-        
-        // 合并结果，排除当前聚焦的小行动
-        var result = incomplete + recentlyCompleted
-        
-        // 从结果中排除当前聚焦的小行动
-        if let focusedItem = focusedChecklistItem {
-            result = result.filter { $0.id != focusedItem.id }
-        }
-        
-        return result
-    }
-} 
diff --git a/1Step/1Step/Views/FocusView/TaskRowView+Swipe.swift b/1Step/1Step/Views/FocusView/TaskRowView+Swipe.swift
deleted file mode 100644
index 60d5252..0000000
--- a/1Step/1Step/Views/FocusView/TaskRowView+Swipe.swift
+++ /dev/null
@@ -1,179 +0,0 @@
-import SwiftUI
-import SwipeActions
-
-// 扩展TaskRowView以处理滑动相关的功能
-extension TaskRowView {
-    // MARK: - 滑动处理方法
-    
-    /// 关闭滑动视图方法
-    func closeSwipeView() {
-        // 添加短暂延迟，确保UI状态已更新
-        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
-            if let context = self.activeContext {
-                context.state.wrappedValue = .closed
-            }
-        }
-    }
-    
-    /// 处理任务完成操作
-    func handleCompleteAction() {
-        // 保存当前状态用于可能的撤销（虽然主要逻辑在 FocusView）
-        let oldStatus = task.status
-        
-        // 添加卡片微动画
-        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
-            cardScale = 0.95
-        }
-        
-        // 通知父视图处理任务完成意图
-        onTaskCompleted?(task)
-    }
-    
-    /// 处理主任务聚焦操作
-    func handleFocusAction() {
-        // 先关闭滑动菜单
-        closeSwipeView()
-        // 调用回调，通知父视图聚焦此任务
-        onFocusRequest?(task)
-    }
-    
-    /// 处理安排操作 - 显示状态选择菜单
-    func handleArrangeAction() {
-        // 先关闭滑动菜单
-        closeSwipeView()
-        
-        // 触发震动反馈
-        let generator = UIImpactFeedbackGenerator(style: .light)
-        generator.impactOccurred()
-        
-        // 计算显示位置
-        var position: CGPoint
-        
-        if cardFrame != .zero {
-            // 使用卡片的顶部中点，在其上方20pt显示
-            position = CGPoint(
-                x: UIScreen.main.bounds.width / 2, // 水平居中
-                y: cardFrame.minY - 20 // 垂直在卡片上方20pt
-            )
-        } else {
-            // 默认位置
-            position = CGPoint(
-                x: UIScreen.main.bounds.width / 2,
-                y: UIScreen.main.bounds.height * 0.3
-            )
-        }
-        
-        // 确保不超出屏幕顶部安全区域
-        position.y = max(position.y, 100)
-        
-        // 使用 actionSheetManager 显示状态选择菜单
-        actionSheetManager.showActionSheet(
-            for: task,
-            at: position,
-            onChange: { newStatus in
-                // 任务状态改变时通知父视图
-                self.task.status = newStatus
-                self.task.updatedAt = Date()
-                self.taskManager.saveTask(self.task)
-                self.onTaskStatusChanged?(self.task)
-                
-                // 添加轻震动反馈
-                let generator = UIImpactFeedbackGenerator(style: .light)
-                generator.impactOccurred()
-                
-                // 显示轻量级提示
-                let statusDescription = TaskStatus(rawValue: newStatus)?.description ?? "其他状态"
-                self.toastManager.showSuperLightInfo("已移动到\(statusDescription)")
-                
-                // 如果改变为非"一步"状态，添加视觉反馈
-                if newStatus != TaskStatus.doing.rawValue {
-                    withAnimation(.easeInOut(duration: 0.25)) {
-                        self.cardOpacity = 0.2
-                        self.cardScale = 0.95
-                    }
-                }
-            },
-            onDismiss: {
-                // 菜单关闭后，恢复卡片正常状态
-                withAnimation {
-                    self.cardOpacity = 1.0
-                    self.cardScale = 1.0
-                }
-            }
-        )
-    }
-    
-    /// 处理退出聚焦操作 - 用于聚焦模式下
-    func handleExitFocusAction() {
-        // 先关闭滑动菜单
-        closeSwipeView()
-        
-        // 触发震动反馈
-        let generator = UIImpactFeedbackGenerator(style: .light)
-        generator.impactOccurred()
-        
-        // 如果有回调，则退出聚焦模式
-        if let onExitFocus = onExitFocus {
-            onExitFocus()
-        }
-    }
-    
-    /// 处理移回下一步操作 - 使用Toast提供撤销选项
-    func handleMoveToNextAction() {
-        // 先关闭滑动菜单
-        closeSwipeView()
-        
-        // 等待滑动菜单关闭后再执行动画和状态变更
-        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
-            // 添加卡片微动画
-            withAnimation(.easeInOut(duration: 0.25)) {
-                self.cardOpacity = 0.2
-                self.cardScale = 0.95
-            }
-            
-            // 触发震动反馈
-            let generator = UIImpactFeedbackGenerator(style: .medium)
-            generator.impactOccurred()
-            
-            // 保存原始状态，以便可以撤销
-            self.oldSection = self.task.status
-            
-            // 显示移动提示，包含任务标题
-            self.toastManager.show("\"\(self.task.title)\" 已移回下一步", type: .info, position: .top, action: ToastAction(title: "撤销", action: {
-                // 撤销移动操作
-                self.undoMove()
-                
-            }), onDismiss: {
-                // 用户未撤销，执行实际移动操作
-                self.task.status = TaskStatus.done.rawValue
-                self.task.completedAt = Date()
-                self.taskManager.saveTask(self.task)
-                
-                // 通知父视图更新列表
-                self.onTaskStatusChanged?(self.task)
-            })
-        }
-    }
-    
-    /// 撤销移动操作
-    func undoMove() {
-        if let oldSection = oldSection {
-            // 恢复任务状态
-            task.status = oldSection
-            task.updatedAt = Date()
-            taskManager.saveTask(task)
-            
-            // 添加动画恢复卡片视觉状态
-            withAnimation(.easeInOut(duration: 0.2)) {
-                cardOpacity = 1.0
-                cardScale = 1.0
-            }
-            
-            // 使用新方法确保前一个Toast完全隐藏后再显示撤销Toast
-            toastManager.hideCurrentAndThenShow {
-                // 显示撤销提示
-                toastManager.showUndoDeletionSuccess("已撤销移动")
-            }
-        }
-    }
-} 
\ No newline at end of file
diff --git a/1Step/1Step/Views/FocusView/TaskRowView+Utils.swift b/1Step/1Step/Views/FocusView/TaskRowView+Utils.swift
deleted file mode 100644
index bb9871f..0000000
--- a/1Step/1Step/Views/FocusView/TaskRowView+Utils.swift
+++ /dev/null
@@ -1,55 +0,0 @@
-import SwiftUI
-import SwiftData
-
-// 扩展TaskRowView以添加辅助方法
-extension TaskRowView {
-    
-    // MARK: - 属性计算和数据访问
-    
-    /// 获取项目显示名称
-    func getProjectDisplayName(for projectId: UUID?) -> String? {
-        guard let projectId = projectId else { return nil }
-        if let project = taskManager.getProjectById(projectId) {
-            return project.name
-        }
-        return nil
-    }
-    
-    /// 获取项目颜色
-    func getProjectColor(for projectId: UUID?) -> Color? {
-        guard let projectId = projectId else { return nil }
-        if let project = taskManager.getProjectById(projectId) {
-            return Color(hex: project.color ?? "#007AFF") ?? Color.purple
-        }
-        return nil
-    }
-    
-    /// 获取标签颜色
-    func getTagColor(for tagName: String) -> Color? {
-        if let tag = taskManager.getTagByName(tagName) {
-            return Color(hex: tag.color ?? "#007AFF") ?? Color.blue
-        }
-        return nil
-    }
-    
-    /// 获取任务背景颜色
-    func getTaskBackgroundColor() -> Color {
-        return Color(.systemBackground)
-    }
-    
-    /// 是否应该显示展开按钮
-    var shouldShowExpandButton: Bool {
-        // 聚焦模式下始终显示
-        if isFocusMode {
-            return true
-        }
-        
-        // 如果已经处于展开状态，应该显示收起按钮
-        if isExpanded {
-            return true
-        }
-        
-        // 非聚焦模式下且未展开时，只有有未完成小行动的任务才显示展开按钮
-        return task.incompleteChecklistCount > 0
-    }
-} 
\ No newline at end of file
diff --git a/1Step/1Step/Views/FocusView/TaskRowView.swift b/1Step/1Step/Views/FocusView/TaskRowView.swift
deleted file mode 100644
index 5f8e628..0000000
--- a/1Step/1Step/Views/FocusView/TaskRowView.swift
+++ /dev/null
@@ -1,481 +0,0 @@
-import SwiftUI
-import SwiftData
-import SwipeActions
-import AVFoundation // 导入音频播放所需框架
-
-// Helper extension for conditional modifiers
-extension View {
-    /// Applies the given transform if the condition evaluates to `true`.
-    /// - Parameters:
-    ///   - condition: The condition to evaluate.
-    ///   - transform: The transform to apply to the source `View`.
-    /// - Returns: Either the original `View` or the modified `View` if the condition is `true`.
-    @ViewBuilder func `if`<Content: View>(_ condition: Bool, transform: (Self) -> Content) -> some View {
-        if condition {
-            transform(self)
-        } else {
-            self
-        }
-    }
-}
-
-// 用于存储和传递位置信息的PreferenceKey，使用UUID作为键
-struct TaskRowPositionPreferenceKey: PreferenceKey {
-    static var defaultValue: [UUID: CGRect] = [:]
-    
-    static func reduce(value: inout [UUID: CGRect], nextValue: () -> [UUID: CGRect]) {
-        value.merge(nextValue()) { $1 }
-    }
-}
-
-struct TaskRowView: View {
-    // MARK: - 依赖
-    let taskManager: TaskManager
-    
-    // MARK: - 环境变量
-    @Environment(\.colorScheme) var colorScheme
-    @Environment(\.toastManager) var toastManager
-    @Environment(\.confettiManager) var confettiManager
-    @Environment(\.actionSheetManager) var actionSheetManager
-
-    // MARK: - 属性
-    var task: Task
-    var isPendingCompletion: Bool
-    var isDragging: Bool
-    var isFocusMode: Bool = false
-    var onTaskCompleted: ((Task) -> Void)? = nil
-    var onTaskStatusChanged: ((Task) -> Void)? = nil
-    var onTaskDeleted: ((Task) -> Void)? = nil
-    var onFocusRequest: ((Task) -> Void)? = nil
-    var onSubtaskFocused: ((Task, ChecklistItem) -> Void)? = nil
-    var onExitFocus: (() -> Void)? = nil
-    var onRestoreActionFocus: ((UUID, UUID, [UUID]) -> Void)? = nil
-    var initiallyToFocusSubtaskID: UUID?
-    
-    // MARK: - 状态管理
-    @State var showingTaskDetail = false
-    @State var cardScale: CGFloat = 1.0
-    @State var cardOpacity: Double = 1.0
-    @State var activeContext: SwipeContext? = nil // 用于保存滑动上下文
-    @Binding var isExpanded: Bool
-    @State var newChecklistItemTitle: String = ""
-    @FocusState var isInputFocused: Bool
-    
-    // 编辑状态管理
-    @State var editingChecklistItemId: UUID? = nil
-    @State var editingText: String = ""
-    @FocusState var isEditingFocused: Bool
-    
-    // 用于保存正在动画的小行动项目ID和完成时间，用于延迟排序
-    @State var animatingItemIds: [UUID: Date] = [:]
-    
-    // 小行动聚焦状态
-    @State var focusedChecklistItem: ChecklistItem? = nil
-    @State var pendingChecklistItemCompletionID: UUID? = nil
-    @State var audioPlayer: AVAudioPlayer?
-
-    // 用于检测任务状态变化
-    @State var previousStatus: String = ""
-    
-    // 添加用于撤销操作的状态变量
-    @State var oldStatus: String = ""
-    @State var oldSection: String? = nil
-    
-    // 添加位置跟踪状态
-    @State var cardFrame: CGRect = .zero
-    @State var isTracking: Bool = false
-    
-    // MARK: - 辅助视图
-    
-    /// 创建对齐的HStack
-    func aHStack<Content: View>(spacing: CGFloat = 0, @ViewBuilder content: () -> Content) -> some View {
-        HStack(alignment: .center, spacing: spacing) {
-            content()
-        }
-    }
-    
-    // MARK: - 初始化方法
-    init(
-        task: Task,
-        isPendingCompletion: Bool,
-        isDragging: Bool,
-        isExpanded: Binding<Bool> = .constant(false),
-        isFocusMode: Bool = false,
-        taskManager: TaskManager = DependencyContainer.taskManager(),
-        onTaskCompleted: ((Task) -> Void)? = nil,
-        onTaskStatusChanged: ((Task) -> Void)? = nil,
-        onTaskDeleted: ((Task) -> Void)? = nil,
-        onFocusRequest: ((Task) -> Void)? = nil,
-        onSubtaskFocused: ((Task, ChecklistItem) -> Void)? = nil,
-        onExitFocus: (() -> Void)? = nil,
-        onRestoreActionFocus: ((UUID, UUID, [UUID]) -> Void)? = nil,
-        initiallyToFocusSubtaskID: UUID? = nil
-    ) {
-        self.task = task
-        self.isPendingCompletion = isPendingCompletion
-        self.isDragging = isDragging
-        self.isFocusMode = isFocusMode
-        self._isExpanded = isExpanded
-        self.taskManager = taskManager
-        self.onTaskCompleted = onTaskCompleted
-        self.onTaskStatusChanged = onTaskStatusChanged
-        self.onTaskDeleted = onTaskDeleted
-        self.onFocusRequest = onFocusRequest
-        self.onSubtaskFocused = onSubtaskFocused
-        self.onExitFocus = onExitFocus
-        self.onRestoreActionFocus = onRestoreActionFocus
-        self.initiallyToFocusSubtaskID = initiallyToFocusSubtaskID
-    }
-    
-    var body: some View {
-        // 提前计算需要的值以简化视图构建
-        let visualIsDone = task.statusEnum == .done || isPendingCompletion
-        let iconName = visualIsDone ? "checkmark.circle.fill" : "circle"
-        let iconColor = visualIsDone ? Color.green : Color.gray
-        
-        SwipeView {
-            VStack(spacing: 0) {
-                // 行动卡片主体
-                VStack(alignment: .leading, spacing: 0) {
-                    HStack(alignment: .center, spacing: 10) {
-                        // 勾选框 - 点击完成行动
-                        Button(action: {
-                            handleCompleteAction()
-                        }) {
-                            Image(systemName: iconName)
-                                .font(.system(size: 22))
-                                .foregroundColor(iconColor)
-                                .frame(width: 44, height: 44)
-                                .contentShape(Rectangle())
-                        }
-                        .padding(.leading, 4)
-                        .buttonStyle(BorderlessButtonStyle()) // 添加BorderlessButtonStyle确保点击区域限制
-                        
-                        // 行动内容区域
-                        taskContentView
-                            .frame(maxWidth: .infinity)
-                    }
-                    .frame(maxWidth: .infinity)
-                    .scaleEffect(visualIsDone ? 0.95 : cardScale)
-                    .opacity(visualIsDone ? 0.2 : cardOpacity)
-                    
-                    // 显示当前聚焦的小行动
-                    if let focusedItem = focusedChecklistItem {
-                        focusedChecklistItemView(focusedItem)
-                            .padding(.top, 8)
-                            .padding(.horizontal, 16)
-                            .padding(.bottom, focusedChecklistItem != nil && !isExpanded ? 8 : 0)
-                            .contentShape(Rectangle()) // 确保整个区域接收点击事件
-                            .onTapGesture {} // 添加空手势阻止事件传递
-                    }
-                }
-                
-                // 展开区域
-                if isExpanded {
-                    expandedContent
-                    .padding(.top, focusedChecklistItem != nil ? 0 : 8)
-                    .padding(.horizontal, 16)
-                    .padding(.bottom, 12)
-                    .transition(.opacity)
-                    .contentShape(Rectangle()) // 确保整个区域接收点击事件
-                    .onTapGesture {} // 添加空手势阻止事件传递
-                }
-            }
-            // 添加位置跟踪
-            .background(GeometryReader { geometry in
-                Color.clear
-                    .preference(
-                        key: TaskRowPositionPreferenceKey.self,
-                        value: [task.id: geometry.frame(in: .global)]
-                    )
-                    .onAppear {
-                        // 开始跟踪
-                        self.isTracking = true
-                    }
-                    .onDisappear {
-                        // 停止跟踪
-                        self.isTracking = false
-                    }
-            })
-            .onPreferenceChange(TaskRowPositionPreferenceKey.self) { positions in
-                if let frame = positions[task.id], isTracking {
-                    self.cardFrame = frame
-                }
-            }
-        } leadingActions: { context in
-            SwipeAction(
-                action: {
-                    activeContext = context
-                    handleCompleteAction()
-                },
-                label: { highlighted in
-                    VStack(spacing: 4) {
-                        Image(systemName: "checkmark.circle.fill")
-                            .font(.system(size: 18, weight: .semibold))
-                            .foregroundColor(Color.green)
-                        Text("完成")
-                            .font(.caption)
-                            .fontWeight(.medium)
-                            .foregroundColor(Color.green)
-                    }
-                    .padding(.vertical, 4)
-                },
-                background: { _ in Color.clear }
-            )
-        } trailingActions: { context in
-            // 根据是否处于聚焦模式显示不同的操作
-            if isFocusMode {
-                // 退出聚焦操作
-                SwipeAction(
-                    action: {
-                        activeContext = context
-                        handleExitFocusAction()
-                    },
-                    label: { highlighted in
-                        VStack(spacing: 4) {
-                            Image(systemName: "arrow.left")
-                                .font(.system(size: 18, weight: .semibold))
-                                .foregroundColor(Color.gray)
-                            Text("退出一步模式")
-                                .font(.caption)
-                                .fontWeight(.medium)
-                                .foregroundColor(Color.gray)
-                        }
-                        .padding(.vertical, 4)
-                    },
-                    background: { _ in Color.clear }
-                )
-            } else {
-                // 安排操作
-                SwipeAction(
-                    action: {
-                        activeContext = context
-                        handleArrangeAction()
-                    },
-                    label: { highlighted in
-                        VStack(spacing: 4) {
-                            Image(systemName: "calendar.badge.clock")
-                                .font(.system(size: 18, weight: .semibold))
-                                .foregroundColor(Color.blue)
-                            Text("移动")
-                                .font(.caption)
-                                .fontWeight(.medium)
-                                .foregroundColor(Color.blue)
-                        }
-                        .padding(.vertical, 4)
-                    },
-                    background: { _ in Color.clear }
-                )
-                
-                // 聚焦操作
-                SwipeAction(
-                    action: {
-                        activeContext = context
-                        handleFocusAction()
-                    },
-                    label: { highlighted in
-                        VStack(spacing: 4) {
-                            Image(systemName: "target")
-                                .font(.system(size: 18, weight: .semibold))
-                                .foregroundColor(Color.gray)
-                            Text("一步模式")
-                                .font(.caption)
-                                .fontWeight(.medium)
-                                .foregroundColor(Color.gray)
-                        }
-                        .padding(.vertical, 4)
-                    },
-                    background: { _ in Color.clear }
-                )
-            }
-        }
-        .swipeActionsMaskCornerRadius(0)
-        .swipeActionCornerRadius(0)
-        .swipeActionsStyle(.equalWidths)
-        .swipeMinimumDistance(40)
-        .swipeActionsVisibleStartPoint(max(0, 40))
-        .swipeActionsVisibleEndPoint(max(0, 60))
-        .swipeSpacing(0)
-        .swipeOffsetCloseAnimation(stiffness: max(1, 90), damping: max(1, 35))
-        .swipeActionWidth(max(0, 90))
-        .sheet(isPresented: $showingTaskDetail) {
-            // 来自一步列表的行动详情，指定sourcePageType
-            NavigationStack {
-                TaskDetailView(
-                    task: task,
-                    sourcePageType: .doing,
-                    onDeleteIntent: { taskToDelete in
-                        // 直接调用从 FocusView 传递过来的 onTaskDeleted 回调
-                        // 这个回调内部会调用 viewModel.handleTaskDelete(taskToDelete)
-                        self.onTaskDeleted?(taskToDelete)
-                        
-                        // 关闭详情页 Sheet
-                        showingTaskDetail = false 
-                    },
-                    onRestoreActionFocus: { taskId, checklistItemId, subStepPath in
-                        // 调用从FocusView传递过来的恢复ActionFocus回调
-                        self.onRestoreActionFocus?(taskId, checklistItemId, subStepPath)
-                        
-                        // 关闭详情页 Sheet
-                        showingTaskDetail = false 
-                    }
-                )
-            }
-                .presentationDetents([.medium, .large])
-                .presentationDragIndicator(.visible)
-        }
-        .onChange(of: task.status) { oldValue, newValue in
-            // 当任务状态改变时，更新卡片样式
-            if oldValue != newValue {
-                withAnimation {
-                    if newValue != TaskStatus.done.rawValue {
-                        cardOpacity = 1.0
-                        cardScale = 1.0
-                    } else {
-                        // 仅在非等待确认时才应用完成样式，避免动画冲突
-                        if !isPendingCompletion {
-                             cardOpacity = 0.2
-                             cardScale = 0.95
-                        }
-                    }
-                }
-            }
-        }
-        .onAppear {
-            // 初始化卡片样式
-            if task.statusEnum == .done {
-                 cardOpacity = 0.2
-                 cardScale = 0.95
-            } else {
-                 cardOpacity = 1.0
-                 cardScale = 1.0
-            }
-            previousStatus = task.status
-            
-            // 设置音频播放器
-            setupAudioPlayer()
-
-            // 在 onAppear 中处理初始聚焦的小行动ID
-            if isFocusMode, // 确保 TaskRowView 当前是作为主焦点任务显示的
-               let subID = initiallyToFocusSubtaskID,
-               let subItem = task.checklist?.first(where: { $0.id == subID }) {
-                // 使用 DispatchQueue.main.async 确保状态更新在视图层级准备好之后发生
-                DispatchQueue.main.async {
-                    self.focusedChecklistItem = subItem
-                    self.isExpanded = false
-                }
-            }
-        }
-        .onChange(of: isInputFocused) { _, _ in
-            // 处理键盘显示状态
-        }
-    }
-    
-    // 提取行动内容视图为计算属性
-    var taskContentView: some View {
-        // 计算是否已完成
-        let visualIsDone = task.statusEnum == .done || isPendingCompletion
-        let isSubtaskFocused = focusedChecklistItem != nil
-        
-        return ZStack(alignment: .trailing) { // 使用ZStack将展开按钮覆盖在右侧
-            // 主要内容
-            VStack(alignment: .leading, spacing: 4) {
-                // 标题行 - 限制最多为2行
-                Text(task.title)
-                    .font(.system(size: 16))
-                    .foregroundColor(visualIsDone ? 
-                        Color.secondary : 
-                        Color.primary.opacity(0.8))
-                    .multilineTextAlignment(.leading)
-                    .lineLimit(2)
-                    .strikethrough(visualIsDone, color: Color.secondary)
-                    .opacity(isSubtaskFocused ? 0.6 : 1.0)
-                
-                // 小行动数量和项目信息整合到一行
-                HStack(spacing: 8) {
-                    // 项目信息 - 靠左显示
-                    if let projectId = task.project,
-                       let projectName = getProjectDisplayName(for: projectId),
-                       let projectColor = getProjectColor(for: projectId) {
-                        // 项目名称和彩色感叹号
-                        HStack(spacing: 0) {
-                            Text("!")
-                                .font(.caption)
-                                .foregroundColor(projectColor)
-                            Text(projectName)
-                                .font(.caption)
-                                .foregroundColor(Color.gray)
-                        }
-                    }
-                    
-                    Spacer()
-                    
-                    // 移除小行动数量显示
-                }
-                .padding(.trailing, shouldShowExpandButton ? 28 : 0) // 给展开按钮留出足够空间
-            }
-            .frame(maxWidth: .infinity, alignment: .leading)
-            .contentShape(Rectangle())
-            .onTapGesture {
-                showingTaskDetail = true
-            }
-            
-            // 展开图标放在右侧中间位置
-            if shouldShowExpandButton {
-                Button(action: {
-                    isExpanded.toggle()
-                }) {
-                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
-                        .font(.system(size: 13))
-                        .foregroundColor(Color.secondary.opacity(0.5))
-                        .frame(width: 24, height: 24)
-                        .background(Color.clear)
-                        .contentShape(Rectangle()) // 增大点击区域
-                        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isExpanded) // 添加动画效果
-                }
-                .buttonStyle(PlainButtonStyle())
-                .frame(width: 30, height: 30)
-                .contentShape(Rectangle())
-            }
-        }
-    }
-    
-    // MARK: - 音频相关方法
-    
-    /// 设置音频播放器
-    func setupAudioPlayer() {
-        guard let soundURL = Bundle.main.url(forResource: "check", withExtension: "WAV") else {
-            print("TaskRowView: 无法找到音频文件 check.WAV")
-            return
-        }
-        
-        do {
-            audioPlayer = try AVAudioPlayer(contentsOf: soundURL)
-            audioPlayer?.volume = 0.5
-            audioPlayer?.prepareToPlay()
-        } catch {
-            print("TaskRowView: 音频播放器初始化失败 - \(error.localizedDescription)")
-        }
-    }
-}
-
-// MARK: - 预览
-struct TaskRowView_Previews: PreviewProvider {
-    static var previews: some View {
-        let exampleTask = Task(title: "示例行动", status: "Doing")
-        
-        // 创建预览用的ModelContainer和Context
-        let previewContainer = try! ModelContainer(for: Task.self, Project.self, Tag.self)
-        
-        // 使用DependencyContainer中的仓库实例
-        return TaskRowView(
-            task: exampleTask,
-            isPendingCompletion: false,
-            isDragging: false,
-            isExpanded: .constant(true)
-        )
-        .modelContainer(previewContainer)
-        .padding()
-    }
-}
-
