---
description: 
globs: 
alwaysApply: false
---
# ViewModel 到 Service 层重构指南

## 🎯 重构目标

当前正在进行 1Step 应用的架构重构，将 ViewModel 层从直接调用 Repository 迁移到通过 Service 层进行数据访问。

### 核心原则
- **分层架构**：View → ViewModel → Service → Repository → Data
- **最小化改动**：每次只聚焦一个小目标，保持代码可编译
- **逐步迭代**：优先保证核心功能，后续完善细节

## 📁 关键文件

### Service 层
- [TaskManager.swift](mdc:1Step/1Step/Services/TaskManager.swift) - 主要的 Service 类，需要扩展项目和标签相关方法

### ViewModel 基类
- [BaseTaskViewModel.swift](mdc:1Step/1Step/ViewModels/BaseTaskViewModel.swift) - 基础 ViewModel，大部分其他 ViewModel 继承自此类

### 需要重构的 ViewModel
- [ProjectDetailViewModel.swift](mdc:1Step/1Step/ViewModels/ProjectDetailViewModel.swift)
- [TagDetailViewModel.swift](mdc:1Step/1Step/ViewModels/TagDetailViewModel.swift)
- [InboxViewModel.swift](mdc:1Step/1Step/ViewModels/InboxViewModel.swift)
- [NextActionsViewModel.swift](mdc:1Step/1Step/ViewModels/NextActionsViewModel.swift)
- [WaitingViewModel.swift](mdc:1Step/1Step/ViewModels/WaitingViewModel.swift)
- [SMBViewModel.swift](mdc:1Step/1Step/ViewModels/SMBViewModel.swift)
- [SearchViewModel.swift](mdc:1Step/1Step/ViewModels/SearchViewModel.swift)
- [FocusViewModel.swift](mdc:1Step/1Step/ViewModels/FocusViewModel.swift)
- [CompletedTasksViewModel.swift](mdc:1Step/1Step/ViewModels/CompletedTasksViewModel.swift)
- [FilteredTaskListViewModel.swift](mdc:1Step/1Step/ViewModels/FilteredTaskListViewModel.swift)
- [BrowseViewModel.swift](mdc:1Step/1Step/ViewModels/BrowseViewModel.swift)
- [PopupSelectorViewModel.swift](mdc:1Step/1Step/ViewModels/PopupSelectorViewModel.swift)

### 依赖管理
- [DependencyContainer.swift](mdc:1Step/1Step/Core/DependencyContainer.swift) - 依赖注入容器

## 🔧 重构步骤

### 阶段一：扩展 TaskManager

在 [TaskManager.swift](mdc:1Step/1Step/Services/TaskManager.swift) 中添加以下方法：

#### 项目相关方法
```swift
// MARK: - 项目管理
func getAllProjects() -> [Project] {
    return projectRepository.getAllProjects()
}

func getProjectById(_ id: UUID) -> Project? {
    return projectRepository.getProjectById(id)
}

func getProjectByName(_ name: String) -> Project? {
    return projectRepository.getProjectByName(name)
}

func createProject(name: String, color: String) -> Project? {
    return projectRepository.createProject(name: name, color: color)
}

func updateProject(_ project: Project) {
    projectRepository.saveProject(project)
}

func deleteProject(_ project: Project) {
    projectRepository.deleteProject(project)
}
```

#### 标签相关方法
```swift
// MARK: - 标签管理
func getAllTags() -> [Tag] {
    return tagRepository.getAllTags()
}

func getTagByName(_ name: String) -> Tag? {
    return tagRepository.getTagByName(name)
}

func createTag(name: String, color: String) -> Tag? {
    return tagRepository.createTag(name: name, color: color)
}

func updateTag(_ tag: Tag) {
    tagRepository.saveTag(tag)
}

func deleteTag(_ tag: Tag) {
    tagRepository.deleteTag(tag)
}
```

#### 任务相关方法（补充）
```swift
// MARK: - 任务管理（补充）
func getAllTasks() -> [Task] {
    return taskRepository.getAllTasks()
}

func getTaskById(_ id: UUID) -> Task? {
    return taskRepository.getTaskById(id)
}

func getTasksByStatus(_ status: String) -> [Task] {
    return taskRepository.getTasksByStatus(status)
}

func getTasksForProject(_ projectId: UUID, status: String? = nil) -> [Task] {
    return taskRepository.getTasksForProject(projectId, status: status)
}

func updateTask(_ task: Task) {
    taskRepository.updateTask(task)
}
```

### 阶段二：重构 BaseTaskViewModel

在 [BaseTaskViewModel.swift](mdc:1Step/1Step/ViewModels/BaseTaskViewModel.swift) 中：

1. **移除 Repository 依赖**：
```swift
// 删除这些属性
// let taskRepository: TaskRepository
// let projectRepository: ProjectRepository
// let tagRepository: TagRepository

// 只保留 TaskManager
let taskManager: TaskManager
```

2. **更新初始化方法**：
```swift
init(taskManager: TaskManager = DependencyContainer.taskManager()) {
    self.taskManager = taskManager
    setupEventSubscriptions()
}
```

3. **更新方法实现**：
```swift
func getProjectName(for projectId: UUID?) -> String {
    guard let projectId = projectId else { return "无项目" }
    
    if let project = taskManager.getProjectById(projectId) {
        return project.name
    } else {
        return "无项目"
    }
}
```

### 阶段三：重构继承的 ViewModel

对于每个继承自 BaseTaskViewModel 的 ViewModel：

1. **更新初始化方法**，移除 Repository 参数
2. **替换所有 Repository 调用**为 TaskManager 调用
3. **确保功能完整性**

### 阶段四：重构独立的 ViewModel

对于不继承 BaseTaskViewModel 的 ViewModel：

1. **修改构造函数**，注入 TaskManager 而非 Repository
2. **更新所有数据访问调用**
3. **测试功能正常**

## ⚠️ 重构注意事项

### 编译错误处理
- 重构过程中会产生编译错误，这是正常的
- 优先解决直接由改动引发的编译错误
- 确保每个阶段完成后代码可以编译

### Repository 调用替换模式

| Repository 调用 | TaskManager 调用 |
|----------------|------------------|
| `taskRepository.getAllTasks()` | `taskManager.getAllTasks()` |
| `taskRepository.getTaskById(id)` | `taskManager.getTaskById(id)` |
| `taskRepository.updateTask(task)` | `taskManager.updateTask(task)` |
| `projectRepository.getAllProjects()` | `taskManager.getAllProjects()` |
| `projectRepository.getProjectById(id)` | `taskManager.getProjectById(id)` |
| `tagRepository.getAllTags()` | `taskManager.getAllTags()` |

### 依赖注入更新
- 移除 ViewModel 初始化方法中的 Repository 参数
- 只保留 TaskManager 参数
- 更新 DependencyContainer 中的相关方法（如果需要）

### 测试验证
- 每个阶段完成后进行编译测试
- 验证核心功能正常工作
- 确保 UI 交互没有问题

## 📋 重构检查清单

### TaskManager 扩展
- [ ] 添加所有项目相关方法
- [ ] 添加所有标签相关方法  
- [ ] 补充缺失的任务相关方法
- [ ] 确保方法覆盖所有 Repository 使用场景

### BaseTaskViewModel 重构
- [ ] 移除 Repository 属性
- [ ] 更新初始化方法
- [ ] 替换 `getProjectName` 方法实现
- [ ] 更新 `handleTaskComplete` 中的 Repository 调用

### 继承 ViewModel 重构
- [ ] ProjectDetailViewModel
- [ ] TagDetailViewModel  
- [ ] InboxViewModel
- [ ] NextActionsViewModel
- [ ] WaitingViewModel
- [ ] SMBViewModel
- [ ] SearchViewModel
- [ ] FocusViewModel
- [ ] CompletedTasksViewModel
- [ ] FilteredTaskListViewModel

### 独立 ViewModel 重构
- [ ] BrowseViewModel
- [ ] PopupSelectorViewModel

### 清理工作
- [ ] 移除不必要的 Repository 导入
- [ ] 代码格式化
- [ ] 注释更新
- [ ] 编译检查
- [ ] 功能测试

## 🚀 开始重构

重构应该按照以下顺序进行：
1. 首先扩展 TaskManager
2. 然后重构 BaseTaskViewModel
3. 逐个重构继承的 ViewModel
4. 最后重构独立的 ViewModel
5. 进行清理和验证

记住：**最小化改动，保持可编译性，逐步迭代**
