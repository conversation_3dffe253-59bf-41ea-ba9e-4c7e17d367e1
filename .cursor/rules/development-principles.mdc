---
description: 
globs: 
alwaysApply: true
---
# 开发工作原则

在进行代码修改和项目重构时，请严格遵守以下工作原则：

1.  **绝对最小化改动 (Minimize Changes)**：
    *   每一步操作都应聚焦于当前的小目标（例如，修复一个特定的编译错误，或实现一个定义清晰的小功能点）。
    *   只进行必要的最小修改以达成该目标。
    *   避免引入与当前小目标不直接相关的附带修改或大规模重构。

2.  **保持可编译性优先 (Prioritize Compilability)**：
    *   在进行一系列修改时，优先确保每一步或每一个小阶段完成后，代码处于可编译状态。
    *   如果一个改动预期或实际导致了大量的编译错误，应首先集中处理这些直接由改动引发的编译错误，使其恢复可编译状态，然后再进行下一步。
    *   避免在代码不可编译的状态下进行更大范围的逻辑重构或功能添加。

3.  **清晰的步骤和目标 (Clear Steps & Goals)**：
    *   在开始任何一组操作前，应确保对当前步骤的目标有清晰的理解。
    *   复杂任务应分解为一系列具有明确小目标的简单步骤。

4.  **逐步迭代 (Iterative Progress)**：
    *   优先保证核心路径的通畅和基本功能的可用性。
    *   功能的完善、代码的优化和更深层次的重构可以放在核心功能稳定后的后续迭代中进行。
    *   遵循小步快跑、循序渐进的迭代方式。

5.  **遵循用户指令 (Follow User Instructions)**：
    *   仔细理解用户的每条指令和反馈。
    *   如果对指令的理解有任何不确定性，应首先提问澄清。
    *   当用户明确指出之前的方向或方法不正确时，应立即调整策略，严格遵循用户最新的指示。
