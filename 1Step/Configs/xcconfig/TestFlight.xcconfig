//
//  TestFlight.xcconfig
//  1Step
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/4/7.
//
#include? "../../Pods/Target Support Files/Pods-1Step/Pods-1Step.testflight.xcconfig"
#include "Base.xcconfig"

CODE_SIGN_ENTITLEMENTS = $(SRCROOT)/Configs/entitlements/TestFlight.entitlements
INFOPLIST_FILE = $(SRCROOT)/Configs/plist/Info-TestFlight.plist

// Configuration settings file format documentation can be found at:
// https://help.apple.com/xcode/#/dev745c5c974
// 1Step TestFlight配置
PRODUCT_NAME = 1StepBeta
PRODUCT_BUNDLE_IDENTIFIER = com.absir.1Step-Beta
INFOPLIST_KEY_CFBundleDisplayName = "1Step Beta"
INFOPLIST_KEY_CFBundleIconName = AppIcon.beta
INFOPLIST_KEY_UILaunchScreen_Generation = YES
INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait
INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight
INFOPLIST_KEY_UIBackgroundModes = remote-notification
INFOPLIST_KEY_NSCloudKitUsageDescription = 1Step使用iCloud同步您的任务和项目，以便在您的所有设备间保持数据一致。
INFOPLIST_KEY_LSRequiresIPhoneOS = YES
INFOPLIST_KEY_UIRequiredDeviceCapabilities = armv7

ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon.beta
CODE_SIGN_STYLE = Automatic
DEBUG_INFORMATION_FORMAT = dwarf-with-dsym
ENABLE_NS_ASSERTIONS = NO
GCC_OPTIMIZATION_LEVEL = s
SWIFT_OPTIMIZATION_LEVEL = -O
SWIFT_COMPILATION_MODE = wholemodule
VALIDATE_PRODUCT = YES
SWIFT_ACTIVE_COMPILATION_CONDITIONS = TESTFLIGHT
