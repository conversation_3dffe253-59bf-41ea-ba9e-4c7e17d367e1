import Foundation
import Combine
import SwiftUI

// MARK: - 轻量级事件总线系统

/// 应用事件类型
enum AppEvent {
    // 任务操作事件
    case taskDeleted(Task, allowUndo: Bool)
    case taskCompleted(Task, allowUndo: Bool) 
    case taskStatusChanged(Task, oldStatus: String, newStatus: String)
    
    // Toast通知事件 - 添加额外标志
    case showToast(message: String, type: Toast.MessageType, position: Toast.Position, action: Toast.Action?, onDismiss: (() -> Void)?, isDeleteToast: Bool = false, isCompleteToast: Bool = false, isUndo: Bool = false)
    case hideToast
}

/// 轻量级事件总线
class EventBus {
    static let shared = EventBus()
    
    // 使用Combine发布者
    private let eventSubject = PassthroughSubject<AppEvent, Never>()
    
    // 存储任务撤销回调
    private var undoCallbacks: [UUID: (Bool) -> Void] = [:]
    // 存储撤销定时器
    private var undoTimers: [UUID: DispatchWorkItem] = [:]
    
    // 获取事件流
    var events: AnyPublisher<AppEvent, Never> {
        eventSubject.eraseToAnyPublisher()
    }
    
    // 发布事件
    func publish(_ event: AppEvent, undoCallback: ((Bool) -> Void)? = nil) {
        DispatchQueue.main.async {
            switch event {
            case .taskDeleted(let task, let allowUndo) where allowUndo:
                // 对于可撤销的删除事件:
                // 1. 存储撤销回调
                if let callback = undoCallback {
                    self.undoCallbacks[task.id] = callback
                }
                
                // 2. 取消并移除可能存在的旧定时器 (防止快速重复删除导致问题)
                self.undoTimers[task.id]?.cancel()
                self.undoTimers.removeValue(forKey: task.id)
                
                // 3. 创建新的撤销定时器
                let workItem = DispatchWorkItem { [weak self] in
                    guard let self = self else { return }
                    // 检查回调是否存在 (如果不存在，说明已被撤销)
                    if let storedCallback = self.undoCallbacks.removeValue(forKey: task.id) {
                        // 超时未撤销，执行回调通知发起者
                        storedCallback(false)
                        
                        // 在执行最终删除前再次确认任务ID不在待撤销列表中
                        // 这是额外的安全检查
                        if !self.undoCallbacks.keys.contains(task.id) {
                            // 广播最终的删除事件 (allowUndo: false)
                            self.eventSubject.send(.taskDeleted(task, allowUndo: false))
                        }
                    }
                    // 从定时器字典中移除自身
                    self.undoTimers.removeValue(forKey: task.id)
                }
                
                // 4. 存储并安排定时器 (3秒后执行)
                self.undoTimers[task.id] = workItem
                DispatchQueue.main.asyncAfter(deadline: .now() + 3, execute: workItem)

            case .taskCompleted(let task, let allowUndo) where allowUndo:
                 // 对于可撤销的完成事件 (保持原有逻辑或按需修改，当前重点是修复删除流程)
                self.eventSubject.send(event) // 暂时保持原有立即发送逻辑，如果完成也需要撤销超时，则需类似修改
                if let callback = undoCallback {
                    self.undoCallbacks[task.id] = callback
                    // ... 原有的完成超时逻辑 ...
                    DispatchQueue.main.asyncAfter(deadline: .now() + 5) { [weak self] in
                         guard let self = self else { return }
                         if let storedCallback = self.undoCallbacks.removeValue(forKey: task.id) {
                             storedCallback(false)
                         }
                     }
                }

            default:
                // 对于其他事件或非可撤销事件，立即广播
                self.eventSubject.send(event)
            }
        }
    }
    
    // 撤销任务删除
    func undoTaskDeletion(_ taskID: UUID) {
        DispatchQueue.main.async { // 确保在主线程操作字典和定时器
            // 1. 取消并移除定时器
            if let timer = self.undoTimers.removeValue(forKey: taskID) {
                timer.cancel()
            }
            
            // 2. 移除并执行回调 (通知发起者已撤销)
            if let callback = self.undoCallbacks.removeValue(forKey: taskID) {
                callback(true) // 通知已撤销
            }
        }
    }
    
    // 撤销任务完成
    func undoTaskCompletion(_ taskID: UUID) {
         DispatchQueue.main.async { // 确保在主线程操作字典
             // (如果完成逻辑也修改为定时器，这里也需要取消定时器)
             if let callback = self.undoCallbacks.removeValue(forKey: taskID) {
                 callback(true) // 通知已撤销
             }
         }
    }
}

// MARK: - 环境值扩展
private struct EventBusKey: EnvironmentKey {
    static let defaultValue: EventBus = EventBus.shared
}

extension EnvironmentValues {
    var eventBus: EventBus {
        get { self[EventBusKey.self] }
        set { self[EventBusKey.self] = newValue }
    }
} 
