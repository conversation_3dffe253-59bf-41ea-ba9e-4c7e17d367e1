import SwiftUI
import Combine

// MARK: - 主文件索引
/* 
 * 该文件仅作为向后兼容的门面(Facade)，实际实现已迁移到以下文件：
 * 1. Toast/ToastTypes.swift - 枚举和基础结构
 * 2. Toast/ToastData.swift - 数据模型
 * 3. Toast/ToastOperation.swift - 操作模型
 * 4. Toast/ToastManagerProtocol.swift - 接口定义
 * 5. Toast/ToastManagerImplementation.swift - 管理器的具体实现
 * 6. Toast/ToastView.swift - 视图组件
 * 7. Toast/ToastModifiers.swift - 修饰器和扩展
 * 
 * 注意：新代码建议直接使用Toast命名空间下的组件
 */

// MARK: - 类型别名(向后兼容)
typealias ToastPosition = Toast.Position
typealias ToastType = Toast.MessageType
typealias ToastAction = Toast.Action
typealias ToastData = Toast.Data
typealias ToastOperationType = Toast.OperationType
typealias ToastOperation = Toast.Operation
typealias ToastManaging = Toast.Managing

// MARK: - Toast 管理器(向后兼容)
class ToastManager: ObservableObject, ToastManaging {
    static let shared = ToastManager()
    
    // 实际实现对象
    private let implementation = Toast.Manager.shared
    
    // 订阅
    private var cancellables = Set<AnyCancellable>()
    
    // 转发状态
    @Published var currentToast: ToastData? = nil
    @Published var isShowing: Bool = false
    
    // 转发发布者
    var toastPublisher: PassthroughSubject<(message: String, type: ToastType), Never> {
        implementation.toastPublisher
    }
    
    // 转发方法
    func show(_ message: String, type: ToastType = .normal, position: ToastPosition = .top, action: ToastAction? = nil, onDismiss: (() -> Void)? = nil) {
        implementation.show(message, type: type, position: position, action: action, onDismiss: onDismiss)
    }
    
    func hide() {
        implementation.hide()
    }
    
    func hideWithoutDismissCallback() {
        implementation.hideWithoutDismissCallback()
    }
    
    func showSuccess(_ message: String = "已添加行动") {
        implementation.showSuccess(message)
    }
    func showWarning(_ message: String = "已添加行动") {
        implementation.showWarning(message)
    }
    func showError(_ message: String = "已添加行动") {
        implementation.showError(message)
    }
    
    func showInfo(_ message: String = "信息") {
        implementation.showInfo(message)
    }
    
    func showTaskCompleted(_ message: String = "已完成行动", action: ToastAction? = nil, onDismiss: (() -> Void)? = nil) {
        implementation.showTaskCompleted(message, action: action, onDismiss: onDismiss)
    }
    
    func showTaskDeleted(_ message: String = "已删除行动", action: ToastAction? = nil, onDismiss: (() -> Void)? = nil) {
        implementation.showTaskDeleted(message, action: action, onDismiss: onDismiss)
    }
    
    func showUndoDeletionSuccess(_ message: String = "已撤销删除") {
        implementation.showUndoDeletionSuccess(message)
    }
    
    // 用于轻量级通知，没有图标，更简洁，但比超轻量级稍明显
    func showInfoLight(_ message: String = "已更新") {
        implementation.showInfoLight(message)
    }
    
    // 用于超轻量级通知，高度更小，颜色更淡，几乎不引人注意
    func showSuperLightInfo(_ message: String = "已更新") {
        implementation.showSuperLightInfo(message)
    }
    
    
    func hideCurrentAndThenShow(_ newToastOperation: @escaping () -> Void) {
        implementation.hideCurrentAndThenShow(newToastOperation)
    }
    
    private init() {
        // 设置初始值
        self.currentToast = implementation.currentToast
        self.isShowing = implementation.isShowing
        
        // 订阅实现对象的状态变化
        implementation.$currentToast
            .sink { [weak self] toast in
                self?.currentToast = toast
            }
            .store(in: &cancellables)
        
        implementation.$isShowing
            .sink { [weak self] isShowing in
                self?.isShowing = isShowing
            }
            .store(in: &cancellables)
    }
}

// MARK: - 视图扩展(向后兼容)
extension View {
    func withToast() -> some View {
        self.modifier(ToastModifier())
    }
}

