import Foundation
import UIKit

extension String {
    /// 使用系统API将Markdown文本转换为AttributedString (iOS 15+)
    func attributedStringFromMarkdown() -> AttributedString {
        do {
            // 使用系统API解析Markdown
            return try AttributedString(markdown: self)
        } catch {
            print("Markdown解析错误: \(error)")
            return AttributedString(self)
        }
    }
    
    /// 获取NSAttributedString版本的Markdown渲染结果
    func nsAttributedStringFromMarkdown() -> NSAttributedString {
        do {
            let attributedString = try AttributedString(markdown: self)
            return NSAttributedString(attributedString)
        } catch {
            print("Markdown解析错误: \(error)")
            return NSAttributedString(string: self)
        }
    }
}

/// 提供AttributedString -> NSAttributedString的便捷转换
extension AttributedString {
    func toNSAttributedString() -> NSAttributedString {
        return NSAttributedString(self)
    }
}

/// 增强UITextView对Markdown的支持
extension UITextView {
    /// 设置Markdown文本并渲染为富文本
    func applyMarkdownText(_ markdown: String) {
        let attributedString = markdown.nsAttributedStringFromMarkdown()
        self.attributedText = attributedString
    }
    
    /// 获取文本内容的原始文本
    var rawTextContent: String {
        return self.text
    }
} 