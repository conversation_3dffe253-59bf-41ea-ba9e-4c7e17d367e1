import SwiftUI

extension String {
    /// 将Markdown文本转换为AttributedString
    /// 支持以下格式:
    /// - 加粗: **文本** 或 __文本__
    /// - 斜体: *文本* 或 _文本_
    /// - 标题: # 标题 (支持1-3级标题)
    /// - 列表: - 列表项
    /// - 链接: [链接文本](URL)
    /// - 引用: > 引用文本
    func markdownToAttributedString(
        baseFontSize: CGFloat = 15.0,
        textColor: Color = .primary,
        lineSpacing: CGFloat = 8,     
        paragraphSpacing: CGFloat = 14 // 增加段落间距
    ) -> AttributedString {
        var attributedString = AttributedString(self)
        
        // 设置基本样式 - 使用 SF Pro 字体
        attributedString.font = .system(size: baseFontSize, weight: .light)
        attributedString.foregroundColor = textColor
        
        // 创建段落样式
        var paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = lineSpacing
        paragraphStyle.paragraphSpacing = paragraphSpacing
        paragraphStyle.lineHeightMultiple = 1.6 // 增加行高与 CSS 一致
        
        // 应用段落样式到整个文本
        attributedString.paragraphStyle = paragraphStyle
        
        // 处理标题 (# 标题)
        processHeadings(in: &attributedString, baseFontSize: baseFontSize)
        
        // 处理加粗 (**文本** 或 __文本__)
        processBoldText(in: &attributedString, baseFontSize: baseFontSize)
        
        // 处理斜体 (*文本* 或 _文本_)
        processItalicText(in: &attributedString, baseFontSize: baseFontSize)
        
        // 处理列表项 (- 列表项)
        processListItems(in: &attributedString, baseFontSize: baseFontSize)
        
        // 处理引用块 (> 引用文本)
        processQuotes(in: &attributedString, baseFontSize: baseFontSize, textColor: textColor)
        
        // 处理链接 ([链接文本](URL))
        processLinks(in: &attributedString)
        
        return attributedString
    }
    
    // MARK: - 私有辅助方法
    
    /// 处理标题格式
    private func processHeadings(in attributedString: inout AttributedString, baseFontSize: CGFloat) {
        guard let regex = try? NSRegularExpression(pattern: "^(#{1,3})\\s+(.+)$", options: .anchorsMatchLines) else {
            return
        }
        
        let nsString = self as NSString
        let matches = regex.matches(in: self, options: [], range: NSRange(location: 0, length: nsString.length))
        
        for match in matches.reversed() {
            guard match.numberOfRanges >= 3,
                  let headerMarkRange = Range(match.range(at: 1), in: self),
                  let headerTextRange = Range(match.range(at: 2), in: self),
                  let fullRange = Range(match.range, in: self) else {
                continue
            }
            
            let headerLevel = self[headerMarkRange].count
            let headerText = String(self[headerTextRange])
            let fullText = String(self[fullRange])
            
            // 根据标题级别设置字体大小
            let fontSize: CGFloat
            let fontWeight: Font.Weight
            
            switch headerLevel {
            case 1: 
                fontSize = baseFontSize * 1.47  // 22px/15px = 1.47
                fontWeight = .regular
            case 2: 
                fontSize = baseFontSize * 1.2   // 18px/15px = 1.2
                fontWeight = .regular
            case 3: 
                fontSize = baseFontSize * 1.1
                fontWeight = .regular
            default: 
                fontSize = baseFontSize
                fontWeight = .regular
            }
            
            // 创建标题属性字符串
            var headerAttrString = AttributedString(headerText)
            headerAttrString.font = .system(size: fontSize, weight: fontWeight)
            
            // 设置段落样式，增加上下间距
            var headingStyle = NSMutableParagraphStyle()
            headingStyle.paragraphSpacing = 18            // 增加段落后间距
            headingStyle.paragraphSpacingBefore = 24      // 增加段落前间距
            headingStyle.lineHeightMultiple = 1.4         // 标题行高
            headerAttrString.paragraphStyle = headingStyle
            
            // 查找原文本在属性字符串中的范围
            if let range = attributedString.range(of: fullText) {
                attributedString.replaceSubrange(range, with: headerAttrString)
            }
        }
    }
    
    /// 处理加粗文本
    private func processBoldText(in attributedString: inout AttributedString, baseFontSize: CGFloat) {
        // 匹配 **文本** 或 __文本__
        for pattern in ["\\*\\*(.+?)\\*\\*", "__(.+?)__"] {
            guard let regex = try? NSRegularExpression(pattern: pattern, options: []) else {
                continue
            }
            
            let nsString = self as NSString
            let matches = regex.matches(in: self, options: [], range: NSRange(location: 0, length: nsString.length))
            
            for match in matches.reversed() {
                guard match.numberOfRanges >= 2,
                      let contentRange = Range(match.range(at: 1), in: self),
                      let fullRange = Range(match.range, in: self) else {
                    continue
                }
                
                let content = String(self[contentRange])
                let fullText = String(self[fullRange])
                
                // 创建加粗属性字符串
                var boldString = AttributedString(content)
                boldString.font = .system(size: baseFontSize, weight: .medium) // 使用 medium 而非 bold
                
                // 查找原文本在属性字符串中的范围
                if let range = attributedString.range(of: fullText) {
                    attributedString.replaceSubrange(range, with: boldString)
                }
            }
        }
    }
    
    /// 处理斜体文本
    private func processItalicText(in attributedString: inout AttributedString, baseFontSize: CGFloat) {
        // 匹配 *文本* 或 _文本_，但排除已处理的**文本**
        for pattern in ["(?<![*])\\*(?!\\*)(.+?)(?<!\\*)\\*(?![*])", "(?<!_)_(?!_)(.+?)(?<!_)_(?!_)"] {
            guard let regex = try? NSRegularExpression(pattern: pattern, options: []) else {
                continue
            }
            
            let nsString = self as NSString
            let matches = regex.matches(in: self, options: [], range: NSRange(location: 0, length: nsString.length))
            
            for match in matches.reversed() {
                guard match.numberOfRanges >= 2,
                      let contentRange = Range(match.range(at: 1), in: self),
                      let fullRange = Range(match.range, in: self) else {
                    continue
                }
                
                let content = String(self[contentRange])
                let fullText = String(self[fullRange])
                
                // 创建斜体属性字符串
                var italicString = AttributedString(content)
                italicString.font = .system(size: baseFontSize, weight: .light).italic() // 使用轻量斜体
                
                // 查找原文本在属性字符串中的范围
                if let range = attributedString.range(of: fullText) {
                    attributedString.replaceSubrange(range, with: italicString)
                }
            }
        }
    }
    
    /// 处理列表项
    private func processListItems(in attributedString: inout AttributedString, baseFontSize: CGFloat) {
        guard let regex = try? NSRegularExpression(pattern: "^\\s*-\\s+(.+)$", options: .anchorsMatchLines) else {
            return
        }
        
        let nsString = self as NSString
        let matches = regex.matches(in: self, options: [], range: NSRange(location: 0, length: nsString.length))
        
        for match in matches.reversed() {
            guard match.numberOfRanges >= 2,
                  let contentRange = Range(match.range(at: 1), in: self),
                  let fullRange = Range(match.range, in: self) else {
                continue
            }
            
            let content = String(self[contentRange])
            let fullText = String(self[fullRange])
            
            // 创建包含项目符号的文本 - 使用更优雅的圆点
            let bulletedText = "• " + content
            
            // 创建列表项属性字符串
            var listItemString = AttributedString(bulletedText)
            
            // 设置段落样式，增加左缩进
            var listStyle = NSMutableParagraphStyle()
            listStyle.headIndent = 24
            listStyle.firstLineHeadIndent = 8
            listStyle.paragraphSpacing = 8          // 列表项间距
            listStyle.lineHeightMultiple = 1.6      // 匹配主文本的行高
            listItemString.paragraphStyle = listStyle
            listItemString.font = .system(size: baseFontSize, weight: .light) // 使用轻量字体
            
            // 查找原文本在属性字符串中的范围
            if let range = attributedString.range(of: fullText) {
                attributedString.replaceSubrange(range, with: listItemString)
            }
        }
    }
    
    /// 处理链接
    private func processLinks(in attributedString: inout AttributedString) {
        guard let regex = try? NSRegularExpression(pattern: "\\[(.+?)\\]\\((.+?)\\)", options: []) else {
            return
        }
        
        let nsString = self as NSString
        let matches = regex.matches(in: self, options: [], range: NSRange(location: 0, length: nsString.length))
        
        for match in matches.reversed() {
            guard match.numberOfRanges >= 3,
                  let textRange = Range(match.range(at: 1), in: self),
                  let urlRange = Range(match.range(at: 2), in: self),
                  let fullRange = Range(match.range, in: self) else {
                continue
            }
            
            let linkText = String(self[textRange])
            let urlString = String(self[urlRange])
            let fullText = String(self[fullRange])
            
            if let url = URL(string: urlString) {
                // 创建链接属性字符串
                var linkString = AttributedString(linkText)
                linkString.link = url
                linkString.foregroundColor = Color(red: 0.2, green: 0.4, blue: 0.8) // 更柔和的蓝色
                linkString.underlineStyle = .single
                
                // 查找原文本在属性字符串中的范围
                if let range = attributedString.range(of: fullText) {
                    attributedString.replaceSubrange(range, with: linkString)
                }
            }
        }
    }
    
    /// 处理引用块
    private func processQuotes(in attributedString: inout AttributedString, baseFontSize: CGFloat, textColor: Color) {
        guard let regex = try? NSRegularExpression(pattern: "^\\s*>\\s*(.+)$", options: .anchorsMatchLines) else {
            return
        }
        
        let nsString = self as NSString
        let matches = regex.matches(in: self, options: [], range: NSRange(location: 0, length: nsString.length))
        
        for match in matches.reversed() {
            guard match.numberOfRanges >= 2,
                  let contentRange = Range(match.range(at: 1), in: self),
                  let fullRange = Range(match.range, in: self) else {
                continue
            }
            
            let content = String(self[contentRange])
            let fullText = String(self[fullRange])
            
            // 创建引用块属性字符串
            var quoteString = AttributedString(content)
            
            // 设置字体样式 - 轻量斜体
            quoteString.font = .system(size: baseFontSize, weight: .light).italic()
            
            // 设置颜色 - 稍微减淡的文本颜色
            let uiColor = UIColor(textColor).withAlphaComponent(0.8)
            quoteString.foregroundColor = Color(uiColor)
            
            // 设置段落样式
            var quoteStyle = NSMutableParagraphStyle()
            quoteStyle.headIndent = 20               // 左侧缩进
            quoteStyle.firstLineHeadIndent = 20      // 首行缩进与正文一致
            quoteStyle.paragraphSpacing = 12         // 段落间距
            quoteStyle.lineHeightMultiple = 1.5      // 行高略小于正文
            
            // 添加左侧边框效果（通过设置背景色实现视觉效果）
            let attachment = NSTextAttachment()
            let attachmentString = NSAttributedString(attachment: attachment)
            
            quoteString.paragraphStyle = quoteStyle
            
            // 查找原文本在属性字符串中的范围
            if let range = attributedString.range(of: fullText) {
                attributedString.replaceSubrange(range, with: quoteString)
                
                // 为引用块添加背景效果
                if let quoteRange = attributedString.range(of: content) {
                    attributedString[quoteRange].backgroundColor = Color(.systemGray6)
                }
            }
        }
    }
} 