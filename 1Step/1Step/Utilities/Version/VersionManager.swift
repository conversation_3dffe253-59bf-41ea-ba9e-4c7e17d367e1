import SwiftUI
import Foundation

/// 版本管理协议
protocol VersionManaging {
    /// 检查版本更新
    func checkForUpdates()
    
    /// 静默检查版本（不显示UI）
    func checkVersionSilently()
    
    /// 显示强制更新界面
    func showForceUpdateAlert(message: String?)
    
    /// 验证应用版本
    func validateAppVersion(completion: @escaping (Bool) -> Void)
}

/// 版本管理器 - 管理应用版本检查和更新提示
class VersionManager: ObservableObject, VersionManaging {
    /// 单例实例
    static let shared = VersionManager()
    
    /// App ID
    private let appId = "6744005150"  // 替换为实际App ID
    
    /// 最低支持版本 - 当需要强制升级时修改此值
    private var minimumSupportedVersion: String = "1.0"
    
    /// 当前App版本
    private var currentVersion: String {
        Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
    }
    
    /// 初始化方法
    private init() {}
    
    /// 检查版本更新，并在需要时显示提示
    func checkForUpdates() {
        fetchAppStoreVersion { [weak self] result in
            guard let self = self else { return }
            
            switch result {
            case .success(let storeVersion):
                let currentVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
                
                if self.compareVersions(storeVersion, currentVersion) > 0 {
                    DispatchQueue.main.async {
                        self.showUpdateAlert(currentVersion: currentVersion, storeVersion: storeVersion)
                    }
                }
            case .failure(let error):
                print("Version check failed: \(error.localizedDescription)")
            }
        }
    }
    
    /// 静默检查版本（不显示UI）
    func checkVersionSilently() {
        fetchAppStoreVersion { [weak self] result in
            guard let self = self else { return }
            
            switch result {
            case .success(let storeVersion):
                if self.compareVersions(storeVersion, self.currentVersion) > 0 {
                    // 可以在此处记录检测结果或者触发指定逻辑
                    print("New version \(storeVersion) available. Current version: \(self.currentVersion)")
                }
            case .failure(let error):
                print("Silent version check failed: \(error.localizedDescription)")
            }
        }
    }
    
    /// 显示更新提示
    func showUpdateAlert(currentVersion: String, storeVersion: String) {
        DispatchQueue.main.async {
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let rootViewController = windowScene.windows.first?.rootViewController {
                
                let alert = UIAlertController(
                    title: "新版本可用",
                    message: "发现新版本 v\(storeVersion)，您当前使用的是 v\(currentVersion)。是否前往App Store更新？",
                    preferredStyle: .alert
                )
                
                // 更新按钮
                alert.addAction(UIAlertAction(title: "立即更新", style: .default) { _ in
                    self.openAppStore()
                })
                
                // 稍后提示按钮
                alert.addAction(UIAlertAction(title: "稍后再说", style: .cancel, handler: nil))
                
                rootViewController.present(alert, animated: true)
            }
        }
    }
    
    /// 显示强制更新界面
    func showForceUpdateAlert(message: String? = nil) {
        DispatchQueue.main.async {
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let rootViewController = windowScene.windows.first?.rootViewController {
                
                let alert = UIAlertController(
                    title: "需要更新",
                    message: message ?? "当前版本已不再被支持，请更新后继续使用。",
                    preferredStyle: .alert
                )
                
                // 只有更新选项
                alert.addAction(UIAlertAction(title: "前往更新", style: .default) { _ in
                    self.openAppStore()
                })
                
                rootViewController.present(alert, animated: true)
            }
        }
    }
    
    /// 打开App Store
    private func openAppStore() {
        if let appStoreURL = URL(string: "https://apps.apple.com/app/id\(appId)") {
            UIApplication.shared.open(appStoreURL)
        }
    }
    
    /// 从App Store获取应用信息
    private func fetchAppStoreVersion(completion: @escaping (Result<String, Error>) -> Void) {
        guard let url = URL(string: "https://itunes.apple.com/lookup?bundleId=com.absir.1Step") else {
            completion(.failure(NSError(domain: "com.onestep.versionmanager", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])))
            return
        }
        
        let task = URLSession.shared.dataTask(with: url) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "com.onestep.versionmanager", code: -2, userInfo: [NSLocalizedDescriptionKey: "No data received"])))
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                   let results = json["results"] as? [[String: Any]],
                   let firstApp = results.first,
                   let version = firstApp["version"] as? String {
                    completion(.success(version))
                } else {
                    completion(.failure(NSError(domain: "com.onestep.versionmanager", code: -3, userInfo: [NSLocalizedDescriptionKey: "Invalid response format"])))
                }
            } catch {
                completion(.failure(error))
            }
        }
        
        task.resume()
    }
    
    /// 验证应用版本是否符合要求
    func validateAppVersion(completion: @escaping (Bool) -> Void) {
        // 对比本地最低版本
        let versionComparison = compareVersions(currentVersion, minimumSupportedVersion)
        if versionComparison >= 0 {
            completion(true)
            return
        }
        
        // TODO: 将来可以替换为实际的服务端API
        // 目前使用本地硬编码的最低版本进行检查
        completion(false)
    }
    
    /// 版本号比较，返回 > 0 表示v1大于v2，= 0 表示相等，< 0 表示v1小于v2
    private func compareVersions(_ v1: String, _ v2: String) -> Int {
        let v1Components = v1.components(separatedBy: ".").map { Int($0) ?? 0 }
        let v2Components = v2.components(separatedBy: ".").map { Int($0) ?? 0 }
        
        let maxLength = max(v1Components.count, v2Components.count)
        
        for i in 0..<maxLength {
            let v1Component = i < v1Components.count ? v1Components[i] : 0
            let v2Component = i < v2Components.count ? v2Components[i] : 0
            
            if v1Component > v2Component {
                return 1
            } else if v1Component < v2Component {
                return -1
            }
        }
        
        return 0 // 版本相等
    }
} 