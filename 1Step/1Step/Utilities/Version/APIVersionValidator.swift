import Foundation
import UIKit

/// API版本验证工具 - 用于检查应用版本是否被服务器支持
class APIVersionValidator {
    /// 单例
    static let shared = APIVersionValidator()
    
    /// 当前应用版本号
    var currentVersion: String {
        return Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
    }
    
    /// 服务端API地址
    private let versionCheckUrl = "https://api.1step.website/min-version"
    
    private init() {}
    
    /// 检查版本是否有效
    func validateAppVersion(completion: @escaping (Bool, String?) -> Void) {
        // 尝试从服务端获取最低版本
        let task = URLSession.shared.dataTask(with: URL(string: versionCheckUrl)!) { data, response, error in
            // 如果获取失败，默认允许使用
            if error != nil || data == nil {
                DispatchQueue.main.async {
                    completion(true, nil)
                }
                return
            }
            
            // 尝试解析服务端返回的最低版本
            do {
                if let json = try JSONSerialization.jsonObject(with: data!) as? [String: Any],
                   let minVersion = json["minVersion"] as? String {
                    
                    // 比较版本
                    let isValid = self.isVersionValid(self.currentVersion, minVersion)
                    
                    DispatchQueue.main.async {
                        if isValid {
                            completion(true, nil)
                        } else {
                            completion(false, "当前版本过低，需要升级到v\(minVersion)或更高版本")
                        }
                    }
                } else {
                    // 解析失败，允许使用
                    DispatchQueue.main.async {
                        completion(true, nil)
                    }
                }
            } catch {
                // 解析异常，允许使用
                DispatchQueue.main.async {
                    completion(true, nil)
                }
            }
        }
        
        // 启动请求
        task.resume()
        
        // 设置3秒超时
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            if task.state == .running {
                task.cancel()
                completion(true, nil)
            }
        }
    }
    
    /// 检查版本是否满足要求
    private func isVersionValid(_ currentVersion: String, _ minVersion: String) -> Bool {
        let current = currentVersion.components(separatedBy: ".").map { Int($0) ?? 0 }
        let minimum = minVersion.components(separatedBy: ".").map { Int($0) ?? 0 }
        
        let count = max(current.count, minimum.count)
        
        for i in 0..<count {
            let currentComponent = i < current.count ? current[i] : 0
            let minimumComponent = i < minimum.count ? minimum[i] : 0
            
            if currentComponent < minimumComponent {
                return false
            } else if currentComponent > minimumComponent {
                return true
            }
        }
        
        return true // 版本相等，视为有效
    }
    
    /// 执行与版本检查一起的关键操作
    func performVersionDependentOperation(_ operation: @escaping () -> Void) {
        validateAppVersion { isValid, message in
            DispatchQueue.main.async {
                if isValid {
                    // 版本有效，继续执行操作
                    operation()
                } else {
                    // 版本无效，显示强制更新提示
                    DependencyContainer.versionManager().showForceUpdateAlert(message: message)
                }
            }
        }
    }
} 