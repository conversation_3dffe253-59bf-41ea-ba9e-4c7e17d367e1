import SwiftUI

// MARK: - Toast 核心命名空间
enum Toast {
    // MARK: - Toast 位置枚举
    enum Position {
        case top
        case bottom
        case center
    }
    
    // MARK: - Toast 类型枚举
    enum MessageType {
        case success
        case error
        case warning
        case info
        case normal
        
        var icon: String {
            switch self {
            case .success:
                return "checkmark.circle.fill"
            case .error:
                return "xmark.circle.fill"
            case .warning:
                return "exclamationmark.triangle.fill"
            case .info:
                return "info.circle.fill"
            case .normal:
                return ""
            }
        }
        
        var color: Color {
            switch self {
            case .success:
                return .green
            case .error:
                return .red
            case .warning:
                return .orange
            case .info:
                return .blue
            case .normal:
                return .secondary
            }
        }
    }
    
    // MARK: - Toast 操作结构体
    struct Action {
        let title: String
        let action: () -> Void
    }
} 