import Foundation

extension Toast {
    // MARK: - Toast 操作类型枚举
    enum OperationType {
        case show
        case hide
    }
    
    // MARK: - Toast 操作模型
    struct Operation {
        let id = UUID()
        let type: OperationType
        let data: Data?
        let displayDuration: TimeInterval
        
        // 显示操作的初始化器
        init(data: Data, displayDuration: TimeInterval = 2.0) {
            self.type = .show
            self.data = data
            self.displayDuration = displayDuration
        }
        
        // 隐藏操作的初始化器
        init() {
            self.type = .hide
            self.data = nil
            self.displayDuration = 0
        }
    }
} 