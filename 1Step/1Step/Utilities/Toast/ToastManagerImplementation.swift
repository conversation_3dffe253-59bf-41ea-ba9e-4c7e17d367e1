import SwiftUI
import Combine

extension Toast {
    // MARK: - Toast 管理器实现
    class Manager: ObservableObject, Managing {
        static let shared = Manager()
        
        // 发布状态
        @Published var currentToast: Data? = nil
        @Published var isShowing: Bool = false
        
        // Combine 发布者，用于通知订阅者
        let toastPublisher = PassthroughSubject<(message: String, type: MessageType), Never>()
        
        // Toast操作队列和状态
        private var toastQueue: [Operation] = []
        private var isProcessingQueue = false
        private var isHidingForReplacement = false
        
        // 用于确保自动隐藏的准确性
        private var currentToastId: UUID? = nil
        private var hideTask: DispatchWorkItem? = nil
        private var lastToastStartTime: Date? = nil
        
        // 事件系统集成
        private var eventSubscription: AnyCancellable?
        
        // 动画控制
        @Published var animationID = UUID()
        
        private init() {
            // 订阅Toast相关事件
            setupEventSubscription()
        }
        
        // MARK: - 事件系统集成
        
        /// 设置事件总线订阅
        private func setupEventSubscription() {
            
            // 确保之前的订阅被取消
            eventSubscription?.cancel()
            
            // 创建新的订阅
            eventSubscription = EventBus.shared.events
                .filter { [weak self] event in
                    let shouldHandle = self?.shouldHandleEvent(event) ?? false
                    if shouldHandle {
                    }
                    return shouldHandle
                }
                .receive(on: DispatchQueue.main)
                .sink { [weak self] event in
                    self?.handleEvent(event)
                }
            
        }
        
        /// 判断是否应处理事件
        private func shouldHandleEvent(_ event: AppEvent) -> Bool {
            switch event {
            case .showToast, .hideToast:
                return true
            default:
                return false
            }
        }
        
        /// 处理事件
        private func handleEvent(_ event: AppEvent) {
            switch event {
            case .showToast(let message, let type, let position, let action, let onDismiss, let isDeleteToast, let isCompleteToast, let isUndo):
                
                // 根据标志创建适当类型的Toast
                if isDeleteToast {
                    // 使用删除Toast的特殊数据
                    let toastData = Data(
                        message: message,
                        type: type,
                        position: position,
                        action: action,
                        onDismiss: onDismiss,
                        isDeleteToast: true
                    )
                    
                    // 添加到队列并处理
                    let operation = Operation(data: toastData, displayDuration: 1.5)
                    enqueueOperation(operation, highPriority: true)
                    
                    // 触觉反馈已在TaskCardView.swift的handleDeleteAction方法中提供
                    // 包括medium级别的反馈和warning级别的通知，无需在这里重复触发
                } else if isCompleteToast {
                    // 使用完成Toast的特殊数据
                    let toastData = Data(
                        message: message,
                        type: type, 
                        position: position,
                        action: action,
                        onDismiss: onDismiss,
                        isCompleteToast: true
                    )
                    
                    // 添加到队列并处理
                    let operation = Operation(data: toastData, displayDuration: 1.5)
                    enqueueOperation(operation, highPriority: true)
                    
                    // 触发触觉反馈
                    DispatchQueue.main.async {
                        let successFeedback = UINotificationFeedbackGenerator()
                        successFeedback.notificationOccurred(.success)
                    }
                } else if isUndo {
                    // 使用撤销成功Toast的特殊数据
                    let toastData = Data(
                        message: message,
                        type: type,
                        position: position,
                        isUndo: true
                    )
                    
                    // 添加到队列并处理
                    let operation = Operation(data: toastData, displayDuration: 1.5)
                    enqueueOperation(operation, highPriority: true)
                    
                    // 触发轻微触觉反馈
                    DispatchQueue.main.async {
                        let lightFeedback = UIImpactFeedbackGenerator(style: .light)
                        lightFeedback.impactOccurred()
                    }
                } else {
                    // 使用普通Toast
                    show(message, type: type, position: position, action: action, onDismiss: onDismiss)
                }
                
            case .hideToast:
                hide()
            default:
                break
            }
        }
        
        // MARK: - 公共接口方法
        
        /// 显示一个标准的Toast通知
        func show(
            _ message: String, 
            type: MessageType = .normal,
            position: Position = .top, 
            action: Action? = nil,
            onDismiss: (() -> Void)? = nil
        ) {
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                
                // 创建 Toast 数据
                let toastData = Data(
                    message: message,
                    type: type,
                    position: position,
                    action: action,
                    onDismiss: onDismiss
                )
                
                // 添加到队列并处理
                let operation = Operation(data: toastData, displayDuration: 1.5)
                self.enqueueOperation(operation, highPriority: false)
            }
        }
        
        /// 隐藏当前显示的Toast，并执行onDismiss回调
        func hide() {
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                
                // 记录日志：计算实际显示时间
                if let startTime = self.lastToastStartTime {
                    let duration = Date().timeIntervalSince(startTime)
                } else {
                }
                
                // 取消当前的自动隐藏任务
                self.cancelHideTask()
                
                // 如果当前有Toast显示，执行onDismiss回调
                if let onDismiss = self.currentToast?.onDismiss, !self.isHidingForReplacement {
                    onDismiss()
                }
                
                // 使用动画隐藏当前Toast
                self.hideCurrentToast(completion: {
                    self.lastToastStartTime = nil
                    // 处理下一个队列项
                    self.isProcessingQueue = false
                    self.processNextInQueue()
                })
            }
        }
        
        /// 隐藏当前显示的Toast，但不执行onDismiss回调
        func hideWithoutDismissCallback() {
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                
                // 记录日志：计算实际显示时间
                if let startTime = self.lastToastStartTime {
                    let duration = Date().timeIntervalSince(startTime)
                } else {
                }
                
                // 取消当前的自动隐藏任务
                self.cancelHideTask()
                
                // 使用动画隐藏当前Toast
                self.hideCurrentToast(completion: {
                    self.lastToastStartTime = nil
                    // 处理下一个队列项
                    self.isProcessingQueue = false
                    self.processNextInQueue()
                })
            }
        }
        
        /// 特殊方法：等待当前Toast完全隐藏后再显示新Toast，解决撤销Toast显示时间过短的问题
        func hideCurrentAndThenShow(_ newToastOperation: @escaping () -> Void) {
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                
                
                // 如果当前有Toast显示，立即隐藏它
                if self.isShowing {
                    self.hideCurrentToast()
                }
                
                // 直接执行新Toast显示操作
                newToastOperation()
            }
        }
        
        // MARK: - 任务相关的便捷方法
        
        /// 显示任务添加的 Toast
        func showSuccess(_ message: String = "已添加行动") {
            show(message, type: .success, position: .top)
        }
        func showWarning(_ message: String = "已添加行动") {
            show(message, type: .warning, position: .top)
        }
        func showError(_ message: String = "已添加行动") {
            show(message, type: .error, position: .top)
        }
        func showInfo(_ message: String = "已添加行动") {
            show(message, type: .info, position: .top)
        }
        
        func showInfoLight(_ message: String = "已更新") {
            show(message, type: .normal, position: .top)
        }
        
        /// 显示超轻量级 Toast - 占用空间更小、颜色更淡、持续时间更短
        func showSuperLightInfo(_ message: String = "已更新") {
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                
                // 创建超轻量级 Toast 数据
                let toastData = Data(
                    message: message,
                    type: .normal,
                    position: .top,  // 显示在顶部，下拉刷新后更易注意到
                    isSuperLight: true
                )
                
                // 只使用 1.2 秒的显示时间，比标准更短
                let operation = Operation(data: toastData, displayDuration: 1.2)
                
                // 添加到队列
                let highPriority = false
                
                if highPriority {
                    if operation.type == .show, let toastData = operation.data {
                        if self.isShowing {
                            self.isHidingForReplacement = true
                            self.hideCurrentToast {
                                self.isHidingForReplacement = false
                                self.showToast(toastData, duration: operation.displayDuration)
                            }
                        } else {
                            self.showToast(toastData, duration: operation.displayDuration)
                        }
                    }
                } else {
                    // 普通优先级：添加到队列末尾
                    self.toastQueue.append(operation)
                    
                    // 如果当前没有正在处理的操作，开始处理队列
                    if !self.isProcessingQueue {
                        self.processNextInQueue()
                    }
                }
            }
        }

        /// 显示任务完成的 Toast，可选带有撤销操作和消失回调
        func showTaskCompleted(_ message: String = "已完成行动", action: Action? = nil, onDismiss: (() -> Void)? = nil) {
            // 创建完成Toast数据
            let toastData = Data(
                message: message,
                type: .success,
                position: .bottom,
                action: action,
                onDismiss: onDismiss,
                isCompleteToast: true
            )
            
            // 添加到队列，高优先级，确保立即显示
            let operation = Operation(data: toastData, displayDuration: 1.5)
            enqueueOperation(operation, highPriority: true)
            
            // 触发触觉反馈 - 完成操作使用成功类型反馈
            DispatchQueue.main.async {
                let successFeedback = UINotificationFeedbackGenerator()
                successFeedback.notificationOccurred(.success)
            }
        }
        
        /// 显示任务删除的 Toast，可选带有撤销操作和消失回调
        func showTaskDeleted(_ message: String = "已删除行动", action: Action? = nil, onDismiss: (() -> Void)? = nil) {
            // 创建删除Toast数据
            let toastData = Data(
                message: message,
                type: .normal,
                position: .bottom,
                action: action,
                onDismiss: onDismiss,
                isDeleteToast: true
            )
            
            // 添加到队列，高优先级，确保立即显示
            let operation = Operation(data: toastData, displayDuration: 1.5)
            enqueueOperation(operation, highPriority: true)
            
            // 触发触觉反馈
            DispatchQueue.main.async {
                let mediumFeedback = UIImpactFeedbackGenerator(style: .medium)
                mediumFeedback.impactOccurred()
            }
        }
        
        /// 显示撤销操作后的 Toast 通知
        func showUndoDeletionSuccess(_ message: String = "已撤销删除") {
            
            // 创建撤销成功Toast数据，但样式模仿超轻量级
            let toastData = Data(
                message: message,
                type: .normal,
                position: .top,
                isSuperLight: true
            )
            
            // 修改为1.2秒的显示时间
            let operation = Operation(data: toastData, displayDuration: 1.2)
            
            // 记录详细的调用堆栈以便调试
            Thread.callStackSymbols.prefix(10).forEach { print("[UNDOFLOW_DEBUG] \($0)") }
            
            // 高优先级处理这个Toast
            enqueueOperation(operation, highPriority: true)
            
            // 触发触觉反馈
            DispatchQueue.main.async {
                let lightFeedback = UIImpactFeedbackGenerator(style: .light)
                lightFeedback.impactOccurred()
            }
        }
        
        // MARK: - 队列管理方法
        
        /// 将操作添加到队列并处理
        private func enqueueOperation(_ operation: Operation, highPriority: Bool = false) {
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                
                let operationType = operation.type == .show ? 
                    (operation.data?.isDeleteToast == true ? "删除Toast" : 
                     (operation.data?.isUndo == true ? "撤销Toast" : "普通Toast")) : "隐藏操作"
                
                if operation.data?.isUndo == true {
                } else {
                }
                
                if highPriority {
                    // 高优先级操作：直接处理而不入队
                    if operation.data?.isUndo == true {
                    } else {
                    }
                    
                    // 【关键修复】清除可能存在的队列中的低优先级操作
                    if !self.toastQueue.isEmpty {
                        if operation.data?.isUndo == true {
                        } else {
                        }
                        self.toastQueue.removeAll()
                    }
                    
                    if operation.type == .show, let toastData = operation.data {
                        // 如果当前有显示的Toast，设置替换标志并隐藏当前Toast
                        if self.isShowing {
                            if toastData.isUndo == true {
                            } else {
                            }
                            
                            // 【关键修复】立即执行当前Toast的onDismiss回调
                            if let onDismiss = self.currentToast?.onDismiss {
                                onDismiss()
                            }
                            
                            self.isHidingForReplacement = true
                            
                            if let currentToastId = self.currentToastId {
                                let toastType = self.currentToast?.isDeleteToast == true ? "删除Toast" : 
                                               (self.currentToast?.isUndo == true ? "撤销Toast" : "普通Toast")
                                
                                if toastData.isUndo == true {
                                } else {
                                }
                            }
                            
                            // 隐藏当前Toast后显示新Toast
                            self.hideCurrentToast {
                                // 重置替换标志
                                self.isHidingForReplacement = false
                                
                                // 显示新Toast
                                if toastData.isUndo == true {
                                } else {
                                }
                                self.showToast(toastData, duration: operation.displayDuration)
                            }
                        } else {
                            // 如果当前没有Toast显示，直接显示新Toast
                            if toastData.isUndo == true {
                            } else {
                            }
                            self.showToast(toastData, duration: operation.displayDuration)
                        }
                    } else if operation.type == .hide {
                        // 直接执行隐藏操作
                        if operation.data?.isUndo == true {
                        } else {
                        }
                        self.hide()
                    }
                } else {
                    // 普通优先级：添加到队列末尾
                    self.toastQueue.append(operation)
                    if operation.data?.isUndo == true {
                    } else {
                    }
                    
                    // 如果当前没有正在处理的操作，开始处理队列
                    if !self.isProcessingQueue {
                        if operation.data?.isUndo == true {
                        } else {
                        }
                        self.processNextInQueue()
                    } else {
                        if operation.data?.isUndo == true {
                        } else {
                        }
                    }
                }
            }
        }
        
        /// 处理队列中的下一个操作
        private func processNextInQueue() {
            DispatchQueue.main.async { [weak self] in
                guard let self = self, !self.toastQueue.isEmpty, !self.isProcessingQueue else {
                    if self?.toastQueue.isEmpty == true {
                    }
                    return
                }
                
                // 标记开始处理队列
                self.isProcessingQueue = true
                
                // 获取下一个操作
                let operation = self.toastQueue.removeFirst()
                
                // 根据操作类型执行
                switch operation.type {
                case .show:
                    guard let toastData = operation.data else {
                        self.isProcessingQueue = false
                        self.processNextInQueue()
                        return
                    }
                    
                    let toastType = toastData.isDeleteToast ? "删除" : (toastData.isUndo ? "撤销" : "普通")
                    
                    if self.isShowing {
                        // 如果当前有Toast显示，先隐藏
                        self.hideCurrentToast {
                            self.showToast(toastData, duration: operation.displayDuration)
                        }
                    } else {
                        // 直接显示新Toast
                        self.showToast(toastData, duration: operation.displayDuration)
                    }
                    
                case .hide:
                    self.hide()
                }
            }
        }
        
        // MARK: - 核心显示/隐藏方法
        
        /// 显示新的Toast
        private func showToast(_ toastData: Data, duration: TimeInterval) {
            if toastData.isUndo {
            } else {
            }
            
            // 记录开始时间用于统计实际显示时间
            self.lastToastStartTime = Date()
            
            // 【关键修复】SwiftUI动画触发顺序
            // 1. 首先生成新的动画ID（这会使.animation(xxx, value: animationID)重新触发）
            self.animationID = UUID()
            
            // 2. 设置新的Toast数据
            self.currentToast = toastData
            self.currentToastId = toastData.id
            
            if toastData.isUndo {
            }
            
            // 3. 等待一个微小的时间，让视图系统响应数据变化
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                
                // 4. 最后，改变显示状态，这会触发transition动画
                if toastData.isUndo {
                } else {
                }
                withAnimation(.spring(response: 0.5, dampingFraction: 0.65)) {
                    self.isShowing = true
                }
                
                // 发送通知
                self.toastPublisher.send((toastData.message, toastData.type))
                
                // 创建自动隐藏任务
                if toastData.isUndo {
                } else {
                }
                self.scheduleHideTask(after: duration)
            }
        }
        
        /// 隐藏当前Toast
        private func hideCurrentToast(completion: @escaping () -> Void = {}) {
            
            // 重要：捕获当前要隐藏的Toast ID和onDismiss回调
            let toastIdToHide = self.currentToastId
            let onDismissCallback = self.currentToast?.onDismiss
            let isReplacingToast = self.isHidingForReplacement
            
            // 输出正在隐藏的Toast信息
            if let toastId = toastIdToHide {
                let toastType = self.currentToast?.isDeleteToast == true ? "删除Toast" : 
                               (self.currentToast?.isUndo == true ? "撤销Toast" : "普通Toast")
            }
            
            // 取消当前的自动隐藏任务
            self.cancelHideTask()
            
            // 使用动画隐藏Toast
            withAnimation(.easeOut(duration: 0.3)) {
                self.isShowing = false
            }
            
            // 隐藏完成后执行回调
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.35) { [weak self] in
                guard let self = self else { return }
                
                // 【关键修复】先执行onDismiss回调，再清理状态
                // 只要不是替换操作，就应该执行onDismiss
                if !isReplacingToast, let onDismiss = onDismissCallback {
                    onDismiss()
                }
                
                // 关键修复：只清除匹配ID的Toast，避免清除其他Toast
                if let toastIdToHide = toastIdToHide, self.currentToastId == toastIdToHide {
                    // 清除当前Toast数据
                    self.currentToast = nil
                    self.currentToastId = nil
                } else if let currentId = self.currentToastId, toastIdToHide != nil {
                    // Toast已更改，不清除当前显示的Toast
                }
                
                // 无论如何都执行完成回调
                completion()
            }
        }
        
        // MARK: - 辅助方法
        
        /// 安排自动隐藏的任务
        private func scheduleHideTask(after seconds: TimeInterval) {
            // 取消之前的任务
            cancelHideTask()
            
            // 保存当前Toast ID，确保只隐藏当前Toast
            let toastId = self.currentToastId
            let toastType = self.currentToast?.isDeleteToast == true ? "删除" : 
                           (self.currentToast?.isUndo == true ? "撤销" : "普通")
            
            
            // 创建新的隐藏任务
            let task = DispatchWorkItem { [weak self] in
                guard let self = self, self.currentToastId == toastId else {
                    return
                }
                
                // 记录实际显示时间
                if let startTime = self.lastToastStartTime {
                    let actualDuration = Date().timeIntervalSince(startTime)
                } else {
                }
                
                // 执行隐藏
                self.hide()
            }
            
            // 保存任务引用
            self.hideTask = task
            
            // 延迟执行任务
            // 【关键修复】确保延迟时间足够长，并且精确执行
            DispatchQueue.main.asyncAfter(deadline: .now() + seconds) {
                task.perform()
            }
        }
        
        /// 取消当前的自动隐藏任务
        private func cancelHideTask() {
            if let task = hideTask {
                task.cancel()
                hideTask = nil
            }
        }
    }
} 