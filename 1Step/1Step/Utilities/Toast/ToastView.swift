import SwiftUI

extension Toast {
    // MARK: - Toast 视图
    struct View: SwiftUI.View {
        let toast: Data
        var onTap: () -> Void
        
        @Environment(\.colorScheme) private var colorScheme
        
        var body: some SwiftUI.View {
            // 根据Toast类型选择不同的视图样式
            if toast.isDeleteToast {
                deleteToastView
            } else if toast.isCompleteToast {
                completeToastView
            } else if toast.isUndo {
                undoSuccessToastView
            } else if toast.isSuperLight {
                // 超轻量级样式
                superLightToastView
            } else {
                regularToastView
            }
        }
        
        // 常规Toast视图
        private var regularToastView: some SwiftUI.View {
            HStack(spacing: 12) {
                // 图标
                if !toast.type.icon.isEmpty {
                    SwiftUI.Image(systemName: toast.type.icon)
                        .foregroundColor(toast.type.color)
                        .font(.system(size: 16, weight: .semibold))
                }
                
                // 消息
                Text(toast.message)
                    .font(.footnote)
                    .foregroundColor(.primary)
                
                // 动作按钮
                if let action = toast.action {
                    Spacer()
                    
                    Button(action: {
                        action.action()
                        onTap()
                    }) {
                        Text(action.title)
                            .font(.footnote.bold())
                            .foregroundColor(toast.type.color)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(
                                Capsule()
                                    .fill(toast.type.color.opacity(0.15))
                            )
                    }
                    .buttonStyle(PlainButtonStyle())
                    .contentShape(Rectangle())
                    .padding(4)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(colorScheme == .dark ? Color(UIColor.systemGray6) : Color.white)
                    .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
            )
            .onTapGesture {
                onTap()
            }
        }
        
        // 删除Toast特殊视图
        private var deleteToastView: some SwiftUI.View {
            HStack(spacing: 12) {
                // 删除图标 - 使用次要颜色
                SwiftUI.Image(systemName: "trash")
                    .font(.system(size: 16))
                    .foregroundColor(.secondary) // 改为次要颜色
                    .frame(width: 24, height: 24) // 可以稍微缩小图标区域
                    // .overlay(...) // 移除虚线圆圈边框
                
                // 只显示"已删除"
                Text("已删除")
                    .font(.system(size: 15, weight: .medium))
                    .foregroundColor(.secondary) // 改为次要颜色
                
                Spacer()
                
                // 撤销按钮 - 仅显示文本，保留颜色
                if let action = toast.action {
                    Text(action.title)
                        .font(.system(size: 15, weight: .medium))
                        .foregroundColor(Color.red) // 保留红色
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 10) // 圆角稍小
                    .fill(colorScheme == .dark ? Color(UIColor.systemGray6) : Color.white)
                    // .overlay(...) // 移除边框
                    .shadow(color: Color.black.opacity(colorScheme == .dark ? 0.1 : 0.08), radius: 4, x: 0, y: 1) // 减弱阴影
            )
            .onTapGesture {
                if let action = toast.action {
                    action.action()
                    onTap()
                }
            }
        }
        
        // 完成Toast特殊视图
        private var completeToastView: some SwiftUI.View {
            HStack(spacing: 12) {
                // 完成图标 - 使用次要颜色
                SwiftUI.Image(systemName: "checkmark.circle")
                    .font(.system(size: 16))
                    .foregroundColor(.secondary) // 改为次要颜色
                    .frame(width: 24, height: 24) // 可以稍微缩小图标区域
                    // .overlay(...) // 移除虚线圆圈边框
                
                // 只显示"已完成"
                Text("已完成")
                    .font(.system(size: 15, weight: .medium))
                    .foregroundColor(.secondary) // 改为次要颜色
                
                Spacer()
                
                // 撤销按钮 - 仅显示文本，保留颜色
                if let action = toast.action {
                    Text(action.title)
                        .font(.system(size: 15, weight: .medium))
                        .foregroundColor(Color.green) // 保留绿色
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 10) // 圆角稍小
                    .fill(colorScheme == .dark ? Color(UIColor.systemGray6) : Color.white)
                    // .overlay(...) // 移除边框
                    .shadow(color: Color.black.opacity(colorScheme == .dark ? 0.1 : 0.08), radius: 4, x: 0, y: 1) // 减弱阴影
            )
            .onTapGesture {
                if let action = toast.action {
                    action.action()
                    onTap()
                }
            }
        }
        
        // 提取任务标题的辅助方法 - 不再在 delete/complete 视图中使用
        // private func extractTaskTitle(from message: String) -> String { ... }
        
        // 超轻量级 Toast 视图 - 用于不打扰用户的微小提示
        private var superLightToastView: some SwiftUI.View {
            HStack(spacing: 8) {
                // 简化的消息文本
                Text(toast.message)
                    .font(.footnote)
                    .foregroundColor(colorScheme == .dark ? Color(red: 0.92, green: 0.92, blue: 0.96).opacity(0.8) : Color(red: 0.2, green: 0.2, blue: 0.2).opacity(0.8))
            }
            .padding(.horizontal, 12) // 减小水平内边距
            .padding(.vertical, 6)    // 减小垂直内边距
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(colorScheme == .dark ? 
                          Color(UIColor.systemGray6).opacity(0.8) : 
                          AppColors.UI.card(for: colorScheme).opacity(0.8))
                    .shadow(color: Color.black.opacity(0.05), radius: 3, x: 0, y: 1) // 减小阴影
            )
            .onTapGesture {
                onTap()
            }
        }
        
        // 撤销成功的特殊Toast视图
        private var undoSuccessToastView: some SwiftUI.View {
            HStack(spacing: 12) {
                // 简洁的撤销图标 - 使用主要颜色
                SwiftUI.Image(systemName: "arrow.uturn.left")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.primary) // 改为主要颜色
                
                // 消息文本 - 使用主要颜色
                Text(toast.message)
                    .font(.system(size: 15, weight: .medium))
                    .foregroundColor(.primary) // 改为主要颜色
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 10) // 与其他卡片样式统一
                    .fill(colorScheme == .dark ? Color(UIColor.systemGray6) : Color.white)
                    // .overlay(...) // 移除边框
                    .shadow(color: Color.black.opacity(colorScheme == .dark ? 0.1 : 0.08), radius: 4, x: 0, y: 1) // 减弱阴影
            )
            .onTapGesture {
                onTap()
            }
        }
    }
} 