import Foundation

extension Toast {
    // MARK: - Toast 数据模型
    struct Data: Identifiable {
        let id = UUID()
        let message: String
        let type: MessageType
        let position: Position
        let action: Action?
        let onDismiss: (() -> Void)?
        let isDeleteToast: Bool
        let isUndo: Bool
        let isCompleteToast: Bool
        let isSuperLight: Bool
        
        init(message: String, type: MessageType, position: Position, action: Action? = nil, onDismiss: (() -> Void)? = nil) {
            self.message = message
            self.type = type
            self.position = position
            self.action = action
            self.onDismiss = onDismiss
            self.isDeleteToast = false
            self.isUndo = false
            self.isCompleteToast = false
            self.isSuperLight = false
        }
        
        init(message: String, type: MessageType, position: Position, action: Action? = nil, onDismiss: (() -> Void)? = nil, isDeleteToast: Bool) {
            self.message = message
            self.type = type
            self.position = position
            self.action = action
            self.onDismiss = onDismiss
            self.isDeleteToast = isDeleteToast
            self.isUndo = false
            self.isCompleteToast = false
            self.isSuperLight = false
        }
        
        // 专用于完成Toast的初始化器
        init(message: String, type: MessageType, position: Position, action: Action? = nil, onDismiss: (() -> Void)? = nil, isCompleteToast: Bool) {
            self.message = message
            self.type = type
            self.position = position
            self.action = action
            self.onDismiss = onDismiss
            self.isDeleteToast = false
            self.isUndo = false
            self.isCompleteToast = isCompleteToast
            self.isSuperLight = false
        }
        
        // 专用于撤销成功的初始化器
        init(message: String, type: MessageType, position: Position, isUndo: Bool) {
            self.message = message
            self.type = type
            self.position = position
            self.action = nil
            self.onDismiss = nil
            self.isDeleteToast = false
            self.isUndo = isUndo
            self.isCompleteToast = false
            self.isSuperLight = false
        }
        
        // 专用于超轻量级Toast的初始化器
        init(message: String, type: MessageType, position: Position, isSuperLight: Bool) {
            self.message = message
            self.type = type
            self.position = position
            self.action = nil
            self.onDismiss = nil
            self.isDeleteToast = false
            self.isUndo = false
            self.isCompleteToast = false
            self.isSuperLight = isSuperLight
        }
    }
} 