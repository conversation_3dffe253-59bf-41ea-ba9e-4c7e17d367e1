import SwiftUI

// MARK: - Toast 修饰器
struct ToastModifier: ViewModifier {
    @StateObject private var toastManager = Toast.Manager.shared
    
    func body(content: Content) -> some View {
        ZStack {
            content
            
            // 将条件移到内部视图，让SwiftUI处理整个VStack的添加/移除
            VStack {
                if toastManager.isShowing, let toast = toastManager.currentToast {
                    switch toast.position {
                    case .top:
                        Toast.View(toast: toast) {
                            handleToastTap(toast)
                        }
                        .padding(.top, 80)
                        .padding(.horizontal)
                        .transition(
                            .asymmetric(
                                insertion: AnyTransition.move(edge: .top)
                                    .combined(with: .opacity),
                                removal: AnyTransition.opacity
                            )
                        )
                        .zIndex(100)
                        Spacer()
                        
                    case .bottom:
                        Spacer()
                        Toast.View(toast: toast) {
                            handleToastTap(toast)
                        }
                        .padding(.bottom, toast.isDeleteToast ? 30 : (toast.isUndo ? 30 : 8))
                        .padding(.horizontal, toast.isDeleteToast || toast.isUndo ? 20 : 16)
                        .transition(
                            .asymmetric(
                                insertion: AnyTransition.move(edge: .bottom)
                                    .combined(with: .opacity),
                                removal: AnyTransition.opacity
                            )
                        )
                        .zIndex(100)
                        
                    case .center:
                        Spacer()
                        Toast.View(toast: toast) {
                            handleToastTap(toast)
                        }
                        .padding(.horizontal)
                        .transition(.scale.combined(with: .opacity))
                        .zIndex(100)
                        Spacer()
                    }
                }
            }
            // 关键修改：将整个动画移至外层，使用两个不同的触发值
            .animation(.spring(response: 0.5, dampingFraction: 0.65), value: toastManager.animationID)
            .animation(.easeOut(duration: 0.3), value: toastManager.isShowing)
        }
    }
    
    private func handleToastTap(_ toast: Toast.Data) {
        // 对于删除Toast，由于onTapGesture中已经完整处理了点击事件，这里不做任何操作
        if toast.isDeleteToast {
            return
        }
        
        // 其他类型的Toast保持原有逻辑
        if let action = toast.action {
            action.action()
        }
        toastManager.hideWithoutDismissCallback()
    }
}

// 撤销成功图标的动画修饰符
struct UndoSuccessIconModifier: ViewModifier {
    @State private var isAnimating = false
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isAnimating ? 1.1 : 1.0)
            .onAppear {
                withAnimation(Animation.easeInOut(duration: 0.5).repeatForever(autoreverses: true)) {
                    isAnimating = true
                }
            }
    }
}

// 脉冲效果动画修饰符
struct PulseEffect: ViewModifier {
    @State private var isAnimating = false
    
    func body(content: Content) -> some View {
        content
            .opacity(isAnimating ? 1.0 : 0.7)
            .scaleEffect(isAnimating ? 1.1 : 1.0)
            .onAppear {
                withAnimation(Animation.easeInOut(duration: 0.8).repeatForever(autoreverses: true)) {
                    isAnimating = true
                }
            }
    }
} 