import Foundation

extension Toast {
    // MARK: - Toast 管理协议
    protocol Managing {
        func show(_ message: String, type: MessageType, position: Position, action: Action?, onDismiss: (() -> Void)?)
        func hide()
        func hideWithoutDismissCallback()
        func hideCurrentAndThenShow(_ newToastOperation: @escaping () -> Void)

        // 任务相关的便捷方法
        func showSuccess(_ message: String)
        func showWarning(_ message: String)
        func showError(_ message: String)
        func showInfo(_ message: String)
        func showTaskCompleted(_ message: String, action: Action?, onDismiss: (() -> Void)?)
        func showTaskDeleted(_ message: String, action: Action?, onDismiss: (() -> Void)?)
        func showUndoDeletionSuccess(_ message: String)
        func showInfoLight(_ message: String)
        func showSuperLightInfo(_ message: String)
    }
} 