import Foundation
import _Concurrency // 导入Swift并发框架

/// 验证结果模型
struct VerificationResult {
    let valid: Bool
    let remainingUses: Int?
    let message: String
}

/// 邀请码云服务 - 处理与云端的交互
class InviteCloudService {
    private let baseURL = URL(string: "https://192.168.2.187:3000/api/invites/")!
    private let apiKey = "1step_dev_api_key" // 实际项目中应从安全存储获取
    
    /// 验证邀请码
    /// - Parameter code: 要验证的邀请码
    /// - Returns: 验证结果，包括是否有效和剩余使用次数
    func verifyCode(_ code: String) async throws -> VerificationResult {
        // 构建请求URL
        let url = baseURL.appendingPathComponent("verify")
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue(apiKey, forHTTPHeaderField: "X-API-Key")
        
        // 准备请求体
        let requestBody = ["code": code]
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        // 发送实际网络请求
        let (data, response) = try await URLSession.shared.data(for: request)
        
        // 检查HTTP响应状态码
        guard let httpResponse = response as? HTTPURLResponse else {
            throw InviteError.networkError
        }
        
        // 检查状态码
        guard 200...299 ~= httpResponse.statusCode else {
            throw InviteError.networkError
        }
        
        // 解析响应数据
        guard let jsonResult = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let isValid = jsonResult["valid"] as? Bool,
              let message = jsonResult["message"] as? String else {
            throw InviteError.networkError
        }
        
        let remainingUses = jsonResult["remainingUses"] as? Int
        
        return VerificationResult(valid: isValid, remainingUses: remainingUses, message: message)
    }
    
    /// 检查邀请码是否已存在
    /// - Parameter code: 要检查的邀请码
    /// - Returns: 是否已存在
    func checkCodeExists(_ code: String) async throws -> Bool {
        // 构建请求URL
        let url = baseURL.appendingPathComponent("check")
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue(apiKey, forHTTPHeaderField: "X-API-Key")
        
        // 准备请求体
        let requestBody = ["code": code]
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        // 发送实际网络请求
        let (data, response) = try await URLSession.shared.data(for: request)
        
        // 检查HTTP响应状态码
        guard let httpResponse = response as? HTTPURLResponse else {
            throw InviteError.networkError
        }
        
        // 检查状态码
        guard 200...299 ~= httpResponse.statusCode else {
            throw InviteError.networkError
        }
        
        // 解析响应数据
        guard let jsonResult = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let exists = jsonResult["exists"] as? Bool else {
            throw InviteError.networkError
        }
        
        return exists
    }
    
    /// 将邀请码标记为已使用
    /// - Parameters:
    ///   - code: 邀请码
    ///   - usedByDeviceId: 使用设备ID
    func markCodeAsUsed(_ code: String, usedByDeviceId: String) async throws {
        // 构建请求URL
        let url = baseURL.appendingPathComponent("use")
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue(apiKey, forHTTPHeaderField: "X-API-Key")
        
        // 准备请求体
        let requestBody = ["code": code, "usedByDeviceId": usedByDeviceId]
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        // 发送实际网络请求
        let (data, response) = try await URLSession.shared.data(for: request)
        
        // 检查HTTP响应状态码
        guard let httpResponse = response as? HTTPURLResponse else {
            throw InviteError.networkError
        }
        
        // 检查状态码
        guard 200...299 ~= httpResponse.statusCode else {
            throw InviteError.networkError
        }
        
        // 确认请求成功
        _ = try JSONSerialization.jsonObject(with: data) as? [String: Any]
    }
    
    /// 创建邀请码
    /// - Parameters:
    ///   - code: 新邀请码
    ///   - creatorDeviceId: 创建者设备ID
    ///   - maxUses: 最大使用次数，默认为1
    func createCode(_ code: String, creatorDeviceId: String, maxUses: Int = 1) async throws {
        // 先检查是否已存在
        let exists = try await checkCodeExists(code)
        if exists {
            throw InviteError.codeAlreadyExists
        }
        
        // 构建请求URL
        let url = baseURL.appendingPathComponent("create")
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue(apiKey, forHTTPHeaderField: "X-API-Key")
        
        // 准备请求体
        let requestBody: [String: Any] = [
            "code": code,
            "creatorDeviceId": creatorDeviceId,
            "maxUses": maxUses
        ]
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        // 发送实际网络请求
        let (_, response) = try await URLSession.shared.data(for: request)
        
        // 检查HTTP响应状态码
        guard let httpResponse = response as? HTTPURLResponse else {
            throw InviteError.networkError
        }
        
        // 检查状态码
        guard 200...299 ~= httpResponse.statusCode else {
            throw InviteError.networkError
        }
        
        // 响应成功，不需要返回任何数据
    }
    
    /// 获取邀请码详情
    /// - Parameter code: 邀请码
    /// - Returns: 邀请码详情
    func getInviteDetails(_ code: String) async throws -> InviteCode? {
        // 构建请求URL
        let url = baseURL.appendingPathComponent("details/\(code)")
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.addValue(apiKey, forHTTPHeaderField: "X-API-Key")
        
        // 发送实际网络请求
        let (data, response) = try await URLSession.shared.data(for: request)
        
        // 检查HTTP响应状态码
        guard let httpResponse = response as? HTTPURLResponse else {
            throw InviteError.networkError
        }
        
        // 检查状态码
        guard 200...299 ~= httpResponse.statusCode else {
            throw InviteError.networkError
        }
        
        // 如果返回404，代表邀请码不存在
        if httpResponse.statusCode == 404 {
            return nil
        }
        
        // 解析响应数据
        guard let jsonResult = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let codeString = jsonResult["code"] as? String,
              let creatorId = jsonResult["creatorDeviceId"] as? String,
              let status = jsonResult["status"] as? String,
              let maxUses = jsonResult["maxUses"] as? Int,
              let currentUses = jsonResult["currentUses"] as? Int,
              let createdAtString = jsonResult["createdAt"] as? String,
              let usedByArray = jsonResult["usedBy"] as? [[String: Any]] else {
            throw InviteError.networkError
        }
        
        // 解析日期
        let dateFormatter = ISO8601DateFormatter()
        guard let createdAt = dateFormatter.date(from: createdAtString) else {
            throw InviteError.networkError
        }
        
        // 解析使用记录
        var useRecords: [InviteUseRecord] = []
        for useRecord in usedByArray {
            if let deviceId = useRecord["deviceId"] as? String,
               let usedAtString = useRecord["usedAt"] as? String,
               let usedAt = dateFormatter.date(from: usedAtString) {
                useRecords.append(InviteUseRecord(deviceId: deviceId, usedAt: usedAt))
            }
        }
        
        // 创建并返回邀请码对象
        return InviteCode(
            id: UUID(),
            code: codeString,
            creatorDeviceId: creatorId,
            createdAt: createdAt,
            status: InviteCodeStatus(rawValue: status) ?? .expired,
            maxUses: maxUses,
            currentUses: currentUses,
            usedBy: useRecords
        )
    }
    
    /// 自定义延迟函数，替代Task.sleep
    private func delay(seconds: Double) async throws {
        // 使用异步等待和Timer实现延迟
        try await withCheckedThrowingContinuation { continuation in
            DispatchQueue.main.asyncAfter(deadline: .now() + seconds) {
                continuation.resume(returning: ())
            }
        }
    }
} 