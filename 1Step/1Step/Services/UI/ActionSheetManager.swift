import SwiftUI

/// 定义ActionSheet管理协议
protocol ActionSheetManaging: ObservableObject {
    var isPresented: Bool { get set }
    var currentTask: Task? { get }
    var onChangeStatus: ((String) -> Void)? { get }
    var onDismiss: (() -> Void)? { get }
    var position: CGPoint? { get set }
    
    func showActionSheet(for task: Task, at position: CGPoint?, onChange: @escaping (String) -> Void, onDismiss: (() -> Void)?)
    func hideActionSheet()
}

/// 全局任务操作菜单管理器
class ActionSheetManager: ActionSheetManaging {
    // 单例实现
    static let shared = ActionSheetManager()
    
    // 发布属性，使视图能够响应变化
    @Published var isPresented: Bool = false
    @Published var currentTask: Task? = nil
    @Published var onChangeStatus: ((String) -> Void)? = nil
    @Published var onDismiss: (() -> Void)? = nil
    @Published var position: CGPoint? = nil
    
    // 私有初始化器确保只使用共享实例
    private init() {}
    
    /// 显示任务操作菜单
    /// - Parameters:
    ///   - task: 要操作的任务
    ///   - position: 弹出位置（如果为nil则居中显示）
    ///   - onChange: 状态变化时的回调
    ///   - onDismiss: 菜单消失时的回调
    func showActionSheet(for task: Task, at position: CGPoint? = nil, onChange: @escaping (String) -> Void, onDismiss: (() -> Void)? = nil) {
        self.currentTask = task
        self.onChangeStatus = onChange
        self.onDismiss = onDismiss
        self.position = position
        
        // 使用动画显示
        withAnimation(.easeInOut(duration: 0.2)) {
            self.isPresented = true
        }
    }
    
    /// 隐藏任务操作菜单
    func hideActionSheet() {
        // 使用动画隐藏
        withAnimation(.easeInOut(duration: 0.2)) {
            self.isPresented = false
        }
        
        // 执行关闭回调
        self.onDismiss?()
        self.position = nil
    }
}

/// 在SwiftUI视图上添加全局操作菜单的视图修饰器
struct WithActionSheetModifier: ViewModifier {
    @ObservedObject var manager: ActionSheetManager = ActionSheetManager.shared
    @Environment(\.taskRepository) private var taskRepository
    @Environment(\.toastManager) private var toastManager
    
    func body(content: Content) -> some View {
        ZStack {
            // 原始内容
            content
                .allowsHitTesting(!manager.isPresented) // 当菜单显示时禁用下层视图的交互
            
            // 全局操作菜单层
            if manager.isPresented, let task = manager.currentTask, let onChange = manager.onChangeStatus {
                Color.black.opacity(0.001) // 几乎透明的全屏覆盖层
                    .edgesIgnoringSafeArea(.all)
                    .onTapGesture {
                        manager.hideActionSheet()
                    }
                
                GeometryReader { geometry in
                    menuContent(for: task, onChange: onChange, in: geometry)
                }
            }
        }
    }
    
    // 根据位置显示菜单内容
    private func menuContent(for task: Task, onChange: @escaping (String) -> Void, in geometry: GeometryProxy) -> some View {
        let menuView = HStack(spacing: 12) {
            buttonGroupForTask(task, onChange: onChange)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(UIColor.systemBackground))
                .shadow(color: Color.black.opacity(0.15), radius: 10, x: 0, y: 4)
        )
        .padding(.horizontal, 24)
        
        if let position = manager.position {
            // 使用提供的位置进行定位
            return AnyView(menuView
                .position(
                    // 水平居中于屏幕
                    x: UIScreen.main.bounds.width / 2,
                    // 使用传入的Y坐标，确保不会太靠上
                    y: max(position.y, 100)
                )
                .transition(.move(edge: .bottom).combined(with: .opacity)))
        } else {
            // 使用默认居中显示
            return AnyView(VStack {
                Spacer()
                menuView
                Spacer()
            })
        }
    }
    
    // 生成当前状态对应的按钮组
    private func buttonGroupForTask(_ task: Task, onChange: @escaping (String) -> Void) -> some View {
        HStack(spacing: 16) {
            switch task.status {
            case TaskStatus.na.rawValue:
                statusButton(title: "一步", icon: "flag.checkered", color: .green) {
                    handleStatusChange(TaskStatus.doing.rawValue, checkFunction: taskRepository.canMoveToDoing, onChange: onChange)
                }
                
                statusButton(title: "等待中", icon: "hourglass", color: .orange) {
                    handleStatusChange(TaskStatus.waiting.rawValue, onChange: onChange)
                }
                
                statusButton(title: "将来也许", icon: "sparkles", color: .purple) {
                    handleStatusChange(TaskStatus.smb.rawValue, onChange: onChange)
                }
                
            case TaskStatus.waiting.rawValue:
                statusButton(title: "一步", icon: "flag.checkered", color: .green) {
                    handleStatusChange(TaskStatus.doing.rawValue, checkFunction: taskRepository.canMoveToDoing, onChange: onChange)
                }
                
                statusButton(title: "下一步", icon: "arrow.right", color: .blue) {
                    handleStatusChange(TaskStatus.na.rawValue, checkFunction: taskRepository.canMoveToNA, onChange: onChange)
                }
                
                statusButton(title: "将来也许", icon: "sparkles", color: .purple) {
                    handleStatusChange(TaskStatus.smb.rawValue, onChange: onChange)
                }
                
            case TaskStatus.inbox.rawValue:
                statusButton(title: "一步", icon: "flag.checkered", color: .green) {
                    handleStatusChange(TaskStatus.doing.rawValue, checkFunction: taskRepository.canMoveToDoing, onChange: onChange)
                }
                
                statusButton(title: "下一步", icon: "arrow.right", color: .blue) {
                    handleStatusChange(TaskStatus.na.rawValue, checkFunction: taskRepository.canMoveToNA, onChange: onChange)
                }
                
                statusButton(title: "等待中", icon: "hourglass", color: .orange) {
                    handleStatusChange(TaskStatus.waiting.rawValue, onChange: onChange)
                }
                
                statusButton(title: "将来也许", icon: "sparkles", color: .purple) {
                    handleStatusChange(TaskStatus.smb.rawValue, onChange: onChange)
                }
                
            case TaskStatus.doing.rawValue:
                statusButton(title: "下一步", icon: "arrow.right", color: .blue) {
                    handleStatusChange(TaskStatus.na.rawValue, checkFunction: taskRepository.canMoveToNA, onChange: onChange)
                }
                
                statusButton(title: "等待中", icon: "hourglass", color: .orange) {
                    handleStatusChange(TaskStatus.waiting.rawValue, onChange: onChange)
                }
                
                statusButton(title: "将来也许", icon: "sparkles", color: .purple) {
                    handleStatusChange(TaskStatus.smb.rawValue, onChange: onChange)
                }
                
            case TaskStatus.smb.rawValue:
                statusButton(title: "一步", icon: "flag.checkered", color: .green) {
                    handleStatusChange(TaskStatus.doing.rawValue, checkFunction: taskRepository.canMoveToDoing, onChange: onChange)
                }
                
                statusButton(title: "下一步", icon: "arrow.right", color: .blue) {
                    handleStatusChange(TaskStatus.na.rawValue, checkFunction: taskRepository.canMoveToNA, onChange: onChange)
                }
                
                statusButton(title: "等待中", icon: "hourglass", color: .orange) {
                    handleStatusChange(TaskStatus.waiting.rawValue, onChange: onChange)
                }
                
            default:
                Text("无选项")
                    .foregroundColor(.gray)
            }
        }
    }
    
    // 辅助方法：处理状态变更和检查
    private func handleStatusChange(_ newStatus: String, checkFunction: (() -> Bool)? = nil, onChange: @escaping (String) -> Void) {
        // 检查状态变更是否允许
        if let check = checkFunction {
            if !check() {
                // 显示适当的警告消息
                let message = newStatus == TaskStatus.na.rawValue ? 
                    "下一步行动数量已达上限" : 
                    "少即是多，先去完成现有的吧"
                toastManager.showWarning(message)
                return
            }
        }
        
        // 关闭菜单并执行状态变更
        ActionSheetManager.shared.hideActionSheet()
        onChange(newStatus)
    }
    
    // 带图标的状态按钮
    private func statusButton(title: String, icon: String, color: Color, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.system(size: 18))
                    .frame(width: 36, height: 36)
                    .background(color.opacity(0.1))
                    .clipShape(Circle())
                
                Text(title)
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(.primary)
            }
            .frame(width: 70)
        }
        .buttonStyle(BorderlessButtonStyle())
    }
}

// MARK: - 视图扩展
extension View {
    /// 在视图中添加全局操作菜单
    func withActionSheet() -> some View {
        self.modifier(WithActionSheetModifier())
    }
} 