import Foundation
#if canImport(UMCommon)
import UMCommon
#endif

/// 埋点服务，负责记录用户行为数据
/// 设计原则：简单直接，只记录事件发生的事实
class AnalyticsService {
    /// 单例实例
    static let shared = AnalyticsService()
    
    /// 私有初始化方法
    private init() {}
    
    /// 记录事件
    /// - Parameter eventName: 事件名称
    func trackEvent(_ eventName: String) {
        #if canImport(UMCommon)
        MobClick.event(eventName)
        #endif
        
        #if DEBUG
        print("[Analytics] 事件: \(eventName)")
        #endif
    }
}

// MARK: - 事件名称常量
extension AnalyticsService {
    struct EventNames {
        // 任务相关
        static let taskCreated = "task_created"
        static let taskCompleted = "task_completed"
        
        // 小任务相关
        static let subtaskCreated = "subtask_created"
        static let subtaskCompleted = "subtask_completed"
        
        // 功能使用
        static let searchUsed = "search_used"
        static let echoFeatureUsed = "echo_feature_used"
        static let focusModeUsed = "focus_mode_used"
        
        // 视图浏览
        static let projectViewEntered = "project_view_entered"
        static let tagViewEntered = "tag_view_entered"
        static let inboxViewEntered = "inbox_view_entered"
        static let nextViewEntered = "next_view_entered"
        static let waitingViewEntered = "waiting_view_entered"
        static let somedayViewEntered = "someday_view_entered"
        static let completedViewEntered = "completed_view_entered"
        static let workbenchViewEntered = "workbench_view_entered"
        static let backboardViewEntered = "backboard_view_entered"
    }
} 