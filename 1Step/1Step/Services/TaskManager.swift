import Foundation
import Combine
import SwiftUI

/// 任务管理器 - 负责处理所有任务操作
class TaskManager {
    // 单例实例
    static let shared = TaskManager(
        taskRepository: DependencyContainer.taskRepository(),
        projectRepository: DependencyContainer.projectRepository(),
        tagRepository: DependencyContainer.tagRepository()
    )
    
    // 依赖
    private let taskRepository: TaskRepository
    private let projectRepository: ProjectRepository
    private let tagRepository: TagRepository
    
    // 状态跟踪
    private var cancellables = Set<AnyCancellable>()
    
    // 初始化
    init(
        taskRepository: TaskRepository,
        projectRepository: ProjectRepository,
        tagRepository: TagRepository
    ) {
        self.taskRepository = taskRepository
        self.projectRepository = projectRepository
        self.tagRepository = tagRepository
        
        // 订阅任务相关事件
        setupEventSubscriptions()
    }
    
    // MARK: - 事件订阅设置
    
    private func setupEventSubscriptions() {
        
        // 确保之前的订阅被取消
        cancellables.removeAll()
        
        // 创建新的订阅
        EventBus.shared.events
            .filter { [weak self] event in
                self?.shouldHandleEvent(event) ?? false
            }
            .receive(on: DispatchQueue.main)
            .sink { [weak self] event in
                self?.handleEvent(event)
            }
            .store(in: &cancellables)
        
    }
    
    // MARK: - 事件处理
    
    /// 判断是否应该处理事件
    private func shouldHandleEvent(_ event: AppEvent) -> Bool {
        switch event {
        case .taskDeleted, .taskCompleted, .taskStatusChanged:
            return true
        default:
            return false
        }
    }
    
    /// 处理应用事件
    private func handleEvent(_ event: AppEvent) {
        switch event {
        case .taskDeleted(let task, let allowUndo) where !allowUndo:
            handleTaskDeleted(task)
        case .taskCompleted(let task, _):
            handleTaskCompleted(task)
        case .taskStatusChanged(let task, let oldStatus, let newStatus):
            handleTaskStatusChanged(task, oldStatus: oldStatus, newStatus: newStatus)
        default:
            break
        }
    }
    
    // MARK: - 任务操作处理
    
    /// 处理因事件总线触发的任务删除（通常是不可撤销的，或撤销已过期）
    ///
    /// 注意：此方法接收Task对象但仅使用其ID进行删除。这样可以避免SwiftData常见的"陈旧对象"问题，
    /// 即视图中持有的对象引用可能与SwiftData上下文中的对象状态不同步，直接删除可能导致EXC_BAD_ACCESS崩溃。
    /// 通过ID删除可以确保先获取最新的有效对象再执行删除操作。
    private func handleTaskDeleted(_ task: Task) { // 参数仍为 Task，因为事件总线可能传递 Task 对象
        let taskIdToDelete = task.id // 获取 ID
        // 使用主队列执行删除操作，确保与UI交互的同步性
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            // 直接尝试用 ID 删除，仓库内部会处理是否存在的问题
            self.taskRepository.deleteTask(withId: taskIdToDelete)
            print("[TaskManager] EventBus: Processed deletion for task ID: \(taskIdToDelete) and cleared focus memory.")
        }
    }
    
    /// 处理任务完成
    private func handleTaskCompleted(_ task: Task) {
        task.status = TaskStatus.done.rawValue
        task.completedAt = Date()
        taskRepository.updateTask(task)
        print("[TaskManager] EventBus: Processed completion for task ID: \(task.id) and cleared focus memory.")
    }
    
    /// 处理任务状态变更
    private func handleTaskStatusChanged(_ task: Task, oldStatus: String, newStatus: String) {
        task.status = newStatus
        task.updatedAt = Date()
        
        if newStatus == TaskStatus.done.rawValue {
            task.completedAt = Date()
        }
        
        taskRepository.updateTask(task) // updateTask 内部应该也是安全的
    }

    // MARK: - 公共接口，供 ViewModel 调用

    /// ViewModel 请求立即并永久删除一个任务。
    /// 如果需要可撤销的删除流程，ViewModel 应先处理UI（如Toast）和撤销逻辑，
    /// 待用户确认或撤销期过后，再调用此方法执行实际删除。
    public func requestPermanentTaskDeletion(taskID: UUID) {
        print("[TaskManager] ViewModel requested permanent deletion for task ID: \(taskID)")
        // 直接调用仓库进行删除。仓库的 deleteTask(withId:) 是安全的。
        self.taskRepository.deleteTask(withId: taskID)
        print("[TaskManager] ViewModel requested permanent deletion for task ID: \(taskID) and cleared focus memory.")
        
        // 可选：如果ViewModel发起的永久删除也需要通知其他系统（例如，用于清理缓存或更新统计）
        // EventBus.shared.post(event: .taskPermanentlyDeleted(id: taskID)) // 假设有这样的事件
    }

    // MARK: - 任务查询 (公开)

    /// 根据ID获取任务
    public func getTask(withId id: UUID) -> Task? {
        return taskRepository.getTaskById(id)
    }

    // MARK: - 项目管理
    
    /// 获取所有项目
    public func getAllProjects() -> [Project] {
        return projectRepository.getAllProjects()
    }
    
    /// 根据ID获取项目
    public func getProjectById(_ id: UUID) -> Project? {
        return projectRepository.getProjectById(id)
    }
    
    /// 根据名称获取项目
    public func getProjectByName(_ name: String) -> Project? {
        return projectRepository.getProjectByName(name)
    }
    
    /// 获取活跃项目（非归档）
    public func getActiveProjects() -> [Project] {
        return projectRepository.getAllProjects().filter { !$0.isArchived }
    }
    
    /// 创建新项目
    public func createProject(name: String, color: String) -> Project? {
        return projectRepository.createProject(name: name, color: color)
    }
    
    /// 更新项目
    public func updateProject(_ project: Project) {
        projectRepository.saveProject(project)
    }
    
    /// 删除项目
    public func deleteProject(_ project: Project) {
        projectRepository.deleteProject(project)
    }
    
    /// 切换项目归档状态
    public func toggleProjectArchiveStatus(_ project: Project) {
        project.isArchived.toggle()
        projectRepository.saveProject(project)
    }

    // MARK: - 标签管理
    
    /// 获取所有标签
    public func getAllTags() -> [Tag] {
        return tagRepository.getAllTags()
    }
    
    /// 根据名称获取标签
    public func getTagByName(_ name: String) -> Tag? {
        return tagRepository.getTagByName(name)
    }
    
    /// 创建新标签
    public func createTag(name: String, color: String) -> Tag? {
        return tagRepository.createTag(name: name, color: color)
    }
    
    /// 更新标签
    public func updateTag(_ tag: Tag) {
        tagRepository.saveTag(tag)
    }
    
    /// 删除标签
    public func deleteTag(_ tag: Tag) {
        tagRepository.deleteTag(tag)
    }
    
    /// 重命名标签
    public func renameTag(_ tag: Tag, newName: String) {
        let oldName = tag.name
        tag.name = newName
        tagRepository.saveTag(tag)
        
        // 更新所有使用该标签的任务
        let tasksToUpdate = taskRepository.getAllTasks().filter { $0.tags.contains(oldName) }
        for task in tasksToUpdate {
            if let index = task.tags.firstIndex(of: oldName) {
                var updatedTags = task.tags
                updatedTags[index] = newName
                task.tags = updatedTags
                taskRepository.updateTask(task)
            }
        }
    }

    // MARK: - 任务管理（补充）
    
    /// 获取所有任务
    public func getAllTasks() -> [Task] {
        return taskRepository.getAllTasks()
    }
    
    /// 根据ID获取任务
    public func getTaskById(_ id: UUID) -> Task? {
        return taskRepository.getTaskById(id)
    }
    
    /// 根据状态获取任务
    public func getTasksByStatus(_ status: String) -> [Task] {
        return taskRepository.getTasksByStatus(status)
    }
    
    /// 获取项目下的任务
    public func getTasksForProject(_ projectId: UUID, status: String? = nil) -> [Task] {
        return taskRepository.getTasksForProject(projectId, status: status)
    }
    
    /// 获取项目下的所有任务（不限状态）
    public func getTasksForProject(_ projectId: UUID) -> [Task] {
        return taskRepository.getTasksForProject(projectId)
    }
    
    /// 获取项目下的笔记
    public func getNotesForProject(_ projectId: UUID) -> [Task] {
        return taskRepository.getNotesForProject(projectId)
    }
    
    /// 更新任务
    public func updateTask(_ task: Task) {
        taskRepository.updateTask(task)
    }
    
    /// 切换任务完成状态
    public func toggleTaskCompletion(_ task: Task) {
        let currentStatus = TaskStatus(rawValue: task.status) ?? .inbox
        let newStatus = (currentStatus == .done) ? TaskStatus.doing : TaskStatus.done
        taskRepository.updateTaskStatus(task, newStatus: newStatus)
    }
    
    /// 更新任务标题
    public func updateTaskTitle(_ task: Task, title: String) {
        task.title = title
        taskRepository.updateTask(task)
    }
    
    /// 切换小行动完成状态
    public func toggleChecklistItemCompletion(_ task: Task, itemId: UUID) {
        taskRepository.toggleChecklistItemCompletion(task, itemId: itemId)
    }
    
    /// 更新小行动标题
    public func updateChecklistItemTitle(_ task: Task, itemId: UUID, newTitle: String) {
        taskRepository.updateChecklistItemTitle(task, itemId: itemId, newTitle: newTitle)
    }
    
    /// 切换子步骤完成状态
    public func toggleSubStepCompletion(_ task: Task, checklistItemId: UUID, subStepId: UUID) {
        if let checklist = task.checklist,
           let itemIndex = checklist.firstIndex(where: { $0.id == checklistItemId }) {
            toggleSubStepCompletionRecursive(in: &task.checklist![itemIndex].subStepsList, subStepId: subStepId)
            taskRepository.updateTask(task)
        }
    }
    
    /// 递归切换子步骤完成状态
    private func toggleSubStepCompletionRecursive(in subSteps: inout [SubStep], subStepId: UUID) -> Bool {
        for index in subSteps.indices {
            if subSteps[index].id == subStepId {
                subSteps[index].isCompleted.toggle()
                subSteps[index].completedAt = subSteps[index].isCompleted ? Date() : nil
                return true
            }
            
            if toggleSubStepCompletionRecursive(in: &subSteps[index].subSteps, subStepId: subStepId) {
                return true
            }
        }
        return false
    }
    
    /// 更新子步骤标题
    public func updateSubStepTitle(_ task: Task, checklistItemId: UUID, subStepId: UUID, title: String) {
        if let checklist = task.checklist,
           let itemIndex = checklist.firstIndex(where: { $0.id == checklistItemId }) {
            updateSubStepTitleRecursive(in: &task.checklist![itemIndex].subStepsList, subStepId: subStepId, title: title)
            taskRepository.updateTask(task)
        }
    }
    
    /// 递归更新子步骤标题
    private func updateSubStepTitleRecursive(in subSteps: inout [SubStep], subStepId: UUID, title: String) -> Bool {
        for index in subSteps.indices {
            if subSteps[index].id == subStepId {
                subSteps[index].title = title
                return true
            }
            
            if updateSubStepTitleRecursive(in: &subSteps[index].subSteps, subStepId: subStepId, title: title) {
                return true
            }
        }
        return false
    }
    
    /// 添加笔记
    public func addNote(title: String, content: String, project: UUID?, tags: [String] = [], isFocused: Bool = false) -> Task {
        return taskRepository.addNote(
            title: title,
            content: content,
            project: project,
            tags: tags,
            isFocused: isFocused
        )
    }
    
    /// 更新笔记标题
    public func updateNoteTitle(_ note: Task, newTitle: String) {
        taskRepository.updateNoteTitle(note, newTitle: newTitle)
    }
    
    /// 更新笔记内容
    public func updateNoteContent(_ note: Task, newContent: String) {
        taskRepository.updateNoteContent(note, newContent: newContent)
    }
    
    /// 更新笔记重点关注状态
    public func updateNoteFocusStatus(_ note: Task, isFocused: Bool) {
        taskRepository.updateNoteFocusStatus(note, isFocused: isFocused)
    }
    
    /// 将笔记转换为任务
    public func convertNoteToTask(_ note: Task) -> Task? {
        return taskRepository.convertNoteToTask(note)
    }
    
    /// 添加任务
    public func addTask(
        title: String,
        status: String,
        notes: String = "",
        project: UUID? = nil,
        tags: [String] = [],
        isFocused: Bool = false
    ) -> Task {
        let task = taskRepository.addTask(
            title: title,
            status: status,
            notes: notes,
            project: project,
            tags: tags
        )
        
        // 如果需要设置 isFocused，在创建后单独设置
        if isFocused {
            task.isFocused = isFocused
            taskRepository.updateTask(task)
        }
        
        return task
    }
    
    /// 更新任务状态
    public func updateTaskStatus(_ task: Task, newStatus: String) {
        task.status = newStatus
        task.updatedAt = Date()
        if newStatus == TaskStatus.done.rawValue {
            task.completedAt = Date()
        }
        taskRepository.updateTask(task)
    }
    
    /// 添加检查清单项到任务
    public func addChecklistItemToTask(_ task: Task, title: String, createdAt: Date) -> ChecklistItem? {
        return taskRepository.addChecklistItemToTask(task, title: title, createdAt: createdAt)
    }
    
    /// 从任务中移除检查清单项
    public func removeChecklistItemFromTask(_ task: Task, itemId: UUID) {
        taskRepository.removeChecklistItemFromTask(task, itemId: itemId)
    }
    
    /// 移除子步骤
    public func removeSubStep(_ task: Task, checklistItemId: UUID, subStepId: UUID) {
        if let checklist = task.checklist,
           let itemIndex = checklist.firstIndex(where: { $0.id == checklistItemId }) {
            removeSubStepRecursive(from: &task.checklist![itemIndex].subStepsList, subStepId: subStepId)
            taskRepository.updateTask(task)
        }
    }
    
    /// 递归移除子步骤
    private func removeSubStepRecursive(from subSteps: inout [SubStep], subStepId: UUID) -> Bool {
        // 在当前层级寻找
        if let index = subSteps.firstIndex(where: { $0.id == subStepId }) {
            subSteps.remove(at: index)
            return true
        }
        
        // 递归搜索更深层级
        for index in subSteps.indices {
            if removeSubStepRecursive(from: &subSteps[index].subSteps, subStepId: subStepId) {
                return true
            }
        }
        
        return false
    }
    
    /// 向小行动添加子步骤
    public func addSubStepToChecklistItem(_ task: Task, checklistItemId: UUID, title: String) -> SubStep? {
        if let checklist = task.checklist,
           let itemIndex = checklist.firstIndex(where: { $0.id == checklistItemId }) {
            let newSubStep = SubStep(title: title)
            task.checklist![itemIndex].subStepsList.append(newSubStep)
            taskRepository.updateTask(task)
            return newSubStep
        }
        return nil
    }
    
    /// 向子步骤添加子步骤
    public func addSubStepToSubStep(_ task: Task, checklistItemId: UUID, parentStepId: UUID, title: String) -> SubStep? {
        if let checklist = task.checklist,
           let itemIndex = checklist.firstIndex(where: { $0.id == checklistItemId }) {
            if let newSubStep = addSubStepRecursive(to: &task.checklist![itemIndex].subStepsList, parentStepId: parentStepId, title: title) {
                taskRepository.updateTask(task)
                return newSubStep
            }
        }
        return nil
    }
    
    /// 递归添加子步骤
    private func addSubStepRecursive(to subSteps: inout [SubStep], parentStepId: UUID, title: String) -> SubStep? {
        // 在当前层级寻找父步骤
        if let index = subSteps.firstIndex(where: { $0.id == parentStepId }) {
            let newSubStep = SubStep(title: title)
            subSteps[index].subSteps.append(newSubStep)
            return newSubStep
        }
        
        // 递归搜索更深层级
        for index in subSteps.indices {
            if let newSubStep = addSubStepRecursive(to: &subSteps[index].subSteps, parentStepId: parentStepId, title: title) {
                return newSubStep
            }
        }
        
        return nil
    }
    
    /// 保存任务
    public func saveTask(_ task: Task) {
        taskRepository.updateTask(task)
    }
    
    /// 保存更改
    public func save() throws {
        try taskRepository.save()
    }
}

// MARK: - 依赖容器扩展
extension DependencyContainer {
    static nonisolated func taskManager() -> TaskManager {
        MainActor.assumeIsolated {
            // 返回单例实例
            return TaskManager.shared
        }
    }
} 