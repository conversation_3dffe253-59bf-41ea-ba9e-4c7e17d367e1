import Foundation
import SwiftUI
#if canImport(UMCommon)
import UMCommon
#endif

/// 友盟服务管理类，负责友盟SDK的初始化和生命周期管理
/// 遵循工信部要求，仅在用户同意隐私政策后才初始化SDK
@MainActor
class UMengService: ObservableObject {
    /// 单例实例
    static let shared = UMengService()
    
    /// 隐私政策同意状态
    @AppStorage("hasAcceptedPrivacyPolicy") private var hasAcceptedPrivacyPolicy: Bool = false
    
    /// 是否已初始化
    @Published private var isInitialized: Bool = false
    
    /// 私有初始化方法
    private init() {}
    
    /// 初始化友盟SDK
    /// - Parameters:
    ///   - appKey: 友盟AppKey
    ///   - channel: 渠道标识，默认为"App Store"
    func initializeSDK(appKey: String, channel: String = "App Store") {
        guard hasAcceptedPrivacyPolicy && !isInitialized else {
            print("UMeng SDK未初始化: 用户未同意隐私政策或已初始化")
            return
        }
        
        #if canImport(UMCommon)
        UMConfigure.initWithAppkey(appKey, channel: channel)
        print("UMeng SDK初始化成功: AppKey=\(appKey), Channel=\(channel)")
        #else
        print("UMeng SDK未集成，无法初始化")
        #endif
        
        isInitialized = true
    }
    
    /// 用户同意隐私政策
    /// - Parameter appKey: 友盟AppKey
    func userAcceptedPrivacyPolicy(appKey: String) {
        hasAcceptedPrivacyPolicy = true
        // 初始化SDK
        initializeSDK(appKey: appKey)
    }
    
    /// 检查是否可以初始化SDK
    /// - Returns: 是否可以初始化
    func canInitializeSDK() -> Bool {
        return hasAcceptedPrivacyPolicy && !isInitialized
    }
    
    /// 获取初始化状态
    /// - Returns: 是否已初始化
    func isSDKInitialized() -> Bool {
        return isInitialized
    }
    
    /// 重置服务状态（用于测试）
    func reset() {
        isInitialized = false
    }
}

// MARK: - SwiftUI环境修饰器
extension View {
    /// 添加友盟服务到SwiftUI环境
    func withUMengService() -> some View {
        self.environmentObject(UMengService.shared)
    }
} 