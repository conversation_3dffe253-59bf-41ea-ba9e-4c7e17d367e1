import Foundation
import SwiftData
import UIKit
import UniformTypeIdentifiers

/// 备份服务 - 提供数据备份和恢复功能
class BackupService {
    // 单例模式
    static let shared = BackupService()
    
    // 私有初始化方法
    private init() {}
    
    // MARK: - Constants
    
    // 备份文件结构版本
    private let currentBackupVersion = 2 // 更新版本号以支持新模型
    
    // MARK: - Types
    
    // 备份元数据
    struct BackupMetadata: Codable {
        let version: Int
        let createdAt: Date
        let appVersion: String
        let deviceName: String
        let itemCounts: ItemCounts
    }
    
    // 各类型数据的计数
    struct ItemCounts: Codable {
        let tasks: Int
        let projects: Int
        let tags: Int
        let checklistItems: Int
        let thoughts: Int // 添加思绪计数
    }
    
    // 备份数据传输对象
    struct BackupData: Codable {
        let metadata: BackupMetadata
        let tasks: [TaskDTO]
        let projects: [ProjectDTO]
        let tags: [TagDTO]
        let thoughts: [ThoughtDTO]? // 添加思绪数据，使用可选类型保持向后兼容
    }
    
    // 任务DTO
    struct TaskDTO: Codable {
        let id: UUID
        let title: String
        let status: String
        let project: UUID?
        let tags: [String]
        let notes: String
        let createdAt: Date
        let updatedAt: Date
        let completedAt: Date?
        let isPinned: Bool
        let sortOrder: Int16
        let isDeleted: Bool
        let checklist: [ChecklistItemDTO]?
        
        init(from task: Task) {
            self.id = task.id
            self.title = task.title
            self.status = task.status
            self.project = task.project
            self.tags = task.tags
            self.notes = task.notes
            self.createdAt = task.createdAt
            self.updatedAt = task.updatedAt
            self.completedAt = task.completedAt
            self.isPinned = task.isPinned
            self.sortOrder = task.sortOrder
            self.isDeleted = task.isDeleted
            
            if let checklist = task.checklist, !checklist.isEmpty {
                self.checklist = checklist.map { ChecklistItemDTO(from: $0) }
            } else {
                self.checklist = nil
            }
        }
    }
    
    // 检查项DTO
    struct ChecklistItemDTO: Codable {
        let id: UUID
        let title: String
        let isCompleted: Bool
        let createdAt: Date
        let completedAt: Date?
        
        init(from item: ChecklistItem) {
            self.id = item.id
            self.title = item.title
            self.isCompleted = item.isCompleted
            self.createdAt = item.createdAt
            self.completedAt = item.completedAt
        }
    }
    
    // 项目DTO
    struct ProjectDTO: Codable {
        let id: UUID
        let name: String
        let emoji: String?
        let color: String?
        let createdAt: Date
        let updatedAt: Date
        let isArchived: Bool
        let notes: String
        let sortOrder: Int
        
        init(from project: Project) {
            self.id = project.id
            self.name = project.name
            self.emoji = project.emoji
            self.color = project.color
            self.createdAt = project.createdAt
            self.updatedAt = project.updatedAt
            self.isArchived = project.isArchived
            self.notes = project.notes
            self.sortOrder = project.sortOrder
        }
    }
    
    // 标签DTO
    struct TagDTO: Codable {
        let id: UUID
        let name: String
        let emoji: String?
        let color: String?
        let createdAt: Date
        let updatedAt: Date
        let usageCount: Int
        let lastUsed: Date?
        let sortOrder: Int
        
        init(from tag: Tag) {
            self.id = tag.id
            self.name = tag.name
            self.emoji = tag.emoji
            self.color = tag.color
            self.createdAt = tag.createdAt
            self.updatedAt = tag.updatedAt
            self.usageCount = tag.usageCount
            self.lastUsed = tag.lastUsed
            self.sortOrder = tag.sortOrder
        }
    }
    
    // 思绪DTO - 添加思绪数据传输对象
    struct ThoughtDTO: Codable {
        let id: UUID
        let content: String
        let createdAt: Date
        let relatedTaskId: UUID?
        let relatedProjectId: UUID?
        let isSystemMessage: Bool
        let type: String
        
        init(from thought: Thought) {
            self.id = thought.id
            self.content = thought.content
            self.createdAt = thought.createdAt
            self.relatedTaskId = thought.relatedTaskId
            self.relatedProjectId = thought.relatedProjectId
            self.isSystemMessage = thought.isSystemMessage
            self.type = thought.type
        }
    }
    
    // MARK: - Public Methods
    
    /// 创建备份数据
    /// - Parameter modelContext: SwiftData 上下文
    /// - Returns: 打包的备份数据
    func createBackup(modelContext: ModelContext) async throws -> Data {
        // 1. 从数据库获取所有数据
        let tasks = try await fetchAllTasks(modelContext)
        let projects = try await fetchAllProjects(modelContext)
        let tags = try await fetchAllTags(modelContext)
        let thoughts = try await fetchAllThoughts(modelContext) // 获取所有思绪记录
        
        // 2. 创建元数据
        let metadata = createMetadata(tasks: tasks, projects: projects, tags: tags, thoughts: thoughts)
        
        // 3. 转换为DTO并创建备份数据结构
        let taskDTOs = tasks.map { TaskDTO(from: $0) }
        let projectDTOs = projects.map { ProjectDTO(from: $0) }
        let tagDTOs = tags.map { TagDTO(from: $0) }
        let thoughtDTOs = thoughts.map { ThoughtDTO(from: $0) }
        
        let backupData = BackupData(
            metadata: metadata,
            tasks: taskDTOs,
            projects: projectDTOs,
            tags: tagDTOs,
            thoughts: thoughtDTOs
        )
        
        // 4. 编码为JSON数据
        return try JSONEncoder().encode(backupData)
    }
    
    /// 将备份文件保存到文档目录
    /// - Parameter backupData: 备份数据
    /// - Returns: 备份文件URL
    func saveBackupToDocuments(_ backupData: Data) throws -> URL {
        // 1. 获取文档目录
        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        
        // 2. 创建文件名（包含时间戳）
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd_HHmmss"
        let timestamp = dateFormatter.string(from: Date())
        let fileName = "1Step_Backup_\(timestamp).json"
        
        // 3. 创建完整的文件URL
        let fileURL = documentsDirectory.appendingPathComponent(fileName)
        
        // 4. 写入文件
        try backupData.write(to: fileURL)
        
        return fileURL
    }
    
    /// 从URL导入备份数据
    /// - Parameters:
    ///   - fileURL: 备份文件URL
    ///   - modelContext: SwiftData 上下文
    /// - Returns: 导入结果摘要
    func importBackup(from fileURL: URL, modelContext: ModelContext) async throws -> String {
        // 1. 读取备份文件
        let backupData = try Data(contentsOf: fileURL)
        
        // 2. 解码备份数据
        let decoder = JSONDecoder()
        let backup = try decoder.decode(BackupData.self, from: backupData)
        
        // 3. 验证版本兼容性
        guard backup.metadata.version <= currentBackupVersion else {
            throw BackupError.newerBackupVersion
        }
        
        // 4. 清除现有数据（可选步骤，根据需求决定）
        try await clearAllData(modelContext)
        
        // 5. 导入数据
        // 按特定顺序导入以维护引用完整性：先导入独立实体，再导入依赖实体
        try await importTags(backup.tags, modelContext)
        try await importProjects(backup.projects, modelContext)
        try await importTasks(backup.tasks, modelContext)
        
        // 导入思绪记录（如果存在）
        var thoughtCount = 0
        if let thoughts = backup.thoughts {
            try await importThoughts(thoughts, modelContext)
            thoughtCount = thoughts.count
        }
        
        // 6. 保存更改
        try modelContext.save()
        
        // 7. 构建导入摘要
        return """
        恢复完成！
        
        已导入:
        - 任务: \(backup.tasks.count)个
        - 项目: \(backup.projects.count)个
        - 标签: \(backup.tags.count)个
        - 检查项: \(backup.tasks.reduce(0) { $0 + ($1.checklist?.count ?? 0) })个
        - 思绪记录: \(thoughtCount)个
        
        备份创建于: \(formatDate(backup.metadata.createdAt))
        """
    }
    
    /// 分享备份文件
    /// - Parameters:
    ///   - fileURL: 备份文件URL
    ///   - presenter: 用于展示分享页面的UIViewController
    func shareBackup(_ fileURL: URL, from presenter: UIViewController) {
        let activityViewController = UIActivityViewController(
            activityItems: [fileURL], 
            applicationActivities: nil
        )
        
        // 在iPad上需要特殊处理
        if let popoverController = activityViewController.popoverPresentationController {
            popoverController.sourceView = presenter.view
            popoverController.sourceRect = CGRect(x: presenter.view.bounds.midX, y: presenter.view.bounds.midY, width: 0, height: 0)
            popoverController.permittedArrowDirections = []
        }
        
        presenter.present(activityViewController, animated: true)
    }
    
    /// 清除所有数据
    /// - Parameter modelContext: SwiftData 上下文
    func clearAllData(_ modelContext: ModelContext) async throws {
        // 清除思绪记录
        try await deleteAllEntities(of: Thought.self, in: modelContext)
        
        // 清除任务（包括关联的检查项）
        try await deleteAllEntities(of: Task.self, in: modelContext)
        
        // 清除项目
        try await deleteAllEntities(of: Project.self, in: modelContext)
        
        // 清除标签
        try await deleteAllEntities(of: Tag.self, in: modelContext)
        
        try modelContext.save()
    }
    
    // MARK: - Private Methods
    
    private func fetchAllTasks(_ modelContext: ModelContext) async throws -> [Task] {
        let descriptor = FetchDescriptor<Task>()
        return try modelContext.fetch(descriptor)
    }
    
    private func fetchAllProjects(_ modelContext: ModelContext) async throws -> [Project] {
        let descriptor = FetchDescriptor<Project>()
        return try modelContext.fetch(descriptor)
    }
    
    private func fetchAllTags(_ modelContext: ModelContext) async throws -> [Tag] {
        let descriptor = FetchDescriptor<Tag>()
        return try modelContext.fetch(descriptor)
    }
    
    private func fetchAllThoughts(_ modelContext: ModelContext) async throws -> [Thought] {
        let descriptor = FetchDescriptor<Thought>()
        return try modelContext.fetch(descriptor)
    }
    
    private func createMetadata(tasks: [Task], projects: [Project], tags: [Tag], thoughts: [Thought]) -> BackupMetadata {
        // 获取应用版本
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "未知版本"
        
        // 获取设备名称
        let deviceName = UIDevice.current.name
        
        // 计算检查项数量
        let checklistItemCount = tasks.reduce(0) { $0 + ($1.checklist?.count ?? 0) }
        
        return BackupMetadata(
            version: currentBackupVersion,
            createdAt: Date(),
            appVersion: appVersion,
            deviceName: deviceName,
            itemCounts: ItemCounts(
                tasks: tasks.count,
                projects: projects.count,
                tags: tags.count,
                checklistItems: checklistItemCount,
                thoughts: thoughts.count
            )
        )
    }
    
    private func deleteAllEntities<T: PersistentModel>(of type: T.Type, in modelContext: ModelContext) async throws {
        let descriptor = FetchDescriptor<T>()
        let entities = try modelContext.fetch(descriptor)
        
        for entity in entities {
            modelContext.delete(entity)
        }
    }
    
    private func importTags(_ tags: [TagDTO], _ modelContext: ModelContext) async throws {
        for tag in tags {
            let newTag = Tag(id: tag.id, name: tag.name, emoji: tag.emoji, color: tag.color)
            newTag.createdAt = tag.createdAt
            newTag.updatedAt = tag.updatedAt
            newTag.usageCount = tag.usageCount
            newTag.lastUsed = tag.lastUsed
            newTag.sortOrder = tag.sortOrder
            
            modelContext.insert(newTag)
        }
    }
    
    private func importProjects(_ projects: [ProjectDTO], _ modelContext: ModelContext) async throws {
        for project in projects {
            let newProject = Project(id: project.id, name: project.name, emoji: project.emoji, color: project.color)
            newProject.createdAt = project.createdAt
            newProject.updatedAt = project.updatedAt
            newProject.isArchived = project.isArchived
            newProject.notes = project.notes
            newProject.sortOrder = project.sortOrder
            
            modelContext.insert(newProject)
        }
    }
    
    private func importTasks(_ tasks: [TaskDTO], _ modelContext: ModelContext) async throws {
        for task in tasks {
            let newTask = Task(
                id: task.id,
                title: task.title,
                status: task.status,
                project: task.project,
                tags: task.tags,
                notes: task.notes,
                createdAt: task.createdAt,
                updatedAt: task.updatedAt,
                completedAt: task.completedAt,
                isPinned: task.isPinned,
                sortOrder: task.sortOrder,
                isDeleted: task.isDeleted
            )
            
            // 导入检查项
            if let checklist = task.checklist {
                newTask.checklist = []
                for item in checklist {
                    let newItem = ChecklistItem(
                        id: item.id,
                        title: item.title,
                        isCompleted: item.isCompleted,
                        createdAt: item.createdAt,
                        completedAt: item.completedAt
                    )
                    newTask.checklist?.append(newItem)
                }
            }
            
            modelContext.insert(newTask)
        }
    }
    
    // 添加思绪导入方法
    private func importThoughts(_ thoughts: [ThoughtDTO], _ modelContext: ModelContext) async throws {
        // 缓存已创建的任务和项目ID，避免多次查询
        var taskCache = [UUID: Task]()
        var projectCache = [UUID: Project]()
        
        for thoughtDTO in thoughts {
            let newThought = Thought(
                id: thoughtDTO.id,
                content: thoughtDTO.content,
                createdAt: thoughtDTO.createdAt,
                relatedTaskId: thoughtDTO.relatedTaskId,
                relatedProjectId: thoughtDTO.relatedProjectId,
                isSystemMessage: thoughtDTO.isSystemMessage,
                type: thoughtDTO.type
            )
            
            // 建立与任务的关联关系
            if let taskId = thoughtDTO.relatedTaskId {
                if let cachedTask = taskCache[taskId] {
                    newThought.task = cachedTask
                } else {
                    // 查找相关任务
                    let predicate = #Predicate<Task> { $0.id == taskId }
                    let descriptor = FetchDescriptor<Task>(predicate: predicate)
                    
                    if let task = try modelContext.fetch(descriptor).first {
                        newThought.task = task
                        taskCache[taskId] = task
                    }
                }
            }
            
            // 建立与项目的关联关系
            if let projectId = thoughtDTO.relatedProjectId {
                if let cachedProject = projectCache[projectId] {
                    newThought.project = cachedProject
                } else {
                    // 查找相关项目
                    let predicate = #Predicate<Project> { $0.id == projectId }
                    let descriptor = FetchDescriptor<Project>(predicate: predicate)
                    
                    if let project = try modelContext.fetch(descriptor).first {
                        newThought.project = project
                        projectCache[projectId] = project
                    }
                }
            }
            
            modelContext.insert(newThought)
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

// MARK: - Error Handling

extension BackupService {
    enum BackupError: Error, LocalizedError {
        case dataExtractionFailed
        case encodingFailed
        case decodingFailed
        case newerBackupVersion
        case importFailed
        
        var errorDescription: String? {
            switch self {
            case .dataExtractionFailed:
                return "无法提取数据库数据"
            case .encodingFailed:
                return "无法编码备份数据"
            case .decodingFailed:
                return "无法解码备份文件"
            case .newerBackupVersion:
                return "备份文件版本过新，请更新应用后重试"
            case .importFailed:
                return "导入数据失败"
            }
        }
    }
} 