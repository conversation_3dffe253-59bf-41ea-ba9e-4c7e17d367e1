import Foundation

// MARK: - Node Protocol

/// 统一的节点协议，支持Task、ChecklistItem、SubStep的统一操作
protocol Node {
    var id: UUID { get }
    var title: String { get set }
    var isCompleted: Bool { get set }
    var children: [Node] { get }
    var canHaveChildren: Bool { get }
}

// MARK: - Task Node Extension

extension Task: Node {
    var isCompleted: Bool {
        get { statusEnum == .done }
        set { 
            if newValue {
                status = TaskStatus.done.rawValue
                completedAt = Date()
            } else {
                status = TaskStatus.doing.rawValue
                completedAt = nil
            }
        }
    }
    
    var children: [Node] {
        return (checklist ?? []).map { $0 as Node }
    }
    
    var canHaveChildren: Bool { true }
}

// MARK: - ChecklistItem Node Extension

extension ChecklistItem: Node {
    var children: [Node] {
        return subStepsList.map { $0 as Node }
    }
    
    var canHaveChildren: Bool { true }
}

// MARK: - SubStep Node Extension

extension SubStep: Node {
    var children: [Node] {
        return subSteps.map { $0 as Node }
    }
    
    var canHaveChildren: Bool { true }
}

// MARK: - Node Utilities

extension Node {
    /// 递归查找指定ID的节点
    func findNode(withId id: UUID) -> Node? {
        if self.id == id {
            return self
        }
        
        for child in children {
            if let found = child.findNode(withId: id) {
                return found
            }
        }
        
        return nil
    }
    
    /// 获取所有子节点（平铺）
    var allChildren: [Node] {
        var result: [Node] = []
        
        func collectChildren(_ node: Node) {
            result.append(node)
            for child in node.children {
                collectChildren(child)
            }
        }
        
        for child in children {
            collectChildren(child)
        }
        
        return result
    }
    
    /// 计算完成进度
    var completionProgress: (completed: Int, total: Int) {
        let allNodes = [self] + allChildren
        let completed = allNodes.filter { $0.isCompleted }.count
        return (completed: completed, total: allNodes.count)
    }
} 