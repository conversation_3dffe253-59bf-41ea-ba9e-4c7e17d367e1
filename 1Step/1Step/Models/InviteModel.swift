import SwiftData
import Foundation

/// 邀请码状态枚举
enum InviteCodeStatus: String, Codable {
    case active     // 有效可用
    case used       // 已被使用
    case expired    // 已过期
}

/// 邀请码使用记录
struct InviteUseRecord: Codable, Hashable {
    var deviceId: String   // 使用者设备ID
    var usedAt: Date       // 使用时间
    
    init(deviceId: String, usedAt: Date = Date()) {
        self.deviceId = deviceId
        self.usedAt = usedAt
    }
}

/// 邀请码模型 - 使用SwiftData实体
@Model
class InviteCode {
    // 基本属性
    var id: UUID = UUID()
    var code: String = ""       // 添加默认值
    var creatorDeviceId: String = ""  // 添加默认值
    var createdAt: Date = Date()     // 添加默认值
    var status: String = InviteCodeStatus.active.rawValue      // 添加默认值
    var maxUses: Int = 1        // 添加默认值
    var currentUses: Int = 0    // 添加默认值
    
    // 使用记录 - 需要使用Transformable存储
    @Transient private var _usedByData: Data?
    var usedBy: [InviteUseRecord] {
        get {
            if let data = _usedByData,
               let decoded = try? JSONDecoder().decode([InviteUseRecord].self, from: data) {
                return decoded
            }
            return []
        }
        set {
            _usedByData = try? JSONEncoder().encode(newValue)
        }
    }
    
    // 构造方法
    init(
        id: UUID = UUID(),
        code: String,
        creatorDeviceId: String,
        createdAt: Date = Date(),
        status: InviteCodeStatus = .active,
        maxUses: Int = 1,
        currentUses: Int = 0,
        usedBy: [InviteUseRecord] = []
    ) {
        self.id = id
        self.code = code
        self.creatorDeviceId = creatorDeviceId
        self.createdAt = createdAt
        self.status = status.rawValue
        self.maxUses = maxUses
        self.currentUses = currentUses
        self.usedBy = usedBy
    }
    
    // 兼容旧属性的访问方法
    var usedByDeviceId: String? {
        return usedBy.first?.deviceId
    }
    
    var usedAt: Date? {
        return usedBy.first?.usedAt
    }
    
    // 检查是否还有剩余使用次数
    var hasRemainingUses: Bool {
        return status == InviteCodeStatus.active.rawValue && currentUses < maxUses
    }
    
    // 剩余使用次数
    var remainingUses: Int {
        return max(0, maxUses - currentUses)
    }
}

/// 设备邀请状态 - 使用UserDefaults存储
struct DeviceInviteStatus: Codable {
    var isVerified: Bool                // 设备是否已验证
    var deviceId: String                // 设备唯一标识符
    var usedInviteCode: String?         // 使用的邀请码
    var verifiedAt: Date?               // 验证时间
    var ownInviteCodes: [String]        // 用户创建的邀请码列表

    init(
        isVerified: Bool = false,
        deviceId: String = UUID().uuidString,
        usedInviteCode: String? = nil,
        verifiedAt: Date? = nil,
        ownInviteCodes: [String] = []
    ) {
        self.isVerified = isVerified
        self.deviceId = deviceId
        self.usedInviteCode = usedInviteCode
        self.verifiedAt = verifiedAt
        self.ownInviteCodes = ownInviteCodes
    }
} 