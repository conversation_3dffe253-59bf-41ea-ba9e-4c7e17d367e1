import Foundation

/// 单个任务的ActionFocus状态
struct ActionFocusState: Codable {
    let taskId: UUID
    let checklistItemId: UUID
    let subStepPath: [UUID]
    let timestamp: Date
    
    var isExpired: Bool {
        Date().timeIntervalSince(timestamp) > 7 * 24 * 60 * 60 // 7天过期
    }
}

/// 管理多个任务ActionFocus状态的容器
struct ActionFocusStateManager: Codable {
    private var states: [UUID: ActionFocusState] = [:]
    
    /// 保存任务的ActionFocus状态
    mutating func saveState(taskId: UUID, checklistItemId: UUID, subStepPath: [UUID]) {
        states[taskId] = ActionFocusState(
            taskId: taskId,
            checklistItemId: checklistItemId,
            subStepPath: subStepPath,
            timestamp: Date()
        )
        cleanExpiredStates()
    }
    
    /// 获取指定任务的ActionFocus状态
    func getState(for taskId: UUID) -> ActionFocusState? {
        guard let state = states[taskId], !state.isExpired else {
            return nil
        }
        return state
    }
    
    /// 移除指定任务的ActionFocus状态
    mutating func removeState(for taskId: UUID) {
        states.removeValue(forKey: taskId)
    }
    
    /// 清理过期的状态
    mutating func cleanExpiredStates() {
        states = states.filter { !$0.value.isExpired }
    }
    
    /// 获取所有有效状态的任务ID
    var validTaskIds: Set<UUID> {
        return Set(states.compactMap { $0.value.isExpired ? nil : $0.key })
    }
} 