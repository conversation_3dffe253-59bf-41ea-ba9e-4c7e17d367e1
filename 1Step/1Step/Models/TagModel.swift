import Foundation
import SwiftData

@Model
class Tag {
    // 基本信息
    var id: UUID = UUID()
    var name: String = "" // 添加默认值
    var emoji: String? // emoji图标
    var color: String? // 存储颜色的十六进制代码或名称
    
    // 元数据
    var createdAt: Date = Date()
    var updatedAt: Date = Date()
    
    // 使用统计 - 可选，用于智能排序
    var usageCount: Int = 0
    var lastUsed: Date?
    
    // 可选的附加信息
    var sortOrder: Int = 0 // 用于自定义排序
    
    init(id: UUID = UUID(), name: String, emoji: String? = nil, color: String? = nil) {
        self.id = id
        self.name = name
        self.emoji = emoji
        self.color = color
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}

// 扩展方法，用于获取标签下的任务
extension Tag {
    // 获取标签下的所有任务
    func tasks(modelContext: ModelContext) -> [Task] {
        // 先获取所有任务
        do {
            let allTasks = try modelContext.fetch(FetchDescriptor<Task>())
            // 在内存中过滤出包含当前标签的任务
            return allTasks.filter { $0.tags.contains(self.name) }
        } catch {
            print("获取标签任务失败: \(error)")
            return []
        }
    }
}
