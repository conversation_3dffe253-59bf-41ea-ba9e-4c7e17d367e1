import Foundation
import SwiftData

@Model
class Project {
    // 基本信息
    var id: UUID = UUID()
    var name: String = "" // 添加默认值
    var emoji: String? // emoji图标
    var color: String? // 存储颜色的十六进制代码或名称
    
    // 元数据
    var createdAt: Date = Date()
    var updatedAt: Date = Date()
    
    // 状态
    var isArchived: Bool = false
    
    // 可选的附加信息
    var notes: String = ""
    var sortOrder: Int = 0 // 用于自定义排序
    
    // 添加与思绪记录的关系
    @Relationship(deleteRule: .cascade)
    var thoughts: [Thought]? = []
    
    init(id: UUID = UUID(), name: String, emoji: String? = nil, color: String? = nil) {
        self.id = id
        self.name = name
        self.emoji = emoji
        self.color = color
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}

// 扩展方法，用于获取项目下的任务
extension Project {
    // 获取项目下的所有任务
    func tasks(modelContext: ModelContext) -> [Task] {
        
        // 使用字符串比较来避免类型问题
        // 这里不需要id的字符串形式
        // let projectIdString = self.id.uuidString
        
        do {
            // 先获取所有任务
            let allTasks = try modelContext.fetch(FetchDescriptor<Task>())
            
            // 在内存中过滤出属于当前项目的任务
            let projectTasks = allTasks.filter { task in
                if let taskProjectId = task.project {
                    return taskProjectId == self.id
                }
                return false
            }
            
            
            // 打印所有任务的项目ID，帮助调试
            for task in allTasks {
                if let taskProjectId = task.project {
                } else {
                }
            }
            
            return projectTasks
        } catch {
            print("获取任务失败: \(error)")
            return []
        }
    }
}
