import Foundation
import SwiftUI

// MARK: - 显示上下文

/// 节点的显示上下文，决定整体的显示模式
enum DisplayContext {
    case taskList       // 任务列表模式（未聚焦）
    case focusMode      // 一步模式（聚焦到任何节点）
}

// MARK: - 节点样式

/// 节点的样式级别，基于在聚焦层级中的相对位置
enum NodeStyle {
    case primary        // 主要节点（当前聚焦的节点，大勾选框、粗体）
    case secondary      // 次要节点（子节点，中等大小、target图标）
    case tertiary       // 三级节点（深层子节点，小巧精致、缩进显示）
}

// MARK: - 样式计算器

struct NodeStyleCalculator {
    
    /// 根据节点在聚焦路径中的位置计算样式
    static func calculateStyle(
        nodeId: UUID,
        focusPath: [UUID],
        context: DisplayContext
    ) -> NodeStyle {
        
        switch context {
        case .taskList:
            // 任务列表模式：所有任务都是primary样式
            return .primary
            
        case .focusMode:
            // 不管是什么类型的节点，只要是聚焦路径的最后一个（当前显示的根节点），都显示为primary
            if focusPath.last == nodeId {
                return .primary
            } else if focusPath.contains(nodeId) {
                return .secondary
            } else {
                return .tertiary
            }
        }
    }
    
    /// 根据节点层级深度计算样式
    static func calculateStyleByDepth(
        depth: Int,
        isInFocusPath: Bool,
        context: DisplayContext
    ) -> NodeStyle {
        
        switch context {
        case .taskList:
            return .primary
            
        case .focusMode:
            if isInFocusPath {
                return depth == 0 ? .primary : .secondary
            } else {
                return depth <= 1 ? .secondary : .tertiary
            }
        }
    }
}

// MARK: - 样式配置

extension NodeStyle {
    
    /// 勾选框大小
    var checkboxSize: CGFloat {
        switch self {
        case .primary: return 24
        case .secondary: return 20
        case .tertiary: return 16
        }
    }
    
    /// 字体大小
    var fontSize: CGFloat {
        switch self {
        case .primary: return 16
        case .secondary: return 15
        case .tertiary: return 14
        }
    }
    
    /// 字体粗细
    var fontWeight: Font.Weight {
        switch self {
        case .primary: return .medium
        case .secondary: return .regular
        case .tertiary: return .regular
        }
    }
    
    /// 左侧缩进
    var leadingPadding: CGFloat {
        switch self {
        case .primary: return 0
        case .secondary: return 10
        case .tertiary: return 20
        }
    }
    
    /// 行间距
    var spacing: CGFloat {
        switch self {
        case .primary: return 12
        case .secondary: return 10
        case .tertiary: return 8
        }
    }
    
    /// 操作图标大小
    var actionIconSize: CGFloat {
        switch self {
        case .primary: return 20
        case .secondary: return 18
        case .tertiary: return 16
        }
    }
}

// MARK: - 显示上下文配置

extension DisplayContext {
    
    /// 是否显示面包屑
    var showsBreadcrumb: Bool {
        switch self {
        case .taskList: return false
        case .focusMode: return true
        }
    }
    
    /// 默认展开状态
    var defaultExpanded: Bool {
        switch self {
        case .taskList: return false
        case .focusMode: return true
        }
    }
    
    /// 背景模糊效果
    var backgroundBlur: Bool {
        switch self {
        case .taskList: return false
        case .focusMode: return true
        }
    }
} 