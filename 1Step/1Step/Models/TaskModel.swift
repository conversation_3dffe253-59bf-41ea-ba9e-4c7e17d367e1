import Foundation
import SwiftData
import SwiftUI

// MARK: - SubStep 数据结构 (用于 ActionFocus 功能)

/// 子步骤数据结构，支持无限层级嵌套
struct SubStep: Codable, Identifiable, Hashable {
    let id: UUID
    var title: String
    var isCompleted: Bool
    var subSteps: [SubStep]
    var createdAt: Date
    var completedAt: Date?
    
    init(title: String) {
        self.id = UUID()
        self.title = title
        self.isCompleted = false
        self.subSteps = []
        self.createdAt = Date()
        self.completedAt = nil
    }
    
    // 便利属性
    var hasSubSteps: Bool { 
        !subSteps.isEmpty 
    }
    
    var incompleteCount: Int { 
        subSteps.filter { !$0.isCompleted }.count 
    }
    
    var totalSubStepsCount: Int {
        subSteps.count + subSteps.reduce(0) { $0 + $1.totalSubStepsCount }
    }
    
    var completedSubStepsCount: Int {
        subSteps.filter { $0.isCompleted }.count + 
        subSteps.reduce(0) { $0 + $1.completedSubStepsCount }
    }
    
    // 完成状态切换
    mutating func toggleCompletion() {
        isCompleted.toggle()
        completedAt = isCompleted ? Date() : nil
    }
    
    // 添加子步骤
    mutating func addSubStep(_ step: SubStep) {
        subSteps.append(step)
    }
    
    // 移除子步骤
    mutating func removeSubStep(withId id: UUID) {
        subSteps.removeAll { $0.id == id }
    }
}

// MARK: - TaskStatus 枚举

enum TaskStatus: String, CaseIterable, Identifiable {
    case inbox = "Inbox"
    case na = "NA"
    case doing = "Doing"
    case waiting = "Waiting"
    case smb = "SMB"
    case done = "Done"
    
    var id: String { self.rawValue }
    
    var description: String {
        switch self {
        case .inbox: return "收集箱"
        case .na: return "下一步"
        case .doing: return "一步"
        case .waiting: return "等待中"
        case .smb: return "将来也许"
        case .done: return "已完成"
        }
    }
    
    // 使用动态颜色，会根据当前环境的 colorScheme 自动调整
    var statusColor: Color {
        // 获取当前的 colorScheme
        let colorScheme = UITraitCollection.current.userInterfaceStyle == .dark ? ColorScheme.dark : ColorScheme.light
        return color(for: colorScheme)
    }
}

@Model
class Task: Codable {
    enum CodingKeys: String, CodingKey {
        case id, title, status, project, tags, notes, createdAt, updatedAt, completedAt, isPinned, sortOrder, isDeleted, isNote, isFocused, noteTitle
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(title, forKey: .title)
        try container.encode(status, forKey: .status)
        try container.encode(project, forKey: .project)
        try container.encode(tagsString, forKey: .tags)
        try container.encode(notes, forKey: .notes)
        try container.encode(createdAt, forKey: .createdAt)
        try container.encode(updatedAt, forKey: .updatedAt)
        try container.encodeIfPresent(completedAt, forKey: .completedAt)
        try container.encode(isPinned, forKey: .isPinned)
        try container.encode(sortOrder, forKey: .sortOrder)
        try container.encode(isDeleted, forKey: .isDeleted)
        try container.encode(isNote, forKey: .isNote)
        try container.encode(isFocused, forKey: .isFocused)
        try container.encode(noteTitle, forKey: .noteTitle)
    }
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        title = try container.decode(String.self, forKey: .title)
        status = try container.decode(String.self, forKey: .status)
        project = try container.decodeIfPresent(UUID.self, forKey: .project)
        let tagsStr = try container.decode(String.self, forKey: .tags)
        _tagsString = tagsStr
        notes = try container.decode(String.self, forKey: .notes)
        createdAt = try container.decode(Date.self, forKey: .createdAt)
        updatedAt = try container.decode(Date.self, forKey: .updatedAt)
        completedAt = try container.decodeIfPresent(Date.self, forKey: .completedAt)
        isPinned = try container.decode(Bool.self, forKey: .isPinned)
        sortOrder = try container.decode(Int16.self, forKey: .sortOrder)
        isDeleted = try container.decode(Bool.self, forKey: .isDeleted)
        isNote = try container.decodeIfPresent(Bool.self, forKey: .isNote) ?? false
        isFocused = try container.decodeIfPresent(Bool.self, forKey: .isFocused) ?? false
        noteTitle = try container.decodeIfPresent(String.self, forKey: .noteTitle) ?? ""
    }
    var id: UUID = UUID()
    var title: String = ""
    var status: String = TaskStatus.inbox.rawValue
    var project: UUID?
    
    // 使用字符串存储标签，以逗号分隔
    @Attribute var _tagsString: String = ""
    
    // 计算属性，提供数组接口
    var tags: [String] {
        get {
            if _tagsString.isEmpty {
                return []
            }
            return _tagsString.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
        }
        set {
            _tagsString = newValue.joined(separator: ",")
        }
    }
    
    // 为编码提供原始字符串
    var tagsString: String {
        return _tagsString
    }
    
    // 在Task中保留关系定义，但设为可选
    @Relationship(deleteRule: .cascade)
    var checklist: [ChecklistItem]? = []
    
    // 添加与思绪记录的关系
    @Relationship(deleteRule: .cascade)
    var thoughts: [Thought]? = []
    
    var notes: String = ""
    var createdAt: Date = Date()
    var updatedAt: Date = Date()
    var completedAt: Date? = nil
    var isPinned: Bool = false
    var sortOrder: Int16 = 0
    var isDeleted: Bool = false
    
    // 笔记相关扩展
    var isNote: Bool = false
    var isFocused: Bool = false
    var noteTitle: String = ""
    
    init(id: UUID = UUID(), title: String, status: String, project: UUID? = nil, tags: [String] = [], notes: String = "", createdAt: Date = Date(), updatedAt: Date = Date(), completedAt: Date? = nil, isPinned: Bool = false, sortOrder: Int16 = 0, isDeleted: Bool = false, isNote: Bool = false, isFocused: Bool = false, noteTitle: String = "") {
        self.id = id
        self.title = title
        self.status = status
        self.project = project
        self.tags = tags
        self.notes = notes
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.completedAt = completedAt
        self.isPinned = isPinned
        self.sortOrder = sortOrder
        self.isDeleted = isDeleted
        self.isNote = isNote
        self.isFocused = isFocused
        self.noteTitle = noteTitle
    }
    
    var statusEnum: TaskStatus {
        return TaskStatus(rawValue: status) ?? .inbox
    }
    
    var incompleteChecklistCount: Int {
        return checklist?.filter { !$0.isCompleted }.count ?? 0
    }
    
    var hasChecklist: Bool {
        return checklist?.isEmpty == false
    }
    
    var hasNotes: Bool {
        return !notes.isEmpty
    }
    
    // 笔记相关方法
    
    /// 从文本内容中提取笔记标题
    func extractNoteTitle() -> String {
        if !noteTitle.isEmpty {
            return noteTitle
        }
        
        // 如果没有设置标题，尝试从笔记内容的第一行获取
        let firstLine = notes.split(separator: "\n").first?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        return firstLine.isEmpty ? "无标题笔记" : firstLine
    }
    
    /// 将笔记转换为普通任务
    func convertToTask() -> Task {
        let task = Task(
            title: self.extractNoteTitle(),
            status: TaskStatus.inbox.rawValue,
            project: self.project,
            tags: self.tags,
            notes: self.notes
        )
        return task
    }
    
    /// 创建笔记实例
    static func createNote(title: String, content: String, project: UUID? = nil, tags: [String] = [], isFocused: Bool = false) -> Task {
        let note = Task(
            title: "",
            status: TaskStatus.inbox.rawValue, // 使用收集箱状态，但会被isNote标记区分
            project: project,
            tags: tags,
            notes: content,
            isNote: true,
            isFocused: isFocused,
            noteTitle: title
        )
        return note
    }
}

@Model
class ChecklistItem: Codable, Identifiable {
    enum CodingKeys: String, CodingKey {
        case id, title, isCompleted, createdAt, completedAt, subSteps
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(title, forKey: .title)
        try container.encode(isCompleted, forKey: .isCompleted)
        try container.encode(createdAt, forKey: .createdAt)
        try container.encodeIfPresent(completedAt, forKey: .completedAt)
        try container.encode(subSteps, forKey: .subSteps)
    }
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        title = try container.decode(String.self, forKey: .title)
        isCompleted = try container.decode(Bool.self, forKey: .isCompleted)
        createdAt = try container.decodeIfPresent(Date.self, forKey: .createdAt) ?? Date()
        completedAt = try container.decodeIfPresent(Date.self, forKey: .completedAt)
        subSteps = try container.decodeIfPresent(String.self, forKey: .subSteps) ?? ""
    }
    
    var id: UUID = UUID()
    var title: String = ""
    var isCompleted: Bool = false
    var createdAt: Date = Date()
    var completedAt: Date? = nil
    
    // 新增：子步骤 JSON 字段
    var subSteps: String = ""
    
    // 在ChecklistItem中定义反向关系
    var task: Task?
    
    init(id: UUID = UUID(), title: String, isCompleted: Bool = false, createdAt: Date = Date(), completedAt: Date? = nil, subSteps: String = "") {
        self.id = id
        self.title = title
        self.isCompleted = isCompleted
        self.createdAt = createdAt
        self.completedAt = completedAt
        self.subSteps = subSteps
    }
}

// MARK: - ChecklistItem 扩展 (ActionFocus 功能)

extension ChecklistItem {
    /// 解析和设置子步骤列表
    var subStepsList: [SubStep] {
        get {
            guard !subSteps.isEmpty,
                  let data = subSteps.data(using: .utf8) else { 
                return [] 
            }
            
            do {
                return try JSONDecoder().decode([SubStep].self, from: data)
            } catch {
                print("⚠️ ChecklistItem.subStepsList: JSON 解析失败 - \(error)")
                return []
            }
        }
        set {
            do {
                let data = try JSONEncoder().encode(newValue)
                if let jsonString = String(data: data, encoding: .utf8) {
                    subSteps = jsonString
                } else {
                    print("⚠️ ChecklistItem.subStepsList: JSON 编码为字符串失败")
                    subSteps = ""
                }
            } catch {
                print("⚠️ ChecklistItem.subStepsList: JSON 编码失败 - \(error)")
                subSteps = ""
            }
        }
    }
    
    /// 是否有子步骤
    var hasSubSteps: Bool {
        !subStepsList.isEmpty
    }
    
    /// 未完成的子步骤数量
    var incompleteSubStepsCount: Int {
        subStepsList.filter { !$0.isCompleted }.count
    }
    
    /// 总子步骤数量（包括嵌套）
    var totalSubStepsCount: Int {
        subStepsList.reduce(0) { $0 + 1 + $1.totalSubStepsCount }
    }
    
    /// 已完成的子步骤数量（包括嵌套）
    var completedSubStepsCount: Int {
        subStepsList.reduce(0) { $0 + ($1.isCompleted ? 1 : 0) + $1.completedSubStepsCount }
    }
    
    /// 添加子步骤
    func addSubStep(_ step: SubStep) {
        var steps = subStepsList
        steps.append(step)
        subStepsList = steps
    }
    
    /// 移除子步骤
    func removeSubStep(withId id: UUID) {
        var steps = subStepsList
        steps.removeAll { $0.id == id }
        subStepsList = steps
    }
    
    /// 切换子步骤完成状态
    func toggleSubStepCompletion(withId id: UUID) {
        var steps = subStepsList
        if let index = steps.firstIndex(where: { $0.id == id }) {
            steps[index].toggleCompletion()
            subStepsList = steps
        }
    }
    
    /// 更新子步骤标题
    func updateSubStepTitle(withId id: UUID, newTitle: String) {
        var steps = subStepsList
        if let index = steps.firstIndex(where: { $0.id == id }) {
            steps[index].title = newTitle
            subStepsList = steps
        }
    }
    
    /// 更新指定路径的子步骤标题
    func updateSubStepTitle(at stepId: UUID, to newTitle: String) {
        var steps = subStepsList
        updateSubStepTitleInSteps(&steps, stepId: stepId, newTitle: newTitle)
        subStepsList = steps
    }
    
    /// 在指定路径添加子步骤
    func addSubStep(at path: [UUID], step: SubStep) {
        var steps = subStepsList
        addSubStepToPath(&steps, path: path, step: step)
        subStepsList = steps
    }
    
    /// 在指定路径切换子步骤完成状态
    func toggleSubStepCompletion(at path: [UUID]) {
        var steps = subStepsList
        toggleSubStepCompletionAtPath(&steps, path: path)
        subStepsList = steps
    }
    
    /// 在指定路径删除子步骤
    func removeSubStep(at path: [UUID], stepId: UUID) {
        var steps = subStepsList
        removeSubStepAtPath(&steps, path: path, stepId: stepId)
        subStepsList = steps
    }
    
    // MARK: - 私有辅助方法
    
    private func updateSubStepTitleInSteps(_ steps: inout [SubStep], stepId: UUID, newTitle: String) {
        for index in steps.indices {
            if steps[index].id == stepId {
                steps[index].title = newTitle
                return
            }
            // 递归搜索子步骤
            updateSubStepTitleInSteps(&steps[index].subSteps, stepId: stepId, newTitle: newTitle)
        }
    }
    
    private func addSubStepToPath(_ steps: inout [SubStep], path: [UUID], step: SubStep) {
        guard !path.isEmpty else {
            steps.append(step)
            return
        }
        
        let targetId = path[0]
        let remainingPath = Array(path.dropFirst())
        
        if let index = steps.firstIndex(where: { $0.id == targetId }) {
            if remainingPath.isEmpty {
                steps[index].addSubStep(step)
            } else {
                addSubStepToPath(&steps[index].subSteps, path: remainingPath, step: step)
            }
        }
    }
    
    private func toggleSubStepCompletionAtPath(_ steps: inout [SubStep], path: [UUID]) {
        guard !path.isEmpty else { return }
        
        let targetId = path[0]
        let remainingPath = Array(path.dropFirst())
        
        if let index = steps.firstIndex(where: { $0.id == targetId }) {
            if remainingPath.isEmpty {
                steps[index].toggleCompletion()
            } else {
                toggleSubStepCompletionAtPath(&steps[index].subSteps, path: remainingPath)
            }
        }
    }
    
    private func removeSubStepAtPath(_ steps: inout [SubStep], path: [UUID], stepId: UUID) {
        guard !path.isEmpty else {
            steps.removeAll { $0.id == stepId }
            return
        }
        
        let targetId = path[0]
        let remainingPath = Array(path.dropFirst())
        
        if let index = steps.firstIndex(where: { $0.id == targetId }) {
            var targetSteps = steps[index].subSteps
            removeSubStepAtPath(&targetSteps, path: remainingPath, stepId: stepId)
            steps[index].subSteps = targetSteps
        }
    }
}