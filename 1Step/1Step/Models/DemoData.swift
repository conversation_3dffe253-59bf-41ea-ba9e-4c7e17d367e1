import Foundation
import SwiftData

struct DemoData {
    // 添加演示任务
    static func addDemoTasks(modelContext: ModelContext, forceLoad: Bool = false) {
        let taskDescriptor = FetchDescriptor<Task>()
        let projectDescriptor = FetchDescriptor<Project>()
        do {
            let existingTasks = try modelContext.fetch(taskDescriptor)
            let existingProjects = try modelContext.fetch(projectDescriptor)
            if !forceLoad && !existingTasks.isEmpty && !existingProjects.isEmpty {
                return
            }
            // 创建唯一的"1Step入门"项目
            let project = Project(
                name: "1Step入门",
                emoji: nil,
                color: "#8B5CF6"
            )
            modelContext.insert(project)
            // 生成所有演示任务
            let tasks = createDemoTasks(project: project)
            tasks.forEach { modelContext.insert($0) }
            try modelContext.save()
        } catch {
            print("获取现有数据失败: \(error)")
        }
    }

    // 创建演示任务（只生成1Step入门项目的任务，分布于各状态）
    private static func createDemoTasks(project: Project) -> [Task] {
        var allTasks = [Task]()
        let projectId = project.id
        // 一步（doing）
        allTasks.append(contentsOf: [
            createTask(
                title: "'下一步'行动在哪？点右上角 ☰ 探索管理空间",
                status: TaskStatus.doing.rawValue,
                project: projectId,
                notes: "你的收集箱、所有待选的'下一步'行动、那些'等待中'或'将来也许'的可能，以及项目、标签等，都统一放在了由右上角 ☰ 菜单进入的'管理中心'。去探索一下吧！"
            ),
            createTask(
                title: "大海捞针？试试左上角的 🔍 搜索功能",
                status: TaskStatus.doing.rawValue,
                project: projectId,
                notes: "随着行动增多，如何快速找到目标？利用左上角的 🔍 搜索功能！无论是行动的标题、备注内容，还是项目、标签名称，输入关键词都能帮你精准定位。"
            ),
            createTask(
                title: "深度专注：先左滑我，然后点 🎯 图标进入'一步模式'",
                status: TaskStatus.doing.rawValue,
                project: projectId,
                notes: "当你需要沉浸处理一个行动时，'一步模式'能帮你排除干扰，只聚焦于此。试试左滑这个任务，然后点击出现的 🎯 图标，进入它的专属专注空间吧！",
                checklist: [
                    "关掉键盘，点击底部的‘返回’按钮退出此模式",
                    "左滑，能看到退出入口",
                    "点击我右侧的 ◎",
                    "在下方输入框添加一个新的小行动",
                    "观察界面变化 (进入一步模式后)",
                    "完成这个引导小行动"
                ]
            ),
            createTask(
                title: "点这个行动右下角的展开图标",
                status: TaskStatus.doing.rawValue,
                project: projectId,
                notes: "每个行动都可以承载更多。点标题查看详情。试试展开小行动，或滑动卡片发现更多操作。",
                checklist: [
                    "左滑，点击移动，把我移动到下一步",
                    "尝试向右滑动这个卡片，也有两个操作",
                    "尝试向左滑动这个卡片，看到完成入口了",
                    "点击上面的任务标题打开详情页",
                    "再次点击 ∧ 图标收起列表",
                ]
            ),
            createTask(
                title: "轻点底部 +添加你的行动 (长按有惊喜)",
                status: TaskStatus.doing.rawValue,
                project: projectId,
                notes: "随时用底部按钮捕捉想法或下一步行动，默认会添加到当前的'一步'列表。另外，长按它试试，可以快速'回声'释放那些无需记录的念头！"
            ),
            createTask(
                title: "欢迎！→ 点左圈完成我，开启1Step之旅 👋",
                status: TaskStatus.doing.rawValue,
                project: projectId,
                notes: "恭喜迈出了第一步！在1Step，完成行动就是这么简单自然。试试向右滑动这个任务条，或者直接点击左边的圆圈来完成我吧。"
            ),




        ])
        // 收集箱（inbox）
        allTasks.append(contentsOf: [
            createTask(
                title: "左滑，完成我，然后进入下一步列表",
                status: TaskStatus.inbox.rawValue,
                project: projectId,
                notes: "这里是你的全局收集箱。任何灵感、任务、念头...都可以先快速记录在此，无需分类。记得稍后从'管理中心'(右上角☰)找到我，再决定我的去向吧！"
            ),
            createTask(
                title: "想法暂存站：左滑，把我删除吧📥",
                status: TaskStatus.inbox.rawValue,
                project: projectId,
                notes: "这里是你的全局收集箱。任何灵感、任务、念头...都可以先快速记录在此，无需分类。记得稍后从'管理中心'(右上角☰)找到我，再决定我的去向吧！"
            ),
            createTask(
                title: "周末整理照片？右滑，安排我到【下一步】",
                status: TaskStatus.inbox.rawValue,
                project: projectId,
                notes: "像这样不确定、未细化的念头，是收集箱的常客。处理时想清楚再分配状态哦。"
            )
        ])
        // 下一步（na/nextStep）
        allTasks.append(contentsOf: [
            createTask(
                title: "我在'下一步'等你，准备好激活我了吗？🚀",
                status: TaskStatus.na.rawValue,
                project: projectId,
                notes: "'下一步'是你的行动候选池。当你准备好处理我时，可以在这里(管理中心内)右滑，选择【安排】【一步】，让我出现在主界面吧！"
            ),
            createTask(
                title: "探索行动状态：了解'等待中'与'将来也许'",
                status: TaskStatus.na.rawValue,
                project: projectId,
                notes: "行动并非只有'做'或'不做'。在'管理中心'(右上角☰)看看这两个特殊的状态，它们能帮你更好地管理行动节奏，减轻焦虑。"
            ),
            createTask(
                title: "组织你的行动：试试'项目'与'标签'",
                status: TaskStatus.na.rawValue,
                project: projectId,
                notes: "想把相关的行动归类？可以创建'项目'(行动的容器)和'标签'(你的关键词)。入口同样在'管理中心'(右上角☰)。它们是可选的哦！"
            ),
            createTask(
                title: "记录思考过程？留意【行动台】与【项目背景板】",
                status: TaskStatus.na.rawValue,
                project: projectId,
                notes: "1Step不仅关心你做什么，也关心你怎么想。对于复杂行动或项目，它们的详情页里可能有【行动台】或【背景板】入口，用来记录过程中的想法和进展。"
            )
        ])
        // 等待中（waiting）
        allTasks.append(
            createTask(
                title: "我正在'等待中'...时机未到，安心搁置 ⏳",
                status: TaskStatus.waiting.rawValue,
                project: projectId,
                notes: "当你主动将行动移到这里，就代表'现在不适合推进'。没有压力，等条件成熟或你想继续时，再把我移回【下一步】或【一步】。"
            )
        )
        // 将来也许（smb/somedayMaybe）
        allTasks.append(
            createTask(
                title: "一个遥远的火花：也许将来学画画？🎨",
                status: TaskStatus.smb.rawValue,
                project: projectId,
                notes: "这里是安放不确定、不紧急或遥远梦想的地方。完全没有压力，可以随时回顾，也可以让它们静静沉睡。"
            )
        )
        // 已完成（done）
        allTasks.append(
            createTask(
                title: "已完成印记：你走过的每一步都在这里",
                status: TaskStatus.done.rawValue,
                project: projectId,
                notes: "所有完成的行动会汇集于此。你还可以恢复最近一个小时内完成的行动",
                completedAt: Date().addingTimeInterval(-60*60*24)
            )
        )
        return allTasks
    }

    // 辅助方法：创建任务并添加小行动
    private static func createTask(
        title: String,
        status: String,
        project: UUID? = nil,
        notes: String = "",
        checklist: [String] = [],
        completedAt: Date? = nil
    ) -> Task {
        let task = Task(
            title: title,
            status: status,
            project: project,
            tags: [],
            notes: notes,
            completedAt: completedAt
        )
        for item in checklist {
            let checklistItem = ChecklistItem(
                title: item,
                isCompleted: status == TaskStatus.done.rawValue
            )
            if status == TaskStatus.done.rawValue {
                checklistItem.completedAt = completedAt
            }
            if task.checklist == nil {
                task.checklist = []
            }
            task.checklist?.append(checklistItem)
        }
        return task
    }
}
