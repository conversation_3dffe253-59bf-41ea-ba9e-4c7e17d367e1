import Foundation
import SwiftData
import SwiftUI

/// 思绪类型枚举
enum ThoughtType: String, CaseIterable, Identifiable {
    case workbench = "Workbench" // 行动台
    case backboard = "Backboard" // 背景板
    
    var id: String { self.rawValue }
    
    var description: String {
        switch self {
        case .workbench: return "行动台"
        case .backboard: return "背景板"
        }
    }
}

/// 思绪记录模型 - 用于行动台和背景板
@Model
class Thought: Codable {
    enum CodingKeys: String, CodingKey {
        case id, content, createdAt, relatedTaskId, relatedProjectId, isSystemMessage, type
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(content, forKey: .content)
        try container.encode(createdAt, forKey: .createdAt)
        try container.encodeIfPresent(relatedTaskId, forKey: .relatedTaskId)
        try container.encodeIfPresent(relatedProjectId, forKey: .relatedProjectId)
        try container.encode(isSystemMessage, forKey: .isSystemMessage)
        try container.encode(type, forKey: .type)
    }
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        content = try container.decode(String.self, forKey: .content)
        createdAt = try container.decode(Date.self, forKey: .createdAt)
        relatedTaskId = try container.decodeIfPresent(UUID.self, forKey: .relatedTaskId)
        relatedProjectId = try container.decodeIfPresent(UUID.self, forKey: .relatedProjectId)
        isSystemMessage = try container.decode(Bool.self, forKey: .isSystemMessage)
        type = try container.decode(String.self, forKey: .type)
    }
    
    var id: UUID = UUID()
    var content: String = ""
    var createdAt: Date = Date()
    
    // 关联ID - 二选一
    var relatedTaskId: UUID? = nil // 关联到行动台
    var relatedProjectId: UUID? = nil // 关联到背景板
    
    // 背景板专用字段
    var isSystemMessage: Bool = false // 标识是否为系统消息
    
    // 类型区分
    var type: String = ThoughtType.workbench.rawValue
    
    // 关系定义
    @Relationship(deleteRule: .cascade, inverse: \Task.thoughts)
    var task: Task?
    
    @Relationship(deleteRule: .cascade, inverse: \Project.thoughts)
    var project: Project?
    
    init(
        id: UUID = UUID(),
        content: String,
        createdAt: Date = Date(),
        relatedTaskId: UUID? = nil,
        relatedProjectId: UUID? = nil,
        isSystemMessage: Bool = false,
        type: String
    ) {
        self.id = id
        self.content = content
        self.createdAt = createdAt
        self.relatedTaskId = relatedTaskId
        self.relatedProjectId = relatedProjectId
        self.isSystemMessage = isSystemMessage
        self.type = type
    }
    
    // 便捷方法：创建行动台记录
    static func createWorkbenchThought(content: String, taskId: UUID) -> Thought {
        return Thought(
            content: content,
            relatedTaskId: taskId,
            isSystemMessage: false,
            type: ThoughtType.workbench.rawValue
        )
    }
    
    // 便捷方法：创建背景板用户记录
    static func createBackboardUserThought(content: String, projectId: UUID) -> Thought {
        return Thought(
            content: content,
            relatedProjectId: projectId,
            isSystemMessage: false,
            type: ThoughtType.backboard.rawValue
        )
    }
    
    // 便捷方法：创建背景板系统记录
    static func createBackboardSystemThought(content: String, projectId: UUID) -> Thought {
        return Thought(
            content: content,
            relatedProjectId: projectId,
            isSystemMessage: true,
            type: ThoughtType.backboard.rawValue
        )
    }
    
    // 判断是否为行动台思绪
    var isWorkbench: Bool {
        return type == ThoughtType.workbench.rawValue
    }
    
    // 判断是否为背景板思绪
    var isBackboard: Bool {
        return type == ThoughtType.backboard.rawValue
    }
    
    // 获取类型枚举
    var typeEnum: ThoughtType {
        return ThoughtType(rawValue: type) ?? .workbench
    }
}

// 注意：Task和Project模型需要在各自的模型文件中更新，添加thoughts属性
// 这里仅保留方法，移除存储属性

// Task模型的扩展，用于关联思绪记录
extension Task {
    // 便捷方法：添加思绪记录
    func addThought(_ content: String) {
        let thought = Thought.createWorkbenchThought(content: content, taskId: self.id)
        
        if thoughts == nil {
            thoughts = []
        }
        
        thoughts?.append(thought)
    }
    
    // 便捷方法：获取行动台思绪记录
    func getWorkbenchThoughts() -> [Thought] {
        return thoughts?.filter { $0.isWorkbench } ?? []
    }
}

// Project模型的扩展，用于关联背景板记录
extension Project {
    // 便捷方法：添加用户背景板记录
    func addBackboardUserRecord(_ content: String) {
        let thought = Thought.createBackboardUserThought(content: content, projectId: self.id)
        
        if thoughts == nil {
            thoughts = []
        }
        
        thoughts?.append(thought)
    }
    
    // 便捷方法：添加系统背景板记录
    func addBackboardSystemRecord(_ content: String) {
        let thought = Thought.createBackboardSystemThought(content: content, projectId: self.id)
        
        if thoughts == nil {
            thoughts = []
        }
        
        thoughts?.append(thought)
    }
    
    // 便捷方法：获取背景板记录
    func getBackboardThoughts() -> [Thought] {
        return thoughts?.filter { $0.isBackboard } ?? []
    }
    
    // 便捷方法：获取背景板用户记录
    func getBackboardUserThoughts() -> [Thought] {
        return thoughts?.filter { $0.isBackboard && !$0.isSystemMessage } ?? []
    }
    
    // 便捷方法：获取背景板系统记录
    func getBackboardSystemThoughts() -> [Thought] {
        return thoughts?.filter { $0.isBackboard && $0.isSystemMessage } ?? []
    }
} 