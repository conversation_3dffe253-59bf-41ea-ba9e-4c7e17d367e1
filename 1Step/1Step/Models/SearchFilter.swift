import Foundation

enum SearchFilter: String, CaseIterable, Identifiable {
    case all = "All"
    case tasks = "Tasks"
    case projects = "Projects"
    case tags = "Tags"
    case notes = "Notes"
    case smb = "SMB"
    
    var id: String { self.rawValue }
    
    var displayName: String {
        switch self {
        case .all: return "汇总"
        case .tasks: return "行动"
        case .projects: return "项目"
        case .tags: return "标签"
        case .notes: return "备注"
        case .smb: return "将来也许"
        }
    }
}