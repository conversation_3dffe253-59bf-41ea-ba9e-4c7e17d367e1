import Foundation
import SwiftData

/// 邀请码仓库协议
protocol InviteRepositoryProtocol {
    func verifyInviteCode(_ code: String) async throws -> (Bool, Int?)
    func createInviteCode(code: String, maxUses: Int) async throws -> InviteCode
    func checkInviteCodeExists(_ code: String) async throws -> Bool
    func getLocalInviteStatus() -> DeviceInviteStatus
    func saveLocalInviteStatus(_ status: DeviceInviteStatus)
    func getInviteCodesByCreator(deviceId: String) -> [InviteCode]
    func getInviteCodeDetails(code: String) async throws -> InviteCode?
}

/// 邀请码仓库 - 管理邀请码相关的数据操作
class InviteRepository: InviteRepositoryProtocol {
    // MARK: - 属性
    
    private let modelContext: ModelContext
    private let userDefaults = UserDefaults.standard
    private let statusKey = "deviceInviteStatus"
    private let cloudService: InviteCloudService
    
    // MARK: - 初始化方法
    
    init(modelContext: ModelContext, cloudService: InviteCloudService = InviteCloudService()) {
        self.modelContext = modelContext
        self.cloudService = cloudService
    }
    
    // MARK: - 公共方法
    
    /// 验证邀请码
    /// - Parameter code: 要验证的邀请码
    /// - Returns: 是否验证成功以及剩余使用次数
    func verifyInviteCode(_ code: String) async throws -> (Bool, Int?) {
        // 先查本地数据库
        // 使用字符串字面量代替枚举值，避免SwiftData谓词编译器问题
        let activeStatus = InviteCodeStatus.active.rawValue
        let descriptor = FetchDescriptor<InviteCode>(
            predicate: #Predicate<InviteCode> { $0.code == code && $0.status == activeStatus }
        )
        
        let localCodes = try modelContext.fetch(descriptor)
        if let localCode = localCodes.first {
            // 检查是否还有剩余使用次数
            if localCode.currentUses < localCode.maxUses {
                // 本地找到激活码且有剩余使用次数，标记为已使用
                let (success, remainingUses) = try await markCodeAsUsed(localCode)
                return (success, remainingUses)
            } else {
                // 已达到最大使用次数
                return (false, 0)
            }
        }
        
        // 本地没找到，查询云端
        do {
            let result = try await cloudService.verifyCode(code)
            let isValid = result.valid
            let remainingUses = result.remainingUses
            
            if isValid {
                // 云端验证成功，在本地记录
                let status = getLocalInviteStatus()
                let newStatus = DeviceInviteStatus(
                    isVerified: true,
                    deviceId: status.deviceId,
                    usedInviteCode: code,
                    verifiedAt: Date(),
                    ownInviteCodes: status.ownInviteCodes
                )
                saveLocalInviteStatus(newStatus)
            }
            
            return (isValid, remainingUses)
        } catch {
            throw InviteError.networkError
        }
    }
    
    /// 检查邀请码是否已存在
    /// - Parameter code: 邀请码
    /// - Returns: 是否已存在
    func checkInviteCodeExists(_ code: String) async throws -> Bool {
        // 先检查本地数据库
        let descriptor = FetchDescriptor<InviteCode>(
            predicate: #Predicate<InviteCode> { $0.code == code }
        )
        
        let localCodes = try modelContext.fetch(descriptor)
        if !localCodes.isEmpty {
            // 本地找到相同邀请码
            return true
        }
        
        // 本地没找到，检查云端
        return try await cloudService.checkCodeExists(code)
    }
    
    /// 创建邀请码
    /// - Parameters:
    ///   - code: 邀请码内容
    ///   - maxUses: 最大使用次数，默认为1
    /// - Returns: 创建的邀请码对象
    func createInviteCode(code: String, maxUses: Int = 1) async throws -> InviteCode {
        // 先检查邀请码是否已存在
        let codeExists = try await checkInviteCodeExists(code)
        if codeExists {
            throw InviteError.codeAlreadyExists
        }
        
        let status = getLocalInviteStatus()
        
        // 创建新邀请码
        let newCode = InviteCode(
            code: code,
            creatorDeviceId: status.deviceId,
            maxUses: maxUses,
            currentUses: 0
        )
        
        // 添加到数据库
        modelContext.insert(newCode)
        try modelContext.save()
        
        // 更新本地设备状态中的邀请码列表
        var updatedStatus = status
        updatedStatus.ownInviteCodes.append(code)
        saveLocalInviteStatus(updatedStatus)
        
        // 同步到云端
        try await cloudService.createCode(code, creatorDeviceId: status.deviceId, maxUses: maxUses)
        
        return newCode
    }
    
    /// 获取本地邀请状态
    /// - Returns: 设备邀请状态
    func getLocalInviteStatus() -> DeviceInviteStatus {
        if let data = userDefaults.data(forKey: statusKey),
           let status = try? JSONDecoder().decode(DeviceInviteStatus.self, from: data) {
            return status
        }
        
        // 如果没有状态，创建一个新的
        let newStatus = DeviceInviteStatus()
        saveLocalInviteStatus(newStatus)
        return newStatus
    }
    
    /// 保存本地邀请状态
    /// - Parameter status: 要保存的状态
    func saveLocalInviteStatus(_ status: DeviceInviteStatus) {
        if let encoded = try? JSONEncoder().encode(status) {
            userDefaults.set(encoded, forKey: statusKey)
        }
    }
    
    /// 获取用户创建的邀请码
    /// - Parameter deviceId: 设备ID
    /// - Returns: 该设备创建的邀请码列表
    func getInviteCodesByCreator(deviceId: String) -> [InviteCode] {
        let descriptor = FetchDescriptor<InviteCode>(
            predicate: #Predicate<InviteCode> { $0.creatorDeviceId == deviceId },
            sortBy: [SortDescriptor(\InviteCode.createdAt, order: .reverse)]
        )
        
        do {
            return try modelContext.fetch(descriptor)
        } catch {
            print("获取邀请码失败: \(error)")
            return []
        }
    }
    
    /// 获取邀请码详情
    /// - Parameter code: 邀请码
    /// - Returns: 邀请码详情，包括使用记录
    func getInviteCodeDetails(code: String) async throws -> InviteCode? {
        // 先查本地数据库
        let descriptor = FetchDescriptor<InviteCode>(
            predicate: #Predicate<InviteCode> { $0.code == code }
        )
        
        let localCodes = try modelContext.fetch(descriptor)
        if let localCode = localCodes.first {
            return localCode
        }
        
        // 本地没找到，查询云端
        do {
            if let remoteCode = try await cloudService.getInviteDetails(code) {
                // 保存到本地数据库
                modelContext.insert(remoteCode)
                try modelContext.save()
                return remoteCode
            }
            return nil
        } catch {
            throw InviteError.networkError
        }
    }
    
    // MARK: - 私有方法
    
    /// 将邀请码标记为已使用
    /// - Parameter inviteCode: 要标记的邀请码对象
    /// - Returns: 操作是否成功和剩余使用次数
    private func markCodeAsUsed(_ inviteCode: InviteCode) async throws -> (Bool, Int) {
        let status = getLocalInviteStatus()
        
        // 检查该设备是否已使用过此邀请码
        if inviteCode.usedBy.contains(where: { $0.deviceId == status.deviceId }) {
            // 该设备已使用过此邀请码
            return (true, inviteCode.remainingUses)
        }
        
        // 检查是否超过最大使用次数
        if inviteCode.currentUses >= inviteCode.maxUses {
            return (false, 0)
        }
        
        // 添加使用记录
        var updatedUsedBy = inviteCode.usedBy
        updatedUsedBy.append(InviteUseRecord(deviceId: status.deviceId))
        inviteCode.usedBy = updatedUsedBy
        
        // 更新使用计数
        inviteCode.currentUses += 1
        
        // 如果达到最大使用次数，更新状态为已使用
        if inviteCode.currentUses >= inviteCode.maxUses {
            inviteCode.status = InviteCodeStatus.used.rawValue
        }
        
        // 更新本地设备状态
        var updatedStatus = status
        updatedStatus.isVerified = true
        updatedStatus.usedInviteCode = inviteCode.code
        updatedStatus.verifiedAt = Date()
        saveLocalInviteStatus(updatedStatus)
        
        // 注释掉云端同步，仅使用本地功能
        /*
        do {
            try await cloudService.markCodeAsUsed(inviteCode.code, usedByDeviceId: status.deviceId)
        } catch {
            print("云端同步失败: \(error.localizedDescription)")
            // 继续执行，不让云端错误影响本地操作
        }
        */
        
        // 保存更改
        try modelContext.save()
        
        // 返回剩余使用次数
        return (true, inviteCode.remainingUses)
    }
}

/// 邀请码错误类型
enum InviteError: Error {
    case codeAlreadyExists
    case invalidCode
    case networkError
    case maxUsesExceeded
    
    var localizedDescription: String {
        switch self {
        case .codeAlreadyExists:
            return "邀请码已存在，请使用其他邀请码"
        case .invalidCode:
            return "无效的邀请码"
        case .networkError:
            return "网络错误，请稍后再试"
        case .maxUsesExceeded:
            return "邀请码已达到最大使用次数"
        }
    }
} 