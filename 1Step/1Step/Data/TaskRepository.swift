import Foundation
import SwiftUI
import SwiftData

/// 任务数据仓库 - 封装所有数据访问操作
class TaskRepository {
    // 私有模型上下文，不对外暴露
    private let modelContext: ModelContext
    
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
    }
    
    /// 保存当前更改
    func save() throws {
        try modelContext.save()
    }
    
    // MARK: - 任务更新方法
    
    /// 更新任务标题
    func updateTaskTitle(_ task: Task, newTitle: String) {
        guard !newTitle.isEmpty, newTitle != task.title else { return }
        task.title = newTitle
        saveTask(task)
    }
    
    /// 更新任务笔记
    func updateTaskNotes(_ task: Task, newNotes: String) {
        if newNotes != task.notes {
            task.notes = newNotes
            saveTask(task)
        }
    }
    
    /// 更新任务状态
    func updateTaskStatus(_ task: Task, newStatus: String) {
        if newStatus != task.status {
            task.status = newStatus
            
            // 如果任务状态变为已完成，则设置完成时间
            if newStatus == TaskStatus.done.rawValue {
                task.completedAt = Date()
                // 埋点：任务完成
                AnalyticsService.shared.trackEvent(AnalyticsService.EventNames.taskCompleted)
            } else if task.completedAt != nil {
                // 如果任务从已完成变为其他状态，则清除完成时间
                task.completedAt = nil
            }
            
            saveTask(task)
        }
    }
    
    /// 添加标签到任务
    func addTagToTask(_ task: Task, tag: String) {
        guard !tag.isEmpty, !task.tags.contains(tag) else { return }
        task.tags.append(tag)
        saveTask(task)
    }
    
    /// 从任务移除标签
    func removeTagFromTask(_ task: Task, tag: String) {
        if let index = task.tags.firstIndex(of: tag) {
            task.tags.remove(at: index)
            saveTask(task)
        }
    }
    
    /// 直接更新任务
    func updateTask(_ task: Task) {
        task.updatedAt = Date()
        saveTask(task)
    }
    
    /// 保存任务更改
    func saveTask(_ task: Task) {
        task.updatedAt = Date()
        do {
            do {
                // 尝试查询这个 ID 的任务
                let descriptor = FetchDescriptor<Task>(predicate: #Predicate<Task> { $0.id == task.id })
                let existingTasks = try modelContext.fetch(descriptor)
                
                if existingTasks.isEmpty {
                    modelContext.insert(task)
                }
            } catch {
                // 如果查询出错，尝试直接插入
                modelContext.insert(task)
            }
            
            try modelContext.save()
        } catch {
            print("保存任务失败: \(error)")
            // 不返回任何值，因为这是一个 void 函数
        }
    }
    
    /// 批量更新任务标签
    func updateTaskTags(_ task: Task, newTags: [String]) {
        task.tags = newTags
        saveTask(task)
    }
    
    /// 删除任务（硬删除）
    func deleteTask(withId taskId: UUID) {
        // 根据 ID 查找任务
        // 注意：这里的查询语法取决于你如何设置 SwiftData 的查询，
        // 可能需要使用 FetchDescriptor
        let fetchDescriptor = FetchDescriptor<Task>(predicate: #Predicate { $0.id == taskId })
        
        do {
            if let taskToDelete = try modelContext.fetch(fetchDescriptor).first {
                modelContext.delete(taskToDelete) // 删除从 context 新获取的实例
            try modelContext.save()
            } else {
                print("[TaskRepository] 尝试删除时未找到任务: \(taskId)")
            }
        } catch {
            print("[TaskRepository] 删除任务失败: \(error)")
            // 处理错误
        }
    }
    

    // MARK: - 查询方法
    
    /// 根据标题查询任务
    func getTasksByTitle(_ title: String) -> [Task] {
        do {
            let predicate = #Predicate<Task> { task in
                task.title == title
            }
            
            let descriptor = FetchDescriptor<Task>(
                predicate: predicate,
                sortBy: []
            )
            
            return try modelContext.fetch(descriptor)
        } catch {
            print("[错误] 根据标题查询任务失败: \(error)")
            return []
        }
    }
    
    /// 根据ID获取任务
    func getTaskById(_ id: UUID) -> Task? {
        do {
            let predicate = #Predicate<Task> { task in
                task.id == id
            }
            
            let descriptor = FetchDescriptor<Task>(
                predicate: predicate,
                sortBy: []
            )
            
            let tasks = try modelContext.fetch(descriptor)
            return tasks.first
        } catch {
            print("[错误] 根据ID获取任务失败: \(error)")
            return nil
        }
    }
        
    /// 获取特定项目下特定状态的任务
    func getTasksForProject(_ projectId: UUID, status: String? = nil, sortBy: SortDescriptor<Task> = SortDescriptor(\.createdAt, order: .reverse)) -> [Task] {
        // 使用更简单的方法，先获取所有任务，然后在内存中过滤
        do {
            // 获取所有任务
            let allTasks = try modelContext.fetch(FetchDescriptor<Task>())
            
            // 在内存中过滤任务
            return allTasks.filter { task in
                // 项目匹配
                guard let taskProject = task.project, taskProject == projectId else {
                    return false
                }
                
                // 状态匹配（如果指定了状态）
                if let status = status, task.status != status {
                    return false
                }
                
                
                return true
            }.sorted(by: { task1, task2 in
                // 手动排序，模拟 sortBy 的行为
                if sortBy.order == .forward {
                    return task1.createdAt < task2.createdAt
                } else {
                    return task1.createdAt > task2.createdAt
                }
            })
        } catch {
            print("[错误] 获取任务失败: \(error)")
            return []
        }
    }
    
    /// 获取特定状态的所有任务
    func getTasksByStatus(_ status: String, sortBy: SortDescriptor<Task> = SortDescriptor(\.createdAt, order: .reverse)) -> [Task] {
        do {
            let predicate = #Predicate<Task> { task in
                task.status == status
            }
            
            let descriptor = FetchDescriptor<Task>(
                predicate: predicate,
                sortBy: [sortBy]
            )
            
            // 增加查询的安全性
            let result = try modelContext.fetch(descriptor)
            
            // 返回复制的数组而不是直接引用，防止并发修改问题
            return result
        } catch {
            print("[错误] 获取任务失败: \(error)")
            return []
        }
    }
    
    /// 获取所有任务
    func getAllTasks(sortBy: SortDescriptor<Task> = SortDescriptor(\.createdAt, order: .reverse)) -> [Task] {
        do {
            let descriptor = FetchDescriptor<Task>(
                sortBy: [sortBy]
            )
            
            return try modelContext.fetch(descriptor)
        } catch {
            print("获取所有任务失败: \(error)")
            return []
        }
    }
    
    /// 获取特定项目和状态的任务
    func fetchTasks(projectId: UUID, status: String) -> [Task] {
        let predicate = #Predicate<Task> { task in
            task.project == projectId && task.status == status
        }
        
        let descriptor = FetchDescriptor<Task>(
            predicate: predicate,
            sortBy: []
        )
        
        do {
            return try modelContext.fetch(descriptor)
        } catch {
            print("获取任务失败: \(error)")
            return []
        }
    }
    
    /// 搜索任务
    /// - Parameters:
    ///   - query: 搜索关键词
    ///   - status: 任务状态（可选）
    ///   - projectId: 项目ID（可选）
    /// - Returns: 匹配的任务列表
    func searchTasks(query: String, status: String? = nil, projectId: UUID? = nil) -> [Task] {
        do {
            // 获取所有任务
            let allTasks = try modelContext.fetch(FetchDescriptor<Task>())
            
            // 在内存中过滤任务
            return allTasks.filter { task in
                // 状态匹配（如果指定了状态）
                if let status = status, task.status != status {
                    return false
                }
                
                // 项目匹配（如果指定了项目）
                if let projectId = projectId, task.project != projectId {
                    return false
                }
                
                // 搜索关键词匹配
                let searchText = query.lowercased()
                return task.title.lowercased().contains(searchText) ||
                       task.notes.lowercased().contains(searchText) ||
                       task.tags.contains { $0.lowercased().contains(searchText) }
            }
        } catch {
            print("[错误] 搜索任务失败: \(error)")
            return []
        }
    }
    
    // MARK: - 数据操作方法
    
    /// 更新任务状态
    func updateTaskStatus(_ task: Task, newStatus: TaskStatus) {
        task.status = newStatus.rawValue
        
        // 如果任务状态变为已完成，则设置完成时间
        if newStatus == .done {
            task.completedAt = Date()
            // 埋点：任务完成
            AnalyticsService.shared.trackEvent(AnalyticsService.EventNames.taskCompleted)
        } else if task.completedAt != nil {
            // 如果任务从已完成变为其他状态，则清除完成时间
            task.completedAt = nil
        }
        
        // 更新任务的更新时间
        task.updatedAt = Date()
        
        do {
            try modelContext.save()
        } catch {
            print("更新任务状态失败: \(error)")
        }
    }
    
    // MARK: - 小行动相关方法
    
    /// 添加小行动到任务
    /// - Parameters:
    ///   - task: 要添加小行动的任务
    ///   - title: 小行动标题
    ///   - createdAt: 创建时间，默认为当前时间
    /// - Returns: 新创建的小行动项
    func addChecklistItemToTask(_ task: Task, title: String, createdAt: Date = Date()) -> ChecklistItem {
        // 尝试方法3: 使用独立方法创建小行动
        // 不使用事务，而是单独创建并保存小行动
        let newItem = createChecklistItem(title: title, createdAt: createdAt)
        // 然后将小行动添加到任务中并保存
        addExistingChecklistItemToTask(task, item: newItem)
        // 埋点：小任务创建
        AnalyticsService.shared.trackEvent(AnalyticsService.EventNames.subtaskCreated)
        return newItem
    }
    
    /// 创建并保存一个新的小行动
    private func createChecklistItem(title: String, createdAt: Date) -> ChecklistItem {
        let newItem = ChecklistItem(title: title, createdAt: createdAt)
        
        // 将小行动插入到数据库
        modelContext.insert(newItem)
        
        do {
            try modelContext.save()
        } catch {
        }
        
        return newItem
    }
    
    /// 将已存在的小行动添加到任务中
    private func addExistingChecklistItemToTask(_ task: Task, item: ChecklistItem) {
        print("[小行动调试] 将小行动\(item.id)添加到任务\(task.id)")
        
        // 初始化checklist如果为nil
        if task.checklist == nil {
            task.checklist = []
        }
        
        // 添加到任务的关系中
        task.checklist?.append(item)
        
        do {
            try modelContext.save()
        } catch {
            
            if let nsError = error as NSError? {
            }
        }
    }
    
    /// 从任务中移除小行动
    func removeChecklistItemFromTask(_ task: Task, itemId: UUID) {
        // 在事务中移除小行动，确保数据一致性
        if let checklist = task.checklist,
           let index = checklist.firstIndex(where: { $0.id == itemId }) {
            let item = checklist[index]
            task.checklist?.remove(at: index)
            modelContext.delete(item) // 从数据库中删除
            
            do {
                try modelContext.save()
            } catch {
                print("移除小行动失败: \(error)")
            }
        }
    }
    
    /// 切换小行动完成状态
    func toggleChecklistItemCompletion(_ task: Task, itemId: UUID) {
        if let checklist = task.checklist,
           let index = checklist.firstIndex(where: { $0.id == itemId }) {
            // 切换完成状态
            task.checklist?[index].isCompleted.toggle()
            
            // 更新完成时间
            if let isCompleted = task.checklist?[index].isCompleted, isCompleted {
                // 如果标记为完成，设置完成时间
                task.checklist?[index].completedAt = Date()
                // 埋点：小任务完成
                AnalyticsService.shared.trackEvent(AnalyticsService.EventNames.subtaskCompleted)
            } else {
                // 如果标记为未完成，清除完成时间
                task.checklist?[index].completedAt = nil
            }
            
            do {
                try modelContext.save()
            } catch {
                print("切换小行动状态失败: \(error)")
            }
        }
    }
    
    /// 更新小行动标题
    func updateChecklistItemTitle(_ task: Task, itemId: UUID, newTitle: String) {
        guard !newTitle.isEmpty else { return }
        
        if let checklist = task.checklist,
           let index = checklist.firstIndex(where: { $0.id == itemId }) {
            task.checklist?[index].title = newTitle
            
            do {
                try modelContext.save()
            } catch {
                print("更新小行动标题失败: \(error)")
            }
        }
    }
    
    /// 切换任务状态 - 按顺序循环切换
    func cycleTaskStatus(_ task: Task, throughStatuses: [TaskStatus] = [.na, .waiting, .inbox, .smb]) {
        let currentStatus = TaskStatus(rawValue: task.status) ?? .inbox
        let currentIndex = throughStatuses.firstIndex(of: currentStatus) ?? 0
        let nextIndex = (currentIndex + 1) % throughStatuses.count
        let nextStatus = throughStatuses[nextIndex]
        
        updateTaskStatus(task, newStatus: nextStatus)
    }
    
    /// 添加新任务
    /// - Parameters:
    ///   - title: 任务标题
    ///   - status: 任务状态
    ///   - notes: 任务备注
    ///   - project: 项目ID（可选）
    ///   - tags: 标签列表（可选）
    /// - Returns: 新创建的任务
    func addTask(
        title: String,
        status: String,
        notes: String = "",
        project: UUID? = nil,
        tags: [String] = []
    ) -> Task {
        // 创建新任务
        let newTask = Task(
            title: title,
            status: status,
            project: project,
            tags: tags,
            notes: notes
        )
        
        // 插入到数据库
        modelContext.insert(newTask)
        
        // 保存更改
        do {
            try modelContext.save()
            // 埋点：任务创建
            AnalyticsService.shared.trackEvent(AnalyticsService.EventNames.taskCreated)
        } catch {
            print("添加任务失败: \(error)")
        }
        
        return newTask
    }
    
    /// 判断是否可以将任务移动到一步状态（检查一步任务数量是否已达上限）
    func canMoveToDoing() -> Bool {
        return true
    }
    
    /// 判断是否可以将任务移动到下一步状态（检查下一步任务数量是否已达上限）
    func canMoveToNA() -> Bool {
        return true
    }
    
    /// 加载示例数据
    func loadDemoData(forceLoad: Bool = false) {
        DemoData.addDemoTasks(modelContext: modelContext, forceLoad: forceLoad)
    }
    
    /// 获取特定项目在指定时间段内完成的任务
    func getCompletedTasksForProject(projectId: UUID, since: Date) -> [Task] {
        do {
            let allTasks = try modelContext.fetch(FetchDescriptor<Task>())
            return allTasks.filter { task in
                guard let taskProject = task.project, 
                      taskProject == projectId,
                      task.status == TaskStatus.done.rawValue,
                      let completedAt = task.completedAt,
                      completedAt >= since else {
                    return false
                }
                return true
            }.sorted(by: { $0.completedAt ?? Date() > $1.completedAt ?? Date() })
        } catch {
            print("[错误] 获取已完成任务失败: \(error)")
            return []
        }
    }
    
    // MARK: - 笔记相关方法
    
    /// 添加笔记
    /// - Parameters:
    ///   - title: 笔记标题
    ///   - content: 笔记内容
    ///   - project: 项目ID
    ///   - tags: 标签列表
    ///   - isFocused: 是否重点关注
    /// - Returns: 新创建的笔记（任务）
    func addNote(title: String, content: String, project: UUID? = nil, tags: [String] = [], isFocused: Bool = false) -> Task {
        // 创建笔记
        let note = Task(
            title: "",
            status: TaskStatus.inbox.rawValue,
            project: project,
            tags: tags,
            notes: content,
            isNote: true,
            isFocused: isFocused,
            noteTitle: title
        )
        
        // 插入到数据库
        modelContext.insert(note)
        
        // 保存更改
        do {
            try modelContext.save()
        } catch {
            print("添加笔记失败: \(error)")
        }
        
        return note
    }
    
    /// 获取指定项目的笔记
    func getNotesForProject(_ projectId: UUID, sortBy: SortDescriptor<Task> = SortDescriptor(\.createdAt, order: .reverse)) -> [Task] {
        do {
            let predicate = #Predicate<Task> { task in
                task.isNote && task.project == projectId
            }
            
            let descriptor = FetchDescriptor<Task>(
                predicate: predicate,
                sortBy: [sortBy]
            )
            
            return try modelContext.fetch(descriptor)
        } catch {
            print("[错误] 获取项目笔记失败: \(error)")
            return []
        }
    }
    
    /// 获取所有重点关注的笔记
    func getFocusedNotes(sortBy: SortDescriptor<Task> = SortDescriptor(\.updatedAt, order: .reverse)) -> [Task] {
        do {
            let predicate = #Predicate<Task> { task in
                task.isNote && task.isFocused
            }
            
            let descriptor = FetchDescriptor<Task>(
                predicate: predicate,
                sortBy: [sortBy]
            )
            
            return try modelContext.fetch(descriptor)
        } catch {
            print("[错误] 获取重点关注笔记失败: \(error)")
            return []
        }
    }
    
    /// 更新笔记标题
    func updateNoteTitle(_ note: Task, newTitle: String) {
        guard note.isNote else { return }
        note.noteTitle = newTitle
        saveTask(note)
    }
    
    /// 更新笔记内容
    func updateNoteContent(_ note: Task, newContent: String) {
        guard note.isNote else { return }
        note.notes = newContent
        saveTask(note)
    }
    
    /// 更新笔记重点关注状态
    func updateNoteFocusStatus(_ note: Task, isFocused: Bool) {
        guard note.isNote else { return }
        note.isFocused = isFocused
        saveTask(note)
    }
    
    /// 将笔记转换为任务
    func convertNoteToTask(_ note: Task) -> Task? {
        guard note.isNote else { return nil }
        
        // 创建新任务
        let task = Task(
            title: note.noteTitle.isEmpty ? note.extractNoteTitle() : note.noteTitle,
            status: TaskStatus.inbox.rawValue,
            project: note.project,
            tags: note.tags,
            notes: note.notes
        )
        
        // 插入新任务
        modelContext.insert(task)
        
        // 删除原笔记
        modelContext.delete(note)
        
        // 保存更改
        do {
            try modelContext.save()
            return task
        } catch {
            print("转换笔记为任务失败: \(error)")
            return nil
        }
    }
}
