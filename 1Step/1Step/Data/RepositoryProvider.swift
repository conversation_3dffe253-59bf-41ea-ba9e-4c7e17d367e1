import Foundation
import SwiftData

/// 仓库提供者 - 一个简单的服务定位器，管理所有仓库实例
class RepositoryProvider {
    // 使用私有静态变量存储单例实例
    private static var sharedInstance: RepositoryProvider?
    
    // 公开的共享实例访问方法，带有安全检查
    static var shared: RepositoryProvider {
        guard let instance = sharedInstance else {
            fatalError("请在使用RepositoryProvider前调用initialize(modelContext:)进行初始化")
        }
        return instance
    }
    
    private var modelContext: ModelContext
    
    // 各个仓库的懒加载实例
    private(set) lazy var taskRepository: TaskRepository = TaskRepository(modelContext: modelContext)
    private(set) lazy var projectRepository: ProjectRepository = ProjectRepository(modelContext: modelContext)
    private(set) lazy var tagRepository: TagRepository = TagRepository(modelContext: modelContext)
    private(set) lazy var thoughtRepository: ThoughtRepository = ThoughtRepository(modelContext: modelContext)
    
    /// 私有初始化方法，只能通过initialize静态方法调用
    private init(modelContext: ModelContext) {
        self.modelContext = modelContext
    }
    
    /// 初始化方法，由App启动时调用
    static func initialize(modelContext: ModelContext) {
        // 防止重复初始化
        guard sharedInstance == nil else {
            print("警告: RepositoryProvider已经初始化过，忽略此次调用")
            return
        }
        
        // 创建单例实例
        sharedInstance = RepositoryProvider(modelContext: modelContext)
    }
    
    /// 重置所有仓库（主要用于测试）
    func reset(with modelContext: ModelContext) {
        self.modelContext = modelContext
        self.taskRepository = TaskRepository(modelContext: modelContext)
        self.projectRepository = ProjectRepository(modelContext: modelContext)
        self.tagRepository = TagRepository(modelContext: modelContext)
        self.thoughtRepository = ThoughtRepository(modelContext: modelContext)
    }
}
