import Foundation
import SwiftUI
import SwiftData

/// 思绪记录数据仓库 - 封装行动台和背景板相关数据操作
class ThoughtRepository {
    // 私有模型上下文，不对外暴露
    private let modelContext: ModelContext
    
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
    }
    
    /// 保存当前更改
    func save() throws {
        try modelContext.save()
    }
    
    // MARK: - 基本操作方法
    
    /// 添加思绪记录
    /// - Parameters:
    ///   - content: 思绪内容
    ///   - taskId: 关联的任务ID（行动台）
    /// - Returns: 新创建的思绪记录
    func addWorkbenchThought(content: String, taskId: UUID) -> Thought {
        let thought = Thought.createWorkbenchThought(content: content, taskId: taskId)
        
        // 插入到数据库
        modelContext.insert(thought)
        
        // 更新与任务的关联关系
        if let task = getTaskById(taskId) {
            if task.thoughts == nil {
                task.thoughts = []
            }
            task.thoughts?.append(thought)
        }
        
        // 保存更改
        do {
            try modelContext.save()
        } catch {
            print("添加行动台思绪记录失败: \(error)")
        }
        
        return thought
    }
    
    /// 添加背景板用户记录
    /// - Parameters:
    ///   - content: 记录内容
    ///   - projectId: 关联的项目ID
    /// - Returns: 新创建的背景板记录
    func addBackboardUserThought(content: String, projectId: UUID) -> Thought {
        let thought = Thought.createBackboardUserThought(content: content, projectId: projectId)
        
        // 插入到数据库
        modelContext.insert(thought)
        
        // 更新与项目的关联关系
        if let project = getProjectById(projectId) {
            if project.thoughts == nil {
                project.thoughts = []
            }
            project.thoughts?.append(thought)
        }
        
        // 保存更改
        do {
            try modelContext.save()
        } catch {
            print("添加背景板用户记录失败: \(error)")
        }
        
        return thought
    }
    
    /// 添加背景板系统记录
    /// - Parameters:
    ///   - content: 系统消息内容
    ///   - projectId: 关联的项目ID
    ///   - relatedTaskId: 关联的任务ID（可选）
    ///   - createdAt: 创建时间，默认为当前时间
    /// - Returns: 新创建的系统记录
    func addBackboardSystemThought(
        content: String, 
        projectId: UUID,
        relatedTaskId: UUID? = nil,
        createdAt: Date = Date()
    ) -> Thought {
        // 创建系统思绪记录
        let thought = Thought(
            content: content,
            createdAt: createdAt,
            relatedTaskId: relatedTaskId,
            relatedProjectId: projectId,
            isSystemMessage: true,
            type: ThoughtType.backboard.rawValue
        )
        
        // 插入到数据库
        modelContext.insert(thought)
        
        // 更新与项目的关联关系
        if let project = getProjectById(projectId) {
            if project.thoughts == nil {
                project.thoughts = []
            }
            project.thoughts?.append(thought)
        }
        
        // 保存更改
        do {
            try modelContext.save()
        } catch {
            print("添加背景板系统记录失败: \(error)")
        }
        
        return thought
    }
    
    /// 删除思绪记录
    /// - Parameter thought: 要删除的思绪记录
    func deleteThought(_ thought: Thought) {
        // 先解除与 Task 和 Project 的关系
        thought.task = nil
        thought.project = nil
        
        // 再删除对象
        modelContext.delete(thought)
        
        do {
            try modelContext.save()
        } catch {
            print("删除思绪记录失败: \(error)")
        }
    }
    
    // MARK: - 查询方法
    
    /// 根据ID获取思绪记录
    /// - Parameter id: 思绪记录ID
    /// - Returns: 找到的思绪记录，如果不存在则返回nil
    func getThoughtById(_ id: UUID) -> Thought? {
        do {
            let predicate = #Predicate<Thought> { thought in
                thought.id == id
            }
            
            let descriptor = FetchDescriptor<Thought>(
                predicate: predicate,
                sortBy: []
            )
            
            let thoughts = try modelContext.fetch(descriptor)
            return thoughts.first
        } catch {
            print("[错误] 根据ID获取思绪记录失败: \(error)")
            return nil
        }
    }
    
    /// 获取任务的行动台记录
    /// - Parameters:
    ///   - taskId: 任务ID
    ///   - limit: 最大记录数，默认为0（不限制）
    ///   - sortBy: 排序方式，默认按创建时间倒序
    /// - Returns: 思绪记录列表
    func getWorkbenchThoughts(taskId: UUID, limit: Int = 0, sortBy: SortDescriptor<Thought> = SortDescriptor(\.createdAt, order: .reverse)) -> [Thought] {
        do {
            // 使用字符串常量代替枚举值
            let workbenchTypeValue = "Workbench"
            
            let predicate = #Predicate<Thought> { thought in
                thought.relatedTaskId == taskId && thought.type == workbenchTypeValue
            }
            
            var descriptor = FetchDescriptor<Thought>(
                predicate: predicate,
                sortBy: [sortBy]
            )
            
            // 设置查询数量限制（如果有）
            if limit > 0 {
                descriptor.fetchLimit = limit
            }
            
            return try modelContext.fetch(descriptor)
        } catch {
            print("[错误] 获取行动台记录失败: \(error)")
            return []
        }
    }
    
    /// 获取项目的背景板记录
    /// - Parameters:
    ///   - projectId: 项目ID
    ///   - limit: 最大记录数，默认为0（不限制）
    ///   - sortBy: 排序方式，默认按创建时间顺序（聊天记录通常按时间顺序显示）
    /// - Returns: 背景板记录列表
    func getBackboardThoughts(projectId: UUID, limit: Int = 0, sortBy: SortDescriptor<Thought> = SortDescriptor(\.createdAt, order: .forward)) -> [Thought] {
        do {
            // 使用字符串常量代替枚举值
            let backboardTypeValue = "Backboard"
            
            let predicate = #Predicate<Thought> { thought in
                thought.relatedProjectId == projectId && thought.type == backboardTypeValue
            }
            
            var descriptor = FetchDescriptor<Thought>(
                predicate: predicate,
                sortBy: [sortBy]
            )
            
            // 设置查询数量限制（如果有）
            if limit > 0 {
                descriptor.fetchLimit = limit
            }
            
            return try modelContext.fetch(descriptor)
        } catch {
            print("[错误] 获取背景板记录失败: \(error)")
            return []
        }
    }
    
    /// 获取项目的背景板用户记录（不包括系统消息）
    /// - Parameters:
    ///   - projectId: 项目ID
    ///   - limit: 最大记录数，默认为0（不限制）
    /// - Returns: 用户记录列表
    func getBackboardUserThoughts(projectId: UUID, limit: Int = 0) -> [Thought] {
        do {
            // 使用字符串常量代替枚举值
            let backboardTypeValue = "Backboard"
            
            let predicate = #Predicate<Thought> { thought in
                thought.relatedProjectId == projectId && 
                thought.type == backboardTypeValue &&
                !thought.isSystemMessage
            }
            
            var descriptor = FetchDescriptor<Thought>(
                predicate: predicate,
                sortBy: [SortDescriptor(\.createdAt, order: .forward)]
            )
            
            // 设置查询数量限制（如果有）
            if limit > 0 {
                descriptor.fetchLimit = limit
            }
            
            return try modelContext.fetch(descriptor)
        } catch {
            print("[错误] 获取背景板用户记录失败: \(error)")
            return []
        }
    }
    
    /// 获取项目的背景板系统记录
    /// - Parameter projectId: 项目ID
    /// - Returns: 系统记录列表
    func getBackboardSystemThoughts(projectId: UUID, limit: Int = 0) -> [Thought] {
        do {
            // 使用字符串常量代替枚举值
            let backboardTypeValue = "Backboard"
            
            let predicate = #Predicate<Thought> { thought in
                thought.relatedProjectId == projectId && 
                thought.type == backboardTypeValue &&
                thought.isSystemMessage
            }
            
            var descriptor = FetchDescriptor<Thought>(
                predicate: predicate,
                sortBy: [SortDescriptor(\.createdAt, order: .forward)]
            )
            
            // 设置查询数量限制（如果有）
            if limit > 0 {
                descriptor.fetchLimit = limit
            }
            
            return try modelContext.fetch(descriptor)
        } catch {
            print("[错误] 获取背景板系统记录失败: \(error)")
            return []
        }
    }
    
    /// 获取所有思绪记录
    /// - Returns: 所有思绪记录列表
    func getAllThoughts() -> [Thought] {
        do {
            let descriptor = FetchDescriptor<Thought>(
                sortBy: [SortDescriptor(\.createdAt, order: .reverse)]
            )
            
            return try modelContext.fetch(descriptor)
        } catch {
            print("[错误] 获取所有思绪记录失败: \(error)")
            return []
        }
    }
    
    // MARK: - 辅助方法
    
    /// 根据ID获取任务
    private func getTaskById(_ id: UUID) -> Task? {
        do {
            let predicate = #Predicate<Task> { task in
                task.id == id
            }
            
            let descriptor = FetchDescriptor<Task>(
                predicate: predicate,
                sortBy: []
            )
            
            let tasks = try modelContext.fetch(descriptor)
            return tasks.first
        } catch {
            print("[错误] 根据ID获取任务失败: \(error)")
            return nil
        }
    }
    
    /// 根据ID获取项目
    private func getProjectById(_ id: UUID) -> Project? {
        do {
            let predicate = #Predicate<Project> { project in
                project.id == id
            }
            
            let descriptor = FetchDescriptor<Project>(
                predicate: predicate,
                sortBy: []
            )
            
            let projects = try modelContext.fetch(descriptor)
            return projects.first
        } catch {
            print("[错误] 根据ID获取项目失败: \(error)")
            return nil
        }
    }
    
    /// 分页获取行动台思绪记录
    /// - Parameters:
    ///   - taskId: 任务ID
    ///   - page: 页码（从0开始）
    ///   - pageSize: 每页记录数
    /// - Returns: 当前页的思绪记录
    func getWorkbenchThoughtsPaginated(taskId: UUID, page: Int, pageSize: Int) -> [Thought] {
        guard page >= 0 && pageSize > 0 else { return [] }
        
        do {
            // 使用字符串常量代替枚举值
            let workbenchTypeValue = "Workbench"
            
            let predicate = #Predicate<Thought> { thought in
                thought.relatedTaskId == taskId && thought.type == workbenchTypeValue
            }
            
            var descriptor = FetchDescriptor<Thought>(
                predicate: predicate,
                sortBy: [SortDescriptor(\.createdAt, order: .reverse)]
            )
            
            // 设置分页参数
            descriptor.fetchOffset = page * pageSize
            descriptor.fetchLimit = pageSize
            
            return try modelContext.fetch(descriptor)
        } catch {
            print("[错误] 分页获取行动台记录失败: \(error)")
            return []
        }
    }
    
    /// 分页获取背景板记录
    /// - Parameters:
    ///   - projectId: 项目ID
    ///   - page: 页码（从0开始）
    ///   - pageSize: 每页记录数
    /// - Returns: 当前页的背景板记录
    func getBackboardThoughtsPaginated(projectId: UUID, page: Int, pageSize: Int) -> [Thought] {
        guard page >= 0 && pageSize > 0 else { return [] }
        
        do {
            // 使用字符串常量代替枚举值
            let backboardTypeValue = "Backboard"
            
            let predicate = #Predicate<Thought> { thought in
                thought.relatedProjectId == projectId && thought.type == backboardTypeValue
            }
            
            var descriptor = FetchDescriptor<Thought>(
                predicate: predicate,
                sortBy: [SortDescriptor(\.createdAt, order: .forward)]
            )
            
            // 设置分页参数
            descriptor.fetchOffset = page * pageSize
            descriptor.fetchLimit = pageSize
            
            return try modelContext.fetch(descriptor)
        } catch {
            print("[错误] 分页获取背景板记录失败: \(error)")
            return []
        }
    }
    
    /// 获取任务最新的几条行动台记录
    /// - Parameters:
    ///   - taskId: 任务ID
    ///   - count: 记录数量
    /// - Returns: 最新的几条记录
    func getLatestWorkbenchThoughts(taskId: UUID, count: Int) -> [Thought] {
        return getWorkbenchThoughts(taskId: taskId, limit: count)
    }
    
    /// 获取项目最新的几条背景板记录
    /// - Parameters:
    ///   - projectId: 项目ID
    ///   - count: 记录数量
    /// - Returns: 最新的几条记录
    func getLatestBackboardThoughts(projectId: UUID, count: Int) -> [Thought] {
        // 获取背景板记录，然后手动反转顺序
        var thoughts = getBackboardThoughts(projectId: projectId)
        
        // 限制数量
        if thoughts.count > count {
            // 保留最新的几条记录（最后几条）
            thoughts = Array(thoughts.suffix(count))
        }
        
        return thoughts
    }
    
    // MARK: - 通用辅助方法
    
    /// 获取项目的背景板记录数量
    /// - Parameter projectId: 项目ID
    /// - Returns: 记录数量
    func getBackboardThoughtsCount(projectId: UUID) -> Int {
        do {
            // 使用字符串常量代替枚举值
            let backboardTypeValue = "Backboard"
            
            let predicate = #Predicate<Thought> { thought in
                thought.relatedProjectId == projectId && thought.type == backboardTypeValue
            }
            
            let descriptor = FetchDescriptor<Thought>(
                predicate: predicate
            )
            
            // 只获取数量
            return try modelContext.fetchCount(descriptor)
        } catch {
            print("[错误] 获取背景板记录数量失败: \(error)")
            return 0
        }
    }
    
    /// 获取任务的行动台记录数量
    /// - Parameter taskId: 任务ID
    /// - Returns: 记录数量
    func getWorkbenchThoughtsCount(taskId: UUID) -> Int {
        do {
            // 使用字符串常量代替枚举值
            let workbenchTypeValue = "Workbench"
            
            let predicate = #Predicate<Thought> { thought in
                thought.relatedTaskId == taskId && thought.type == workbenchTypeValue
            }
            
            let descriptor = FetchDescriptor<Thought>(
                predicate: predicate
            )
            
            // 只获取数量
            return try modelContext.fetchCount(descriptor)
        } catch {
            print("[错误] 获取行动台记录数量失败: \(error)")
            return 0
        }
    }
    
    /// 添加通用背景板记录
    /// - Parameters:
    ///   - content: 记录内容
    ///   - projectId: 项目ID
    ///   - isSystemMessage: 是否为系统消息
    /// - Returns: 新创建的记录
    func addBackboardThought(content: String, projectId: UUID, isSystemMessage: Bool = false) -> Thought {
        if isSystemMessage {
            return addBackboardSystemThought(content: content, projectId: projectId)
        } else {
            return addBackboardUserThought(content: content, projectId: projectId)
        }
    }
} 