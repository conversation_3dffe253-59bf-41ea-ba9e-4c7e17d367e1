import Foundation
import SwiftUI
import SwiftData

/// 项目数据仓库 - 封装所有项目数据访问操作
class ProjectRepository {
    private let modelContext: ModelContext
    private var projectsCache: [Project]? = nil
    
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
    }
    
    // MARK: - 基础CRUD操作
    
    /// 获取所有项目
    func getAllProjects(includeArchived: Bool = false, sortBy: SortDescriptor<Project> = SortDescriptor(\.name)) -> [Project] {
        // 如果缓存存在且不包括归档项目，则使用缓存
        if let cachedProjects = projectsCache, !includeArchived {
            return cachedProjects.filter { !$0.isArchived }
        }
        
        do {
            var predicate: Predicate<Project>? = nil
            
            // 如果不包括归档的项目，则添加过滤条件
            if !includeArchived {
                predicate = #Predicate<Project> { !$0.isArchived }
            }
            
            let descriptor = FetchDescriptor<Project>(
                predicate: predicate,
                sortBy: [sortBy]
            )
            
            let projects = try modelContext.fetch(descriptor)
            
            // 更新缓存
            if !includeArchived {
                projectsCache = projects
            }
            
            return projects
        } catch {
            print("获取项目失败: \(error)")
            return []
        }
    }
    
    /// 获取活跃（未归档）的项目
    func getActiveProjects(sortBy: SortDescriptor<Project> = SortDescriptor(\.name)) -> [Project] {
        return getAllProjects(includeArchived: false, sortBy: sortBy)
    }
    
    /// 根据ID获取特定项目
    func getProjectById(_ projectId: UUID) -> Project? {
        // 首先检查缓存
        if let cachedProjects = projectsCache {
            if let project = cachedProjects.first(where: { $0.id == projectId }) {
                return project
            }
        }
        
        // 如果缓存中没有，从数据库查询
        do {
            let predicate = #Predicate<Project> { project in
                project.id == projectId
            }
            let descriptor = FetchDescriptor<Project>(predicate: predicate)
            let projects = try modelContext.fetch(descriptor)
            return projects.first
        } catch {
            print("通过ID获取项目失败: \(error)")
            return nil
        }
    }
    
    /// 获取归档的项目
    func getArchivedProjects(sortBy: SortDescriptor<Project> = SortDescriptor(\.name)) -> [Project] {
        do {
            let predicate = #Predicate<Project> { $0.isArchived }
            
            let descriptor = FetchDescriptor<Project>(
                predicate: predicate,
                sortBy: [sortBy]
            )
            
            return try modelContext.fetch(descriptor)
        } catch {
            print("获取归档项目失败: \(error)")
            return []
        }
    }
    
    /// 根据ID获取单个项目
    func getProject(byId id: UUID) -> Project? {
        // 使用变量声明，这样可以修改fetchLimit
        var descriptor = FetchDescriptor<Project>(predicate: #Predicate<Project> { project in
            project.id == id
        })
        descriptor.fetchLimit = 1
        
        do {
            let projects = try modelContext.fetch(descriptor)
            return projects.first
        } catch {
            print("获取项目失败: \(error)")
            return nil
        }
    }
    
    /// 根据名称获取项目（不区分大小写）
    func getProjectByName(_ name: String) -> Project? {
        // 首先查找缓存
        if let cachedProjects = projectsCache {
            return cachedProjects.first { $0.name.lowercased() == name.lowercased() }
        }
        
        // 从数据库中查询
        do {
            let descriptor = FetchDescriptor<Project>()
            let projects = try modelContext.fetch(descriptor)
            return projects.first { $0.name.lowercased() == name.lowercased() }
        } catch {
            print("通过名称获取项目失败: \(error)")
            return nil
        }
    }
    
    /// 检查项目名是否已存在（不区分大小写）
    func projectExists(_ name: String) -> Bool {
        return getProjectByName(name) != nil
    }
    
    /// 创建新项目
    /// - Parameters:
    ///   - name: 项目名称
    ///   - emoji: 可选的表情符号
    ///   - color: 可选的颜色
    /// - Returns: 如果项目名已存在则返回 nil，否则返回新创建的项目
    func createProject(name: String, emoji: String? = nil, color: String? = nil) -> Project? {
        // 检查是否已存在相同名称的项目（不区分大小写）
        if projectExists(name) {
            return nil
        }
        
        let project = Project(name: name, emoji: emoji, color: color)
        modelContext.insert(project)
        
        // 清除缓存以便下次获取时包含新项目
        projectsCache = nil
        
        do {
            try modelContext.save()
            return project
        } catch {
            print("保存项目失败: \(error)")
            return nil
        }
    }
    
    /// 更新项目
    /// - Parameter project: 要更新的项目
    /// - Returns: 更新是否成功
    @discardableResult
    func updateProject(_ project: Project) -> Bool {
        // 如果项目名发生变化，需要检查是否与其他项目重名
        if let originalProject = getProjectByName(project.name),
           originalProject.id != project.id {
            return false
        }
        
        project.updatedAt = Date()
        
        // 清除缓存
        projectsCache = nil
        
        do {
            try modelContext.save()
            return true
        } catch {
            print("更新项目失败: \(error)")
            return false
        }
    }
    
    /// 保存项目
    func saveProject(_ project: Project) {
        do {
            // 直接尝试保存项目
            modelContext.insert(project)
            try modelContext.save()
            
            // 清除缓存，确保下次获取项目时重新获取
            projectsCache = nil
        } catch {
            print("保存项目失败: \(error)")
        }
    }
    
    /// 添加项目
    func addProject(_ project: Project) {
        modelContext.insert(project)
        
        // 清除缓存以便下次获取时包含新项目
        projectsCache = nil
        
        do {
            try modelContext.save()
        } catch {
            print("添加项目失败: \(error)")
        }
    }
    
    /// 删除项目及其下所有任务
    func deleteProject(_ project: Project) {
        do {
            // 先删除项目下的所有任务
            let taskRepository = DependencyContainer.taskRepository()
            let tasks = taskRepository.getTasksForProject(project.id)
            for task in tasks {

                DispatchQueue.main.async {
                    // 直接尝试用 ID 删除，仓库内部会处理是否存在的问题
                    taskRepository.deleteTask(withId: task.id)
                }
            }
            
            // 再删除项目
            modelContext.delete(project)
            try modelContext.save()
            
            // 清除缓存，确保下次获取项目时重新获取
            projectsCache = nil
        } catch {
            print("删除项目失败: \(error)")
        }
    }
    
    // MARK: - 业务特定操作
    
    /// 获取指定任务关联的项目
    func getProjectForTask(_ task: Task) -> Project? {
        guard let projectId = task.project else {
            return nil
        }
        
        return getProject(byId: projectId)
    }
    
    /// 获取项目及其任务数量
    func getProjectsWithTasksCount() -> [(project: Project, tasksCount: Int)] {
        let projects = getActiveProjects()
        var result: [(project: Project, tasksCount: Int)] = []
        
        // 先获取所有未完成的任务
        let taskPredicate = #Predicate<Task> { task in
            task.status != "Done"
        }
        
        let taskDescriptor = FetchDescriptor<Task>(predicate: taskPredicate)
        
        do {
            // 获取所有非完成任务
            let tasks = try modelContext.fetch(taskDescriptor)
            
            // 对每个项目，在内存中过滤任务
            for project in projects {
                let projectTasks = tasks.filter { task in 
                    if let taskProject = task.project {
                        return taskProject == project.id
                    }
                    return false
                }
                result.append((project: project, tasksCount: projectTasks.count))
            }
        } catch {
            print("获取项目任务数量失败: \(error)")
            // 遇到错误时，为所有项目设置零任务
            for project in projects {
                result.append((project: project, tasksCount: 0))
            }
        }
        
        // 按任务数量排序
        return result.sorted { $0.tasksCount > $1.tasksCount }
    }
    
    /// 清除缓存
    func clearCache() {
        projectsCache = nil
    }
}
