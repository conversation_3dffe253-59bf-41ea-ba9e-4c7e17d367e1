import Foundation
import SwiftUI
import SwiftData

/// 标签数据仓库 - 封装所有标签数据访问操作
class TagRepository {
    private let modelContext: ModelContext
    private var tagsCache: [Tag]? = nil
    
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
    }
    
    // MARK: - 基础CRUD操作
    
    /// 获取所有标签
    func getAllTags(sortBy: SortDescriptor<Tag> = SortDescriptor(\.name)) -> [Tag] {
        // 如果缓存存在则使用缓存
        if let cachedTags = tagsCache {
            return cachedTags
        }
        
        do {
            let descriptor = FetchDescriptor<Tag>(
                sortBy: [sortBy]
            )
            
            let tags = try modelContext.fetch(descriptor)
            
            // 更新缓存
            tagsCache = tags
            
            return tags
        } catch {
            print("获取标签失败: \(error)")
            return []
        }
    }
    
    /// 根据名称获取标签（不区分大小写）
    func getTagByName(_ name: String) -> Tag? {
        // 首先查找缓存
        if let cachedTags = tagsCache {
            return cachedTags.first { $0.name.lowercased() == name.lowercased() }
        }
        
        // 从数据库中查询
        do {
            // 获取所有标签，然后在内存中进行不区分大小写的比较
            let descriptor = FetchDescriptor<Tag>()
            let tags = try modelContext.fetch(descriptor)
            return tags.first { $0.name.lowercased() == name.lowercased() }
        } catch {
            print("通过名称获取标签失败: \(error)")
            return nil
        }
    }
    
    /// 检查标签名是否已存在（不区分大小写）
    func tagExists(_ name: String) -> Bool {
        return getTagByName(name) != nil
    }
    

    
    /// 创建新标签
    /// - Parameters:
    ///   - name: 标签名称
    ///   - emoji: 可选的表情符号
    ///   - color: 可选的颜色
    /// - Returns: 如果标签名已存在则返回 nil，否则返回新创建的标签
    func createTag(name: String, emoji: String? = nil, color: String? = nil) -> Tag? {
        // 检查是否已存在相同名称的标签（不区分大小写）
        if tagExists(name) {
            return nil
        }
        
        let tag = Tag(name: name, emoji: emoji, color: color)
        modelContext.insert(tag)
        
        // 清除缓存以便下次获取时包含新标签
        tagsCache = nil
        
        do {
            try modelContext.save()
            return tag
        } catch {
            print("保存标签失败: \(error)")
            return nil
        }
    }
    
    /// 更新标签
    /// - Parameter tag: 要更新的标签
    /// - Returns: 更新是否成功
    @discardableResult
    func updateTag(_ tag: Tag) -> Bool {
        // 如果标签名发生变化，需要检查是否与其他标签重名
        if let originalTag = getTagByName(tag.name),
           originalTag.id != tag.id {
            return false
        }
        
        tag.updatedAt = Date()
        
        // 清除缓存
        tagsCache = nil
        
        do {
            try modelContext.save()
            return true
        } catch {
            print("更新标签失败: \(error)")
            return false
        }
        
        do {
            try modelContext.save()
        } catch {
            print("更新标签失败: \(error)")
        }
    }
    
    /// 保存标签
    func saveTag(_ tag: Tag) {
        // 如果标签还不在上下文中，则添加到上下文
        if tag.modelContext == nil {
            modelContext.insert(tag)
        }
        
        tag.updatedAt = Date()
        
        // 清除缓存
        tagsCache = nil
        
        do {
            try modelContext.save()
        } catch {
            print("保存标签失败: \(error)")
        }
    }
    
    /// 删除标签
    func deleteTag(_ tag: Tag) {
        modelContext.delete(tag)
        
        // 清除缓存
        tagsCache = nil
        
        do {
            try modelContext.save()
        } catch {
            print("删除标签失败: \(error)")
        }
    }
    
    // MARK: - 业务特定操作
    
    /// 获取指定任务关联的标签对象
    func getTagsForTask(_ task: Task) -> [Tag] {
        let allTags = getAllTags()
        let taskTagNames = task.tags
        
        return allTags.filter { taskTagNames.contains($0.name) }
    }
    
    /// 获取热门标签（使用次数最多的）
    func getPopularTags(limit: Int = 10) -> [Tag] {
        let allTags = getAllTags(sortBy: SortDescriptor(\.usageCount, order: .reverse))
        return Array(allTags.prefix(limit))
    }
    
    /// 获取最近使用的标签
    func getRecentlyUsedTags(limit: Int = 10) -> [Tag] {
        let allTags = getAllTags()
        
        // 过滤掉没有lastUsed日期的标签，并按lastUsed排序
        let tagsWithDate = allTags.filter { $0.lastUsed != nil }
            .sorted { ($0.lastUsed ?? Date.distantPast) > ($1.lastUsed ?? Date.distantPast) }
        
        return Array(tagsWithDate.prefix(limit))
    }
    
    /// 获取标签及其任务数量
    func getTagsWithTasksCount() -> [(tag: Tag, count: Int)] {
        let allTags = getAllTags()
        var result: [(tag: Tag, count: Int)] = []
        
        // 只获取一次所有未完成的任务，避免重复查询
        do {
            // 获取所有非完成任务
            let tasks = try modelContext.fetch(FetchDescriptor<Task>(predicate: #Predicate<Task> { $0.status != "Done" }))
            
            // 对每个标签，在内存中计算任务数量
            for tag in allTags {
                // 在内存中过滤包含该标签的任务
                let count = tasks.filter { $0.tags.contains(tag.name) }.count
                result.append((tag: tag, count: count))
            }
        } catch {
            print("获取标签任务数量失败: \(error)")
            // 如果查询失败，所有标签返回零任务
            for tag in allTags {
                result.append((tag: tag, count: 0))
            }
        }
        
        // 按任务数量排序
        return result.sorted { $0.count > $1.count }
    }
    
    /// 清除缓存
    func clearCache() {
        tagsCache = nil
    }
}
