import SwiftData
import SwiftUI

/// 全局依赖注入容器
@MainActor
class DependencyContainer {
    // 单例模式
    static let shared = DependencyContainer()
    
    // 私有属性 - 初始为nil，等待外部设置
    private var modelContainer: ModelContainer?
    private var modelContext: ModelContext?
    
    // 标记是否已初始化
    private var isInitialized = false
    
    // 仓储实例 - 需要在modelContext设置后才能初始化
    private var _taskRepository: TaskRepository?
    private var _projectRepository: ProjectRepository?
    private var _tagRepository: TagRepository?
    private var _thoughtRepository: ThoughtRepository?
    private var _inviteRepository: InviteRepository?
    
    // 不依赖ModelContext的服务可以直接初始化
    let _toastManager = ToastManager.shared
    let _confettiManager = ConfettiManager.shared
    let _versionManager = VersionManager.shared
    let _apiVersionValidator = APIVersionValidator.shared
    let _umengService = UMengService.shared
    let _navigationStateManager = NavigationStateManager.shared
    let _actionSheetManager = ActionSheetManager.shared
    
    // 私有初始化方法
    private init() {
        // 不创建任何容器，等待外部配置
    }
    
    // 添加配置方法，接收外部创建的ModelContainer
    func configureWith(container: ModelContainer) {
        // 防止重复配置
        guard !isInitialized else {
            return
        }
        
        self.modelContainer = container
        self.modelContext = container.mainContext
        
        // 初始化所有仓库
        initializeRepositories()
        
        // 标记为已初始化
        isInitialized = true
        
    }
    
    // 初始化所有仓库
    private func initializeRepositories() {
        guard let context = modelContext else {
            fatalError("[DependencyContainer] 尝试在没有ModelContext的情况下初始化仓库")
        }
        
        self._taskRepository = TaskRepository(modelContext: context)
        self._projectRepository = ProjectRepository(modelContext: context)
        self._tagRepository = TagRepository(modelContext: context)
        self._inviteRepository = InviteRepository(modelContext: context)
        self._thoughtRepository = ThoughtRepository(modelContext: context)
        
    }
    
    // 重置方法
    func reset() {
        guard let context = modelContext else {
            print("[DependencyContainer] 警告：尝试在未配置时重置仓库")
            return
        }
        
        // 重新初始化仓储
        self._taskRepository = TaskRepository(modelContext: context)
        self._projectRepository = ProjectRepository(modelContext: context)
        self._tagRepository = TagRepository(modelContext: context)
        self._inviteRepository = InviteRepository(modelContext: context)
        self._thoughtRepository = ThoughtRepository(modelContext: context)
    }
    
    // 提供仓储访问方法，带有状态检查
    nonisolated var taskRepository: TaskRepository {
        MainActor.assumeIsolated {
            guard isInitialized, let repo = _taskRepository else {
                fatalError("[DependencyContainer] 尝试在未初始化时访问taskRepository")
            }
            return repo
        }
    }
    
    nonisolated var projectRepository: ProjectRepository {
        MainActor.assumeIsolated {
            guard isInitialized, let repo = _projectRepository else {
                fatalError("[DependencyContainer] 尝试在未初始化时访问projectRepository")
            }
            return repo
        }
    }
    
    nonisolated var tagRepository: TagRepository {
        MainActor.assumeIsolated {
            guard isInitialized, let repo = _tagRepository else {
                fatalError("[DependencyContainer] 尝试在未初始化时访问tagRepository")
            }
            return repo
        }
    }
    
    nonisolated var thoughtRepository: ThoughtRepository {
        MainActor.assumeIsolated {
            guard isInitialized, let repo = _thoughtRepository else {
                fatalError("[DependencyContainer] 尝试在未初始化时访问thoughtRepository")
            }
            return repo
        }
    }
    
    nonisolated var inviteRepository: InviteRepository {
        MainActor.assumeIsolated {
            guard isInitialized, let repo = _inviteRepository else {
                fatalError("[DependencyContainer] 尝试在未初始化时访问inviteRepository")
            }
            return repo
        }
    }
    
    // 非隔离访问其他服务（无需检查初始化状态）
    nonisolated var toastManager: ToastManager {
        MainActor.assumeIsolated {
            _toastManager
        }
    }
    
    nonisolated var confettiManager: ConfettiManager {
        MainActor.assumeIsolated {
            _confettiManager
        }
    }
    
    nonisolated var versionManager: VersionManager {
        MainActor.assumeIsolated {
            _versionManager
        }
    }
    
    nonisolated var umengService: UMengService {
        MainActor.assumeIsolated {
            _umengService
        }
    }
    
    nonisolated var navigationStateManager: NavigationStateManager {
        MainActor.assumeIsolated {
            _navigationStateManager
        }
    }
    
    nonisolated var actionSheetManager: ActionSheetManager {
        MainActor.assumeIsolated {
            _actionSheetManager
        }
    }
    
    // 静态方法
    static nonisolated func taskRepository() -> TaskRepository {
        MainActor.assumeIsolated { 
            guard shared.isInitialized, let repo = shared._taskRepository else {
                fatalError("[DependencyContainer] 尝试在未初始化时访问taskRepository")
            }
            return repo
        }
    }
    
    static nonisolated func projectRepository() -> ProjectRepository {
        MainActor.assumeIsolated { 
            guard shared.isInitialized, let repo = shared._projectRepository else {
                fatalError("[DependencyContainer] 尝试在未初始化时访问projectRepository")
            }
            return repo
        }
    }
    
    static nonisolated func tagRepository() -> TagRepository {
        MainActor.assumeIsolated { 
            guard shared.isInitialized, let repo = shared._tagRepository else {
                fatalError("[DependencyContainer] 尝试在未初始化时访问tagRepository")
            }
            return repo
        }
    }
    
    static nonisolated func inviteRepository() -> InviteRepository {
        MainActor.assumeIsolated { 
            guard shared.isInitialized, let repo = shared._inviteRepository else {
                fatalError("[DependencyContainer] 尝试在未初始化时访问inviteRepository")
            }
            return repo
        }
    }
    
    static nonisolated func thoughtRepository() -> ThoughtRepository {
        MainActor.assumeIsolated { 
            guard shared.isInitialized, let repo = shared._thoughtRepository else {
                fatalError("[DependencyContainer] 尝试在未初始化时访问thoughtRepository")
            }
            return repo
        }
    }
    
    static nonisolated func toastManager() -> ToastManaging {
        MainActor.assumeIsolated { shared._toastManager }
    }
    
    static nonisolated func confettiManager() -> ConfettiManaging {
        MainActor.assumeIsolated { shared._confettiManager }
    }
    
    static nonisolated func versionManager() -> VersionManaging {
        MainActor.assumeIsolated { shared._versionManager }
    }
    
    static nonisolated func umengService() -> UMengService {
        MainActor.assumeIsolated { shared._umengService }
    }
    
    static nonisolated func navigationStateManager() -> NavigationStateManaging {
        MainActor.assumeIsolated { shared._navigationStateManager }
    }
    
    static nonisolated func actionSheetManager() -> ActionSheetManager {
        MainActor.assumeIsolated { shared._actionSheetManager }
    }
    
    static nonisolated func apiVersionValidator() -> APIVersionValidator {
        return APIVersionValidator.shared
    }
}

// MARK: - 依赖注入协议
protocol DependencyInjectable {
    nonisolated var taskRepository: TaskRepository { get }
    nonisolated var projectRepository: ProjectRepository { get }
    nonisolated var tagRepository: TagRepository { get }
    nonisolated var inviteRepository: InviteRepository { get }
    nonisolated var thoughtRepository: ThoughtRepository { get }
    nonisolated var toastManager: ToastManager { get }
    nonisolated var confettiManager: ConfettiManager { get }
    nonisolated var versionManager: VersionManager { get }
    nonisolated var umengService: UMengService { get }
    nonisolated var navigationStateManager: NavigationStateManager { get }
    nonisolated var actionSheetManager: ActionSheetManager { get }
}

// MARK: - 默认依赖注入实现
extension DependencyContainer: DependencyInjectable {}

// MARK: - 环境键扩展
private struct TaskRepositoryKey: EnvironmentKey {
    static var defaultValue: TaskRepository {
        DependencyContainer.taskRepository()
    }
}

private struct ProjectRepositoryKey: EnvironmentKey {
    static var defaultValue: ProjectRepository {
        DependencyContainer.projectRepository()
    }
}

private struct TagRepositoryKey: EnvironmentKey {
    static var defaultValue: TagRepository {
        DependencyContainer.tagRepository()
    }
}

private struct InviteRepositoryKey: EnvironmentKey {
    static var defaultValue: InviteRepository {
        DependencyContainer.inviteRepository()
    }
}

private struct ThoughtRepositoryKey: EnvironmentKey {
    static var defaultValue: ThoughtRepository {
        DependencyContainer.thoughtRepository()
    }
}

private struct ToastManagerKey: EnvironmentKey {
    static var defaultValue: ToastManaging {
        DependencyContainer.toastManager()
    }
}

private struct ConfettiManagerKey: EnvironmentKey {
    static var defaultValue: ConfettiManaging {
        DependencyContainer.confettiManager()
    }
}

private struct VersionManagerKey: EnvironmentKey {
    static var defaultValue: VersionManaging {
        DependencyContainer.versionManager()
    }
}

private struct UMengServiceKey: EnvironmentKey {
    static var defaultValue: UMengService {
        DependencyContainer.umengService()
    }
}

private struct NavigationStateManagerKey: EnvironmentKey {
    static var defaultValue: NavigationStateManaging {
        DependencyContainer.navigationStateManager()
    }
}

private struct ActionSheetManagerKey: EnvironmentKey {
    static var defaultValue: ActionSheetManager {
        DependencyContainer.actionSheetManager()
    }
}

// MARK: - 环境变量扩展
extension EnvironmentValues {
    var taskRepository: TaskRepository {
        get { self[TaskRepositoryKey.self] }
        set { self[TaskRepositoryKey.self] = newValue }
    }
    
    var projectRepository: ProjectRepository {
        get { self[ProjectRepositoryKey.self] }
        set { self[ProjectRepositoryKey.self] = newValue }
    }
    
    var tagRepository: TagRepository {
        get { self[TagRepositoryKey.self] }
        set { self[TagRepositoryKey.self] = newValue }
    }
    
    var inviteRepository: InviteRepository {
        get { self[InviteRepositoryKey.self] }
        set { self[InviteRepositoryKey.self] = newValue }
    }
    
    var thoughtRepository: ThoughtRepository {
        get { self[ThoughtRepositoryKey.self] }
        set { self[ThoughtRepositoryKey.self] = newValue }
    }
    
    var toastManager: ToastManaging {
        get { self[ToastManagerKey.self] }
        set { self[ToastManagerKey.self] = newValue as! ToastManager }
    }
    
    var confettiManager: ConfettiManaging {
        get { self[ConfettiManagerKey.self] }
        set { self[ConfettiManagerKey.self] = newValue as! ConfettiManager }
    }
    
    var versionManager: VersionManaging {
        get { self[VersionManagerKey.self] }
        set { self[VersionManagerKey.self] = newValue as! VersionManager }
    }
    
    var umengService: UMengService {
        get { self[UMengServiceKey.self] }
        set { self[UMengServiceKey.self] = newValue }
    }
    
    var navigationStateManager: NavigationStateManaging {
        get { self[NavigationStateManagerKey.self] }
        set { self[NavigationStateManagerKey.self] = newValue }
    }
    
    var actionSheetManager: ActionSheetManager {
        get { self[ActionSheetManagerKey.self] }
        set { self[ActionSheetManagerKey.self] = newValue }
    }
}

// MARK: - 视图扩展
extension View {
    /// 注入默认仓储依赖
    func injectDefaultRepositories() -> some View {
        self.environment(\.taskRepository, DependencyContainer.taskRepository())
            .environment(\.projectRepository, DependencyContainer.projectRepository())
            .environment(\.tagRepository, DependencyContainer.tagRepository())
            .environment(\.inviteRepository, DependencyContainer.inviteRepository())
            .environment(\.thoughtRepository, DependencyContainer.thoughtRepository())
            .environment(\.toastManager, DependencyContainer.toastManager())
            .environment(\.confettiManager, DependencyContainer.confettiManager())
            .environment(\.versionManager, DependencyContainer.versionManager())
            .environment(\.navigationStateManager, DependencyContainer.navigationStateManager())
            .environment(\.actionSheetManager, DependencyContainer.actionSheetManager())
    }
    
    /// 注入默认仓储依赖
    func injectDependencies(
        taskRepository: TaskRepository = DependencyContainer.taskRepository(),
        projectRepository: ProjectRepository = DependencyContainer.projectRepository(),
        tagRepository: TagRepository = DependencyContainer.tagRepository(),
        inviteRepository: InviteRepository = DependencyContainer.inviteRepository(),
        thoughtRepository: ThoughtRepository = DependencyContainer.thoughtRepository(),
        toastManager: ToastManaging = DependencyContainer.toastManager(),
        confettiManager: ConfettiManaging = DependencyContainer.confettiManager(),
        versionManager: VersionManaging = DependencyContainer.versionManager(),
        navigationStateManager: NavigationStateManaging = DependencyContainer.navigationStateManager(),
        actionSheetManager: ActionSheetManager = DependencyContainer.actionSheetManager()
    ) -> some View {
        self
            .environment(\.taskRepository, taskRepository)
            .environment(\.projectRepository, projectRepository)
            .environment(\.tagRepository, tagRepository)
            .environment(\.inviteRepository, inviteRepository)
            .environment(\.thoughtRepository, thoughtRepository)
            .environment(\.toastManager, toastManager)
            .environment(\.confettiManager, confettiManager)
            .environment(\.versionManager, versionManager)
            .environment(\.navigationStateManager, navigationStateManager)
            .environment(\.actionSheetManager, actionSheetManager)
    }
}
