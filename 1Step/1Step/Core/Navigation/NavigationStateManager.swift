import Foundation
import SwiftUI

/// 定义可能的导航路径目的地
public enum NavigationDestination: Hashab<PERSON>, Codable {
    case projectList
    case projectDetail(projectID: UUID)
    case projectNotes(projectID: UUID)
    case tagList
    case tagDetail(tag: String)
    case settingsPage
    case statisticsPage
    case searchResults(query: String)
    case taskDetail(taskID: UUID)
    // 新增 Browse Section Cases
    case inboxTasks
    case waitingTasks
    case smbTasks
    case doneTasks
    case nextTasks
    // 可以根据需要扩展更多目的地...
}

/// 定义导航历史记录项
struct NavigationHistoryItem: Codable, Identifiable {
    var id = UUID()
    let tab: String
    let destination: String
    let timestamp: Date
    
    init(tab: String, destination: String) {
        self.tab = tab
        self.destination = destination
        self.timestamp = Date()
    }
}

/// 导航状态管理协议
public protocol NavigationStateManaging {
    // 当前选中的标签
    var selectedTab: Tab { get set }
    
    // 保存导航状态 - 更新方法签名
    func updateAndPersistNavigationState(for tab: Tab, destination: NavigationDestination)
    
    // 获取导航状态
    func getNavigationState(for tab: Tab) -> NavigationDestination?
    
    // 清除导航状态
    func clearNavigationState(for tab: Tab)
    
    // 返回到上一个标签
    func goBackToPreviousTab()
    
    // 重置导航状态
    func resetNavigationState()
    
    // 日志
    func logNavigation(message: String)
    
    // 统一处理导航事件
    func handleNavigationEvent(source: Tab, destination: NavigationDestination, context: [String: Any])
    
    // 持久化所有导航状态
    func persistNavigationStates()
}

/// 导航状态管理实现
class NavigationStateManager: NavigationStateManaging, ObservableObject {
    /// 单例
    static let shared = NavigationStateManager()
    
    /// 当前选中的标签
    @Published var selectedTab: Tab = .main
    
    /// 各标签的导航状态
    @Published private var navigationStates: [Tab: NavigationDestination] = [:]
    
    /// 上一个选中的标签
    private var previousTab: Tab = .main
    
    /// 导航历史记录
    @Published private var navigationHistory: [NavigationHistoryItem] = []
    
    /// 最大历史记录数量
    private let maxHistoryItems = 50
    
    /// 持久化键
    private let selectedTabKey = "selectedTab"
    private let navigationStatesKey = "navigationStates"
    
    /// 防抖动相关属性
    private var lastUpdateTimes: [Tab: Date] = [:]
    private let minimumUpdateInterval: TimeInterval = 0.15 // 150毫秒的防抖动间隔
    
    // MARK: - 协调器实例
    
    /// 浏览协调器 - 延迟初始化避免循环引用
    lazy var browseCoordinator: BrowseCoordinator = {
        BrowseCoordinator(navigationStateManager: self)
    }()
    
    /// 焦点视图协调器 - 延迟初始化
    lazy var focusCoordinator: FocusCoordinator = {
        FocusCoordinator(navigationStateManager: self)
    }()
    
    /// 下一步视图协调器 - 延迟初始化
    lazy var nextActionsCoordinator: NextActionsCoordinator = {
        NextActionsCoordinator(navigationStateManager: self)
    }()
    
    // Helper function to convert string to Tab, handling old values
    private func tabFromString(_ stringValue: String) -> Tab? {
        if let newTab = Tab(rawValue: stringValue) { // Try with current Tab enum (only matches "main")
            return newTab
        } else {
            // If it's a known old tab rawValue, map to .main
            // Here, if not "main", treat as unknown/old, return .main so app can start
            print("Warning: Restoring unknown or old tab value '\(stringValue)' from UserDefaults. Defaulting to Tab.main.")
            return .main 
        }
    }
    
    private init() {
        // 从UserDefaults恢复上次的标签状态
        if let savedTabString = UserDefaults.standard.string(forKey: selectedTabKey),
           let savedTab = tabFromString(savedTabString) {
            self.selectedTab = savedTab
            self.previousTab = savedTab
            logNavigation(message: "从持久化存储恢复标签: \(savedTab)")
        }
        
        // 初始化防抖动时间戳
        for tab in Tab.allCases {
            lastUpdateTimes[tab] = Date.distantPast
        }
        
        // 恢复导航状态
        restoreNavigationStates()
        
        // 添加应用生命周期观察者
        setupAppLifecycleObservers()
    }
    
    private func setupAppLifecycleObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillResignActive),
            name: UIApplication.willResignActiveNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
    }
    
    @objc private func appWillResignActive() {
        logNavigation(message: "应用即将进入非活动状态，保存导航状态")
        persistNavigationStates()
    }
    
    @objc private func appDidEnterBackground() {
        logNavigation(message: "应用进入后台，保存导航状态")
        persistNavigationStates()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    /// 保存导航状态 - 添加防抖动机制
    func updateAndPersistNavigationState(for tab: Tab, destination: NavigationDestination) {
        // 防抖动：检查同一标签页的更新间隔
        let now = Date()
        if let lastUpdate = lastUpdateTimes[tab], now.timeIntervalSince(lastUpdate) < minimumUpdateInterval {
            logNavigation(message: "忽略过于频繁的导航状态更新[\(tab)]: \(destination.description)")
            return
        }
        
        // 更新时间戳
        lastUpdateTimes[tab] = now
        
        // 更新状态
        navigationStates[tab] = destination
        
        // 添加到历史记录
        addToHistory(tab: tab.rawValue, destination: destination.description)
        
        // 使用异步方式延迟持久化，避免频繁写入和阻塞主线程
        DispatchQueue.global().asyncAfter(deadline: .now() + 0.1) { [weak self] in
            self?.persistNavigationStates()
        }
        
        logNavigation(message: "保存[\(tab)]导航状态: \(destination.description)")
    }
    
    /// 获取导航状态
    func getNavigationState(for tab: Tab) -> NavigationDestination? {
        let state = navigationStates[tab]
        logNavigation(message: "获取[\(tab)]导航状态: \(state?.description ?? "无")")
        return state
    }
    
    /// 清除导航状态
    func clearNavigationState(for tab: Tab) {
        navigationStates[tab] = nil
        persistNavigationStates()
        logNavigation(message: "清除[\(tab)]导航状态")
    }
    
    /// 切换标签
    /// - Parameter tab: 目标标签
    func switchTab(_ tab: Tab) {
        guard tab != selectedTab else { return }

        // 更新标签状态
        previousTab = selectedTab
        selectedTab = tab

        // 保存新的标签选择
        UserDefaults.standard.set(tab.rawValue, forKey: selectedTabKey)

        // 记录导航事件
        logNavigation(message: "切换到标签: \(tab)")

        // 注意：不再在此处调用 persistNavigationStates() 或旧的 saveNavigationState
    }
    
    /// 返回到上一个标签
    func goBackToPreviousTab() {
        switchTab(previousTab)
    }
    
    /// 重置所有导航状态
    func resetNavigationState() {
        navigationStates.removeAll()
        persistNavigationStates()
        logNavigation(message: "重置所有导航状态")
    }
    
    /// 统一处理导航事件
    func handleNavigationEvent(source: Tab, destination: NavigationDestination, context: [String: Any] = [:]) {
        // 保存导航状态
        updateAndPersistNavigationState(for: source, destination: destination)

        // 根据标签分发到对应协调器
        switch source {
        // case .browse: // 旧的 case，可以移除或注释
        //     browseCoordinator.handleNavigationEvent(to: destination, context: context)
        case .main: // 现在我们只有一个 .main case
            focusCoordinator.handleNavigationEvent(to: destination, context: context)
        // case .nextActions: // 旧的 case，可以移除或注释
        //     nextActionsCoordinator.handleNavigationEvent(to: destination, context: context)
        default: // 以防万一，尽管 source 理论上应该总是 .main
            print("Warning: handleNavigationEvent received unexpected source tab: \(source). Defaulting to focusCoordinator.")
            focusCoordinator.handleNavigationEvent(to: destination, context: context)
        }

        logNavigation(message: "处理导航事件: \(source) -> \(destination.description)")
    }
    
    /// 日志输出
    func logNavigation(message: String) {
        #if DEBUG
        //print("[1Step导航] \(message)")
        #endif
    }
    
    // MARK: - 私有辅助方法
    
    /// 添加导航历史记录
    private func addToHistory(tab: String, destination: String) {
        let historyItem = NavigationHistoryItem(tab: tab, destination: destination)
        navigationHistory.insert(historyItem, at: 0)
        
        // 限制历史记录长度
        if navigationHistory.count > maxHistoryItems {
            navigationHistory = Array(navigationHistory.prefix(maxHistoryItems))
        }
    }
    
    /// 持久化所有导航状态
    func persistNavigationStates() {
        do {
            // 将整个字典编码为单个数据
            let data = try JSONEncoder().encode(navigationStates)
            UserDefaults.standard.set(data, forKey: navigationStatesKey)
            logNavigation(message: "成功持久化导航状态字典，共 \(navigationStates.count) 个状态")
        } catch {
            logNavigation(message: "持久化导航状态字典失败: \(error.localizedDescription)")
        }
        // UserDefaults.standard.synchronize() // 移除同步写入，避免阻塞主线程
    }
    
    /// 恢复所有导航状态 - 更新逻辑
    private func restoreNavigationStates() {
        // 从单个键恢复整个字典
        guard let data = UserDefaults.standard.data(forKey: navigationStatesKey) else {
            logNavigation(message: "未找到持久化的导航状态字典")
            return
        }
        
        do {
            let restoredStates = try JSONDecoder().decode([Tab: NavigationDestination].self, from: data)
            navigationStates = restoredStates
            logNavigation(message: "恢复所有导航状态完成，共 \(restoredStates.count) 个状态")
        } catch {
            logNavigation(message: "恢复导航状态字典失败: \(error.localizedDescription)")
            // 如果恢复失败，可以选择清空状态或保留现有（可能为空）的状态
            navigationStates = [:]
        }
    }
    
    /// 获取所有保存的导航状态
    func getAllNavigationStates() -> [Tab: NavigationDestination] {
        return navigationStates
    }
}

// 扩展NavigationDestination以提供更好的描述
extension NavigationDestination {
    var description: String {
        switch self {
        case .projectList:
            return "项目列表"
        case .projectDetail(let projectID):
            return "项目详情(\(projectID))"
        case .projectNotes(let projectID):
            return "项目笔记(\(projectID))"
        case .tagList:
            return "标签列表"
        case .tagDetail(let tag):
            return "标签(\(tag))"
        case .settingsPage:
            return "设置页面"
        case .statisticsPage:
            return "统计页面"
        case .searchResults(let query):
            return "搜索结果(\(query))"
        case .taskDetail(let taskID):
            return "任务详情(\(taskID))"
        case .inboxTasks:
            return "收件箱任务"
        case .waitingTasks:
            return "等待任务"
        case .smbTasks:
            return "SMB任务"
        case .doneTasks:
            return "已完成任务"
        case .nextTasks:
            return "下一步任务"
        }
    }
} 