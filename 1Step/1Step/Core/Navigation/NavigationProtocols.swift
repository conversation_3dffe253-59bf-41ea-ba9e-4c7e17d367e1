import SwiftUI

/// 基础导航协调器协议
protocol NavigationCoordinator: ObservableObject {
    /// 当前导航路径
    var path: NavigationPath { get set }
    
    /// 是否正在恢复导航状态
    var isRestoringNavigation: Bool { get set }
    
    /// 导航状态管理器引用
    var navigationStateManager: NavigationStateManaging { get }
    
    /// 关联的标签页
    var associatedTab: Tab { get }
    
    /// 尝试恢复之前保存的导航状态
    func restoreNavigationIfNeeded()
    
    /// 清除导航路径
    func clearNavigation()
    
    /// 获取当前导航路径的最后一个目的地
    func getLastDestinationFromPath() -> NavigationDestination?
}

/// 导航恢复能力
protocol NavigationRestorable {
    /// 恢复导航状态
    func restoreNavigation(to destination: NavigationDestination)
}

/// 导航事件处理能力
protocol NavigationEventHandling {
    /// 处理导航事件
    func handleNavigationEvent(to destination: NavigationDestination, context: [String: Any])
}

/// 导航路由能力
protocol NavigationRouting {
    /// 导航到指定目的地
    func navigateToDestination(_ destination: NavigationDestination)
    
    /// 返回到根视图
    func navigateToRoot()
}

/// 扩展的协调器协议，组合多种导航能力
typealias FullNavigationCoordinator = NavigationCoordinator & NavigationRestorable & NavigationEventHandling & NavigationRouting

