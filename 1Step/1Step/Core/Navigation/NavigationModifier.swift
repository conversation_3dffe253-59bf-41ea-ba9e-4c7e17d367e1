import SwiftUI

// MARK: - View Modifier for Navigation

struct NavigationStackModifier<Coordinator: NavigationCoordinator>: ViewModifier {
    @StateObject var coordinator: Coordinator
    // @State private var hasRestoredNavigation = false // Temporarily unused
    // @State private var lastPathChangeTime = Date.distantPast // Temporarily unused
    
    // Access repositories from environment
    @Environment(\.taskRepository) private var taskRepository
    @Environment(\.projectRepository) private var projectRepository
    @Environment(\.tagRepository) private var tagRepository

    func body(content: Content) -> some View {
        content // 直接使用 content
                .navigationDestination(for: NavigationDestination.self) { destination in
                    // Build the view directly here
                    destinationView(for: destination)
        }
        // Keep the outer .environmentObject(coordinator)
        .environmentObject(coordinator)
        // 使用视图ID保持导航状态，防止重建
        .id("\(coordinator.associatedTab)_NavigationContext") // ID 的意义可能改变，但暂时保留
        // 监听导航路径变化，确保状态同步，添加防抖动
        // .onChange(of: coordinator.path) { oldPath, newPath in
        //     print("Modifier: Coordinator path changed from: \(oldPath) to: \(newPath)")
        //     // ALL LOGIC INSIDE HERE COMMENTED OUT FOR NOW
        //     /*
        //     let now = Date()
        //     guard now.timeIntervalSince(lastPathChangeTime) > 0.1 else {
        //         return
        //     }
        //     lastPathChangeTime = now
        //     
        //     if newPath.isEmpty {
        //         coordinator.navigationStateManager.clearNavigationState(for: coordinator.associatedTab)
        //         coordinator.navigationStateManager.logNavigation(message: "导航路径变为空，已清除\(coordinator.associatedTab)的导航状态")
        //     } 
        //     else if oldPath.count > newPath.count {
        //         if let destination = coordinator.getLastDestinationFromPath() {
        //             coordinator.navigationStateManager.updateAndPersistNavigationState(
        //                 for: coordinator.associatedTab, 
        //                 destination: destination
        //             )
        //             coordinator.navigationStateManager.logNavigation(message: "通过返回按钮更新\(coordinator.associatedTab)的导航状态到: \(destination.description)")
        //         }
        //     }
        //     */
        // }
        // .onAppear {
        //     print("Modifier: onAppear for \(coordinator.associatedTab)")
        //     // if !hasRestoredNavigation {
        //     //     coordinator.restoreNavigationIfNeeded() // COMMENTED OUT
        //     //     hasRestoredNavigation = true
        //     //     coordinator.navigationStateManager.logNavigation(message: "首次出现时恢复\(coordinator.associatedTab)的导航状态")
        //     // }
        // }
        // .onReceive(NotificationCenter.default.publisher(for: UIApplication.didBecomeActiveNotification)) { _ in
        //     print("Modifier: didBecomeActive for \(coordinator.associatedTab)")
        //     // if hasRestoredNavigation {
        //     //     DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
        //     //         coordinator.restoreNavigationIfNeeded() // COMMENTED OUT
        //     //         coordinator.navigationStateManager.logNavigation(message: "应用激活后恢复\(coordinator.associatedTab)的导航状态")
        //     //     }
        //     // }
        // }
    }
    
    // Add the view building logic here
    @ViewBuilder
    private func destinationView(for destination: NavigationDestination) -> some View {
        // Wrap logging in an immediately-executing closure to avoid ViewBuilder issues
        // let _ = { // CLEANED
        //     let coordinatorPathCodableDescription: String = coordinator.path.codable.map { String(describing: $0) } ?? \"is nil\"
        //     print(\"[Modifier] Building destinationView for: \\\\(destination.description)\")
        //     print(\"  Coordinator path count at build time: \\\\(coordinator.path.count)\")
        //     print(\"  Coordinator path codable representation: \\\\(coordinatorPathCodableDescription)\")
        // }()

        // This switch statement was previously in BaseCoordinator.view(for:)
        // We need access to repositories here
        switch destination {
        case .projectList:
            ProjectView(
                projectRepository: projectRepository,
                taskRepository: taskRepository,
                tagRepository: tagRepository
            )
        case .projectDetail(let projectID):
             ProjectDetailViewWrapper(
                 projectID: projectID,
                 taskRepository: taskRepository,
                 projectRepository: projectRepository,
                 tagRepository: tagRepository
             )
         case .projectNotes(let projectID):
            // TODO: Replace with actual ProjectNotesPageViewWrapper or view
            Text("项目笔记页面 (ID: \(projectID))")
        case .tagList:
            TagView(
                tagRepository: tagRepository
            )
        case .tagDetail(let tag):
             // TODO: Replace with actual TagDetailView
             // Need to fetch Tag object first
            Text("标签详情: \(tag)")
        case .settingsPage:
            SettingsView()
        case .taskDetail(let taskID):
             // TODO: Replace with actual TaskDetailView
             // Need to fetch Task object first
             Text("任务详情页面 (ID: \(taskID))")
        case .inboxTasks:
             InboxView(
                taskRepository: self.taskRepository,
                projectRepository: self.projectRepository,
                tagRepository: self.tagRepository
             )
        case .waitingTasks:
             WaitingView(
                taskRepository: self.taskRepository,
                projectRepository: self.projectRepository,
                tagRepository: self.tagRepository
             )
        case .smbTasks:
             SMBView(
                taskRepository: self.taskRepository,
                projectRepository: self.projectRepository,
                tagRepository: self.tagRepository
             )
        case .doneTasks:
             DoneView(
                taskRepository: self.taskRepository,
                projectRepository: self.projectRepository,
                tagRepository: self.tagRepository
             )
        case .nextTasks:
             NextActionsView(
                taskRepository: self.taskRepository,
                projectRepository: self.projectRepository,
                tagRepository: self.tagRepository
             )
        case .statisticsPage:
             Text("统计页面")
         case .searchResults(let query):
             Text("搜索结果: \(query)")
        // Note: No default case needed if NavigationDestination is fully covered
        }
    }
}

// MARK: - View Extension

extension View {
    func withNavigation<Coordinator: NavigationCoordinator>(coordinator: Coordinator) -> some View {
        self.modifier(NavigationStackModifier(coordinator: coordinator))
    }
} 