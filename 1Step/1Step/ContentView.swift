//
//  ContentView.swift
//  1Step
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/3/24.
//

import SwiftUI
import SwiftData
// UIKit 可能不再需要，除非 FocusView 内部直接使用

// 临时的 Tab 枚举定义，以帮助解决编译错误
public enum Tab: String, CaseIterable, Codable {
    case main // 或者可以叫 focus, 代表主流程
}

struct ContentView: View {
    // MARK: - 环境与状态
    
    // navigationStateManager 和 navigationManager 不再直接用于 Tab 切换
    // @Environment(\.navigationStateManager) private var navigationStateManager
    // @ObservedObject private var navigationManager: NavigationStateManager
    
    @Binding var activeShortcut: AppShortcutType?
    
    // TaskFormCoordinator 暂时保留，其触发方式后续可能调整
    @StateObject private var formCoordinator = TaskFormCoordinator.shared
    
    // MARK: - 初始化
    
    init(activeShortcut: Binding<AppShortcutType?>) {
        // 初始化不再需要设置 navigationManager for TabView
        // let manager = DependencyContainer.navigationStateManager()
        // self._navigationManager = ObservedObject(wrappedValue: manager as! NavigationStateManager)
        self._activeShortcut = activeShortcut
    }
    
    // MARK: - 视图主体
    
    var body: some View {
        FocusView() // 直接返回 FocusView
        // .withNavigation(coordinator: ...) // 之前 FocusView 的 coordinator 注入方式可能需要审视
                                        // 如果 FocusView 现在完全依赖其自身的 NavigationView，
                                        // 并且不再需要外部 coordinator 来管理 Tab 切换后的深层链接，
                                        // 这个修饰符可能就不需要了。
                                        // 暂时移除，如有问题再具体分析。

        // TabView 相关代码全部移除
        // .id("MainTabView")
        // .onChange(of: navigationManager.selectedTab) { ... }
        
        // .environment(\.eventBus, EventBus.shared) // 保留，FocusView 或其子视图可能需要
        // .withActionSheet() // 保留，FocusView 或其子视图可能需要
        .onAppear {
            // navigationManager.logNavigation(message: "ContentView出现...") // 移除或修改
            print("ContentView (now a FocusView wrapper) appeared.")
        }
        .onChange(of: activeShortcut) { _, newShortcut in
            if let shortcut = newShortcut {
                handleShortcut(shortcut)
                activeShortcut = nil
            }
        }
    }
    
    // MARK: - 快捷方式处理
    private func handleShortcut(_ shortcut: AppShortcutType) {
        print("[ContentView] Handling shortcut: \(shortcut)")
        switch shortcut {
        case .createTask:
            // 恢复任务创建功能
            TaskFormCoordinator.shared.showForm()
        }
    }
}

// MARK: - 预览提供器

struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView(activeShortcut: .constant(nil))
            .injectDefaultRepositories() // 保留，用于注入 Repository 依赖
            // .withThemeMode() // 如果有，保留
            // .withToast() // 如果有，保留
            // .withConfetti() // 如果有，保留
            // .withUMengService() // 如果有，保留
            // .environment(\.eventBus, EventBus.shared) // 如果有，保留
    }
}

