import SwiftUI

/// 开发者菜单视图 - 仅在开发模式下显示的管理工具
struct DeveloperMenuView: View {
    // MARK: - 环境依赖
    @Environment(\.dismiss) private var dismiss
    @Environment(\.inviteRepository) private var inviteRepository
    @Environment(\.toastManager) private var toastManager
    @Environment(\.taskRepository) private var taskRepository
    
    // MARK: - 状态属性
    @State private var showDemoLoadConfirm = false
    
    // MARK: - 主视图
    var body: some View {
        NavigationView {
            List {
                Section(header: Text("数据操作")) {
                    Button("加载示例数据") {
                        showDemoLoadConfirm = true
                    }
                    
                    Button("清除登录状态") {
                        clearLoginState()
                        dismiss()
                    }
                }
                
                Section(header: Text("信息")) {
                    Text("版本: \(Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "未知")")
                    Text("构建: \(Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "未知")")
                    Text("设备ID: \(inviteRepository.getLocalInviteStatus().deviceId)")
                        .font(.system(size: 14))
                        .foregroundColor(.secondary)
                }
            }
            .navigationTitle("开发者菜单")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
            .alert("加载示例数据", isPresented: $showDemoLoadConfirm) {
                Button("取消", role: .cancel) { }
                Button("确认") {
                    // 使用Repository加载示例数据
                    taskRepository.loadDemoData(forceLoad: true)
                }
            } message: {
                Text("这将加载所有示例项目、标签和行动，但不会影响您现有的数据。是否继续？")
            }
        }
    }
    
    // MARK: - 辅助方法
    
    /// 清除登录状态
    private func clearLoginState() {
        // 1. 重置邀请验证状态
        let currentStatus = inviteRepository.getLocalInviteStatus()
        let newStatus = DeviceInviteStatus(
            isVerified: false,
            deviceId: currentStatus.deviceId,
            usedInviteCode: nil,
            verifiedAt: nil,
            ownInviteCodes: currentStatus.ownInviteCodes
        )
        inviteRepository.saveLocalInviteStatus(newStatus)
        
        // 2. 清除用户协议同意状态
        UserDefaults.standard.set(false, forKey: "hasAcceptedPrivacyPolicy")
        
        // 3. 显示操作成功提示
        toastManager.show(
            "已清除登录状态，下次启动应用将需要重新验证",
            type: .success,
            position: .top,
            action: nil,
            onDismiss: nil
        )
    }
}

// MARK: - 预览
#Preview {
    DeveloperMenuView()
} 