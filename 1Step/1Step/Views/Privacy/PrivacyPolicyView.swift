import SwiftUI
import WebKit

/// 隐私政策弹窗 - 在首次启动应用时显示，用于获取用户同意
struct PrivacyPolicyView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.openURL) private var openURL
    @StateObject private var umengService = UMengService.shared
    @State private var showUserAgreement = false
    @State private var showPrivacyPolicy = false
    @State private var userAgreementContent: AttributedString = AttributedString("")
    @State private var privacyPolicyContent: AttributedString = AttributedString("")
    
    var onAccepted: (() -> Void)?
    var onRejected: (() -> Void)?
    private let umengAppKey: String
    
    // 标记是否是仅展示模式，用于在设置中查看完整协议
    private let displayMode: Bool
    
    init(umengAppKey: String, onAccepted: (() -> Void)? = nil, onRejected: (() -> Void)? = nil, displayMode: Bool = false) {
        self.umengAppKey = umengAppKey
        self.onAccepted = onAccepted
        self.onRejected = onRejected
        self.displayMode = displayMode
    }
    
    var body: some View {
        if displayMode {
            // 仅展示模式，直接显示协议内容
            ScrollView {
                if let policyURL = Bundle.main.url(forResource: "PrivacyPolicy", withExtension: "md"),
                   let policyData = try? Data(contentsOf: policyURL),
                   let policyString = String(data: policyData, encoding: .utf8) {
                    MarkdownWebView(markdownString: policyString)
                        .frame(minHeight: UIScreen.main.bounds.height * 0.8)
                        .padding(.horizontal, 8)
                } else {
                    VStack(spacing: 16) {
                        Text("无法加载隐私政策")
                            .font(.headline)
                        Text("文件位置：Resources/PrivacyPolicy.md")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                }
            }
            .navigationTitle("隐私政策")
            .navigationBarTitleDisplayMode(.inline)
        } else {
            // 常规模式，显示弹窗让用户同意
            VStack(spacing: 16) {
                Text("欢迎使用1Step")
                    .font(.system(size: 17, weight: .semibold))
                    .padding(.top, 20)
                
                Text("为了提供更好的服务，请阅读并同意")
                    .font(.system(size: 15))
                    .foregroundColor(.secondary)
                
                HStack(spacing: 4) {
                    Button("《用户协议》") {
                        showUserAgreement = true
                    }
                    .font(.system(size: 15))
                    .foregroundColor(.accentColor)
                    
                    Text("和")
                        .font(.system(size: 15))
                        .foregroundColor(.secondary)
                    
                    Button("《隐私政策》") {
                        showPrivacyPolicy = true
                    }
                    .font(.system(size: 15))
                    .foregroundColor(.accentColor)
                }
                
                Button(action: handleAccept) {
                    Text("同意并继续")
                        .frame(maxWidth: .infinity)
                        .frame(height: 36)
                        .background(Color.accentColor)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                }
                .font(.system(size: 15, weight: .medium))
                .padding(.horizontal, 16)
                .padding(.top, 8)
                
                Button(action: handleReject) {
                    Text("不同意")
                        .font(.system(size: 14))
                        .foregroundColor(.secondary.opacity(0.8))
                }
                .padding(.bottom, 16)
            }
            .frame(width: 280)
            .background(Color(.systemBackground))
            .cornerRadius(14)
            .onAppear {
                loadMarkdownContents()
            }
            .sheet(isPresented: $showUserAgreement) {
                NavigationView {
                    ScrollView {
                        if let agreementURL = Bundle.main.url(forResource: "UserAgreement", withExtension: "md"),
                           let agreementData = try? Data(contentsOf: agreementURL),
                           let agreementString = String(data: agreementData, encoding: .utf8) {
                            MarkdownWebView(markdownString: agreementString)
                                .frame(minHeight: UIScreen.main.bounds.height * 0.8)
                                .padding(.horizontal, 8)
                        } else {
                            VStack(spacing: 16) {
                                Text("无法加载用户协议")
                                    .font(.headline)
                                Text("文件位置：Resources/UserAgreement.md")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            .padding()
                        }
                    }
                    .navigationTitle("用户协议")
                    .navigationBarTitleDisplayMode(.inline)
                    .toolbar {
                        ToolbarItem(placement: .navigationBarTrailing) {
                            Button("关闭") { showUserAgreement = false }
                        }
                    }
                }
            }
            .sheet(isPresented: $showPrivacyPolicy) {
                NavigationView {
                    ScrollView {
                        if let policyURL = Bundle.main.url(forResource: "PrivacyPolicy", withExtension: "md"),
                           let policyData = try? Data(contentsOf: policyURL),
                           let policyString = String(data: policyData, encoding: .utf8) {
                            MarkdownWebView(markdownString: policyString)
                                .frame(minHeight: UIScreen.main.bounds.height * 0.8)
                                .padding(.horizontal, 8)
                        } else {
                            VStack(spacing: 16) {
                                Text("无法加载隐私政策")
                                    .font(.headline)
                                Text("文件位置：Resources/PrivacyPolicy.md")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            .padding()
                        }
                    }
                    .navigationTitle("隐私政策")
                    .navigationBarTitleDisplayMode(.inline)
                    .toolbar {
                        ToolbarItem(placement: .navigationBarTrailing) {
                            Button("关闭") { showPrivacyPolicy = false }
                        }
                    }
                }
            }
        }
    }
    
    /// 加载Markdown文件内容
    private func loadMarkdownContents() {
        // 加载用户协议
        if let agreementURL = Bundle.main.url(forResource: "UserAgreement", withExtension: "md"),
           let agreementData = try? Data(contentsOf: agreementURL),
           let agreementString = String(data: agreementData, encoding: .utf8) {
            do {
                // 配置Markdown解析选项，使用完整解析支持所有格式
                var options = AttributedString.MarkdownParsingOptions()
                options.interpretedSyntax = .full
                userAgreementContent = try AttributedString(markdown: agreementString, options: options)
            } catch {
                print("无法解析用户协议Markdown: \(error)")
                // 回退到基本文本
                userAgreementContent = AttributedString("无法加载用户协议，请稍后再试。")
            }
        } else {
            // 输出更详细的错误信息
            let resourcePaths = Bundle.main.paths(forResourcesOfType: "md", inDirectory: nil)
            print("无法加载用户协议文件，查找到的.md文件: \(resourcePaths)")
            userAgreementContent = AttributedString("无法加载用户协议，请稍后再试。")
        }
        
        // 加载隐私政策
        if let policyURL = Bundle.main.url(forResource: "PrivacyPolicy", withExtension: "md"),
           let policyData = try? Data(contentsOf: policyURL),
           let policyString = String(data: policyData, encoding: .utf8) {
            do {
                // 配置Markdown解析选项，使用完整解析支持所有格式
                var options = AttributedString.MarkdownParsingOptions()
                options.interpretedSyntax = .full
                privacyPolicyContent = try AttributedString(markdown: policyString, options: options)
            } catch {
                print("无法解析隐私政策Markdown: \(error)")
                // 回退到基本文本
                privacyPolicyContent = AttributedString("无法加载隐私政策，请稍后再试。")
            }
        } else {
            // 输出更详细的错误信息
            let resourcePaths = Bundle.main.paths(forResourcesOfType: "md", inDirectory: nil)
            print("无法加载隐私政策文件，查找到的.md文件: \(resourcePaths)")
            privacyPolicyContent = AttributedString("无法加载隐私政策，请稍后再试。")
        }
    }
    
    private func handleAccept() {
        umengService.userAcceptedPrivacyPolicy(appKey: umengAppKey)
        onAccepted?()
        dismiss()
    }
    
    private func handleReject() {
        onRejected?()
        dismiss()
    }
}

// 创建新的用于展示的隐私政策视图
struct PrivacyPolicyDisplayView: View {
    var body: some View {
        ScrollView {
            if let policyURL = Bundle.main.url(forResource: "PrivacyPolicy", withExtension: "md"),
               let policyData = try? Data(contentsOf: policyURL),
               let policyString = String(data: policyData, encoding: .utf8) {
                MarkdownWebView(markdownString: policyString)
                    .frame(minHeight: UIScreen.main.bounds.height * 0.8)
                    .padding(.horizontal, 8)
            } else {
                VStack(spacing: 16) {
                    Text("无法加载隐私政策")
                        .font(.headline)
                    Text("文件位置：Resources/PrivacyPolicy.md")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding()
            }
        }
        .navigationTitle("隐私政策")
        .navigationBarTitleDisplayMode(.inline)
    }
}

// 创建新的用于展示的用户协议视图
struct UserAgreementDisplayView: View {
    var body: some View {
        ScrollView {
            if let agreementURL = Bundle.main.url(forResource: "UserAgreement", withExtension: "md"),
               let agreementData = try? Data(contentsOf: agreementURL),
               let agreementString = String(data: agreementData, encoding: .utf8) {
                MarkdownWebView(markdownString: agreementString)
                    .frame(minHeight: UIScreen.main.bounds.height * 0.8)
                    .padding(.horizontal, 8)
            } else {
                VStack(spacing: 16) {
                    Text("无法加载用户协议")
                        .font(.headline)
                    Text("文件位置：Resources/UserAgreement.md")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding()
            }
        }
        .navigationTitle("用户协议")
        .navigationBarTitleDisplayMode(.inline)
    }
}

// MARK: - 预览
struct PrivacyPolicyView_Previews: PreviewProvider {
    static var previews: some View {
        ZStack {
            Color.black.opacity(0.3)
                .ignoresSafeArea()
            
            PrivacyPolicyView(umengAppKey: "预览AppKey")
        }
        .preferredColorScheme(.light)
    }
} 