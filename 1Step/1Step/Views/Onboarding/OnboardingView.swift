import SwiftUI
import UIKit

struct OnboardingView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var currentPage = 0
    @ObservedObject private var onboardingManager = OnboardingManager.shared
    
    var body: some View {
        ZStack {
            // 背景色
            Color(UIColor.systemBackground).edgesIgnoringSafeArea(.all)
            
            VStack(spacing: 0) {
                // 顶部区域：跳过按钮和页面指示器
                HStack {
                    Spacer()
                    
                    // 跳过按钮(仅在第一页显示)
                    if currentPage < 1 {
                        Button("跳过") {
                            completeOnboarding()
                        }
                        .foregroundColor(.gray)
                        .padding(.trailing, 20)
                        .padding(.top, 16)
                    }
                }
                
                // 页面指示器
                HStack(spacing: 8) {
                    ForEach(0..<2) { index in
                        Circle()
                            .fill(currentPage == index ? 
                                  Color.blue : Color.gray.opacity(0.3))
                            .frame(width: 8, height: 8)
                    }
                }
                .padding(.top, 16)
                .padding(.bottom, 24)
                
                // 滑动页面
                TabView(selection: $currentPage) {
                    // 第一页：欢迎与品牌展示
                    welcomePage.tag(0)
                    
                    // 第二页：核心理念
                    corePrinciplePage.tag(1)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                .animation(.easeInOut, value: currentPage)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                
                // 底部区域：下一步/开始使用按钮（居中）
                HStack {
                    Spacer()
                    
                    Button(currentPage == 1 ? "开始使用" : "下一步") {
                        if currentPage == 1 {
                            completeOnboarding()
                        } else {
                            currentPage += 1
                        }
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 32)
                    .padding(.vertical, 12)
                    .background(Color.blue)
                    .cornerRadius(20)
                    
                    Spacer()
                }
                .padding(.bottom, 48)
            }
        }
        .onAppear {
            // 配置引导页外观
            UIPageControl.appearance().currentPageIndicatorTintColor = UIColor.blue
            UIPageControl.appearance().pageIndicatorTintColor = UIColor.gray.withAlphaComponent(0.3)
        }
    }
    
    // MARK: - 完成引导
    private func completeOnboarding() {
        // 使用OnboardingManager标记引导完成
        onboardingManager.completeOnboarding()
        dismiss()
    }
    
    // MARK: - 页面内容
    
    // 第一页：欢迎与品牌展示
    private var welcomePage: some View {
        VStack(spacing: 40) {
            Spacer()
            
            // 使用应用图标图片
            AppIconView()
                .frame(width: 120, height: 120)
            
            // 欢迎文字
            VStack(spacing: 16) {
                Text("欢迎来到1Step")
                    .font(.system(size: 28, weight: .medium))
                
                Text("为真实人类设计的行动系统")
                    .font(.system(size: 17))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 32)
            }
            
            Spacer()
            Spacer()
        }
    }
    
    // 第二页：核心理念
    private var corePrinciplePage: some View {
        VStack(spacing: 40) {
            Spacer()
            
            // 插画
            Image("onboarding_principle") // 需要创建此图片资源
                .resizable()
                .scaledToFit()
                .frame(height: 180)
            
            // 理念文字
            VStack(spacing: 16) {
                Text("不求完美，只需迈出一步")
                    .font(.system(size: 24, weight: .medium))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 32)
                
                Text("与你的节奏共处")
                    .font(.system(size: 17))
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            Spacer()
        }
    }
}

// 获取应用图标的视图
struct AppIconView: View {
    var body: some View {
        Image(uiImage: getAppIcon())
            .resizable()
            .scaledToFit()
            .cornerRadius(22) // 应用图标圆角
    }
    
    // 获取应用图标
    private func getAppIcon() -> UIImage {
        if let iconsDictionary = Bundle.main.infoDictionary?["CFBundleIcons"] as? [String: Any],
           let primaryIconsDictionary = iconsDictionary["CFBundlePrimaryIcon"] as? [String: Any],
           let iconFiles = primaryIconsDictionary["CFBundleIconFiles"] as? [String],
           let lastIcon = iconFiles.last {
            return UIImage(named: lastIcon) ?? UIImage()
        }
        
        // 如果无法获取，则使用一个预定义的图片
        return UIImage(named: "1024") ?? UIImage()
    }
}

// MARK: - 预览
struct OnboardingView_Previews: PreviewProvider {
    static var previews: some View {
        OnboardingView()
    }
} 