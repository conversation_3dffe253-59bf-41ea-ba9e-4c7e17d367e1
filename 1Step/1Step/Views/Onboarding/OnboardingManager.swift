import SwiftUI

/// 管理应用的新手引导状态和显示逻辑
class OnboardingManager: ObservableObject {
    // 单例模式
    static let shared = OnboardingManager()
    
    // 引导完成状态
    @Published var hasCompletedOnboarding: Bool {
        didSet {
            // 当状态变化时保存到UserDefaults
            UserDefaults.standard.set(hasCompletedOnboarding, forKey: "hasCompletedOnboarding")
        }
    }
    
    // 是否显示引导页
    @Published var shouldShowOnboarding: Bool = false
    
    // 私有初始化方法
    private init() {
        // 从UserDefaults读取已完成状态
        self.hasCompletedOnboarding = UserDefaults.standard.bool(forKey: "hasCompletedOnboarding")
        
        // 如果未完成引导，设置为显示
        if !hasCompletedOnboarding {
            self.shouldShowOnboarding = true
        }
    }
    
    /// 标记引导已完成
    func completeOnboarding() {
        hasCompletedOnboarding = true
        shouldShowOnboarding = false
    }
    
    /// 重置引导状态（用于测试）
    func resetOnboarding() {
        hasCompletedOnboarding = false
        shouldShowOnboarding = true
    }
    
    /// 检查并显示引导（如果需要）
    func checkAndShowOnboarding() {
        shouldShowOnboarding = !hasCompletedOnboarding
    }
}

// 为View添加引导页能力的修饰器
extension View {
    /// 添加引导页支持
    func withOnboarding() -> some View {
        self.modifier(OnboardingViewModifier())
    }
}

// 引导页视图修饰器
struct OnboardingViewModifier: ViewModifier {
    @ObservedObject private var onboardingManager = OnboardingManager.shared
    
    func body(content: Content) -> some View {
        content
            .sheet(isPresented: $onboardingManager.shouldShowOnboarding) {
                OnboardingView()
            }
    }
} 