import SwiftUI
import SwiftData

// Assuming Task, Project, Tag, AppColors, etc. are accessible
// May need import oneStep<PERSON>ore or similar

struct FocusTaskSelectorRowView: View {
    
    let task: Task
    let isSelected: Bool
    let isDisabled: Bool // To grey out if limit reached and not selected
    
    @Environment(\.colorScheme) private var colorScheme

    // Inject repositories to fetch project/tag details
    private let projectRepository: ProjectRepository = DependencyContainer.projectRepository()
    private let tagRepository: TagRepository = DependencyContainer.tagRepository()
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) { // Align to top for potentially multi-line content
            // Selection Indicator
            Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                .font(.system(size: 20)) // Slightly smaller indicator
                .foregroundColor(isSelected ? AppColors.UI.primary(for: colorScheme) : AppColors.UI.secondaryText(for: colorScheme))
                .padding(.top, 2) // Align indicator with first line of text
            
            // Task Content
            VStack(alignment: .leading, spacing: 6) { // Increased spacing
                Text(task.title)
                    .font(.subheadline)
                    .fontWeight(.medium) // Slightly bolder title
                    .foregroundColor(isDisabled ? AppColors.UI.secondaryText(for: colorScheme) : AppColors.UI.primaryText(for: colorScheme))
                    .lineLimit(2)
                
                // --- Add Project/Tags --- 
                if !task.tags.isEmpty || task.project != nil {
                    taskMetadataView
                }
                // ------------------------
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(.horizontal, 12) // Inner padding for card content
        .padding(.vertical, 10)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(colorScheme == .dark ? Color(.systemGray6) : Color.white) // Card background
                .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1) // Subtle shadow
        )
        .opacity(isDisabled ? 0.6 : 1.0) // Use slightly less aggressive opacity for disabled
        .contentShape(Rectangle()) // Make the whole row tappable
    }
    
    // --- Reused/Adapted Metadata View --- 
    private var taskMetadataView: some View {
        HStack {
            // Tags (Limit display for space)
            if !task.tags.isEmpty {
                tagsView
            }
            
            Spacer()
            
            // Project
            projectInfoView
        }
        .font(.caption) // Smaller font for metadata
        .foregroundColor(isDisabled ? AppColors.UI.secondaryText(for: colorScheme) : AppColors.UI.secondaryText(for: colorScheme))
    }

    private var tagsView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 4) {
                ForEach(Array(task.tags.prefix(2)), id: \.self) { tag in // Show max 2 tags
                    Text("#\(tag)") // Add hash for clarity
                        .padding(.horizontal, 5)
                        .padding(.vertical, 2)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(4)
                        .lineLimit(1)
                }
                if task.tags.count > 2 {
                    Text("+\(task.tags.count - 2)")
                        .padding(.horizontal, 5)
                        .padding(.vertical, 2)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(4)
                }
            }
            .frame(height: 18) // Constrain height
        }
    }
    
    private var projectInfoView: some View {
        Group {
            if let projectId = task.project {
                let projectName = getProjectName(for: projectId)
                // Combine two Text views for different colors
                (Text(projectName)
                    .foregroundColor(isDisabled ? AppColors.UI.secondaryText(for: colorScheme) : AppColors.UI.secondaryText(for: colorScheme)) + // Project name color (secondary)
                 Text("!")
                    .foregroundColor(getProjectColor(for: projectId))) // Exclamation mark color (project color)
                    .lineLimit(1)
            }
        }
    }

    // Helper functions (assuming these exist or adapt as needed)
    private func getProjectName(for projectId: UUID) -> String {
        projectRepository.getProject(byId: projectId)?.name ?? ""
    }

    private func getProjectColor(for projectId: UUID) -> Color {
        if let project = projectRepository.getProject(byId: projectId), let hex = project.color {
            return Color(hex: hex)
        }
        return .gray // Default color
    }
    // -------------------------------------
}

// --- Preview Update (if needed) ---
struct FocusTaskSelectorRowView_Previews: PreviewProvider {
    static var previews: some View {
        let exampleTask = Task(title: "一个需要从下一步添加到一步的任务示例，这个标题可能有点长，需要换行显示", status: "NA")
        exampleTask.tags = ["工作", "重要", "紧急"]
        // exampleTask.project = UUID() // Add a dummy project ID if needed for preview

        let previewContainer = try! ModelContainer(for: Task.self, Project.self, Tag.self) // Ensure Project/Tag are included
        
        // Mock Project for preview if needed
        // let mockProject = Project(name: "示例项目", color: "#3498DB")
        // previewContainer.mainContext.insert(mockProject)
        // exampleTask.project = mockProject.id 
        
        return VStack(alignment: .leading, spacing: 10) {
            FocusTaskSelectorRowView(task: exampleTask, isSelected: false, isDisabled: false)
            FocusTaskSelectorRowView(task: exampleTask, isSelected: true, isDisabled: false)
            FocusTaskSelectorRowView(task: Task(title: "另一个任务", status: "NA"), isSelected: false, isDisabled: true)
        }
        .padding()
        .modelContainer(previewContainer)
        .environmentObject(ToastManager.shared) 
    }
} 