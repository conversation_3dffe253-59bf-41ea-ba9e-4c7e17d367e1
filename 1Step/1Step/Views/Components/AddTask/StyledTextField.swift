import SwiftUI
import UIKit

// 自定义UIViewRepresentable来包装UITextField
struct StyledTextField: UIViewRepresentable {
    @Binding var text: String
    var project: String
    var tags: [String]
    var placeholder: String
    var onEditingChanged: (Bool) -> Void = { _ in }
    var onCommit: () -> Void = {}
    var onTextChange: (String) -> Void = { _ in }
    
    // 在回调函数中保存delegate，以便在外部更新时重新设置属性文本
    private let textField = UITextField()
    
    // 焦点状态
    @Binding var isFocused: Bool
    
    init(text: Binding<String>, 
         project: String, 
         tags: [String], 
         placeholder: String,
         isFocused: Binding<Bool>,
         onEditingChanged: @escaping (Bool) -> Void = { _ in },
         onCommit: @escaping () -> Void = {},
         onTextChange: @escaping (String) -> Void = { _ in }) {
        self._text = text
        self.project = project
        self.tags = tags
        self.placeholder = placeholder
        self._isFocused = isFocused
        self.onEditingChanged = onEditingChanged
        self.onCommit = onCommit
        self.onTextChange = onTextChange
    }
    
    func makeUIView(context: Context) -> UITextField {
        textField.delegate = context.coordinator
        textField.placeholder = placeholder
        textField.borderStyle = .none
        textField.backgroundColor = .clear
        textField.autocorrectionType = .no
        textField.returnKeyType = .next
        textField.heightAnchor.constraint(equalToConstant: 44).isActive = true
        
        // 设置属性文本
        updateAttributedText()
        
        // 添加编辑事件监听
        textField.addTarget(context.coordinator, action: #selector(Coordinator.textFieldDidChange(_:)), for: .editingChanged)
        
        return textField
    }
    
    func updateUIView(_ uiView: UITextField, context: Context) {
        // 如果文本已经更新，则更新属性文本
        if uiView.text != text {
            updateAttributedText()
        }
        
        // 保存光标位置
        let cursorPosition = uiView.selectedTextRange
        
        // 确保isFocused状态同步
        if isFocused && !uiView.isFirstResponder {
            uiView.becomeFirstResponder()
        } else if !isFocused && uiView.isFirstResponder {
            uiView.resignFirstResponder()
        }
        
        // 恢复光标位置
        if uiView.isFirstResponder, let position = cursorPosition {
            uiView.selectedTextRange = position
        }
        
        // 获取上一次的文本值
        let lastText = StyledTextField.lastTextValues[uiView] ?? ""
        
        // 确保外部更新时光标在文本末尾
        if text != lastText {
            // 将光标移动到末尾
            let newPosition = uiView.endOfDocument
            uiView.selectedTextRange = uiView.textRange(from: newPosition, to: newPosition)
            // 更新上一次的文本值
            StyledTextField.lastTextValues[uiView] = text
        }
    }
    
    func makeCoordinator() -> Coordinator {
        let coordinator = Coordinator(text: $text, 
                                 isFocused: $isFocused,
                                 onEditingChanged: onEditingChanged,
                                 onCommit: onCommit,
                                 onTextChange: onTextChange)
        return coordinator
    }
    
    // 使用静态字典存储每个TextField实例的上一次文本值
    private static var lastTextValues: [UITextField: String] = [:]
    
    // 更新属性文本
    private func updateAttributedText() {
        let attributedText = NSMutableAttributedString(string: text)
        
        // 应用项目样式
        if !project.isEmpty {
            let projectMarker = "!\(project)"
            if let range = text.range(of: projectMarker) {
                let nsRange = NSRange(range, in: text)
                let projectStyle: [NSAttributedString.Key: Any] = [
                    .backgroundColor: UIColor.systemGray5,
                    .foregroundColor: UIColor.label,
                    .font: UIFont.boldSystemFont(ofSize: UIFont.labelFontSize),
                ]
                attributedText.addAttributes(projectStyle, range: nsRange)
                
                // 添加左右间距
                if nsRange.location > 0 {
                    let spaceRange = NSRange(location: nsRange.location - 1, length: 1)
                    if spaceRange.location >= 0 && spaceRange.location < attributedText.length {
                        attributedText.addAttribute(.foregroundColor, value: UIColor.clear, range: spaceRange)
                    }
                }
                
                if nsRange.location + nsRange.length < attributedText.length {
                    let spaceRange = NSRange(location: nsRange.location + nsRange.length, length: 1)
                    if spaceRange.location < attributedText.length {
                        attributedText.addAttribute(.foregroundColor, value: UIColor.clear, range: spaceRange)
                    }
                }
            }
        }
        
        // 应用标签样式
        for tag in tags {
            let tagMarker = "#\(tag)"
            if let range = text.range(of: tagMarker) {
                let nsRange = NSRange(range, in: text)
                let tagStyle: [NSAttributedString.Key: Any] = [
                    .backgroundColor: UIColor.systemBlue.withAlphaComponent(0.2),
                    .foregroundColor: UIColor.systemBlue,
                    .font: UIFont.boldSystemFont(ofSize: UIFont.labelFontSize),
                ]
                attributedText.addAttributes(tagStyle, range: nsRange)
                
                // 添加左右间距
                if nsRange.location > 0 {
                    let spaceRange = NSRange(location: nsRange.location - 1, length: 1)
                    if spaceRange.location >= 0 && spaceRange.location < attributedText.length {
                        attributedText.addAttribute(.foregroundColor, value: UIColor.clear, range: spaceRange)
                    }
                }
                
                if nsRange.location + nsRange.length < attributedText.length {
                    let spaceRange = NSRange(location: nsRange.location + nsRange.length, length: 1)
                    if spaceRange.location < attributedText.length {
                        attributedText.addAttribute(.foregroundColor, value: UIColor.clear, range: spaceRange)
                    }
                }
            }
        }
        
        // 设置文本
        textField.attributedText = attributedText
    }
    
    // 协调器
    class Coordinator: NSObject, UITextFieldDelegate {
        @Binding var text: String
        @Binding var isFocused: Bool
        var onEditingChanged: (Bool) -> Void
        var onCommit: () -> Void
        var onTextChange: (String) -> Void
        
        init(text: Binding<String>, 
             isFocused: Binding<Bool>,
             onEditingChanged: @escaping (Bool) -> Void,
             onCommit: @escaping () -> Void,
             onTextChange: @escaping (String) -> Void) {
            self._text = text
            self._isFocused = isFocused
            self.onEditingChanged = onEditingChanged
            self.onCommit = onCommit
            self.onTextChange = onTextChange
        }
        
        // 文本变化事件处理
        @objc func textFieldDidChange(_ textField: UITextField) {
            text = textField.text ?? ""
            // 更新静态字典中的值
            StyledTextField.lastTextValues[textField] = text
            onTextChange(text)
        }
        
        // UITextFieldDelegate方法
        func textFieldDidBeginEditing(_ textField: UITextField) {
            isFocused = true
            onEditingChanged(true)
        }
        
        func textFieldDidEndEditing(_ textField: UITextField) {
            isFocused = false
            onEditingChanged(false)
        }
        
        func textFieldShouldReturn(_ textField: UITextField) -> Bool {
            onCommit()
            return true
        }
    }
}
