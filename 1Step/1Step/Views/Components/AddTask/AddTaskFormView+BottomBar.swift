//
//  AddTaskFormView+BottomBar.swift
//  1Step
//
//  Created on 2025-04-08.
//

import SwiftUI

// 扩展 AddTaskFormView 添加底部栏功能
extension AddTaskFormView {
    // 表单底部栏
    var formBottomBar: some View {
        HStack {
                // 状态选择器 - 使用自定义菜单
                Menu {
                    // 收集箱选项
                    Button(action: {
                        viewModel.taskStatus = TaskStatus.inbox.rawValue
                    }) {
                        HStack {
                            // 使用静态颜色
                            Circle()
                                .foregroundColor(.gray)
                                .frame(width: 12, height: 12)
                            Text(TaskStatus.inbox.description)
                            if viewModel.taskStatus == TaskStatus.inbox.rawValue {
                                Spacer()
                                Image(systemName: "checkmark")
                            }
                    }
                }
                
                // 下一步选项
                Button(action: {
                    viewModel.taskStatus = TaskStatus.na.rawValue
                }) {
                    HStack {
                        // 使用静态颜色
                        Circle()
                            .foregroundColor(.blue)
                            .frame(width: 12, height: 12)
                        Text(TaskStatus.na.description)
                        if viewModel.taskStatus == TaskStatus.na.rawValue {
                            Spacer()
                            Image(systemName: "checkmark")
                        }
                    }
                }
                
                // 一步选项
                Button(action: {
                    viewModel.taskStatus = TaskStatus.doing.rawValue
                }) {
                    HStack {
                        // 使用静态颜色
                        Circle()
                            .foregroundColor(.green)
                            .frame(width: 12, height: 12)
                        Text(TaskStatus.doing.description)
                        if viewModel.taskStatus == TaskStatus.doing.rawValue {
                            Spacer()
                            Image(systemName: "checkmark")
                        }
                    }
                }
                
                // 等待中选项
                Button(action: {
                    viewModel.taskStatus = TaskStatus.waiting.rawValue
                }) {
                    HStack {
                        // 使用静态颜色
                        Circle()
                            .foregroundColor(.orange)
                            .frame(width: 12, height: 12)
                        Text(TaskStatus.waiting.description)
                        if viewModel.taskStatus == TaskStatus.waiting.rawValue {
                            Spacer()
                            Image(systemName: "checkmark")
                        }
                    }
                }
                
                // 将来也许选项
                Button(action: {
                    viewModel.taskStatus = TaskStatus.smb.rawValue
                }) {
                    HStack {
                        // 使用静态颜色
                        Circle()
                            .foregroundColor(.purple)
                            .frame(width: 12, height: 12)
                        Text(TaskStatus.smb.description)
                        if viewModel.taskStatus == TaskStatus.smb.rawValue {
                            Spacer()
                            Image(systemName: "checkmark")
                        }
                    }
                }
            } label: {
                // 菜单按钮显示当前选中的状态
                HStack {
                    let currentStatus = TaskStatus(rawValue: viewModel.taskStatus) ?? .inbox
                    
                    // 根据状态选择静态颜色
                    let statusColor: Color = {
                        switch currentStatus {
                        case .inbox: return .gray
                        case .na: return .blue
                        case .waiting: return .orange
                        case .smb: return .purple
                        case .doing: return .green
                        case .done: return .gray
                        }
                    }()
                    
                    Circle()
                        .foregroundColor(statusColor)
                        .frame(width: 12, height: 12)
                    Text(currentStatus.description)
                    Image(systemName: "chevron.down")
                        .font(.system(size: 14))
                        .foregroundColor(.gray)
                }
                .frame(width: 120)  // 设置固定宽度
                .padding(.horizontal, 8)
            }
            
            Spacer()
            
            // 使用系统按钮并加入跳动效果
            Button(action: {
                // 添加震动反馈
                UIImpactFeedbackGenerator(style: .medium).impactOccurred()
                
                viewModel.addTask()
                
                // 使用全局 ToastManager 显示任务添加消息
                DependencyContainer.toastManager().showSuperLightInfo("已添加行动")
                
                // 重新聚焦到标题输入框
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    NotificationCenter.default.post(name: .requestKeyboardFocus, object: nil)
                }
            }) {
                Image(systemName: "arrow.up")
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(.white)
                    .frame(width: 40, height: 40)
                    .background(viewModel.taskTitle.isEmpty ? AppColors.UI.gray : AppColors.UI.blue)
                    .clipShape(Circle())
            }
            .buttonStyle(BounceButtonStyle())
            .disabled(viewModel.taskTitle.isEmpty)
            .padding(.trailing, 16)
            .padding(.vertical, 12)
        }
    }
}

// 自定义按钮样式，实现弹跃动画效果
struct BounceButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.85 : 1.0)
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .animation(.spring(response: 0.2, dampingFraction: 0.5), value: configuration.isPressed)
    }
}
