import SwiftUI
import Combine
import SwiftData
import UIKit

struct AddTaskFormView: View {
    // MARK: - 依赖
    private let taskRepository: TaskRepository
    private let projectRepository: ProjectRepository
    private let tagRepository: TagRepository
    
    // MARK: - 状态管理
    @ObservedObject var viewModel: TaskFormViewModel
    @Environment(\.colorScheme) private var colorScheme
    @FocusState private var isTitleFocused: Bool
    @FocusState private var isNoteFocused: Bool
    
    // 搜索关键字状态
    @State private var projectSearchText: String = ""
    @State private var tagSearchText: String = ""
    
    @State private var showingProjectPopup = false
    @State private var showingTagPopup = false
    
    // 通知监听者
    @State private var keyboardObserver: AnyCancellable?
    
    // 是否已经请求过聚焦
    @State private var hasFocusRequested = false
    
    // MARK: - 初始化方法
    init(
        viewModel: TaskFormViewModel,
        taskRepository: TaskRepository = DependencyContainer.taskRepository(),
        projectRepository: ProjectRepository = DependencyContainer.projectRepository(),
        tagRepository: TagRepository = DependencyContainer.tagRepository()
    ) {
        self.viewModel = viewModel
        self.taskRepository = taskRepository
        self.projectRepository = projectRepository
        self.tagRepository = tagRepository
        
        // 注意：FocusState不能在初始化方法中设置，会在onAppear中处理
    }
    
    // MARK: - 私有方法
    
    /// 请求键盘聚焦
    private func requestKeyboardFocus() {
        guard !hasFocusRequested else { return }
        
        // 添加震动反馈
        UIImpactFeedbackGenerator(style: .light).impactOccurred()
        
        hasFocusRequested = true
        
        // 先设置聚焦状态
        isTitleFocused = true
        
        // 然后使用延迟来确保键盘显示
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
            // 这是一种巧妙的方式来强制显示键盘 - 先取消再重新激活聚点
            isTitleFocused = false
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                isTitleFocused = true
            }
        }
    }
    
    var body: some View {
        ZStack(alignment: .bottom) {
            // 半透明遮罩层
            Color.black.opacity(0.3)
                .edgesIgnoringSafeArea(.all)
                .onTapGesture {
                    withAnimation {
                        // 使用viewModel的dismiss方法关闭表单，该方法会处理键盘隐藏、重置字段和回调
                        viewModel.dismiss()
                    }
                }
            
            // 主要内容
            VStack(spacing: 0) {
                // 标题栏 - 移除抽屉线，保留高度以维持间距
                HStack {
                    // 移除Rectangle
                }
                .frame(height: 20) // 从44减为20，保留一定空间但不那么大
                
                ZStack(alignment: .top) {
                    // 项目选择器弹窗
                    if showingProjectPopup {
                        PopupSelectorView(
                            type: .project,
                            selectedItem: $viewModel.taskProject,
                            selectedTags: $viewModel.taskTags,
                            isPresented: $showingProjectPopup,
                            initialSearchText: projectSearchText,
                            returnFocusToTitle: { isTitleFocused = true }
                        )
                        .transition(.move(edge: .top).combined(with: .opacity))
                        .zIndex(1)
                    }
                    
                    // 标签选择器弹窗
                    if showingTagPopup {
                        PopupSelectorView(
                            type: .tag,
                            selectedItem: $viewModel.taskProject,
                            selectedTags: $viewModel.taskTags,
                            isPresented: $showingTagPopup,
                            initialSearchText: tagSearchText,
                            returnFocusToTitle: { isTitleFocused = true }
                        )
                        .transition(.move(edge: .top).combined(with: .opacity))
                        .zIndex(1)
                    }
                    
                    // 表单内容
                    VStack(alignment: .leading, spacing: 0) {
                        // 标题输入
                        formTitleField
                            .frame(height: 44)
                        
                        // 备注输入
                        formNotesField
                            .frame(height: 44)
                        
                        // 工具栏
                        formToolbar
                        
                        Divider()
                        
                        // 状态选择和添加按钮
                        formBottomBar
                    }
                }
                .background(AppColors.UI.systemBackground)
                .cornerRadius(16, corners: [.topLeft, .topRight])
                .edgesIgnoringSafeArea(.bottom)
            }
        }
        .onAppear {
            // 当表单出现时，调用键盘聚焦方法
            requestKeyboardFocus()
            

            
            // 添加通知监听者，监听键盘聚焦请求通知
            keyboardObserver = NotificationCenter.default
                .publisher(for: .requestKeyboardFocus)
                .sink { _ in
                    requestKeyboardFocus()
                }
        }
        .onDisappear {
            // 移除通知监听者
            keyboardObserver?.cancel()
        }
    }
    
    // 表单标题输入字段
    var formTitleField: some View {
        // Use standard TextField
        TextField("行动名称", text: $viewModel.taskTitle)
            .font(.headline)
            .focused($isTitleFocused)
            .onSubmit {
                isNoteFocused = true
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .onChange(of: viewModel.taskTitle) { oldValue, newValue in
                 // --- Restore relevant onChange logic --- 
                 
                 // Detect trigger chars to show popups
                 if let lastChar = newValue.last {
                     if lastChar == "!" || lastChar == "！" {
                         // Remove the trigger char before showing popup
                         viewModel.taskTitle = String(newValue.dropLast())
                         projectSearchText = ""
                         withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                             showingProjectPopup = true
                             showingTagPopup = false
                         }
                     } else if lastChar == "#" || lastChar == "＃" { 
                         // Remove the trigger char before showing popup
                         viewModel.taskTitle = String(newValue.dropLast())
                         tagSearchText = ""
                         withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                             showingTagPopup = true
                             showingProjectPopup = false
                         }
                     }
                 }
                 
                 // Update previous title (needed if syncMarkersWithState is ever restored for backspace)
                 // viewModel.previousTaskTitle = newValue
                 // --- End of restored logic ---
            }
        
        // --- Ensure old MarkedTextField code is fully removed or commented out ---
        /*
        MarkedTextField(
            text: $viewModel.taskTitle,
            placeholder: "行动名称",
            isFocused: $isTitleFocused,
            onSubmit: {
                isNoteFocused = true
            },
            onChange: { newText in ... }
        )
        .padding(.horizontal, 16) 
        .padding(.vertical, 8)
        */
    }
    
    // 表单备注输入字段
    var formNotesField: some View {
        return TextField("描述", text: $viewModel.taskNotes, axis: .vertical)
            .font(.subheadline)
            .foregroundColor(viewModel.taskNotes.isEmpty ? AppColors.UI.gray : AppColors.UI.primaryText)
            .padding(.horizontal, 16) // Standard padding, should be fine
            .padding(.vertical, 8)   // Standard padding, should be fine
            .focused($isNoteFocused)
    }
    
    // 表单工具栏
    var formToolbar: some View {
        HStack(spacing: 16) {
            // 项目按钮
            Button(action: {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                    showingProjectPopup = true
                    showingTagPopup = false
                }
            }) {
                HStack(spacing: 4) {
                    Image(systemName: "folder")
                        .foregroundColor(AppColors.UI.gray)
                    
                    // 显示项目名称或默认文本
                    if viewModel.taskProject == nil {
                        Text("项目")
                            .foregroundColor(AppColors.UI.gray)
                            .font(.subheadline)
                    } else {
                        Text(viewModel.getProjectName(for: viewModel.taskProject))
                            .foregroundColor(AppColors.UI.primaryText)
                            .font(.subheadline)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(AppColors.UI.gray.opacity(0.2))
                            .cornerRadius(4)
                    }
                }
            }
            
            // 标签按钮
            Button(action: {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                    showingTagPopup = true
                    showingProjectPopup = false
                }
            }) {
                HStack(spacing: 4) {
                    Image(systemName: "tag")
                        .foregroundColor(AppColors.UI.gray)
                    
                    // 显示标签或默认文本
                    if viewModel.taskTags.isEmpty {
                        Text("标签")
                            .foregroundColor(AppColors.UI.gray)
                            .font(.subheadline)
                    } else {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 8) {
                                ForEach(viewModel.taskTags, id: \.self) { tag in
                                    Text(tag)
                                        .foregroundColor(AppColors.UI.primaryText)
                                        .font(.subheadline)
                                        .padding(.horizontal, 6)
                                        .padding(.vertical, 2)
                                        .background(AppColors.UI.gray.opacity(0.2))
                                        .cornerRadius(4)
                                }
                            }
                        }
                        .frame(maxWidth: 150)
                    }
                }
            }
            
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
    }
    
    // 提示信息浮层
    var formToastOverlay: some View {
        EmptyView()
    }
    
    // 添加按钮的操作
    private func addTaskAction() {
        // 添加震动反馈
        UIImpactFeedbackGenerator(style: .medium).impactOccurred()
        
        viewModel.addTask()
        
        // 使用全局 ToastManager 显示任务添加消息
        DependencyContainer.toastManager().showSuperLightInfo("已添加行动")
        
        // 注意：不在这里清空输入内容，因为 viewModel.addTask() 中的 resetTaskFields() 已经处理了
        // 重新聚焦到标题输入框
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            NotificationCenter.default.post(name: .requestKeyboardFocus, object: nil)
        }
    }
}
