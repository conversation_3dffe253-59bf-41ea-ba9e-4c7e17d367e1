import Foundation
import SwiftUI
import Combine
import SwiftData

/// 任务表单视图模型
/// 专门用于处理任务表单的逻辑
class TaskFormViewModel: ObservableObject {
    // MARK: - 依赖注入
    var taskManager: TaskManager
    
    // 回调函数
    var onTaskAdded: ((Task, String) -> Void)?
    var onDismiss: (() -> Void)?
    
    // MARK: - 发布属性
    @Published var taskTitle: String = ""
    @Published var taskNotes: String = ""
    @Published var previousTaskTitle: String = "" // 用于跟踪上一次的标题值
    
    // 项目相关
    @Published var taskProject: UUID? = nil
    
    // 标签相关
    @Published var taskTags: [String] = []
    
    // 状态相关
    @Published var taskStatus: String = "inbox" // 默认状态为收集箱
    
    // MARK: - 初始化方法
    init(taskManager: TaskManager = DependencyContainer.taskManager()) {
        self.taskManager = taskManager
        // 默认状态为收集箱
        self.taskStatus = "inbox"
    }
    
    /// 初始化方法，带有初始状态
    init(initialStatus: String, taskManager: TaskManager = DependencyContainer.taskManager()) {
        self.taskManager = taskManager
        // 显式设置初始状态，覆盖默认值
        self.taskStatus = initialStatus
    }
    
    // MARK: - 公共方法
    /// 添加任务
    func addTask() {
        // 检查是否是笔记（以 - 开头）
        if taskTitle.hasPrefix("-") {
            addNote()
            return
        }
        
        // Let taskTitle be the pure title entered by the user
        let cleanedTitle = taskTitle.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 将状态转换为正确的大小写格式
        let correctedStatus = convertToProperCaseStatus(taskStatus)
        
        guard !cleanedTitle.isEmpty else {
            return 
        }
        
        let newTask = taskManager.addTask(
            title: cleanedTitle,
            status: correctedStatus, // 使用修正后的状态字符串
            notes: taskNotes,
            project: taskProject,
            tags: taskTags
        )
        
        // 根据任务状态显示不同的成功提示
        let message: String
        switch taskStatus {
        case "inbox":
            message = "已添加到收集箱"
        case "na":
            message = "已添加到下一步"
        case "waiting":
            message = "已添加到等待中"
        case "smb":
            message = "已添加到未来也许"
        default:
            message = "已添加任务"
        }
        
        // 重置字段
        resetTaskFields()
        
        // 回调，传递任务和消息
        onTaskAdded?(newTask, message)
        
        // 发送任务添加完成通知
        TaskFormCoordinator.shared.notifyTaskAdded()
    }
    
    /// 添加笔记
    private func addNote() {
        // 去掉开头的 "-" 并解析笔记内容
        var content = taskTitle.dropFirst().trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 检查是否包含标题（第一行）
        var title = ""
        if let firstLineEnd = content.firstIndex(of: "\n") {
            title = String(content[..<firstLineEnd]).trimmingCharacters(in: .whitespacesAndNewlines)
            content = String(content[firstLineEnd...]).trimmingCharacters(in: .whitespacesAndNewlines)
        } else {
            // 如果没有换行，则整行作为标题
            title = content
            content = taskNotes
        }
        
        // 如果笔记内容是空的而任务备注不是空的，则使用任务备注作为笔记内容
        if content.isEmpty && !taskNotes.isEmpty {
            content = taskNotes
        }
        
        // 创建笔记
        let newNote = taskManager.addNote(
            title: title,
            content: content,
            project: taskProject,
            tags: taskTags
        )
        
        // 重置字段
        resetTaskFields()
        
        // 回调
        onTaskAdded?(newNote, "已添加笔记")
        
        // 发送任务添加完成通知
        TaskFormCoordinator.shared.notifyTaskAdded()
    }
    
    /// 隐藏键盘和表单
    func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
        TaskFormCoordinator.shared.hideForm()
    }
    
    /// 重置任务字段
    func resetTaskFields() {
        taskTitle = ""
        taskNotes = ""
        taskProject = nil
        taskTags = []
    }
    
    /// 将任务状态转换为正确的大小写格式
    private func convertToProperCaseStatus(_ status: String) -> String {
        // 将状态字符串转换为小写
        let lowercaseStatus = status.lowercased()
        
        switch lowercaseStatus {
        case "inbox":
            return "Inbox"
        case "na":
            return "NA"
        case "doing":
            return "Doing"
        case "waiting":
            return "Waiting"
        case "smb":
            return "SMB"
        case "done":
            return "Done"
        default:
            print("[TaskFormViewModel日志] 警告: 未知的任务状态 '\(status)'，返回原始值")
            return status
        }
    }
    
    /// 获取状态显示名称
    func getStatusDisplayName(for status: String) -> String {
        switch status.lowercased() {
        case "inbox":
            return "收集箱"
        case "na":
            return "下一步"
        case "doing":
            return "一步"
        case "waiting":
            return "等待中"
        case "smb":
            return "未来也许"
        case "done":
            return "已完成"
        default:
            return status
        }
    }
    
    /// 获取项目名称
    func getProjectName(for projectId: UUID?) -> String {
        guard let projectId = projectId else { return "无项目" }
        
        if let project = taskManager.getProjectById(projectId) {
            return project.name
        } else {
            return "无项目"
        }
    }
    
    /// 关闭表单
    func dismiss() {
        hideKeyboard()
        resetTaskFields()
        onDismiss?()
    }
}
