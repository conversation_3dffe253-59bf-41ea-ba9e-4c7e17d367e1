import SwiftUI
import SwiftData

/// 导入行动视图 - 接近全屏的弹窗轻交互
struct ImportTasksView: View {
    // 数据仓库
    var taskRepository: TaskRepository
    var projectRepository: ProjectRepository
    var tagRepository: TagRepository
    
    // 回调
    var onTasksImported: ([Task]) -> Void
    
    // 环境变量
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    
    // 状态
    @State private var selectedTab: TaskStatus = .inbox
    @State private var searchText = ""
    @State private var selectedTasks: Set<UUID> = []
    @FocusState private var isSearchFocused: Bool
    
    // 导入限制相关
    @State private var maxAllowedTasks: Int = 10
    @State private var currentNATasks: Int = 0
    @State private var remainingSlots: Int = 10
    
    // 行动数据
    @State private var inboxTasks: [Task] = []
    @State private var waitingTasks: [Task] = []
    @State private var smbTasks: [Task] = []
    @State private var isLoading = true
    
    // 初始化方法
    init(
        taskRepository: TaskRepository,
        projectRepository: ProjectRepository,
        tagRepository: TagRepository,
        initialTab: TaskStatus = .inbox,
        onTasksImported: @escaping ([Task]) -> Void
    ) {
        self.taskRepository = taskRepository
        self.projectRepository = projectRepository
        self.tagRepository = tagRepository
        self.onTasksImported = onTasksImported
        self._selectedTab = State(initialValue: initialTab)
    }
    
    var body: some View {
        ZStack {
            NavigationView {
                ZStack {
                    VStack(spacing: 0) {
                        // 搜索栏
                        searchBar
                        
                        // 状态页签
                        tabBar
                        
                        // 行动列表区域 - 使用TabView实现原生滑动
                        TabView(selection: $selectedTab) {
                            // 收集箱
                            Group {
                                if isLoading {
                                    loadingView
                                } else if inboxTasks.isEmpty {
                                    emptyStateView
                                } else {
                                    taskListView(for: .inbox)
                                }
                            }
                            .tag(TaskStatus.inbox)
                            
                            // 等待中
                            Group {
                                if isLoading {
                                    loadingView
                                } else if waitingTasks.isEmpty {
                                    emptyStateView
                                } else {
                                    taskListView(for: .waiting)
                                }
                            }
                            .tag(TaskStatus.waiting)
                            
                            // 未来也许
                            Group {
                                if isLoading {
                                    loadingView
                                } else if smbTasks.isEmpty {
                                    emptyStateView
                                } else {
                                    taskListView(for: .smb)
                                }
                            }
                            .tag(TaskStatus.smb)
                        }
                        .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                        .animation(.easeInOut, value: selectedTab)
                    }
                    
                    // 底部导入按钮
                    VStack {
                        Spacer()
                        ZStack {
                            // 背景
                            Rectangle()
                                .fill(AppColors.UI.systemBackground(for: colorScheme))
                                .frame(height: 80)
                                .shadow(color: .black.opacity(0.1), radius: 3, x: 0, y: -2)
                            
                            // 按钮
                            importButton
                        }
                    }
                }
                .navigationTitle("导入行动到下一步")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button("取消") {
                            dismiss()
                        }
                    }
                }
                .onAppear {
                    loadTasks()
                }
            }
        }
        .withToast()  // 使这个模态视图能够响应Toast通知
    }
    
    // MARK: - 视图组件
    
    // 搜索栏
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("搜索行动、标签或项目", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())
                .autocapitalization(.none)
                .disableAutocorrection(true)
                .focused($isSearchFocused)
                .submitLabel(.done)
                .onSubmit {
                    isSearchFocused = false
                }
            
            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            } else if isSearchFocused {
                Button(action: {
                    isSearchFocused = false
                }) {
                    Text("取消")
                        .foregroundColor(.accentColor)
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(.systemGray6))
        )
        .padding(.horizontal, 16)
        .padding(.top, 8)
        .padding(.bottom, 12)
    }
    
    // 标签栏
    private var tabBar: some View {
        HStack(spacing: 0) {
            tabButton(title: "收集箱", status: .inbox)
            tabButton(title: "等待中", status: .waiting)
            tabButton(title: "未来也许", status: .smb)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(AppColors.UI.systemBackground(for: colorScheme))
    }
    
    // 标签按钮
    private func tabButton(title: String, status: TaskStatus) -> some View {
        Button(action: {
            withAnimation {
                selectedTab = status
            }
        }) {
            VStack(spacing: 6) {
                Text(title)
                    .font(.system(.subheadline, design: .rounded))
                    .fontWeight(selectedTab == status ? .semibold : .regular)
                    .foregroundColor(selectedTab == status ? .primary : .secondary)
                
                // 指示器
                Rectangle()
                    .fill(selectedTab == status ? Color.accentColor : Color.clear)
                    .frame(height: 2)
            }
            .frame(maxWidth: .infinity)
        }
    }
    
    // 加载中视图
    private var loadingView: some View {
        VStack {
            Spacer()
            ProgressView()
                .scaleEffect(1.2)
            Text("加载中...")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .padding(.top, 8)
            Spacer()
        }
    }
    
    // 空状态视图
    private var emptyStateView: some View {
        VStack {
            Spacer()
            
            Image(systemName: emptyStateIcon)
                .font(.system(size: 48))
                .foregroundColor(.secondary.opacity(0.6))
                .padding(.bottom, 16)
            
            Text(emptyStateMessage)
                .font(.headline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Spacer()
        }
        .padding()
    }
    
    // 行动列表视图 - 为每个标签页提供内容
    private func taskListView(for status: TaskStatus) -> some View {
        VStack(spacing: 0) {
            // 添加ScrollView，使内容可滚动且不会被拉伸
            ScrollView {
                // 使用完整的TaskSectionView但禁用点击详情
                TaskSectionView(
                    title: "",
                    tasks: filteredTasks(for: status),
                    status: status.rawValue,
                    taskRepository: taskRepository,
                    projectRepository: projectRepository,
                    tagRepository: tagRepository,
                    titleColor: nil,
                    isExpanded: .constant(true),
                    showExpandButton: false,
                    showEmptyState: true,
                    isBatchMode: true,
                    selectedTasks: selectedTasks,
                    onTaskSelected: { taskId in
                        toggleTaskSelection(taskId)
                    },
                    isSelectionDisabled: remainingSlots <= selectedTasks.count,
                    disabledSelectionMessage: "下一步已经够多了哦",
                    onTaskIntent: { intent in
                        // 导入视图中不需要处理任务意图
                        switch intent {
                        case .delete(let task):
                            print("导入视图不处理删除任务")
                        case .complete(let task):
                            print("导入视图不处理完成任务")
                        case .changeStatus(let task, let newStatus):
                            print("导入视图不处理状态变更")
                        }
                    },
                    customTaskView: { task in
                        // 不再需要手动定义空的 actions
                        // let leadingActions: [TaskSwipeAction] = []
                        // let trailingActions: [TaskSwipeAction] = []
                        
                        return AnyView(
                            TaskCardView(
                                task: task,
                                taskRepository: taskRepository,
                                projectRepository: projectRepository,
                                tagRepository: tagRepository,
                                style: .readOnly, // 使用只读样式
                                // 不需要再传递空的 actions
                                // leadingSwipeActions: leadingActions,
                                // trailingSwipeActions: trailingActions,
                                onTaskIntent: { _ in }, // 提供空闭包以满足非可选参数
                                onExitRequested: { _, _ in } // 提供空闭包以满足非可选参数
                                // onTap 仍然省略
                            )
                        )
                    }
                )
            }
            
            // 为底部按钮留出空间
            Spacer()
                .frame(height: 80)
        }
    }
    
    // 已选行动区域头部
    private var selectedTasksHeader: some View {
        HStack {
            Text("已选行动")
                .font(.system(.subheadline, design: .rounded))
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            Spacer()
            
            // 显示已选数量和可导入上限
            if remainingSlots <= 0 {
                Text("已达上限")
                    .font(.system(.caption, design: .rounded))
                    .foregroundColor(.red)
                    .padding(.trailing, 4)
            } else if selectedTasks.count > remainingSlots {
                Text("\(selectedTasks.count)项 (超出\(selectedTasks.count - remainingSlots)项)")
                    .font(.system(.caption, design: .rounded))
                    .foregroundColor(.red)
            } else {
            Text("\(selectedTasks.count)项")
                .font(.system(.subheadline, design: .rounded))
                .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(AppColors.UI.systemBackground(for: colorScheme))
    }
    
    // 可选行动区域头部
    private func availableTasksHeader(count: Int) -> some View {
        HStack {
            Text("可选行动")
                .font(.system(.subheadline, design: .rounded))
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            Spacer()
            
            Text("\(count)项")
                .font(.system(.subheadline, design: .rounded))
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(AppColors.UI.systemBackground(for: colorScheme))
    }
    
    // 导入按钮
    private var importButton: some View {
        VStack(spacing: 4) {
            // 显示剩余可导入数量
            if remainingSlots <= 0 {
                Text("下一步已达上限")
                    .font(.caption)
                    .foregroundColor(.red)
                    .padding(.top, 4)
            } else if selectedTasks.count > remainingSlots {
                Text("只能导入\(remainingSlots)项 (已选\(selectedTasks.count)项)")
                    .font(.caption)
                    .foregroundColor(.orange)
                    .padding(.top, 4)
            } else if remainingSlots < 3 && remainingSlots > 0 {
                // 计算考虑已选任务后的实际可用槽位
                let availableSlots = remainingSlots - selectedTasks.count
                Text("还可导入\(availableSlots)项")
                    .font(.caption)
                    .foregroundColor(.blue)
                    .padding(.top, 4)
            }
            
        Button {
            importSelectedTasks()
        } label: {
                Text(selectedTasks.isEmpty ? "导入到下一步" : 
                     selectedTasks.count > remainingSlots ? 
                     "导入 \(remainingSlots) 项到下一步" : 
                     "导入 \(selectedTasks.count) 项到下一步")
                .font(.system(.body, design: .rounded))
                .fontWeight(.medium)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                            .fill(selectedTasks.isEmpty || remainingSlots <= 0 ? Color.accentColor.opacity(0.5) : Color.accentColor)
                        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                )
                .padding(.horizontal, 16)
                .padding(.bottom, 16)
        }
            .disabled(selectedTasks.isEmpty || remainingSlots <= 0)
        }
    }
    
    // MARK: - 行动行视图
    
    struct TaskRowView: View {
        var task: Task
        var isSelected: Bool
        var projectRepository: ProjectRepository
        
        @Environment(\.colorScheme) private var colorScheme
        
        var body: some View {
            HStack(spacing: 12) {
                // 选择指示器
                Image(systemName: isSelected ? "checkmark.square.fill" : "square")
                    .font(.system(size: 16))
                    .foregroundColor(isSelected ? .accentColor : .secondary.opacity(0.6))
                
                // 行动内容
                VStack(alignment: .leading, spacing: 4) {
                    Text(task.title)
                        .font(.system(.subheadline, design: .rounded))
                        .lineLimit(1)
                    
                    // 标签和项目
                    HStack(spacing: 8) {
                        // 标签
                        if !task.tags.isEmpty {
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack {
                                    ForEach(task.tags.prefix(2), id: \.self) { tag in
                                        Text(tag)
                                            .font(.caption)
                                            .foregroundColor(AppColors.UI.gray(for: colorScheme))
                                            .padding(.horizontal, 6)
                                            .padding(.vertical, 2)
                                            .background(Color.gray.opacity(0.1))
                                            .cornerRadius(4)
                                    }
                                    
                                    if task.tags.count > 2 {
                                        Text("+\(task.tags.count - 2)")
                                            .font(.caption)
                                            .foregroundColor(AppColors.UI.gray(for: colorScheme))
                                    }
                                }
                            }
                            .frame(height: 22)
                        }
                        
                        Spacer()
                        
                        // 项目
                        if let projectId = task.project,
                           let project = projectRepository.getProjectById(projectId) {
                            HStack(spacing: 0) {
                                // 项目名称（灰色）
                                Text(project.name)
                                    .font(.caption)
                                    .foregroundColor(AppColors.UI.gray(for: colorScheme))
                                
                                // 项目色彩感叹号
                                Text("!")
                                    .font(.caption)
                                    .foregroundColor(Color(hex: project.color ?? "6366F1"))
                            }
                        }
                    }
                }
                
                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .background(isSelected ? (colorScheme == .dark ? Color(.systemGray6) : Color(.systemGray6)) : Color.clear)
            .contentShape(Rectangle())
        }
    }
    
    // MARK: - 项目相关方法
    
    // 获取项目名称
    private func getProjectName(for task: Task) -> String {
        if let projectId = task.project,
           let project = projectRepository.getProjectById(projectId) {
            return project.name
        }
        return ""
    }
    
    // 获取项目颜色
    private func getProjectColor(for task: Task) -> Color {
        if let projectId = task.project,
           let project = projectRepository.getProjectById(projectId),
           let colorHex = project.color {
            return Color(hex: colorHex)
        }
        return .blue
    }
    

    
    // MARK: - 辅助方法
    
    // 获取特定状态的行动
    private func getTasksForStatus(_ status: TaskStatus) -> [Task] {
        switch status {
        case .inbox:
            return inboxTasks
        case .waiting:
            return waitingTasks
        case .smb:
            return smbTasks
        default:
            return []
        }
    }
    
    // 过滤行动（根据搜索条件和选中状态）
    private func filteredTasks(for status: TaskStatus) -> [Task] {
        // 获取当前状态的所有任务
        let tasks = getTasksForStatus(status)
        
        // 如果没有搜索内容，直接返回所有任务
        if searchText.isEmpty {
            return tasks
        }
        
        // 根据搜索内容过滤
        if searchText.hasPrefix("#") {
            // 标签搜索
            let tagQuery = searchText.dropFirst().lowercased()
            return tasks.filter { task in
                task.tags.contains { $0.lowercased().contains(tagQuery) }
            }
        } else if searchText.hasPrefix("!") {
            // 项目搜索
            let projectQuery = searchText.dropFirst().lowercased()
            return tasks.filter { task in
                if let projectId = task.project,
                   let project = projectRepository.getProjectById(projectId) {
                    return project.name.lowercased().contains(projectQuery)
                }
                return false
            }
        } else {
            // 普通搜索 - 同时搜索标题、描述、标签和项目
            let query = searchText.lowercased()
            return tasks.filter { task in
                // 搜索标题
                if task.title.lowercased().contains(query) {
                    return true
                }
                
                // 搜索描述
                if !task.notes.isEmpty && task.notes.lowercased().contains(query) {
                    return true
                }
                
                // 搜索标签
                if task.tags.contains(where: { $0.lowercased().contains(query) }) {
                    return true
                }
                
                // 搜索项目名称
                if let projectId = task.project,
                   let project = projectRepository.getProjectById(projectId),
                   project.name.lowercased().contains(query) {
                    return true
                }
                
                return false
            }
        }
    }
    
    // 空状态图标
    private var emptyStateIcon: String {
        switch selectedTab {
        case .inbox:
            return "tray"
        case .waiting:
            return "hourglass"
        case .smb:
            return "calendar"
        default:
            return "doc.text"
        }
    }
    
    // 空状态消息
    private var emptyStateMessage: String {
        if !searchText.isEmpty {
            return "没有找到匹配的行动"
        }
        
        switch selectedTab {
        case .inbox:
            return "收集箱中没有行动\n可以先添加一些行动到收集箱"
        case .waiting:
            return "等待中没有行动\n可以将行动移入等待状态"
        case .smb:
            return "未来也许没有行动\n可以将不急于处理的行动放在这里"
        default:
            return "没有可导入的行动"
        }
    }
    
    // MARK: - 方法
    
    // 加载行动
    private func loadTasks() {
        isLoading = true
        
        // 加载当前"下一步"任务数量和剩余可导入数量
        let naTasks = taskRepository.getTasksByStatus(TaskStatus.na.rawValue)
        currentNATasks = naTasks.count
        remainingSlots = maxAllowedTasks - currentNATasks
        
        // 模拟加载延迟
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            inboxTasks = taskRepository.getTasksByStatus(TaskStatus.inbox.rawValue)
            waitingTasks = taskRepository.getTasksByStatus(TaskStatus.waiting.rawValue)
            smbTasks = taskRepository.getTasksByStatus(TaskStatus.smb.rawValue)
            
            isLoading = false
        }
    }
    
    // 切换任务选择状态 - 修改为接收ID
    private func toggleTaskSelection(_ taskId: UUID) {
        if selectedTasks.contains(taskId) {
            // 移除选中的任务总是允许的
            selectedTasks.remove(taskId)
        } else {
            // 添加新任务时检查是否已超出可用槽位（考虑已选任务）
            let availableSlots = remainingSlots - selectedTasks.count
            
            if availableSlots <= 0 {
                // 已达上限，显示Toast提示
                // 添加延迟以确保UI已更新
                DispatchQueue.main.async {
                    DependencyContainer.toastManager().showWarning("下一步已经够多了哦")
                }
                return
            }
            // 未达上限，允许添加
            selectedTasks.insert(taskId)
        }
    }
    
    // 导入选中的行动
    private func importSelectedTasks() {
        // 如果已达上限，直接提示无法导入
        if remainingSlots <= 0 {
            DependencyContainer.toastManager().showWarning("下一步已达上限，无法导入")
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                dismiss()
            }
            return
        }
        
        // 合并所有状态的行动
        let allTasks = inboxTasks + waitingTasks + smbTasks
        var tasksToImport = allTasks.filter { selectedTasks.contains($0.id) }
        
        // 限制导入数量不超过剩余可导入数量
        if tasksToImport.count > remainingSlots {
            tasksToImport = Array(tasksToImport.prefix(remainingSlots))
            DependencyContainer.toastManager().showInfo("已导入 \(remainingSlots) 项（达到上限）")
        } else if !tasksToImport.isEmpty {
            // 显示正常导入提示
            DependencyContainer.toastManager().showSuperLightInfo("成功导入 \(tasksToImport.count) 项到下一步")
        } else {
            DependencyContainer.toastManager().showSuperLightInfo("没有任务被导入")
            return
        }
        
        if !tasksToImport.isEmpty {
            // 更新行动状态
            for task in tasksToImport {
                // 替换为直接修改状态并调用通用更新方法
                task.status = TaskStatus.na.rawValue
                taskRepository.updateTask(task)
            }
            
            // 调用回调
            onTasksImported(tasksToImport)
            
            // 短暂延迟后关闭视图
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                dismiss()
            }
        }
    }
}
