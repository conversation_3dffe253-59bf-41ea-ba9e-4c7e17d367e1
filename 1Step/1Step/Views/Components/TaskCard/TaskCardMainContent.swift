import SwiftUI
import SwipeActions

// 用于存储和传递位置信息的PreferenceKey，使用UUID作为键
struct TaskPositionPreferenceKey: PreferenceKey {
    static var defaultValue: [UUID: CGRect] = [:]

    static func reduce(value: inout [UUID: CGRect], nextValue: () -> [UUID: CGRect]) {
        value.merge(nextValue()) { $1 }
    }
}

/// 任务卡片主内容视图
struct TaskCardMainContent: View {
    @Environment(\.colorScheme) private var colorScheme

    // 基本数据
    let task: Task
    let style: TaskCardStyle
    let cardState: CardState
    let project: Project?
    let showCompletionMark: Bool

    // 绑定和回调
    @Binding var activeContext: SwipeContext?
    var onSwipeComplete: (TaskSwipeAction) -> Void
    var onArrangeAction: (CGPoint) -> Void
    var onDeleteAction: () -> Void

    // 状态颜色计算
    private var statusColor: Color {
        // 如果有自定义颜色，优先使用
        if let customColor = style.statusBarColor {
            return customColor
        }

        // 根据行动状态返回对应的颜色
        let status = task.status

        if status == TaskStatus.na.rawValue {
            return AppColors.Status.nextAction(for: colorScheme)
        } else if status == TaskStatus.waiting.rawValue {
            return AppColors.Status.waiting(for: colorScheme)
        } else if status == TaskStatus.inbox.rawValue {
            return AppColors.Status.inbox(for: colorScheme)
        } else if status == TaskStatus.doing.rawValue {
            return AppColors.Status.doing(for: colorScheme)
        } else if status == TaskStatus.smb.rawValue {
            return AppColors.Status.someday(for: colorScheme)
        } else if status == TaskStatus.done.rawValue {
            return AppColors.Status.done(for: colorScheme)
        } else {
            return AppColors.UI.secondaryText(for: colorScheme)
        }
    }

    // 存储卡片位置
    @State private var cardFrame: CGRect = .zero
    @State private var isTracking: Bool = false

    var body: some View {
        SwipeView {
            ZStack {
                // 主卡片内容
                HStack(alignment: .top, spacing: 10) {
                    // 状态条
                    StatusBarView(statusColor: statusColor, showStatusBar: style.showStatusBar)

                    // 行动内容区域
                    VStack(alignment: .leading, spacing: task.status == TaskStatus.done.rawValue ? 2 : 4) {
                        // 行动标题
                        HStack(spacing: 6) {
                            // 已完成任务显示勾选图标
                            if task.status == TaskStatus.done.rawValue {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.gray)
                                    .font(.system(size: 14))
                            }

                            Text(task.title)
                                .font(.subheadline)
                                .foregroundColor(style.titleColor ?? (cardState != .normal ? Color.gray : AppColors.UI.primaryText(for: colorScheme)))
                                .strikethrough(cardState != .normal || task.status == TaskStatus.done.rawValue, color: Color.gray)
                                .fixedSize(horizontal: false, vertical: true)
                                .lineLimit(nil)
                        }

                        // 标签、项目和完成时间
                        HStack {
                            // 标签
                            if style.showTags && !task.tags.isEmpty {
                                TagsBadgesView(tags: task.tags)
                            }

                            Spacer()

                            // 完成时间
                            if style.showCompletedTime && task.status == TaskStatus.done.rawValue {
                                Text(task.completedAt?.formatted(.relative(presentation: .named)) ?? "")
                                    .font(.caption2)
                                    .foregroundColor(AppColors.UI.gray(for: colorScheme))
                            }

                            // 项目
                            if style.showProject && project != nil {
                                ProjectBadgeView(project: project, showProject: style.showProject)
                            }
                        }

                        // 备注（如果有且允许显示）
                        if task.hasNotes && style.showNotes {
                            Text(task.notes.components(separatedBy: "\n").first ?? "")
                                .font(.caption)
                                .foregroundColor(AppColors.UI.secondaryText(for: colorScheme))
                                .lineLimit(1)
                        }
                    }
                    .frame(minHeight: task.status == TaskStatus.done.rawValue ? 36 : 44) // 已完成任务使用更小的高度
                }
                .padding(.vertical, task.status == TaskStatus.done.rawValue ? 6 : 8)
                .padding(.trailing, 10)
                .frame(maxWidth: .infinity, alignment: .leading)
                .background(
                    cardState != .normal ? Color.gray.opacity(0.1) : AppColors.UI.card(for: colorScheme)
                )
                .contentShape(Rectangle())
                .opacity(cardState != .normal ? 0.8 : 1.0)
                // 下一步列表中的卡片只显示下边框
                .overlay(
                    VStack {
                        Spacer()
                        Rectangle()
                            .frame(height: 0.5)
                            .foregroundColor(AppColors.UI.cardBorder(for: colorScheme))
                    }
                )
                .background(GeometryReader { geometry in
                    Color.clear
                        .preference(
                            key: TaskPositionPreferenceKey.self,
                            value: [task.id: geometry.frame(in: .global)]
                        )
                        .onAppear {
                            // 开始跟踪
                            self.isTracking = true
                        }
                        .onDisappear {
                            // 停止跟踪
                            self.isTracking = false
                        }
                })
                .onPreferenceChange(TaskPositionPreferenceKey.self) { positions in
                    if let frame = positions[task.id], isTracking {
                        self.cardFrame = frame
                    }
                }

                // 完成标记
                if showCompletionMark {
                    CompletionMarkView(isVisible: showCompletionMark)
                }
            }
            // 统一阴影效果，与 TaskDetailView 卡片保持一致
            .shadow(
                color: Color.black.opacity(colorScheme == .dark ? 0.3 : 0.05),
                radius: colorScheme == .dark ? 4 : 2,
                x: 0,
                y: colorScheme == .dark ? 2 : 1
            )
        } leadingActions: { context in
            // 添加只读判断
            if !style.isReadOnly { // 只有在非只读时才添加右滑操作
                // --- 右滑操作：完成任务 ---
                if task.status != TaskStatus.done.rawValue {
                    SwipeAction(
                        "完成",
                        systemImage: "checkmark",
                        backgroundColor: .green,
                        highlightOpacity: 1
                    ) {
                        let completeAction = TaskSwipeAction(title: "完成", statusValue: TaskStatus.done.rawValue, icon: "checkmark", color: .green, action: { return true })
                        onSwipeComplete(completeAction)
                    }
                    .allowSwipeToTrigger(true) // 支持滑动到底直接完成
                    .foregroundColor(.white)
                    .font(.caption)
                    .onAppear {
                        DispatchQueue.main.async {
                            self.activeContext = context
                        }
                    }
                }
            }
        } trailingActions: { context in
            // 添加只读判断
            if !style.isReadOnly { // 只有在非只读时才添加右滑操作
                // --- 右侧操作（从右往左滑） ---
                if task.status != TaskStatus.done.rawValue {
                    // 第一级：安排操作
                    SwipeAction(
                        systemImage: "calendar.badge.clock",
                        backgroundColor: .blue,
                        highlightOpacity: 1
                    ) {
                        // 使用存储的卡片位置
                        var position: CGPoint

                        if cardFrame != .zero {
                            // 使用卡片的顶部中点，在其上方20pt显示
                            position = CGPoint(
                                x: UIScreen.main.bounds.width / 2, // 水平居中
                                y: cardFrame.minY - 20 // 垂直在卡片上方20pt
                            )
                        } else {
                            // 默认位置
                            position = CGPoint(
                                x: UIScreen.main.bounds.width / 2,
                                y: UIScreen.main.bounds.height * 0.3
                            )
                        }

                        // 确保不超出屏幕顶部安全区域
                        position.y = max(position.y, 100)

                        // 传递位置给回调
                        onArrangeAction(position)
                        let generator = UIImpactFeedbackGenerator(style: .light)
                        generator.impactOccurred()
                    }
                    .allowSwipeToTrigger(false) // 不允许滑动触发，需要点击
                    .foregroundColor(.white)
                    .font(.title2) // 增加图标大小
                    .onAppear {
                        DispatchQueue.main.async {
                            self.activeContext = context
                        }
                    }

                    // 第二级：删除操作（需要滑动更远）
                    SwipeAction(
                        systemImage: "trash",
                        backgroundColor: .red,
                        highlightOpacity: 1
                    ) {
                        // 删除操作的触觉反馈
                        let generator = UINotificationFeedbackGenerator()
                        generator.notificationOccurred(.warning)

                        onDeleteAction()
                    }
                    .allowSwipeToTrigger(false) // 不允许滑动触发，只能点击删除按钮
                    .foregroundColor(.white)
                    .font(.title2) // 增加图标大小
                }
            }
        }
        // 全局设置 - 基于我们的优化经验
        .swipeActionsStyle(.cascade) // 使用cascade样式，更流畅的体验
        .swipeActionsVisibleStartPoint(0) // 从滑动开始就显示按钮
        .swipeActionsVisibleEndPoint(0) // 直到滑动结束都保持可见
        .swipeActionCornerRadius(14)
        .swipeSpacing(6)
        .swipeActionsMaskCornerRadius(14)
        .swipeMinimumDistance(25) // 进一步减少最小滑动距离，让滑动更敏感
        .swipeActionWidth(120) // 增加按钮宽度，让滑动距离更长，触发范围更大
        .swipeOffsetTriggerAnimation(stiffness: 300, damping: 25) // 增加弹性
        .swipeOffsetCloseAnimation(stiffness: 200, damping: 20) // 更流畅的关闭动画
        .swipeReadyToTriggerPadding(80) // 显著增加触发区域，让完成操作更容易触发
        .swipeMinimumPointToTrigger(100) // 降低触发点，让用户更容易滑动到底完成任务
        .swipeEnableTriggerHaptics(true)
    }
}