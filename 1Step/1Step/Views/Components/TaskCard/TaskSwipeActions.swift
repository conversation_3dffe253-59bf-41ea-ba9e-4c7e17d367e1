import SwiftUI

/// 任务滑动操作模型
struct TaskSwipeAction {
    let title: String
    let statusValue: String?
    let icon: String
    let color: Color
    let action: () -> Bool
    
    /// 创建一个完成操作的滑动操作
    static func completeAction() -> TaskSwipeAction {
        return TaskSwipeAction(
            title: "完成", 
            statusValue: TaskStatus.done.rawValue, 
            icon: "checkmark", 
            color: .green, 
            action: { return true }
        )
    }
    
    /// 创建一个安排操作的滑动操作
    static func arrangeAction() -> TaskSwipeAction {
        return TaskSwipeAction(
            title: "安排", 
            statusValue: "arrange", 
            icon: "calendar.badge.clock", 
            color: .blue, 
            action: { return true }
        )
    }
    
    /// 创建一个删除操作的滑动操作
    static func deleteAction() -> TaskSwipeAction {
        return TaskSwipeAction(
            title: "删除", 
            statusValue: "delete", 
            icon: "trash", 
            color: .red, 
            action: { return true }
        )
    }
    
    /// 创建一个状态切换操作
    static func statusChangeAction(title: String, status: String, icon: String, color: Color) -> TaskSwipeAction {
        return TaskSwipeAction(
            title: title,
            statusValue: status,
            icon: icon,
            color: color,
            action: { return true }
        )
    }
}
