import SwiftUI

// MARK: - 卡片UI组件

/// 状态条组件
struct StatusBarView: View {
    let statusColor: Color
    let showStatusBar: Bool
    
    var body: some View {
        Group {
            if showStatusBar {
                RoundedRectangle(cornerRadius: 1)
                    .frame(width: 3, height: nil)
                    .foregroundColor(statusColor)
            }
        }
    }
}

/// 项目标签组件
struct ProjectBadgeView: View {
    let project: Project?
    let showProject: Bool
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        Group {
            if showProject && project != nil {
                HStack(spacing: 0) {
                    // 项目名称（灰色）
                    Text(project?.name ?? "")
                        .font(.caption)
                        .foregroundColor(AppColors.UI.gray(for: colorScheme))
                    
                    // 项目色彩感叹号
                    Text("!")
                        .font(.caption)
                        .foregroundColor(
                            project?.color != nil ? Color(hex: project!.color!) : AppColors.UI.blue(for: colorScheme)
                        )
                }
            }
        }
    }
}

/// 标签显示组件
struct TagsBadgesView: View {
    let tags: [String]
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack {
                ForEach(tags, id: \.self) { tag in
                    Text(tag)
                        .font(.caption)
                        .foregroundColor(AppColors.UI.gray(for: colorScheme))
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(AppColors.UI.tagBackground(for: colorScheme))
                        .cornerRadius(4)
                }
            }
        }
        .frame(height: 24)
        .allowsHitTesting(false) // 禁用标签的交互，防止左滑冲突
    }
}

/// 置顶标记组件
struct PinIndicatorView: View {
    let isPinned: Bool
    let showPinIndicator: Bool
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        Group {
            if isPinned && showPinIndicator {
                HStack {
                    Spacer()
                    Image(systemName: "pin.fill")
                        .font(.system(size: 12))
                        .foregroundColor(AppColors.UI.warning(for: colorScheme).opacity(0.8))
                        .padding(8)
                }
            }
        }
    }
}

/// 完成标记组件
struct CompletionMarkView: View {
    let isVisible: Bool
    
    var body: some View {
        ZStack {
            // 底层光晕效果
            Circle()
                .fill(Color.green.opacity(0.15))
                .frame(width: 54, height: 54)
                .scaleEffect(isVisible ? 1.1 : 0.7)
            
            // 对勾图标
            Image(systemName: "checkmark.circle.fill")
                .foregroundColor(.green)
                .font(.system(size: 45, weight: .semibold))
                .shadow(color: Color.black.opacity(0.1), radius: 1, x: 0, y: 1)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .opacity(0.9)
        .scaleEffect(isVisible ? 1 : 0.7)
    }
}

/// 自定义操作按钮组件
struct ActionButtonView: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.system(size: 18))
                    .frame(width: 28, height: 28)
                
                Text(title)
                    .foregroundColor(.primary)
                    .font(.system(size: 16, weight: .medium))
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 14))
                    .foregroundColor(.gray)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 14)
            .contentShape(Rectangle())
        }
        .buttonStyle(BorderlessButtonStyle())
        .background(Color(UIColor.systemBackground))
        .overlay(
            VStack {
                Spacer()
                Divider()
            }
        )
    }
} 