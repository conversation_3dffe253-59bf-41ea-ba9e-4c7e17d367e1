import SwiftUI

/// 任务卡片样式配置
struct TaskCardStyle {
    // 基本属性
    var isReadOnly: Bool = false
    
    // 显示选项
    var showStatusBar: Bool = true
    var showPinIndicator: Bool = true
    var showTags: Bool = true
    var showProject: Bool = true
    var showNotes: Bool = true
    var showCompletedTime: Bool = true
    
    // 颜色配置
    var titleColor: Color? = nil
    var statusBarColor: Color? = nil
    
    // 预设样式
    static var standard: TaskCardStyle {
        TaskCardStyle()
    }
    
    /// 创建一个只读样式
    static var readOnly: TaskCardStyle {
        var style = TaskCardStyle()
        style.isReadOnly = true
        return style
    }
    
    /// 创建一个简洁样式，只显示状态和标题
    static var minimal: TaskCardStyle {
        var style = TaskCardStyle()
        style.showPinIndicator = false
        style.showTags = false
        style.showProject = false
        style.showNotes = false
        return style
    }
    
    /// 创建一个已完成任务样式
    static var completed: TaskCardStyle {
        var style = TaskCardStyle()
        style.showNotes = false
        style.showTags = false
        style.titleColor = .gray
        return style
    }
}
