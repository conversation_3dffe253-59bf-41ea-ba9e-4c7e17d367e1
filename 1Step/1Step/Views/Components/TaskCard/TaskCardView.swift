import SwiftUI
import SwiftData
import SwipeActions

/// 通用行动卡片视图
struct TaskCardView: View {
    @Environment(\.colorScheme) private var colorScheme
    
    // 必需属性
    var task: Task
    var taskRepository: TaskRepository
    
    // 可选依赖项
    var projectRepository: ProjectRepository
    var tagRepository: TagRepository
    
    // 环境变量
    @Environment(\.toastManager) private var toastManager
    @Environment(\.actionSheetManager) private var actionSheetManager
    
    // 可选配置
    var style: TaskCardStyle = .standard
    var leadingSwipeActions: [TaskSwipeAction] = []
    var trailingSwipeActions: [TaskSwipeAction] = []
    
    // 任务操作回调
    var onTaskIntent: ((TaskIntent) -> Void)
    var onExitRequested: ((Task, String) -> Void)
    var onTap: (() -> Void)? = nil

    // 内部状态
    @State private var showingTaskDetail = false
    @State private var cardState: CardState = .normal
    @State private var project: Project? = nil
    @State private var activeContext: SwipeContext? = nil // 用于保存滑动上下文
    @State private var showCompletionMark: Bool = false // 用于控制完成标记显示
    
    // 状态颜色
    private var statusColor: Color {
        // 如果有自定义颜色，优先使用
        if let customColor = style.statusBarColor {
            return customColor
        }
        
        // 根据行动状态返回对应的颜色
        let status = task.status
        
        if status == TaskStatus.na.rawValue {
            return AppColors.Status.nextAction(for: colorScheme)
        } else if status == TaskStatus.waiting.rawValue {
            return AppColors.Status.waiting(for: colorScheme)
        } else if status == TaskStatus.inbox.rawValue {
            return AppColors.Status.inbox(for: colorScheme)
        } else if status == TaskStatus.doing.rawValue {
            return AppColors.Status.doing(for: colorScheme)
        } else if status == TaskStatus.smb.rawValue {
            return AppColors.Status.someday(for: colorScheme)
        } else if status == TaskStatus.done.rawValue {
            return AppColors.Status.done(for: colorScheme)
        } else {
            return AppColors.UI.secondaryText(for: colorScheme)
        }
    }
    
    var body: some View {
        ZStack {
            // 置顶标记
            PinIndicatorView(isPinned: task.isPinned, showPinIndicator: style.showPinIndicator)
            
            // 主卡片内容
            TaskCardMainContent(
                task: task,
                style: style,
                cardState: cardState,
                project: project,
                showCompletionMark: showCompletionMark,
                activeContext: $activeContext,
                onSwipeComplete: handleCompleteAction,
                onArrangeAction: { location in
                    // 使用全局ActionSheetManager并传递点击位置
                    actionSheetManager.showActionSheet(
                        for: task,
                        at: location, // 传递点击位置
                        onChange: { newStatus in
                            onTaskIntent(.changeStatus(task, newStatus))
                        },
                        onDismiss: {
                            // 当菜单关闭时，确保滑动视图关闭并复原卡片状态
                            self.closeSwipeView()
                        }
                    )
                },
                onDeleteAction: confirmDelete
            )
        }
        .onTapGesture {
            // 如果是只读模式，不响应点击
            if !style.isReadOnly {
                onTap?() ?? {
                    showingTaskDetail = true
                }()
            }
        }
        .sheet(isPresented: $showingTaskDetail) {
            // 使用TaskDetailView实现，指定来自其他页面
            NavigationStack {
                TaskDetailView(
                    task: task,
                    sourcePageType: .other,
                    // 转发删除意图
                    onDeleteIntent: { taskToDelete in
                        onTaskIntent(.delete(taskToDelete)) 
                    }
                )
            }
                .presentationDetents([.medium, .large])
                .presentationDragIndicator(.visible)
        }
        .onAppear {
            loadProjectData()
        }
    }
    
    // MARK: - 辅助方法
    
    /// 加载项目数据
    private func loadProjectData() {
        if let projectId = task.project {
            // 使用projectRepository获取项目数据
            project = projectRepository.getProject(byId: projectId)
        }
    }
    
    /// 处理完成操作
    private func handleCompleteAction(_ action: TaskSwipeAction) {
        // 先关闭滑动菜单
        closeSwipeView()
        
        // 等待滑动菜单关闭后再执行动画
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            // 设置完成状态
            withAnimation(.easeOut(duration: 0.2)) {
                cardState = .completing
            }
            
            // 短暂显示完成标志
            withAnimation(.spring(response: 0.4, dampingFraction: 0.65)) {
                showCompletionMark = true
            }
            
            // 添加成功触觉反馈
            let successGenerator = UINotificationFeedbackGenerator()
            successGenerator.notificationOccurred(.success)
            
            // 延迟执行后续操作
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
                // 隐藏完成标志
                withAnimation {
                    showCompletionMark = false
                }
                
                // 请求父视图执行退出动画
                onExitRequested(task, "complete")
                
                // 向上传递完成意图
                onTaskIntent(.complete(task))
            }
        }
    }
    
    /// 确认删除对话框
    private func confirmDelete() {
        // 显示确认对话框但不关闭滑动视图
        let alertController = UIAlertController(
            title: "确认删除",
            message: "确定要删除这个行动吗？",
            preferredStyle: .alert
        )
        
        alertController.addAction(UIAlertAction(
            title: "取消",
            style: .cancel,
            handler: { _ in
                self.closeSwipeView()
            }
        ))
        
        alertController.addAction(UIAlertAction(
            title: "删除",
            style: .destructive,
            handler: { _ in                                
                // 执行删除
                self.onTaskIntent(.delete(self.task))
            }
        ))
        
        // 获取当前视图控制器并显示警告
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootVC = windowScene.windows.first?.rootViewController {
            var currentVC = rootVC
            while let presentedVC = currentVC.presentedViewController {
                currentVC = presentedVC
            }
            currentVC.present(alertController, animated: true)
        }
    }
    
    /// 关闭滑动视图方法
    private func closeSwipeView() {
        // 添加短暂延迟，确保UI状态已更新
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            if let context = self.activeContext {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    context.state.wrappedValue = .closed
                }
            }
        }
    }
}
