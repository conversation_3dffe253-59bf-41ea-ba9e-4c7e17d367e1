import SwiftUI

// Assuming Task, AppColors, etc. are accessible
// May need import oneStepCore or similar

struct FocusTaskSelectorView: View {
    
    @StateObject private var viewModel: FocusTaskSelectorViewModel
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.toastManager) private var toastManager // For showing limit errors
    
    var onTasksAdded: () -> Void // Callback to refresh FocusView
    
    // Inject currentDoingCount into the ViewModel
    init(currentDoingCount: Int, onTasksAdded: @escaping () -> Void) {
        _viewModel = StateObject(wrappedValue: FocusTaskSelectorViewModel(currentDoingCount: currentDoingCount))
        self.onTasksAdded = onTasksAdded
    }
    
    var body: some View {
        NavigationView { // Use NavigationView for title and toolbar
            VStack(spacing: 0) {
                // Header showing limit status
                limitHeaderView
                    .padding(.horizontal)
                    .padding(.bottom, 8)
                
                Divider()
                
                if viewModel.availableTasks.isEmpty {
                    emptyStateView
                } else {
                    taskList
                }
            }
            .background(AppColors.UI.background(for: colorScheme))
            .navigationTitle("添加到一步")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") { dismiss() }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    addButton
                }
            }
            .onChange(of: viewModel.errorMessage) { _, newError in
                // Show error messages from ViewModel using Toast
                if let error = newError {
                    toastManager.showWarning(error)
                }
            }
        }
    }
    
    // MARK: - Subviews
    
    private var limitHeaderView: some View {
        let remainingSlots = max(0, viewModel.maxDoingTasks - (viewModel.currentDoingCount + viewModel.selectedTaskIDs.count))
        let headerText = "当前 \(viewModel.currentDoingCount) / \(viewModel.maxDoingTasks)，已选 \(viewModel.selectedTaskIDs.count)，还可添加 \(remainingSlots)"
        
        return Text(headerText)
            .font(.caption)
            .foregroundColor(AppColors.UI.secondaryText(for: colorScheme))
            .frame(maxWidth: .infinity, alignment: .center)
            .padding(.top, 10) // Add some top padding
    }
    
    private var taskList: some View {
        List {
            ForEach(viewModel.availableTasks) { task in
                let isSelected = viewModel.selectedTaskIDs.contains(task.id)
                // Disable row if limit is reached AND this task is not already selected
                let isDisabled = !isSelected && !viewModel.canSelectMore
                
                FocusTaskSelectorRowView(task: task, isSelected: isSelected, isDisabled: isDisabled)
                    .onTapGesture {
                        viewModel.toggleSelection(task: task)
                    }
                    // Apply disabled effect for interaction
                    .disabled(isDisabled && !isSelected) // Prevent tapping if disabled and not selected
            }
            .listRowInsets(EdgeInsets(top: 0, leading: 16, bottom: 0, trailing: 16))
            .listRowSeparator(.hidden)
        }
        .listStyle(PlainListStyle())
    }
    
    private var emptyStateView: some View {
        VStack {
            Spacer()
            Text("下一步中没有可开始的行动")
                .font(.headline)
                .foregroundColor(AppColors.UI.secondaryText(for: colorScheme))
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var addButton: some View {
        Button(viewModel.addButtonTitle) {
            if viewModel.addSelectedTasks() {
                onTasksAdded() // Notify FocusView to reload
                dismiss()
            }
            // Error handling is done via viewModel.errorMessage and Toast
        }
        .disabled(viewModel.selectedTaskIDs.isEmpty) // Disable if no tasks are selected
    }
}

// MARK: - Preview
struct FocusTaskSelectorView_Previews: PreviewProvider {
    static var previews: some View {
        // Need to mock TaskRepository or use in-memory store for preview
        FocusTaskSelectorView(currentDoingCount: 1) { 
            print("Tasks added callback triggered in preview")
        }
        // Add necessary environment objects like modelContainer, toastManager
        .environmentObject(ToastManager.shared)
    }
} 