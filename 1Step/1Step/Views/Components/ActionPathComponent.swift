import SwiftUI

// MARK: - 数据结构

/// 当前路径数据，用于在TaskDetailView中显示实时路径
struct CurrentPathData {
    let pathBreadcrumb: String
    let onNavigateToLevel: ((Int) -> Void)?
    
    init(pathBreadcrumb: String, onNavigateToLevel: ((Int) -> Void)? = nil) {
        self.pathBreadcrumb = pathBreadcrumb
        self.onNavigateToLevel = onNavigateToLevel
    }
}

// MARK: - 路径数据工具类

/// 路径数据创建工具类，消除重复代码
struct PathDataHelper {
    
    /// 为TaskDetailView创建当前路径数据
    /// - Parameters:
    ///   - focusManager: 焦点管理器
    ///   - onNavigate: 导航回调闭包
    /// - Returns: 当前路径数据，如果无路径则返回nil
    @MainActor
    static func createCurrentPathData(
        with focusManager: FocusManager,
        onNavigate: @escaping (Int) -> Void
    ) -> CurrentPathData? {
        guard !focusManager.focusPath.isEmpty else { return nil }
        
        var parts: [String] = []
        
        // TaskDetailView中不显示项目名（因为详情页已有项目信息）
        // 直接添加focusPath中的所有节点标题
        for nodeId in focusManager.focusPath {
            if let title = focusManager.getNodeTitle(nodeId) {
                parts.append(title)
            }
        }
        
        guard !parts.isEmpty else { return nil }
        
        let pathBreadcrumb = parts.joined(separator: " › ")
        
        return CurrentPathData(
            pathBreadcrumb: pathBreadcrumb,
            onNavigateToLevel: onNavigate
        )
    }
    
    /// 为TaskDetailView创建导航逻辑
    /// - Parameters:
    ///   - level: 目标层级
    ///   - focusManager: 焦点管理器
    ///   - dismissAction: 关闭详情页的动作
    @MainActor
    static func navigateToTaskDetailLevel(
        _ level: Int,
        focusManager: FocusManager,
        dismissAction: @escaping () -> Void
    ) {
        guard !focusManager.focusPath.isEmpty else { return }
        
        // 获取根任务，检查是否有项目
        guard let rootTaskId = focusManager.focusPath.first,
              let rootTask = focusManager.getTaskForNode(rootTaskId) else { return }
        
        let hasProject = rootTask.project != nil
        
        if hasProject {
            // 有项目的情况：面包屑显示 任务(level 0) > 小行动(level 1) > 子步骤(level 2+)
            // 但实际导航需要调整为 项目(实际level 0, 不显示) > 任务(实际level 1) > 小行动(实际level 2) > 子步骤(实际level 3+)
            let adjustedLevel = level + 1 // 加1来跳过项目层级
            focusManager.navigateToBreadcrumb(adjustedLevel)
        } else {
            // 无项目的情况：直接使用原始level
            focusManager.navigateToBreadcrumb(level)
        }
        
        // 导航完成后关闭任务详情页
        dismissAction()
    }
}

/// 统一的行动路径组件，处理所有面包屑相关的逻辑
struct ActionPathComponent: View {
    
    // MARK: - 显示样式
    enum DisplayStyle {
        case navigation    // 用于 FocusView 中的导航面包屑
        case fullPath      // 用于 TaskDetailView 中的完整路径
        case recentStep    // 用于 TaskDetailView 中的最近一步
    }
    
    // MARK: - 属性
    let style: DisplayStyle
    let focusManager: FocusManager?
    let task: Task?
    let actionFocusState: ActionFocusState?
    let currentPathData: CurrentPathData?
    let onNavigate: ((UUID, UUID, [UUID]) -> Void)?
    let onFocusNavigate: ((Int) -> Void)?
    
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - 初始化
    init(
        style: DisplayStyle,
        focusManager: FocusManager? = nil,
        task: Task? = nil,
        actionFocusState: ActionFocusState? = nil,
        currentPathData: CurrentPathData? = nil,
        onNavigate: ((UUID, UUID, [UUID]) -> Void)? = nil,
        onFocusNavigate: ((Int) -> Void)? = nil
    ) {
        self.style = style
        self.focusManager = focusManager
        self.task = task
        self.actionFocusState = actionFocusState
        self.currentPathData = currentPathData
        self.onNavigate = onNavigate
        self.onFocusNavigate = onFocusNavigate
    }
    
    var body: some View {
        Group {
            switch style {
            case .navigation:
                navigationBreadcrumbView
            case .fullPath:
                fullPathView
            case .recentStep:
                recentStepView
            }
        }
    }
    
    // MARK: - Navigation Breadcrumb (for FocusView)
    
    @ViewBuilder
    private var navigationBreadcrumbView: some View {
        if let focusManager = focusManager {
            let breadcrumbTitles = focusManager.getBreadcrumbTitles()
            
            if shouldShowNavigationBreadcrumb(titles: breadcrumbTitles) {
                HStack(spacing: 8) {
                    ForEach(Array(breadcrumbTitles.enumerated()), id: \.offset) { index, title in
                        navigationBreadcrumbItem(title: title, index: index)
                        
                        if index < breadcrumbTitles.count - 1 {
                            Image(systemName: "chevron.right")
                                .font(.system(size: 12))
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(Color.gray.opacity(0.05))
                .cornerRadius(8)
                .padding(.horizontal, 16)
                .padding(.top, 8)
            }
        }
    }
    
    private func shouldShowNavigationBreadcrumb(titles: [String]) -> Bool {
        guard !titles.isEmpty else { return false }
        
        if let focusManager = focusManager,
           focusManager.focusPath.count == 1 && titles.count == 1 {
            return false
        }
        
        return true
    }
    
    @ViewBuilder
    private func navigationBreadcrumbItem(title: String, index: Int) -> some View {
        let isClickable = isNavigationBreadcrumbClickable(index: index)
        
        if isClickable {
            Button {
                handleNavigationBreadcrumbTap(level: index)
            } label: {
                Text(title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.primary.opacity(0.8))
                    .lineLimit(1)
            }
            .buttonStyle(PlainButtonStyle())
        } else {
            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.secondary)
                .lineLimit(1)
        }
    }
    
    private func isNavigationBreadcrumbClickable(index: Int) -> Bool {
        guard let focusManager = focusManager else { return false }
        
        let breadcrumbTitles = focusManager.getBreadcrumbTitles()
        let title = breadcrumbTitles[index]
        
        if title == "..." { return false }
        
        guard let rootTaskId = focusManager.focusPath.first,
              let rootTask = focusManager.getTaskForNode(rootTaskId) else { return false }
        
        let hasProject = rootTask.project != nil
        
        if hasProject {
            return index > 0
        } else {
            return true
        }
    }
    
    private func handleNavigationBreadcrumbTap(level: Int) {
        guard let focusManager = focusManager else { return }
        
        let breadcrumbTitles = focusManager.getBreadcrumbTitles()
        let title = breadcrumbTitles[level]
        
        if title == "..." { return }
        
        guard let rootTaskId = focusManager.focusPath.first,
              let rootTask = focusManager.getTaskForNode(rootTaskId) else { return }
        
        let hasProject = rootTask.project != nil
        let currentPathCount = focusManager.focusPath.count
        let maxLevels = 3
        
        if hasProject {
            let pathToShowCount = currentPathCount - 1
            
            if pathToShowCount <= maxLevels {
                onFocusNavigate?(level)
            } else {
                if level == 1 {
                    onFocusNavigate?(1)
                } else if level == breadcrumbTitles.count - 1 {
                    let targetLevel = currentPathCount - 2
                    onFocusNavigate?(targetLevel)
                }
            }
        } else {
            let pathToShowCount = currentPathCount - 1
            
            if pathToShowCount <= maxLevels {
                onFocusNavigate?(level)
            } else {
                if level == 0 {
                    onFocusNavigate?(0)
                } else if level == breadcrumbTitles.count - 1 {
                    let targetLevel = currentPathCount - 2
                    onFocusNavigate?(targetLevel)
                }
            }
        }
    }
    
    // MARK: - Full Path View (for TaskDetailView)
    
    @ViewBuilder
    private var fullPathView: some View {
        if shouldShowFullPath {
            VStack(spacing: 0) {
                fullPathBreadcrumbView
                    .padding(.vertical, 12)
                    .padding(.horizontal, 20)
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                Divider()
                    .padding(.leading, 20)
            }
        }
    }
    
    /// 是否应该显示完整路径
    private var shouldShowFullPath: Bool {
        // 1. 如果有CurrentPathData，则显示
        if let currentPathData = currentPathData, !currentPathData.pathBreadcrumb.isEmpty {
            return true
        }
        
        // 2. 如果有ActionFocusState，则显示
        if isValidActionFocusState {
            return true
        }
        
        // 3. 如果有focusManager且当前在聚焦模式，则显示基于focusPath的路径
        if let focusManager = focusManager, !focusManager.focusPath.isEmpty {
            return true
        }
        
        return false
    }
    
    /// 完整路径面包屑视图
    private var fullPathBreadcrumbView: some View {
        let breadcrumb = getFullPathBreadcrumb()
        let titles = breadcrumb.components(separatedBy: " › ")
        
        return FlowLayout(spacing: 8) {
            ForEach(Array(titles.enumerated()), id: \.offset) { index, title in
                HStack(spacing: 8) {
                    fullPathBreadcrumbItem(title: title, index: index)
                    
                    if index < titles.count - 1 {
                        Image(systemName: "chevron.right")
                            .font(.system(size: 12))
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
    }
    
    private func fullPathBreadcrumbItem(title: String, index: Int) -> some View {
        Button {
            navigateToFullPathLevel(at: index)
        } label: {
            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.primary.opacity(0.8))
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    /// 获取完整路径面包屑字符串
    private func getFullPathBreadcrumb() -> String {
        // 优先使用CurrentPathData
        if let currentPathData = currentPathData, !currentPathData.pathBreadcrumb.isEmpty {
            return currentPathData.pathBreadcrumb
        }
        
        // 其次使用ActionFocusState
        if let state = actionFocusState, let task = task {
            return getActionFocusBreadcrumb()
        }
        
        // 最后使用focusPath生成
        if let focusManager = focusManager {
            return getFocusPathBreadcrumb()
        }
        
        return ""
    }
    
    /// 基于focusPath生成完整路径面包屑（包含当前层级）
    private func getFocusPathBreadcrumb() -> String {
        guard let focusManager = focusManager, !focusManager.focusPath.isEmpty else { return "" }
        
        var parts: [String] = []
        
        for nodeId in focusManager.focusPath {
            if let title = focusManager.getNodeTitle(nodeId) {
                parts.append(title)
            }
        }
        
        return parts.joined(separator: " › ")
    }
    
    /// 处理完整路径导航
    private func navigateToFullPathLevel(at index: Int) {
        // 如果基于CurrentPathData，使用其导航回调
        if let currentPathData = currentPathData {
            currentPathData.onNavigateToLevel?(index)
            return
        }
        
        // 如果基于ActionFocusState，使用原有逻辑
        if let state = actionFocusState {
            navigateToPath(at: index)
            return
        }
        
        // 如果基于focusPath，使用focusManager逻辑
        if let focusManager = focusManager {
            navigateToFocusPathLevel(at: index)
        }
    }
    
    /// 基于focusPath的层级导航
    private func navigateToFocusPathLevel(at index: Int) {
        guard let focusManager = focusManager,
              index >= 0 && index < focusManager.focusPath.count else { return }
        
        // 导航到对应层级
        onFocusNavigate?(index)
    }
    
    // MARK: - Recent Step View (for TaskDetailView)
    
    @ViewBuilder
    private var recentStepView: some View {
        if isValidActionFocusState {
            VStack(spacing: 0) {
                Button(action: {
                    restoreActionFocusState()
                }) {
                    HStack {
                        Text("最近一步")
                            .font(.system(size: 15))
                            .foregroundColor(.secondary)
                        
                        Text("·")
                            .font(.system(size: 15))
                            .foregroundColor(.secondary.opacity(0.5))
                        
                        let breadcrumb = getActionFocusBreadcrumb()
                        Text(breadcrumb)
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                            .truncationMode(.middle)
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.secondary.opacity(0.8))
                    }
                    .padding(.vertical, 12)
                    .padding(.horizontal, 20)
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
                .buttonStyle(PlainButtonStyle())
                
                Divider()
                    .padding(.leading, 20)
            }
        }
    }
    
    // MARK: - Shared Breadcrumb Path View
    
    private var breadcrumbPathView: some View {
        let breadcrumb = getActionFocusBreadcrumb()
        let titles = breadcrumb.components(separatedBy: " › ")
        
        return FlowLayout(spacing: 8) {
            ForEach(Array(titles.enumerated()), id: \.offset) { index, title in
                HStack(spacing: 8) {
                    breadcrumbItem(title: title, index: index)
                    
                    if index < titles.count - 1 {
                        Image(systemName: "chevron.right")
                            .font(.system(size: 12))
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
    }
    
    private func breadcrumbItem(title: String, index: Int) -> some View {
        Button {
            navigateToPath(at: index)
        } label: {
            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.primary.opacity(0.8))
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Navigation Logic
    
    private func navigateToPath(at index: Int) {
        guard let state = actionFocusState else { return }
        
        if index == 0 {
            // 点击任务名，直接导航到任务根节点
            onNavigate?(state.taskId, state.checklistItemId, [])
        } else if index == 1 {
            // 点击小行动名，直接导航到小行动
            onNavigate?(state.taskId, state.checklistItemId, [])
        } else {
            // 点击子步骤，计算正确的子步骤路径
            let subPathIndex = index - 2
            guard subPathIndex >= 0 && subPathIndex < state.subStepPath.count else { return }
            
            let targetPath = Array(state.subStepPath.prefix(subPathIndex + 1))
            onNavigate?(state.taskId, state.checklistItemId, targetPath)
        }
        
        dismiss()
    }
    
    private func restoreActionFocusState() {
        guard let state = actionFocusState else { return }
        onNavigate?(state.taskId, state.checklistItemId, state.subStepPath)
        dismiss()
    }
    
    // MARK: - Helper Methods
    
    private func getActionFocusBreadcrumb() -> String {
        guard let state = actionFocusState, let task = task else { return "" }
        
        var parts: [String] = []
        
        // 添加任务名称作为第一个元素
        parts.append(task.title)
        
        // 添加小行动名
        if let checklistItem = task.checklist?.first(where: { $0.id == state.checklistItemId }) {
            parts.append(checklistItem.title)
        }
        
        // 添加子步骤路径
        if !state.subStepPath.isEmpty {
            if let checklistItem = task.checklist?.first(where: { $0.id == state.checklistItemId }) {
                var currentSteps = checklistItem.subStepsList
                
                for stepId in state.subStepPath {
                    if let step = currentSteps.first(where: { $0.id == stepId }) {
                        parts.append(step.title)
                        currentSteps = step.subSteps
                    }
                }
            }
        }
        
        return parts.joined(separator: " › ")
    }
    
    private var isValidActionFocusState: Bool {
        guard let state = actionFocusState, let task = task else { return false }
        return isValidActionFocusState(state, for: task)
    }
    
    private func isValidActionFocusState(_ state: ActionFocusState, for task: Task) -> Bool {
        // 1. 检查任务是否未完成
        guard task.status != TaskStatus.done.rawValue else {
            return false
        }
        
        // 2. 检查小行动是否存在且未完成
        guard let checklist = task.checklist,
              let checklistItem = checklist.first(where: { $0.id == state.checklistItemId }),
              !checklistItem.isCompleted else {
            return false
        }
        
        // 3. 如果有子步骤路径，验证子步骤路径是否仍然有效
        if !state.subStepPath.isEmpty {
            return isValidSubStepPath(state.subStepPath, in: checklistItem.subStepsList)
        }
        
        return true
    }
    
    private func isValidSubStepPath(_ path: [UUID], in steps: [SubStep]) -> Bool {
        guard !path.isEmpty else { return true }
        
        let currentStepId = path[0]
        let remainingPath = Array(path.dropFirst())
        
        guard let currentStep = steps.first(where: { $0.id == currentStepId }),
              !currentStep.isCompleted else {
            return false
        }
        
        if !remainingPath.isEmpty {
            return isValidSubStepPath(remainingPath, in: currentStep.subSteps)
        }
        
        return true
    }
}

// MARK: - FlowLayout

struct FlowLayout: Layout {
    var spacing: CGFloat = 10
    
    func sizeThatFits(proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) -> CGSize {
        let width = proposal.width ?? 0
        var height: CGFloat = 0
        let sizes = subviews.map { $0.sizeThatFits(.unspecified) }
        
        var currentX: CGFloat = 0
        var currentY: CGFloat = 0
        var maxHeight: CGFloat = 0
        
        for (index, _) in subviews.enumerated() {
            let size = sizes[index]
            
            if currentX + size.width > width {
                currentX = 0
                currentY += maxHeight + spacing
                maxHeight = 0
            }
            
            maxHeight = max(maxHeight, size.height)
            currentX += size.width + spacing
        }
        
        height = currentY + maxHeight
        
        return CGSize(width: width, height: height)
    }
    
    func placeSubviews(in bounds: CGRect, proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) {
        let sizes = subviews.map { $0.sizeThatFits(.unspecified) }
        
        var currentX = bounds.minX
        var currentY = bounds.minY
        var maxHeight: CGFloat = 0
        
        for (index, subview) in subviews.enumerated() {
            let size = sizes[index]
            
            if currentX + size.width > bounds.maxX {
                currentX = bounds.minX
                currentY += maxHeight + spacing
                maxHeight = 0
            }
            
            subview.place(
                at: CGPoint(x: currentX, y: currentY),
                proposal: ProposedViewSize(size)
            )
            
            maxHeight = max(maxHeight, size.height)
            currentX += size.width + spacing
        }
    }
} 