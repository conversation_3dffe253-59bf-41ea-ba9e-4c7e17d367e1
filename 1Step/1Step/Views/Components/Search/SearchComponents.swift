import SwiftUI
import SwiftData
import Flow // <-- Uncomment the import

// 搜索内容视图
struct SearchContentView: View {
    @ObservedObject var viewModel: SearchViewModel
    
    var body: some View {
        VStack {
            filterBar
            searchResults
        }
        // Add contentShape and an empty gesture to consume taps within this view
        .contentShape(Rectangle())
        .onTapGesture {}
    }
    
    // 筛选栏组件
    var filterBar: some View {
        Group {
            if !viewModel.searchText.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        NAFilterButton(filter: .all, selectedFilter: $viewModel.selectedFilter)
                        NAFilterButton(filter: .tasks, selectedFilter: $viewModel.selectedFilter)
                        NAFilterButton(filter: .projects, selectedFilter: $viewModel.selectedFilter)
                        NAFilterButton(filter: .tags, selectedFilter: $viewModel.selectedFilter)
                        NAFilterButton(filter: .notes, selectedFilter: $viewModel.selectedFilter)
                        NAFilterButton(filter: .smb, selectedFilter: $viewModel.selectedFilter)
                    }
                    .padding(.horizontal)
                }
            }
        }
    }
    
    // 搜索结果组件
    var searchResults: some View {
        Group {
            if viewModel.searchText.isEmpty {
                if viewModel.isSearchFocused {
                    searchHistoryView
                } else {
                    EmptyView()
                }
            } else {
                ScrollView {
                    VStack(spacing: 12) {
                        ForEach(viewModel.searchResults) { task in
                            TaskCardView(
                                task: task,
                                taskRepository: DependencyContainer.taskRepository(),
                                projectRepository: DependencyContainer.projectRepository(),
                                tagRepository: DependencyContainer.tagRepository(),
                                style: .standard,
                                onTaskIntent: { intent in
                                    viewModel.handleTaskIntent(intent)
                                },
                                onExitRequested: { task, action in
                                    // Search results don't need special exit animation
                                }
                            )
                            .padding(.horizontal)
                            .frame(height: 80) // Keep height limit if desired
                        }
                        
                        if viewModel.searchResults.isEmpty {
                            Text("没有找到匹配的结果")
                                .foregroundColor(.secondary)
                                .padding(.top, 20)
                        }
                    }
                    .padding(.vertical, 8)
                }
            }
        }
    }
    
    // 搜索历史记录视图 (Restore Flow layout)
    var searchHistoryView: some View {
        VStack(alignment: .leading, spacing: 12) {
            if !viewModel.searchHistory.isEmpty {
                // HStack for Title and Clear All button
                HStack {
                    Text("搜索历史")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer() // Pushes the button to the right
                    
                    Button(action: {
                        viewModel.clearSearchHistory() // Call SearchViewModel's method
                    }) {
                        Image(systemName: "trash") // Use trash icon
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(4) // Add padding for easier tapping
                    }
                    .buttonStyle(.plain) // Use plain button style if needed
                }
                .padding(.horizontal) // Add horizontal padding to the HStack
                
                // --- Restore Flow layout ---
                Flow(alignment: .topLeading, spacing: 8) {
                    // Limit the number of history items displayed (e.g., first 12)
                    ForEach(viewModel.searchHistory.prefix(12), id: \.self) { term in
                        Button(action: {
                            viewModel.searchText = term // Update SearchViewModel's searchText
                        }) {
                            Text(term)
                                .font(.caption)
                                .foregroundColor(Color(.label)) // Use label color for text
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color(.systemGray5)) // Use a subtle background
                                .cornerRadius(6)
                        }
                        .buttonStyle(.plain) // Ensure button doesn't have default styling interference
                    }
                }
                .padding(.horizontal) // Add horizontal padding to the Flow container
                 // --- End restored Flow layout ---
                 
            }
        }
        .padding(.vertical) // Keep vertical padding for the whole section
    }
}

// 搜索栏组件
struct SearchBarView: View {
    @ObservedObject var viewModel: SearchViewModel
    @FocusState var isSearchFieldFocused: Bool
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                    .padding(.leading, 8)
                
                TextField("搜索行动、项目、备注...", text: $viewModel.searchText, onEditingChanged: { focused in
                    viewModel.isSearchFocused = focused
                    isSearchFieldFocused = focused
                })
                .focused($isSearchFieldFocused)
                .textFieldStyle(PlainTextFieldStyle())
                .padding(.vertical, 10)
                // Add onSubmit to add search term to history
                .onSubmit {
                    if !viewModel.searchText.isEmpty {
                         viewModel.addSearchHistory(viewModel.searchText)
                    }
                }
                
                if isSearchFieldFocused || !viewModel.searchText.isEmpty {
                    Button(action: {
                        viewModel.isSearchFocused = false
                        viewModel.searchText = ""
                        isSearchFieldFocused = false
                    }) {
                        Text("取消")
                            .foregroundColor(.blue)
                            .padding(.trailing, 8)
                    }
                    .opacity(isSearchFieldFocused ? 1 : 0)
                    .animation(.easeInOut, value: isSearchFieldFocused)
                    .transition(AnyTransition.opacity)
                }
                if !viewModel.searchText.isEmpty {
                    Button(action: { viewModel.searchText = "" }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                            .padding(.trailing, 8)
                    }
                }
            }
            .background(
                searchBarBackground
            )
            .cornerRadius(10)
        }
        .padding(.horizontal)
    }
    
    // 根据颜色主题提供适当的搜索栏背景
    private var searchBarBackground: some View {
        Group {
            if colorScheme == .dark {
                // 黑暗主题下的背景
                Color(UIColor.systemGray6)
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.gray.opacity(0.2), lineWidth: 0.5)
                    )
            } else {
                // 浅色主题下的背景
                Color(red: 0.95, green: 0.95, blue: 0.95)
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color(red: 0.8, green: 0.8, blue: 0.8).opacity(0.2), lineWidth: 0.5)
                    )
            }
        }
    }
}

// 使用Models/SearchFilter.swift中的SearchFilter定义

// 过滤按钮组件
struct NAFilterButton: View {
    let filter: SearchFilter
    @Binding var selectedFilter: SearchFilter
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        Button(action: {
            selectedFilter = filter
        }) {
            Text(filter.displayName)
                .font(.footnote)
                .fontWeight(selectedFilter == filter ? .semibold : .regular)
                .foregroundColor(selectedFilter == filter ? .white : AppColors.UI.primaryText(for: colorScheme))
                .padding(.vertical, 6)
                .padding(.horizontal, 12)
                .background(
                    selectedFilter == filter ? AppColors.UI.primary(for: colorScheme) : Color(UIColor.systemGray5)
                )
                .cornerRadius(8)
                .shadow(color: selectedFilter == filter ? AppColors.UI.gray(for: colorScheme).opacity(0.2) : Color.clear, radius: 1, x: 0, y: 1)
        }
    }
}
