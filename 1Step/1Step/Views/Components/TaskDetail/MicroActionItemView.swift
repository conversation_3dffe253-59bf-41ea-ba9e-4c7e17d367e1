import SwiftUI
import SwiftData
import SwipeActions
import UIKit
import AVFoundation

/// 单个小行动项视图
struct MicroActionItemView: View {
    // MARK: - 属性
    
    /// 视图模型
    @ObservedObject var viewModel: TaskDetailViewModel
    
    /// 小行动项
    var item: ChecklistItem
    
    /// 父视图正在编辑的小行动ID
    @Binding var editingItemId: UUID?
    
    /// 父视图重新排序的回调函数
    var onRequestDelayedSort: ((UUID, Bool) -> Void)?
    
    /// 删除确认状态
    @State private var showDeleteConfirmation = false
    
    /// 完成动画状态
    @State private var isAnimatingCompletion = false
    
    /// 勾选框缩放比例
    @State private var checkboxScale: CGFloat = 1.0
    
    /// 用于强制视图刷新的ID
    @State private var viewStateId = UUID()
    
    /// 记录当前项目的完成状态
    @State private var isItemCompleted: Bool = false
    
    /// 编辑状态下的文本
    @State private var editText: String = ""
    
    /// 输入框焦点状态
    @FocusState private var isInputFocused: Bool
    
    /// 音频播放器
    @State private var audioPlayer: AVAudioPlayer?
    
    /// 计算属性：当前是否处于编辑状态
    private var isEditing: Bool {
        editingItemId == item.id
    }
    
    /// 初始化时设置初始状态
    init(viewModel: TaskDetailViewModel, item: ChecklistItem, editingItemId: Binding<UUID?> = .constant(nil), onRequestDelayedSort: ((UUID, Bool) -> Void)? = nil) {
        self.viewModel = viewModel
        self.item = item
        self._editingItemId = editingItemId
        self.onRequestDelayedSort = onRequestDelayedSort
        self._isItemCompleted = State(initialValue: item.isCompleted)
    }
    
    // MARK: - 视图主体
    
    var body: some View {
        SwipeView {
            HStack(spacing: 10) {
                // 完成状态勾选框
                Button {
                    toggleItemCompletion()
                } label: {
                    ZStack {
                        RoundedRectangle(cornerRadius: 3)
                            .strokeBorder(isItemCompleted ? Color.secondary : Color.secondary.opacity(0.3), lineWidth: 1.5)
                            .frame(width: 18, height: 18)
                            .scaleEffect(checkboxScale)
                        
                        // 使用isItemCompleted状态来控制勾选框显示，与动画状态完全分离
                        if isItemCompleted {
                            Image(systemName: "checkmark")
                                .font(.system(size: 10, weight: .medium))
                                .foregroundColor(Color.secondary)
                                .transition(.scale.combined(with: .opacity))
                                .id("checkmark-\(item.id)-\(viewStateId)")
                        }
                    }
                    .animation(.spring(response: 0.2, dampingFraction: 0.7), value: item.isCompleted)
                    .animation(.spring(response: 0.2, dampingFraction: 0.7), value: isItemCompleted)
                    .animation(.spring(response: 0.2, dampingFraction: 0.7), value: isAnimatingCompletion)
                }
                .buttonStyle(BorderlessButtonStyle())
                .contentShape(Rectangle()) // 扩大点击区域
                
                // 项目标题 - 根据编辑状态显示不同内容
                if isEditing {
                    TextField("小行动内容", text: $editText)
                        .font(.system(size: 15))
                        .foregroundColor(.primary)
                        .focused($isInputFocused)
                        .onAppear {
                            // 确保焦点在编辑模式开启时设置
                            isInputFocused = true
                        }
                        .onSubmit {
                            // 保存编辑并退出编辑模式
                            saveEdit()
                        }
                } else {
                    Text(item.title)
                        .font(.system(size: 15))
                        .strikethrough(isItemCompleted)
                        .foregroundColor(isItemCompleted ? .secondary.opacity(0.7) : .primary)
                        .lineLimit(2)
                        .contentShape(Rectangle()) // 确保整个区域可点击
                        .animation(.easeInOut(duration: 0.2), value: isItemCompleted)
                        .id("title-\(item.id)-\(viewStateId)")
                        .onTapGesture {
                            // 点击文本进入编辑模式
                            startEditing()
                        }
                }
                
                Spacer()
                
                // 如果正在编辑，显示取消和保存按钮
                if isEditing {
                    Button {
                        // 取消编辑
                        editingItemId = nil
                    } label: {
                        Image(systemName: "xmark")
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)
                            .padding(4)
                    }
                    .buttonStyle(BorderlessButtonStyle())
                    
                    Button {
                        // 保存编辑
                        saveEdit()
                    } label: {
                        Text("保存")
                            .font(.system(size: 14))
                            .foregroundColor(.accentColor)
                            .padding(.leading, 2)
                    }
                    .buttonStyle(BorderlessButtonStyle())
                }
                
                // 移除了删除按钮，只保留左滑删除功能
            }
            .padding(.vertical, 6) // 增加垂直间距，提高可点击区域
            .contentShape(Rectangle()) // 确保整行可以响应手势
            .contextMenu {
                // 长按菜单
                Button(action: {
                    withAnimation {
                        viewModel.toggleChecklistItem(item)
                    }
                }) {
                    Label(item.isCompleted ? "标记为未完成" : "标记为已完成", 
                          systemImage: item.isCompleted ? "circle" : "checkmark.circle")
                }
                
                Button(role: .destructive, action: {
                    showDeleteConfirmation = true
                }) {
                    Label("删除", systemImage: "trash")
                }
            }
            .alert("确认删除", isPresented: $showDeleteConfirmation) {
                Button("取消", role: .cancel) {}
                Button("删除", role: .destructive) {
                    withAnimation {
                        viewModel.removeChecklistItem(item)
                    }
                }
            } message: {
                Text("确定要删除这个小行动吗？")
            }
        } leadingActions: { _ in
            // 空实现，禁用从左往右滑动
        } trailingActions: { context in
            SwipeAction(
                action: { 
                    withAnimation {
                        viewModel.removeChecklistItem(item)
                    }
                },
                label: { _ in
                    Image(systemName: "xmark")
                        .font(.system(size: 16))
                        .foregroundColor(.secondary)
                },
                background: { _ in
                    Color.clear
                }
            )
        }
        // 配置滑动操作
        .swipeActionsMaskCornerRadius(0)
        .swipeActionCornerRadius(0)
        .swipeActionsStyle(.equalWidths)  // 使用等宽样式
        .swipeMinimumDistance(60)  // 增加触发距离
        .swipeActionsVisibleStartPoint(max(0, 60))  // 增加显示点，确保非负值
        .swipeActionsVisibleEndPoint(max(0, 90))    // 增加结束点，确保非负值
        .swipeSpacing(max(0, 1))  // 最小间距，确保非负值
        .swipeOffsetCloseAnimation(stiffness: max(1, 120), damping: max(1, 50))  // 增加动画阻力，使滑动更难维持
        .swipeActionWidth(max(0, 85))  // 设置适当的操作宽度，确保非负值
    }
    
    // MARK: - 方法
    
    /// 切换小行动完成状态，并添加动画效果
    private func toggleItemCompletion() {
        // 如果已经在动画中，则不重复触发
        guard !isAnimatingCompletion else { return }
        
        // 如果正在编辑，退出编辑状态
        if isEditing {
            editingItemId = nil
            isInputFocused = false
        }
        
        // 添加触觉反馈
        UIImpactFeedbackGenerator(style: .light).impactOccurred()
        
        // 先更新视图状态ID，强制视图刷新
        viewStateId = UUID()
        
        // 添加勾选框缩放动画
        withAnimation(.spring(response: 0.1, dampingFraction: 0.7)) {
            checkboxScale = 0.85
        }
        
        // 播放完成音效
        playCompletionSound()
        
        // 恢复勾选框大小
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.spring(response: 0.1, dampingFraction: 0.7)) {
                checkboxScale = 1.0
            }
        }
        
        // 检查当前状态是从未完成变为完成，还是从完成变为未完成
        if !item.isCompleted {
            // 从未完成到完成：立即视觉上显示为已完成
            withAnimation(.spring(response: 0.2)) {
                isItemCompleted = true
            }
            
            // 立即更新数据模型
            viewModel.toggleChecklistItem(item)
            
            // 通知父视图这个项目状态改变了，需要延迟排序
            onRequestDelayedSort?(item.id, true)
            
        } else {
            // 从完成到未完成：立即视觉上显示为未完成
            withAnimation(.spring(response: 0.2)) {
                isItemCompleted = false
            }
            
            // 立即更新数据模型
            viewModel.toggleChecklistItem(item)
            
            // 通知父视图这个项目状态改变了，需要延迟排序
            onRequestDelayedSort?(item.id, false)
        }
        
        // 标记动画状态开始，防止重复触发
        isAnimatingCompletion = true
        
        // 延迟重置动画状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            isAnimatingCompletion = false
        }
    }
    
    /// 播放完成音效
    private func playCompletionSound() {
        // 创建一个简单的完成音效
        guard let soundURL = Bundle.main.url(forResource: "check", withExtension: "wav") else {
            // 如果没有找到音效文件，使用系统默认音效
            let systemSoundID: SystemSoundID = 1104 // 使用系统提供的完成音效
            AudioServicesPlaySystemSound(systemSoundID)
            return
        }
        
        do {
            // 设置音频会话为混音模式，不打断其他音频
            try AVAudioSession.sharedInstance().setCategory(.ambient, mode: .default)
            try AVAudioSession.sharedInstance().setActive(true, options: .notifyOthersOnDeactivation)
            
            // 创建音频播放器
            audioPlayer = try AVAudioPlayer(contentsOf: soundURL)
            audioPlayer?.volume = 0.3 // 降低音量以便更好地混合
            audioPlayer?.prepareToPlay()
            audioPlayer?.play()
        } catch {
            print("无法播放完成音效: \(error.localizedDescription)")
        }
    }
    
    /// 开始编辑小行动
    private func startEditing() {
        // 如果已经在完成状态，则不允许编辑
        if isItemCompleted {
            return
        }
        
        // 设置编辑文本
        editText = item.title
        
        // 进入编辑模式
        editingItemId = item.id
        
        // 确保下一个运行循环中获得焦点
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            isInputFocused = true
        }
    }
    
    /// 保存编辑内容
    private func saveEdit() {
        // 确保编辑文本不为空
        if !editText.isEmpty && editText != item.title {
            // 更新小行动标题
            viewModel.updateChecklistItemTitle(item, newTitle: editText)
        }
        
        // 退出编辑模式
        editingItemId = nil
        isInputFocused = false
    }
}

// MARK: - 预览

struct MicroActionItemPreview: View {
    var body: some View {
        let task = Task(title: "测试行动", status: "Inbox")
        let item = ChecklistItem(title: "测试小行动项")
        task.checklist = [item]
        
        let taskManager = DependencyContainer.taskManager()
        let thoughtRepository = DependencyContainer.thoughtRepository()
        let viewModel = TaskDetailViewModel(task: task, taskManager: taskManager, thoughtRepository: thoughtRepository)
        
        return MicroActionItemView(viewModel: viewModel, item: item)
            .frame(width: 375)
            .padding()
    }
}

#Preview {
    MicroActionItemPreview()
}
