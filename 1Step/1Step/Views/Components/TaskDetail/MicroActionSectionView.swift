import SwiftUI
import SwiftData
import UIKit

/// 小行动模块主视图，负责展示和管理行动的小行动（检查清单项）
struct MicroActionSectionView: View {
    // MARK: - 属性
    
    /// 视图模型
    @ObservedObject var viewModel: TaskDetailViewModel
    
    /// 来源页面类型
    private var sourcePageType: TaskDetailView.SourcePage
    
    /// 展开状态
    @State private var isExpanded: Bool = false
    
    /// 初始化展开状态
    private var shouldExpandInitially: Bool {
        // 如果是从一步页面打开，且有未完成小行动，则自动展开
        if sourcePageType == .doing && viewModel.task.incompleteChecklistCount > 0 {
            return true
        }
        // 如果是从一步页面打开，但没有未完成小行动，则折叠
        else if sourcePageType == .doing {
            return false
        }
        // 如果是从其他页面打开，始终折叠
        else if sourcePageType == .other {
            return false
        }
        // 默认情况：如果没有小行动，则展开以便添加
        return !viewModel.task.hasChecklist
    }
    
    /// 新小行动输入框焦点状态
    @FocusState private var isInputFocused: Bool
    
    /// 添加状态
    @State private var isAdding: Bool = false
    
    /// 最近添加的小行动ID
    @State private var recentlyAddedItemId: UUID? = nil
    
    /// 保存输入框的高度
    @State private var textFieldHeight: CGFloat = 0
    
    /// 正在完成动画的项目ID及其目标状态
    @State private var animatingItems: [UUID: Bool] = [:]
    
    /// 当前正在编辑的小行动ID
    @State private var editingItemId: UUID? = nil
    
    // MARK: - 计算属性
    
    /// 排序后的小行动列表
    private var sortedItems: [ChecklistItem] {
        // 如果正在添加新小行动，使用临时排序逻辑，将新添加的小行动放在最上方
        if isAdding, let recentId = recentlyAddedItemId {
            // 找到最近添加的小行动
            let recentlyAdded = viewModel.task.checklist?.filter { $0.id == recentId } ?? []
            
            // 其他未完成项 - 按创建时间倒序排列（最新创建的在上方）
            let otherIncomplete = viewModel.task.checklist?.filter { !$0.isCompleted && $0.id != recentId }
                .sorted(by: { $0.createdAt > $1.createdAt }) ?? []
            
            // 已完成项 - 按完成时间倒序排列（最近完成的在上方）
            let completed = viewModel.task.checklist?.filter { $0.isCompleted }
                .sorted(by: { 
                    guard let date1 = $0.completedAt, let date2 = $1.completedAt else {
                        return $0.createdAt > $1.createdAt
                    }
                    return date1 > date2
                }) ?? []
            
            return recentlyAdded + otherIncomplete + completed
        } else {
            // 定义一个临时项目列表，根据实际状态和动画状态进行分类
            var incomplete: [ChecklistItem] = []
            var completed: [ChecklistItem] = []
            
            // 遍历所有项目，根据动画状态决定项目该放在哪个列表中
            for item in viewModel.task.checklist ?? [] {
                // 检查项目是否正在动画
                if let targetState = animatingItems[item.id] {
                    // 如果正在动画，根据目标状态决定位置
                    if targetState {
                        // 正在变为完成状态，保持在未完成列表中
                        incomplete.append(item)
                    } else {
                        // 正在变为未完成状态，保持在已完成列表中
                        completed.append(item)
                    }
                } else {
                    // 如果没有动画，正常分类
                    if item.isCompleted {
                        completed.append(item)
                    } else {
                        incomplete.append(item)
                    }
                }
            }
            
            // 排序
            incomplete.sort(by: { $0.createdAt > $1.createdAt })
            completed.sort(by: { 
                guard let date1 = $0.completedAt, let date2 = $1.completedAt else {
                    return $0.createdAt > $1.createdAt
                }
                return date1 > date2
            })
            
            return incomplete + completed
        }
    }
    
    /// 未完成项数量
    private var incompleteCount: Int {
        viewModel.task.incompleteChecklistCount
    }
    
    /// 是否有小行动
    private var hasItems: Bool {
        viewModel.task.hasChecklist
    }
    
    /// 是否所有小行动都已完成
    private var allCompleted: Bool {
        hasItems && incompleteCount == 0
    }
    
    // MARK: - 初始化方法
    
    init(viewModel: TaskDetailViewModel, sourcePageType: TaskDetailView.SourcePage = .other) {
        self.viewModel = viewModel
        self.sourcePageType = sourcePageType
    }
    
    // MARK: - 视图主体
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 标题栏 - 点击展开/折叠
            HStack {
                // 小行动标题与未完成数量
                HStack(spacing: 4) {
                    Text("小行动")
                        .font(.system(size: 15))
                        .foregroundColor(.secondary)
                    
                    // 显示未完成数量，使用更低调的样式
                    if hasItems && incompleteCount > 0 {
                        Text("\(incompleteCount)")
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                // 展开/折叠图标
                Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                    .font(.system(size: 12))
                    .foregroundColor(.secondary.opacity(0.8))
            }
            .padding(.vertical, 12)
            .contentShape(Rectangle()) // 确保整个区域可点击
            .onTapGesture {
                // 直接修改状态，让SwiftUI自动处理动画
                isExpanded.toggle()
                
                // 如果折叠了，则退出添加模式
                if !isExpanded {
                    viewModel.isAddingChecklistItem = false
                    isInputFocused = false
                }
            }
            
            // 展开时显示小行动列表和添加区域
            if isExpanded {
                // 始终显示输入框，不再基于添加状态判断
                addMicroActionView
                    .padding(.top, 4)
                
                // 显示小行动列表
                microActionListView
            }
            
            Divider()
        }
        .padding(.horizontal, 20)
        .onChange(of: allCompleted) { _, completed in
            // 当所有小行动完成时，自动折叠
            if completed && hasItems {
                // 直接修改状态，让SwiftUI自动处理动画
                isExpanded = false
            }
        }
        .onAppear {
            // 根据来源页面和小行动状态决定是否展开
            isExpanded = shouldExpandInitially
        }
    }
    
    // MARK: - 子视图
    
    // 移除了不再需要的 statusText 视图
    
    /// 小行动列表视图
    private var microActionListView: some View {
        VStack(spacing: 8) {
            ForEach(sortedItems, id: \.id) { item in
                MicroActionItemView(
                    viewModel: viewModel, 
                    item: item, 
                    editingItemId: $editingItemId,
                    onRequestDelayedSort: handleDelayedSort
                )
            }
        }
        .animation(.spring(response: 0.3), value: sortedItems)
    }
    
    /// 添加小行动视图 - 重新设计的输入框
    private var addMicroActionView: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 输入框放在列表顶部
            HStack(alignment: .center, spacing: 8) {
                // 小行动勾选框占位，保持对齐
                RoundedRectangle(cornerRadius: 3)
                    .strokeBorder(Color.clear)
                    .frame(width: 18, height: 18)
                    .opacity(0)
                
                // 输入框
                ZStack(alignment: .leading) {
                    // 占位文本
                    if viewModel.newChecklistItemTitle.isEmpty {
                        Text("添加小行动...")
                            .foregroundColor(.gray.opacity(0.7))
                            .font(.system(size: 15))
                            .padding(.vertical, 8)
                    }
                    
                    // 真正的输入框
                    TextField("", text: $viewModel.newChecklistItemTitle)
                        .font(.system(size: 15))
                        .focused($isInputFocused)
                        .submitLabel(.done)
                        .padding(.vertical, 8)
                        .onSubmit {
                            addItem()
                        }
                }
                .background(GeometryReader { geo in
                    Color.clear.preference(key: ViewHeightKey.self, value: geo.size.height)
                })
                
                // 添加按钮 - 只在有内容时显示
                if !viewModel.newChecklistItemTitle.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    Button("添加") {
                        addItem()
                    }
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.accentColor)
                }
            }
            .padding(.vertical, 2)
            .background(Color.white.opacity(0.001)) // 透明背景但可点击
            .onTapGesture {
                isInputFocused = true
            }
            
            // 分隔线，与列表项分隔
            if !sortedItems.isEmpty {
                Divider()
                    .padding(.leading, 26) // 与小行动文本对齐
                    .padding(.vertical, 2)
            }
        }
        .onPreferenceChange(ViewHeightKey.self) { height in
            textFieldHeight = height
        }
    }
    
    // 自定义PreferenceKey来获取视图高度
    struct ViewHeightKey: PreferenceKey {
        static var defaultValue: CGFloat = 0
        static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
            value = nextValue()
        }
    }
    
    // MARK: - 方法
    
    /// 添加新小行动
    private func addItem() {
        if !viewModel.newChecklistItemTitle.isEmpty {
            // 标记正在添加状态
            isAdding = true
            
            // 保存当前输入内容
            let title = viewModel.newChecklistItemTitle
            
            // 先清空输入框，然后添加小行动
            viewModel.newChecklistItemTitle = ""
            
            // 添加小行动
            withAnimation(.spring(response: 0.2)) {
                let newItem = viewModel.addChecklistItem(title)
                // 保存新添加的小行动ID
                if let id = newItem?.id {
                    recentlyAddedItemId = id
                }
            }
            
            // 添加触觉反馈
            UIImpactFeedbackGenerator(style: .light).impactOccurred()
            
            // 延迟重新聚焦到输入框，确保输入框已经准备好
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                isInputFocused = true
            }
            
            // 延迟重置添加状态，让新添加的小行动显示一段时间后恢复正常排序
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                withAnimation(.easeInOut) {
                    isAdding = false  // 添加完成
                    recentlyAddedItemId = nil
                }
            }
        }
    }
    
    /// 处理延迟排序请求
    private func handleDelayedSort(itemId: UUID, isCompletingNow: Bool) {
        // 添加到动画项目列表
        animatingItems[itemId] = isCompletingNow
        
        // 2秒后移除，使其回到正常排序
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            
            // 直接修改状态，让SwiftUI自动处理动画
            self.animatingItems.removeValue(forKey: itemId)
        }
    }
}

// MARK: - 预览

struct MicroActionSectionPreview: View {
    var body: some View {
        let task = Task(title: "测试行动", status: "Inbox")
        let taskManager = DependencyContainer.taskManager()
        let thoughtRepository = DependencyContainer.thoughtRepository()
        let viewModel = TaskDetailViewModel(task: task, taskManager: taskManager, thoughtRepository: thoughtRepository)
        
        return MicroActionSectionView(viewModel: viewModel)
            .frame(width: 375)
            .padding()
    }
}

#Preview {
    MicroActionSectionPreview()
}
