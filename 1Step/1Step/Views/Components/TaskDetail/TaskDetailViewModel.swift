import SwiftUI
import SwiftData

/// 行动详情视图的视图模型，管理行动编辑状态
class TaskDetailViewModel: BaseTaskViewModel {
    /// 当前行动
    @Published var task: Task
    
    /// 思绪记录仓库
    private var thoughtRepository: ThoughtRepository
    
    /// 标题草稿
    @Published var titleDraft: String = ""
    
    /// 笔记草稿
    @Published var notesDraft: String = ""
    
    /// 行动台输入内容
    @Published var workbenchInput: String = ""
    
    /// 是否正在编辑标题
    @Published var isEditingTitle: Bool = false
    
    /// 是否正在编辑笔记
    @Published var isEditingNotes: Bool = false
    
    /// 是否显示删除确认对话框
    @Published var isShowingDeleteConfirmation: Bool = false
    
    /// 是否显示项目选择器
    @Published var isShowingProjectSelector: Bool = false
    
    /// 是否显示标签选择器
    @Published var isShowingTagSelector: Bool = false
    
    /// 是否显示标签编辑器
    @Published var isShowingTagEditor: Bool = false
    
    /// 是否显示状态选择器
    @Published var isShowingStatusPicker: Bool = false
    
    /// 新建检查清单项的标题
    @Published var newChecklistItemTitle: String = ""
    
    /// 是否正在添加检查清单项
    @Published var isAddingChecklistItem: Bool = false
    
    /// 行动台记录
    @Published var workbenchThoughts: [Thought] = []
    
    /// 行动台是否展开
    @Published var isWorkbenchExpanded: Bool = false
    
    /// ActionFocus状态管理器
    private var actionFocusStateManager: ActionFocusStateManager {
        let defaults = UserDefaults.standard
        guard let data = defaults.data(forKey: "actionFocusStates"),
              let manager = try? JSONDecoder().decode(ActionFocusStateManager.self, from: data) else {
            return ActionFocusStateManager()
        }
        return manager
    }
    
    /// 获取当前任务的ActionFocus状态
    var currentActionFocusState: ActionFocusState? {
        return actionFocusStateManager.getState(for: task.id)
    }
    
    /// 当前是否有ActionFocus状态
    var hasActionFocusState: Bool {
        return currentActionFocusState != nil
    }
    
    /// 恢复ActionFocus状态的回调
    var onRestoreActionFocus: ((UUID, UUID, [UUID]) -> Void)?
    
    /// 可用的项目列表
    var availableProjects: [Project] {
        // 始终使用TaskManager获取项目列表
        return taskManager.getAllProjects()
    }
    
    /// 可用的标签列表
    var availableTags: [String] {
        // 从数据库获取标签列表，如果你有实际的标签模型，可以替换为真实数据
        return ["重要", "紧急", "计划", "想法", "会议", "阅读", "写作"]
    }
    
    // MARK: - 初始化
    
    /// 基于Repository模式的初始化方法
    init(task: Task, 
         taskManager: TaskManager = DependencyContainer.taskManager(),
         thoughtRepository: ThoughtRepository = DependencyContainer.thoughtRepository()) {
        
        self.task = task
        self.thoughtRepository = thoughtRepository
        
        // 初始化父类
        super.init(taskManager: taskManager)
        
        // 初始化草稿内容
        self.titleDraft = task.title
        self.notesDraft = task.notes
        
        // 加载行动台记录
        loadWorkbenchThoughts()
    }
    
    // 实现父类要求的 loadTasks 方法
    override func loadTasks() {
        // 对于详情视图，我们不需要加载任务列表
        // 这是一个空实现
    }
    
    // MARK: - 行动台方法
    
    /// 加载行动台记录
    func loadWorkbenchThoughts() {
        // 获取最新的20条记录，倒序排列（最新的在前）
        workbenchThoughts = thoughtRepository.getWorkbenchThoughts(taskId: task.id, limit: 20)
    }
    
    /// 添加行动台记录
    func addWorkbenchThought() {
        guard !workbenchInput.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        // 使用ThoughtRepository添加记录
        let thought = thoughtRepository.addWorkbenchThought(content: workbenchInput, taskId: task.id)
        
        // 添加到本地列表的开头（最新记录在前）
        workbenchThoughts.insert(thought, at: 0)
        
        // 清空输入框
        workbenchInput = ""
        
        // 发送通知，确保UI更新
        objectWillChange.send()
    }
    
    /// 删除行动台记录
    func deleteWorkbenchThought(_ thought: Thought) {
        // 使用ThoughtRepository删除记录
        thoughtRepository.deleteThought(thought)
        
        // 从本地列表中移除
        if let index = workbenchThoughts.firstIndex(where: { $0.id == thought.id }) {
            workbenchThoughts.remove(at: index)
        }
        
        // 发送通知，确保UI更新
        objectWillChange.send()
    }
    
    // MARK: - 行动更新方法
    
    /// 更新行动标题
    func updateTitle(_ newTitle: String) {
        guard !newTitle.isEmpty, newTitle != task.title else { return }
        
        // 使用TaskManager更新标题
        task.title = newTitle
        taskManager.updateTask(task)
    }
    
    /// 更新行动笔记
    func updateNotes(_ newNotes: String) {
        if newNotes != task.notes {
            // 使用TaskManager更新笔记
            task.notes = newNotes
            taskManager.updateTask(task)
        }
    }
    
    /// 更新行动状态
    func updateStatus(_ newStatus: TaskStatus) {
        if newStatus.rawValue != task.status {
            // 如果状态变为完成，自动完成所有小行动
            if newStatus == .done {
                completeAllMicroActions()
            }
            
            // 使用TaskManager更新状态
            task.status = newStatus.rawValue
            taskManager.updateTask(task)
        }
    }
    
    /// 添加标签
    func addTag(_ tag: String) {
        guard !tag.isEmpty, !task.tags.contains(tag) else { return }
        
        // 使用TaskManager添加标签
        task.tags.append(tag)
        taskManager.updateTask(task)
    }
    
    /// 移除标签
    func removeTag(_ tag: String) {
        if let index = task.tags.firstIndex(of: tag) {
            // 使用TaskManager移除标签
            task.tags.remove(at: index)
            taskManager.updateTask(task)
        }
    }
    
    /// 切换标签（添加或移除）
    func toggleTag(_ tag: String) {
        if task.tags.contains(tag) {
            removeTag(tag)
        } else {
            addTag(tag)
        }
    }
    
    /// 批量更新标签
    func updateTags(_ newTags: [String]) {
        // 使用TaskManager更新标签
        task.tags = newTags
        taskManager.updateTask(task)
        objectWillChange.send() // 强制更新视图
    }
    
    /// 获取项目名称
    override func getProjectName(for projectId: UUID?) -> String {
        guard let projectId = projectId else { return "无项目" }
        
        // 使用TaskManager获取项目信息
        if let project = taskManager.getProjectById(projectId) {
            return project.name
        }
        
        return "无项目"
    }
    
    /// 获取项目颜色
    func getProjectColor(for projectId: UUID?) -> Color {
        guard let projectId = projectId else { return .secondary.opacity(0.7) }
        
        // 使用TaskManager获取项目颜色
        if let project = taskManager.getProjectById(projectId),
           let colorString = project.color {
            return colorFromString(colorString)
        }
        
        return .primary
    }
    
    /// 获取标签颜色
    func getTagColor(for tagName: String) -> Color {
        // 使用TaskManager查找标签颜色
        if let tag = taskManager.getTagByName(tagName),
           let colorString = tag.color {
            return colorFromString(colorString)
        }
        
        // 如果找不到标签或颜色，返回默认颜色
        return .primary
    }
    
    /// 字符串转换为颜色
    private func colorFromString(_ colorName: String) -> Color {
        switch colorName.lowercased() {
        case "red": return Color(.systemRed)
        case "orange": return Color(.systemOrange)
        case "yellow": return Color(.systemYellow)
        case "green": return Color(.systemGreen)
        case "blue": return Color(.systemBlue)
        case "purple": return Color(.systemPurple)
        case "teal": return Color(.systemTeal)
        case "indigo": return Color(.systemIndigo)
        case "pink": return Color(.systemPink)
        default:
            // 尝试解析十六进制颜色
            if colorName.hasPrefix("#") {
                let hex = String(colorName.dropFirst())
                var rgbValue: UInt64 = 0
                Scanner(string: hex).scanHexInt64(&rgbValue)
                let r = Double((rgbValue & 0xFF0000) >> 16) / 255.0
                let g = Double((rgbValue & 0x00FF00) >> 8) / 255.0
                let b = Double(rgbValue & 0x0000FF) / 255.0
                return Color(red: r, green: g, blue: b)
            }
            return Color.accentColor
        }
    }
    
    /// 更新项目
    func updateProject(_ projectId: UUID?) {
        // 更新项目ID
        task.project = projectId
        taskManager.saveTask(task)
        objectWillChange.send() // 强制更新视图
    }
    
    /// 更新行动标题
    func updateTaskTitle() {
        updateTitle(titleDraft)
    }
    
    /// 更新行动笔记
    func updateTaskNotes() {
        updateNotes(notesDraft)
    }
    
    /// 删除行动
    func delete() {
        // 使用父类的删除机制
        handleTaskIntent(.delete(task))
    }
    
    // MARK: - 检查清单项方法
    
    /// 添加小行动（检查清单项）
    /// - Parameter title: 小行动标题
    /// - Returns: 新添加的小行动项，如果标题为空则返回 nil
    func addChecklistItem(_ title: String) -> ChecklistItem? {
        guard !title.isEmpty else { return nil }
        
        // 使用TaskManager添加小行动，确保在同一个SwiftData事务中完成
        let newItem = taskManager.addChecklistItemToTask(task, title: title, createdAt: Date())
        
        // 强制更新视图
        objectWillChange.send()
        
        return newItem
    }
    
    /// 移除小行动项
    func removeChecklistItem(_ item: ChecklistItem) {
        // 使用TaskManager移除小行动，确保在同一个SwiftData事务中完成
        taskManager.removeChecklistItemFromTask(task, itemId: item.id)
        
        // 强制更新视图
        objectWillChange.send()
    }
    
    /// 切换小行动完成状态
    func toggleChecklistItem(_ item: ChecklistItem) {
        // 使用TaskManager切换小行动状态，确保在同一个SwiftData事务中完成
        taskManager.toggleChecklistItemCompletion(task, itemId: item.id)
        
        // 强制更新视图
        objectWillChange.send()
    }
    
    /// 更新小行动标题
    func updateChecklistItemTitle(_ item: ChecklistItem, newTitle: String) {
        guard !newTitle.isEmpty else { return }
        
        // 通过TaskManager更新小行动标题
        taskManager.updateChecklistItemTitle(task, itemId: item.id, newTitle: newTitle)
        
        // 强制更新视图
        objectWillChange.send()
    }
    
    /// 当行动完成时，将所有小行动标记为已完成
    func completeAllMicroActions() {
        // 在模型上更新数据
        if let checklist = task.checklist {
            for index in checklist.indices {
                task.checklist?[index].isCompleted = true
            }
            
            // 使用TaskManager保存更改
            do {
                try taskManager.save()
                objectWillChange.send() // 强制更新视图
            } catch {
                print("完成所有小行动失败: \(error)")
            }
        }
    }
    
    // MARK: - 辅助方法
    
    /// 保存所有更改到数据库
    func saveAllChanges() {
        // 确保任何未保存的更改都被应用
        if isEditingTitle {
            updateTitle(titleDraft)
            isEditingTitle = false
        }
        
        if isEditingNotes {
            updateNotes(notesDraft)
            isEditingNotes = false
        }
        
        // 保存所有变更
        taskManager.saveTask(task)
    }
    
    // 不再需要saveChanges方法，所有数据保存由Repository处理
}
