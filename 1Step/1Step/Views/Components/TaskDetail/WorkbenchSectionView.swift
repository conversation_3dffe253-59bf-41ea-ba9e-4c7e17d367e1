import SwiftUI
import SwiftData

/// 行动台模块入口视图，触发行动台 Sheet
struct WorkbenchSectionView: View {
    // MARK: - 属性

    /// 触发 Sheet 显示的操作
    var action: () -> Void

    // MARK: - 初始化方法

    // 移除了 viewModel 依赖，因为触发操作由外部处理
    init(action: @escaping () -> Void) {
        self.action = action
    }

    // MARK: - 视图主体

    var body: some View {
        VStack(spacing: 0) {
            But<PERSON>(action: action) {
                HStack {
                    Text("行动台")
                        .font(.system(size: 15))
                        .foregroundColor(.secondary) // 将文字颜色改为灰色

                    Spacer()

                    // 指示箭头
                    Image(systemName: "chevron.right")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary.opacity(0.8))
                }
                .padding(.vertical, 12) // 保持垂直内边距
                .padding(.horizontal, 20) // 应用水平内边距
                .frame(maxWidth: .infinity) // 确保HStack占据整个宽度
            }
            .buttonStyle(PlainButtonStyle()) // 使用朴素样式，使其看起来像列表项
            
            Divider() // 保留下方的分隔线
                .padding(.leading, 20) // 分隔线也应用左边距，与内容对齐
        }
    }
} 