import SwiftUI
import SwiftData

/// 行动台独立页面视图
struct WorkbenchView: View {
    // MARK: - 属性

    /// 获取当前颜色模式
    @Environment(\.colorScheme) private var colorScheme

    /// 视图模型
    @StateObject var viewModel: WorkbenchViewModel // 使用 @StateObject，因为此视图拥有其 ViewModel

    /// 输入框焦点状态
    @FocusState private var isInputFocused: Bool

    /// 正在删除的思绪记录ID
    @State private var deletingThoughtId: UUID? = nil
    
    /// 控制是否显示历史思绪
    @State private var isShowingHistoryThoughts: Bool = false
    
    /// 新添加的思绪列表
    @State private var newThoughts: [Thought] = []
    
    /// 是否有历史思绪可加载
    @State private var hasHistoryToLoad: Bool = false
    
    /// 存储已展开的思绪ID集合
    @State private var expandedThoughtIds: Set<UUID> = []
    
    /// 长内容的行数阈值，超过此值将被折叠
    private let longContentThreshold: Int = 3
    
    /// 存储已折叠的日期（按天）
    @State private var collapsedDates: Set<Date> = []

    // MARK: - 初始化方法

    /// 使用 Task 初始化 WorkbenchViewModel
    init(task: Task) {
        // 在初始化器内部创建 StateObject
        _viewModel = StateObject(wrappedValue: WorkbenchViewModel(
            task: task, 
            taskRepository: DependencyContainer.taskRepository(), 
            thoughtRepository: DependencyContainer.thoughtRepository()
        ))
    }

    // MARK: - 视图主体

    var body: some View {
        ScrollViewReader { scrollViewProxy in
            VStack(spacing: 0) {
                // 思绪列表区域
                if isShowingHistoryThoughts {
                    // 显示全部思绪，包括历史和新添加的
                    thoughtList
                } else {
                    // 新思绪列表，根据情况显示加载历史按钮
                    VStack(spacing: 0) {
                        // 只有在有历史思绪时才显示加载历史按钮
                        if hasHistoryToLoad && !isShowingHistoryThoughts {
                            loadHistoryThoughtsButton
                        }
                        
                        // 新添加的思绪列表
                        if !newThoughts.isEmpty {
                            newThoughtsList
                        } else if isShowingHistoryThoughts {
                            // 空状态
                            EmptyView()
                        } else {
                            // 初始空状态视图
                            emptyStateView
                                .padding(.top, hasHistoryToLoad ? 40 : 20)
                        }
                    }
                }

                // 输入区域分隔线
                Divider()

                // 添加思绪输入框
                addThoughtInputArea(scrollViewProxy: scrollViewProxy)
                    .padding(.horizontal)
                    .padding(.top, 8)
                    .padding(.bottom, 10) // 给键盘留出一些空间或安全区域
            }
            .navigationTitle("") // 清空导航栏标题，使用自定义标题
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .principal) {
                    // 只显示任务标题
                    Text(viewModel.taskTitle)
                        .font(.headline)
                        .foregroundColor(.secondary)
                }
            }
            .onAppear {
                // 检查是否有历史思绪可加载，但不加载内容
                hasHistoryToLoad = viewModel.hasHistoryThoughts()
                // 埋点：进入行动台页面
                AnalyticsService.shared.trackEvent(AnalyticsService.EventNames.workbenchViewEntered)
            }
            // 可以添加手势来取消输入框焦点
            .onTapGesture {
                isInputFocused = false
            }
            .onChange(of: isInputFocused) { focused in
                if focused {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        if let lastThoughtId = determineLastThoughtId() {
                            withAnimation {
                                scrollViewProxy.scrollTo(lastThoughtId, anchor: .bottom)
                            }
                        }
                    }
                }
            }
        }
    }

    // MARK: - 子视图

    /// 加载历史思绪按钮 - 极简版
    private var loadHistoryThoughtsButton: some View {
        Button(action: {
            // 加载思绪并显示
            viewModel.loadThoughts()
            withAnimation(.easeInOut(duration: 0.3)) {
                isShowingHistoryThoughts = true
            }
        }) {
            Text("加载历史思绪")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .padding(.vertical, 12)
        }
        .buttonStyle(.plain)
        .frame(maxWidth: .infinity, alignment: .center)
        .padding(.top, 16)
        .padding(.bottom, 8)
    }
    
    /// 新添加的思绪列表视图
    private var newThoughtsList: some View {
        List {
            ForEach(newThoughts) { thought in
                thoughtItemView(thought)
                    .id(thought.id)
                    .listRowSeparator(.hidden)
                    .listRowBackground(Color.clear)
                    .listRowInsets(EdgeInsets(top: 6, leading: 16, bottom: 6, trailing: 16))
                    .swipeActions(edge: .trailing, allowsFullSwipe: true) {
                        Button(role: .destructive, action: {
                            deleteNewThought(thought)
                        }) {
                            Image(systemName: "zzz")
                        }
                        .tint(Color(.systemGray3)) // 使用灰色按钮
                    }
            }
        }
        .listStyle(.plain)
        .scrollContentBackground(.hidden)
    }

    /// 思绪列表视图
    private var thoughtList: some View {
        Group {
            if viewModel.thoughts.isEmpty && newThoughts.isEmpty {
                // 空状态视图
                emptyStateView
                    .frame(maxHeight: .infinity) // 占据剩余空间
            } else {
                // 使用 List 替代 ScrollView 以支持滑动删除
                List {
                    // 顶部显示"加载历史思绪"标签已完成
                    Text("已加载历史思绪")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity, alignment: .center)
                        .listRowSeparator(.hidden)
                        .listRowBackground(Color.clear)
                        .padding(.vertical, 8)
                    
                    // 显示合并后的思绪列表：历史 + 新添加的
                    let allThoughts = getCombinedThoughts()
                    
                    // 按日期分组处理思绪
                    let groupedThoughts = groupThoughtsByDate(allThoughts)
                    
                    // 遍历日期组
                    ForEach(Array(groupedThoughts.keys.sorted().enumerated()), id: \.element) { _, date in
                        let thoughts = groupedThoughts[date] ?? []
                        let isCurrentDay = isToday(date)
                        
                        // 日期标签（可点击折叠，但今天的不可折叠）
                        if !isCurrentDay {
                            // 过去的日期 - 可点击折叠
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    toggleDateCollapse(date)
                                }
                            }) {
                                Text(formatDate(date))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .frame(maxWidth: .infinity, alignment: .center)
                                    .padding(.vertical, 8)
                            }
                            .buttonStyle(.plain)
                            .listRowSeparator(.hidden)
                            .listRowBackground(Color.clear)
                        } else {
                            // 今天的日期 - 不可点击
                            Text(formatDate(date))
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .frame(maxWidth: .infinity, alignment: .center)
                                .padding(.vertical, 8)
                                .listRowSeparator(.hidden)
                                .listRowBackground(Color.clear)
                        }
                        
                        // 如果日期没有被折叠，或者是今天，显示思绪内容
                        if !isDateCollapsed(date) || isCurrentDay {
                            ForEach(thoughts) { thought in
                                thoughtItemView(thought)
                                    .id(thought.id)
                                    .listRowSeparator(.hidden)
                                    .listRowBackground(Color.clear)
                                    .listRowInsets(EdgeInsets(top: 6, leading: 16, bottom: 6, trailing: 16))
                                    .swipeActions(edge: .trailing, allowsFullSwipe: true) {
                                        Button(role: .destructive, action: {
                                            deleteThoughtAndUpdateUI(thought)
                                        }) {
                                            Image(systemName: "zzz")
                                        }
                                        .tint(Color(.systemGray3))
                                    }
                            }
                        }
                    }
                }
                .listStyle(.plain)
                .scrollContentBackground(.hidden)
            }
        }
    }
    
    /// 获取合并后的思绪列表（历史+新添加）并按时间排序
    private func getCombinedThoughts() -> [Thought] {
        // 创建一个包含所有思绪的数组
        var allThoughts = viewModel.thoughts
        
        // 添加新思绪前先检查是否已存在（避免重复）
        for newThought in newThoughts {
            if !allThoughts.contains(where: { $0.id == newThought.id }) {
                allThoughts.append(newThought)
            }
        }
        
        // 按创建时间排序
        return allThoughts.sorted(by: { $0.createdAt < $1.createdAt })
    }
    
    /// 删除新思绪并更新UI
    private func deleteNewThought(_ thought: Thought) {
        // 从本地删除
        withAnimation {
            newThoughts.removeAll(where: { $0.id == thought.id })
        }
        
        // 从数据库删除
        viewModel.deleteThought(thought)
    }
    
    /// 删除思绪并更新所有UI
    private func deleteThoughtAndUpdateUI(_ thought: Thought) {
        // 从本地删除
        withAnimation {
            newThoughts.removeAll(where: { $0.id == thought.id })
        }
        
        // 从数据库删除并更新viewModel的thoughts
        viewModel.deleteThought(thought)
    }

    /// 判断是否应该为指定索引的思绪显示日期分隔
    private func shouldShowDateSeparator(for index: Int, in thoughts: [Thought]) -> Bool {
        guard index >= 0 && index < thoughts.count else { return false }
        
        // 第一条消息总是显示日期
        if index == 0 { return true }
        
        let currentDate = Calendar.current.startOfDay(for: thoughts[index].createdAt)
        let previousDate = Calendar.current.startOfDay(for: thoughts[index - 1].createdAt)
        
        // 只有日期变化时才显示分隔
        return currentDate != previousDate
    }
    
    /// 格式化日期为"月-日"格式
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "M-d"
        return formatter.string(from: date)
    }
    
    /// 判断日期是否为今天
    private func isToday(_ date: Date) -> Bool {
        return Calendar.current.isDateInToday(date)
    }
    
    /// 获取日期的0点时间（用于比较和存储）
    private func startOfDay(for date: Date) -> Date {
        return Calendar.current.startOfDay(for: date)
    }
    
    /// 检查日期是否被折叠
    private func isDateCollapsed(_ date: Date) -> Bool {
        let dayStart = startOfDay(for: date)
        return collapsedDates.contains(dayStart)
    }
    
    /// 切换日期的折叠状态
    private func toggleDateCollapse(_ date: Date) {
        let dayStart = startOfDay(for: date)
        if collapsedDates.contains(dayStart) {
            collapsedDates.remove(dayStart)
        } else {
            collapsedDates.insert(dayStart)
        }
    }

    /// 添加思绪记录输入区域
    private func addThoughtInputArea(scrollViewProxy: ScrollViewProxy) -> some View {
        ZStack(alignment: .bottomTrailing) {
            // TextEditor - 调整内边距和高度
            TextEditor(text: $viewModel.newThoughtContent)
                .font(.system(size: 16))
                .frame(minHeight: 44, maxHeight: 90) // 修复：使用 minHeight 替代 height
                .scrollContentBackground(.hidden)
                .background(Color.clear)
                .focused($isInputFocused)
                .padding(EdgeInsets(top: 8, leading: 12, bottom: 8, trailing: 45))
                .onTapGesture {
                    isInputFocused = true
                }

            // Placeholder - 对齐调整
            if viewModel.newThoughtContent.isEmpty {
                Text("记录想法、情绪或遇到的阻力...")
                    .font(.system(size: 16))
                    .foregroundColor(Color(.placeholderText))
                    .padding(EdgeInsets(top: 16, leading: 17, bottom: 8, trailing: 45))
                    .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
                    .allowsHitTesting(false)
            }

            // Button - 位置底部右侧
            Button(action: {
                // 添加思绪并更新本地列表
                let newThought = viewModel.addThoughtAndReturn()
                if let thought = newThought {
                    withAnimation(.easeInOut(duration: 0.1)) {
                        // 将新思绪添加到本地列表
                        newThoughts.append(thought)
                    }
                    
                    // 确保在视图更新后执行滚动操作
                    let newThoughtID = thought.id
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            scrollViewProxy.scrollTo(newThoughtID, anchor: .bottom)
                        }
                    }
                }
            }) {
                Image(systemName: "arrow.up.circle.fill")
                    .font(.system(size: 30))
                    .symbolRenderingMode(.palette)
                    .foregroundStyle(
                        viewModel.newThoughtContent.trimmed().isEmpty ? Color(.placeholderText) : Color.white,
                        Color.accentColor
                    )
                    .animation(.easeInOut(duration: 0.15), value: viewModel.newThoughtContent.trimmed().isEmpty)
                    .padding(4)
            }
            .buttonStyle(.plain)
            .disabled(viewModel.newThoughtContent.trimmed().isEmpty)
            .padding(.trailing, 8)
            .padding(.bottom, 6)
        }
        // 为整个ZStack添加手势，确保点击任何区域都能获得焦点
        .onTapGesture {
            isInputFocused = true
        }
        .contentShape(Rectangle()) // 整个ZStack区域可点击
        .frame(height: 44) // 固定初始高度
        .padding(.vertical, 6)
        .padding(.horizontal, 8)
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 14, style: .continuous))
    }

    /// 单个思绪记录项视图 (添加滑动删除功能)
    private func thoughtItemView(_ thought: Thought) -> some View {
        // 移除了折叠逻辑，直接展示完整内容
        return VStack(alignment: .trailing, spacing: 0) {
            // 思绪记录内容
            Text(thought.content)
                .font(.system(size: 15))
                .foregroundColor(.primary)
                .padding(12)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemBlue).opacity(0.15))
                )
                .frame(maxWidth: .infinity, alignment: .trailing)
                .padding(.horizontal, 16)
                .padding(.vertical, 4)
            
            // 移除了折叠/展开按钮
        }
    }
    
    /// 根据颜色模式返回适当的思绪背景色
    private var thoughtBackgroundColor: Color {
        colorScheme == .dark 
            ? Color(.systemGray5).opacity(0.7) // 黑暗模式下使用稍亮的颜色
            : Color(.systemGray6).opacity(0.8) // 明亮模式下使用原来的颜色
    }

    /// 空状态视图 (基本与 WorkbenchSectionView 相同)
    private var emptyStateView: some View {
        VStack(spacing: 12) {
            Image(systemName: "lightbulb.fill") // 换个图标试试
                .font(.system(size: 40))
                .foregroundColor(.secondary.opacity(0.5))
            Text("这里是你的行动台")
                .font(.headline)
                .foregroundColor(.secondary)
            Text("随时记录与此行动相关的想法、情绪或遇到的问题吧！")
                .font(.subheadline)
                .foregroundColor(.secondary.opacity(0.8))
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity) // 占据可用空间
    }

    /// 按日期分组思绪
    private func groupThoughtsByDate(_ thoughts: [Thought]) -> [Date: [Thought]] {
        var result: [Date: [Thought]] = [:]
        
        for thought in thoughts {
            let dayStart = startOfDay(for: thought.createdAt)
            if result[dayStart] == nil {
                result[dayStart] = []
            }
            result[dayStart]?.append(thought)
        }
        
        return result
    }

    /// 确定当前应该滚动的最后一个思绪的ID
    private func determineLastThoughtId() -> UUID? {
        if isShowingHistoryThoughts {
            return getCombinedThoughts().last?.id
        } else {
            return newThoughts.last?.id
        }
    }
}



// MARK: - 扩展

// 字符串扩展，方便去除首尾空格
extension String {
    func trimmed() -> String {
        self.trimmingCharacters(in: .whitespacesAndNewlines)
    }
} 