import SwiftUI
import SwiftData

/// 行动详情页的标签编辑器视图
struct TagEditorView: View {
    // MARK: - 环境与状态
    
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.dismiss) private var dismiss
    
    @Binding var selectedTags: [String]
    @StateObject private var viewModel: TagEditorViewModel
    @State private var searchText: String = ""
    @State private var isCreatingTag: Bool = false
    
    // MARK: - 初始化
    
    init(
        selectedTags: Binding<[String]>,
        tagRepository: TagRepository = DependencyContainer.tagRepository()
    ) {
        self._selectedTags = selectedTags
        _viewModel = StateObject(wrappedValue: TagEditorViewModel(tagRepository: tagRepository))
    }
    
    // MARK: - 主视图
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // 搜索框
                searchBar
                    .padding(.horizontal)
                    .padding(.top, 8)
                    .padding(.bottom, 8)
                
                // 已选标签显示区域
                if !selectedTags.isEmpty {
                    selectedTagsView
                    
                    Divider()
                }
                
                // 标签列表
                ScrollView {
                    LazyVStack(spacing: 0) {
                        // 创建标签按钮（仅当搜索内容不为空且搜索的标签不存在时显示）
                        if !searchText.isEmpty && !viewModel.tagExists(with: searchText) {
                            createTagButton
                            Divider()
                        }
                        
                        // 筛选后的标签
                        ForEach(viewModel.filteredTags(searchText: searchText), id: \.id) { tag in
                            tagRow(tag)
                            Divider()
                        }
                    }
                }
            }
            .background(Color(.systemBackground))
            .navigationTitle("选择标签")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                    .fontWeight(.medium)
                }
            }
            .onAppear {
                viewModel.loadTags()
            }
        }
    }
    
    // MARK: - 子视图
    
    /// 搜索框
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(Color.gray)
            
            TextField("搜索或创建标签", text: $searchText)
                .font(.system(size: 16))
            
            if !searchText.isEmpty {
                Button {
                    searchText = ""
                } label: {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(Color.gray)
                }
            }
        }
        .padding(8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(.systemGray6))
        )
    }
    
    /// 已选标签显示区域
    private var selectedTagsView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                ForEach(selectedTags, id: \.self) { tagName in
                    HStack(spacing: 4) {
                        Text("#\(tagName)")
                            .font(.system(size: 14))
                            .foregroundColor(getTagColor(for: tagName))
                        
                        Button {
                            if let index = selectedTags.firstIndex(of: tagName) {
                                selectedTags.remove(at: index)
                            }
                        } label: {
                            Image(systemName: "xmark")
                                .font(.system(size: 10))
                                .foregroundColor(Color.secondary)
                        }
                    }
                    .padding(.vertical, 4)
                    .padding(.horizontal, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 4)
                            .fill(getTagColor(for: tagName).opacity(0.1))
                    )
                }
            }
            .padding(.horizontal)
            .padding(.vertical, 8)
        }
    }
    
    /// 已选标签的标记
    private func selectedTagBadge(_ tagName: String) -> some View {
        HStack(spacing: 4) {
            Text(tagName)
                .font(.caption)
                .foregroundColor(Color.primary)
            
            Button {
                selectedTags.removeAll { $0 == tagName }
            } label: {
                Image(systemName: "xmark")
                    .font(.system(size: 8, weight: .bold))
                    .foregroundColor(Color.secondary)
            }
        }
        .padding(.vertical, 4)
        .padding(.horizontal, 8)
        .background(
            RoundedRectangle(cornerRadius: 4)
                .fill(Color.accentColor.opacity(0.1))
        )
    }
    
    /// 创建新标签按钮
    private var createTagButton: some View {
        Button {
            createNewTag()
        } label: {
            HStack {
                Image(systemName: "plus.circle.fill")
                    .foregroundColor(Color.accentColor)
                
                Text("创建标签 \"\(searchText)\"")
                    .foregroundColor(Color.primary)
                
                Spacer()
            }
            .padding(.vertical, 12)
            .padding(.horizontal)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    /// 单个标签行
    private func tagRow(_ tag: Tag) -> some View {
        Button(action: {
            toggleTagSelection(tag.name)
        }) {
            HStack {
                // 标签图标和名称
                HStack(spacing: 8) {
                    if let emoji = tag.emoji, !emoji.isEmpty {
                        Text(emoji)
                            .font(.system(size: 16))
                    } else {
                        Text("#")
                            .font(.system(size: 16, weight: .bold))
                            .foregroundColor(viewModel.tagColor(for: tag, colorScheme: colorScheme))
                    }
                    
                    Text(tag.name)
                        .foregroundColor(viewModel.tagColor(for: tag, colorScheme: colorScheme))
                }
                
                Spacer()
                
                // 勾选状态
                if selectedTags.contains(tag.name) {
                    Image(systemName: "checkmark")
                        .foregroundColor(Color.accentColor)
                }
            }
            .padding(.vertical, 12)
            .padding(.horizontal)
            // 添加点击时的背景变化，提供视觉反馈
            .contentShape(Rectangle())
            .background(selectedTags.contains(tag.name) ? Color.accentColor.opacity(0.1) : Color.clear)
        }
        // 使用默认按钮样式，确保点击效果正常
        .buttonStyle(.plain)
    }
    
    // MARK: - 辅助方法
    
    /// 标签颜色处理
    private func tagColor(for tag: Tag) -> Color {
        if let colorName = tag.color, !colorName.isEmpty {
            return colorFromString(colorName)
        }
        return Color.accentColor
    }
    
    /// 获取标签颜色
    private func getTagColor(for tagName: String) -> Color {
        // 从所有标签中查找匹配的标签
        if let tag = viewModel.allTags.first(where: { $0.name == tagName }),
           let colorString = tag.color {
            return colorFromString(colorString)
        }
        return Color.accentColor
    }
    
    /// 从字符串获取颜色
    private func colorFromString(_ colorString: String) -> Color {
        switch colorString.lowercased() {
        case "red": return Color(.systemRed)
        case "orange": return Color(.systemOrange)
        case "yellow": return Color(.systemYellow)
        case "green": return Color(.systemGreen)
        case "blue": return Color(.systemBlue)
        case "purple": return Color(.systemPurple)
        case "pink": return Color(.systemPink)
        default:
            // 尝试解析十六进制颜色
            if colorString.hasPrefix("#") {
                let hex = String(colorString.dropFirst())
                var rgbValue: UInt64 = 0
                Scanner(string: hex).scanHexInt64(&rgbValue)
                let r = Double((rgbValue & 0xFF0000) >> 16) / 255.0
                let g = Double((rgbValue & 0x00FF00) >> 8) / 255.0
                let b = Double(rgbValue & 0x0000FF) / 255.0
                return Color(red: r, green: g, blue: b)
            }
            return Color.accentColor
        }
    }
    
    // MARK: - 计算属性
    

    
    // MARK: - 方法
    

    
    /// 切换标签选择状态
    private func toggleTagSelection(_ tagName: String) {
        
        if selectedTags.contains(tagName) {
            // 如果已包含，则移除
            selectedTags.removeAll { $0 == tagName }
        } else {
            // 如果未包含，则添加
            selectedTags.append(tagName)
            
            // 选择后延迟一小段时间再关闭，让用户看到选中效果
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                dismiss()
            }
        }
    }
    
    /// 创建新标签
    private func createNewTag() {
        guard !searchText.isEmpty else { return }
        
        // 检查是否已存在（不区分大小写）
        if viewModel.tagExists(with: searchText) {
            if let existingTag = viewModel.allTags.first(where: { $0.name.lowercased() == searchText.lowercased() }) {
                // 如果存在但大小写不同，使用已存在的标签名
                if !selectedTags.contains(existingTag.name) {
                    selectedTags.append(existingTag.name)
                }
            }
            return
        }
        
        // 创建新标签
        if let newTag = viewModel.createNewTag(name: searchText) {
            // 添加到选中列表
            if !selectedTags.contains(newTag.name) {
                selectedTags.append(newTag.name)
            }
        }
        
        searchText = ""
    }
}

#Preview {
    NavigationStack {
        TagEditorView(selectedTags: .constant(["工作", "重要"]))
    }
}
