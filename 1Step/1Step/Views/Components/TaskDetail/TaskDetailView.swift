import SwiftUI
import SwiftData
import Combine
import Foundation

// MARK: - 环境值扩展

private struct ScreenSizeKey: EnvironmentKey {
    static let defaultValue: CGSize = UIScreen.main.bounds.size
}

private struct KeyboardShowingKey: EnvironmentKey {
    static let defaultValue: Bool = false
}

extension EnvironmentValues {
    var screenSize: CGSize {
        get { self[ScreenSizeKey.self] }
        set { self[ScreenSizeKey.self] = newValue }
    }
    
    var keyboardShowing: Bool {
        get { self[KeyboardShowingKey.self] }
        set { self[KeyboardShowingKey.self] = newValue }
    }
}

/// 行动详情视图，使用系统Sheet功能展示行动详情
struct TaskDetailView: View {
    // MARK: - 属性
    
    /// 视图模型
    @ObservedObject private var viewModel: TaskDetailViewModel
    
    /// 当前路径数据（由调用方构建）
    private let currentPathData: CurrentPathData?
    
    /// 颜色模式
    @Environment(\.colorScheme) private var colorScheme
    
    /// 标题编辑焦点状态
    @FocusState private var isTitleFocused: Bool
    
    /// 笔记编辑焦点状态
    @FocusState private var isNotesFocused: Bool
    
    /// 环境中的屏幕尺寸
    @Environment(\.screenSize) private var screenSize
    
    /// 环境中的键盘状态
    @Environment(\.keyboardShowing) private var keyboardShowing
    
    /// 环境中的dismiss操作
    @Environment(\.dismiss) private var dismiss
    
    /// 删除确认弹窗状态
    @State private var showingDeleteAlert = false
    
    /// 删除意图回调：当用户确认删除时调用
    var onDeleteIntent: ((Task) -> Void)?
    
    /// 恢复ActionFocus状态的回调
    var onRestoreActionFocus: ((UUID, UUID, [UUID]) -> Void)?
    
    /// 项目选择器锚点
    @State private var projectAnchor: PopoverAttachmentAnchor = .point(.zero)
    
    /// 标签选择器锚点
    @State private var tagAnchor: PopoverAttachmentAnchor = .point(.zero)
    
    /// 动画状态
    @State private var animationState: Double = 0
    
    /// 控制行动台 Sheet 显示状态
    @State private var isShowingWorkbenchSheet = false
    
    // MARK: - 初始化方法
    
    /// 来源页面类型
    enum SourcePage {
        case doing     // 来自一步列表
        case focus     // 来自一步模式
        case other     // 来自任务列表等其他页面
    }
    
    /// 来源页面
    private var sourcePageType: SourcePage
    
    init(
        task: Task,
        sourcePageType: SourcePage = .other,
        currentPathData: CurrentPathData? = nil,
        onDeleteIntent: ((Task) -> Void)? = nil,
        onRestoreActionFocus: ((UUID, UUID, [UUID]) -> Void)? = nil
    ) {
        self.sourcePageType = sourcePageType
        self.currentPathData = currentPathData
        self.onDeleteIntent = onDeleteIntent
        self.onRestoreActionFocus = onRestoreActionFocus
        
        _viewModel = ObservedObject(wrappedValue: TaskDetailViewModel(
            task: task,
            taskManager: DependencyContainer.taskManager(),
            thoughtRepository: DependencyContainer.thoughtRepository()
        ))
    }
    
    // 行动加载时初始化标题草稿
    private func initializeTaskData() {
        // 确保标题草稿被正确初始化
        if viewModel.titleDraft.isEmpty {
            viewModel.titleDraft = viewModel.task.title
        }
    }
    
    // MARK: - 视图主体
    
    var body: some View {
        VStack(spacing: 0) {
            // 顶部栏
            topBarView
            
            // 内容区域
            ScrollView {
                VStack(spacing: 16) {
                    // 标题和备注区域
                    titleAndNotesView
                        .cardStyle()
                    
                    // 导航区域
                    if sourcePageType == .focus {
                        // 来自一步模式：显示当前路径
                        if let currentPathData = currentPathData, !currentPathData.pathBreadcrumb.isEmpty {
                            ActionPathComponent(
                                style: .fullPath,
                                task: viewModel.task,
                                currentPathData: currentPathData
                            )
                            .cardStyle()
                        }
                    } else if viewModel.hasActionFocusState && isValidCurrentActionFocusState {
                        // 来自其他页面：显示最近一步（基于ActionFocusState的历史记录）
                        ActionPathComponent(
                            style: .recentStep,
                            task: viewModel.task,
                            actionFocusState: viewModel.currentActionFocusState,
                            onNavigate: { taskId, checklistItemId, subStepPath in
                                onRestoreActionFocus?(taskId, checklistItemId, subStepPath)
                            }
                        )
                        .cardStyle()
                    }
                    
                    // 行动台区域 (现在是按钮)
                    WorkbenchSectionView(action: {
                        isShowingWorkbenchSheet = true // 触发 Sheet 显示
                    })
                        .cardStyle()
                    
                    // 小行动区域
                    MicroActionSectionView(viewModel: viewModel, sourcePageType: sourcePageType)
                        .cardStyle()
                    
                    // 项目和标签区域
                    projectTagsSectionView
                        .cardStyle()
                }
                .padding(.bottom, keyboardShowing ? screenSize.height * 0.3 : 16)
                .padding(.top, 16)
            }
            .background(AppColors.UI.systemGray6(for: colorScheme))
            .scrollDismissesKeyboard(.never)
        }
        .background(AppColors.UI.systemGray6(for: colorScheme))
        // 添加 .sheet 修饰符来呈现 WorkbenchView
        .sheet(isPresented: $isShowingWorkbenchSheet) {
            NavigationStack { // 包裹在 NavigationStack 中，以便 WorkbenchView 可以有标题
                WorkbenchView(task: viewModel.task)
            }
            .presentationDetents([.large]) // 设置 Sheet 默认高度为大尺寸
            // 可以添加 .presentationDragIndicator(.visible) 如果需要拖动指示器
        }
        .alert("删除行动", isPresented: $showingDeleteAlert) {
            Button("取消", role: .cancel) {}
            Button("删除", role: .destructive) {
                onDeleteIntent?(viewModel.task)
                dismiss()
            }
        } message: {
            Text("确定要删除这个行动吗？此操作无法撤销。")
        }
        .onChange(of: isTitleFocused) { oldValue, newValue in
            if !newValue {
                viewModel.updateTaskTitle()
            }
        }
        .onChange(of: isNotesFocused) { oldValue, newValue in
            if !newValue {
                viewModel.updateTaskNotes()
            }
        }
        .onAppear {
            // 设置恢复ActionFocus状态的回调
            viewModel.onRestoreActionFocus = onRestoreActionFocus
            
            // 检查并清理无效的ActionFocus状态
            if viewModel.hasActionFocusState && !isValidCurrentActionFocusState {
                clearInvalidActionFocusState(for: viewModel.task.id)
            }
        }
    }
    
    // MARK: - 顶部栏
    
    private var topBarView: some View {
        HStack(spacing: 12) {
            // 返回按钮
            Button {
                dismiss()
            } label: {
                Image(systemName: "xmark")
                    .font(.system(size: 15, weight: .medium))
                    .foregroundColor(.primary)
                    .frame(width: 30, height: 30)
            }
            
            // 状态菜单
            Menu {
                ForEach([TaskStatus.inbox, TaskStatus.na, TaskStatus.doing, TaskStatus.waiting, TaskStatus.smb]) { status in
                    Button(action: {
                        withAnimation {
                            viewModel.updateStatus(status)
                        }
                    }) {
                        Label {
                            Text(status.description)
                        } icon: {
                            Circle()
                                .fill(status.color(for: colorScheme))
                                .frame(width: 8, height: 8)
                        }
                    }
                }
            } label: {
                HStack(spacing: 4) {
                    Circle()
                        .fill(TaskStatus(rawValue: viewModel.task.status)?.color(for: colorScheme) ?? .gray)
                        .frame(width: 8, height: 8)
                    
                    Text(TaskStatus(rawValue: viewModel.task.status)?.description ?? viewModel.task.status)
                        .font(.system(size: 14))
                        .foregroundColor(.primary)
                    
                    Image(systemName: "chevron.down")
                        .font(.system(size: 12))
                        .foregroundColor(.secondary.opacity(0.7))
                }
                .padding(.vertical, 4)
                .padding(.horizontal, 8)
                .background(Color(.systemGray6).opacity(0.8))
                .cornerRadius(6)
            }
            
            Spacer()
            
            // 完成按钮
            Button {
                dismiss()
            } label: {
                Text("保存")
                    .font(.system(size: 15, weight: .medium))
                    .foregroundColor(.blue)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(Color.clear)
    }
    
    // MARK: - 标题和笔记区域
    
    private var titleAndNotesView: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 集成的标题和笔记输入区域
            VStack(alignment: .leading, spacing: 4) {
                // 标题输入
                TextField("行动标题", text: $viewModel.titleDraft)
                    .font(.system(size: 18, weight: .medium))
                    .padding(.top, 4)
                    .focused($isTitleFocused)
                    .submitLabel(.next)
                    .onSubmit {
                        // 延迟切换焦点，避免滚动跳动
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                            isNotesFocused = true
                        }
                    }
                    .onChange(of: viewModel.titleDraft) { _, newValue in
                        viewModel.updateTitle(newValue)
                    }
                
                Divider()
                    .padding(.vertical, 8)
                
                // 笔记输入
                ZStack(alignment: .topLeading) {
                    if viewModel.notesDraft.isEmpty {
                        Text("添加备注...")
                            .foregroundColor(.secondary.opacity(0.7))
                            .padding(.top, 4)
                    }
                    
                    TextEditor(text: $viewModel.notesDraft)
                        .font(.system(size: 16))
                        .frame(minHeight: 100)
                        .focused($isNotesFocused)
                        .scrollContentBackground(.hidden)
                        .background(Color.clear)
                        .onChange(of: viewModel.notesDraft) { _, newValue in
                            viewModel.updateNotes(newValue)
                        }
                        // 禁用 TextEditor 的自动滚动
                        .scrollDisabled(true)
                }
            }
            .padding(16)
            // 添加 id 使视图在焦点变化时保持稳定
            .id("titleAndNotesContainer")
        }
    }
    
    // MARK: - 笔记区域
    
    private var notesCardView: some View {
        VStack(alignment: .leading, spacing: 0) {
            if viewModel.isEditingNotes {
                TextEditor(text: $viewModel.notesDraft)
                    .scrollContentBackground(.hidden)
                    .background(Color.clear)
                    .font(.system(size: 15))
                    .foregroundColor(AppColors.UI.primaryText)
                    .frame(minHeight: 100, alignment: .topLeading)
                    .padding(.horizontal, 20)
                    .padding(.top, 0)
                    .padding(.bottom, 10)
                    .focused($isNotesFocused)
                    .onChange(of: isNotesFocused) { oldValue, newValue in
                        // 当失去焦点时保存
                        if oldValue && !newValue {
                            viewModel.updateNotes(viewModel.notesDraft)
                            viewModel.isEditingNotes = false
                        }
                    }
            } else {
                HStack {
                    Text(viewModel.task.notes.isEmpty ? "添加笔记..." : viewModel.task.notes)
                        .font(.system(size: 15))
                        .foregroundColor(viewModel.task.notes.isEmpty ? AppColors.UI.gray : AppColors.UI.primaryText)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(.top, 0)
                        .padding(.bottom, 10)
                        .multilineTextAlignment(.leading)
                        .onTapGesture {
                            viewModel.isEditingNotes = true
                            viewModel.notesDraft = viewModel.task.notes
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                isNotesFocused = true
                            }
                        }
                        
                    Spacer()
                }
                .padding(.horizontal, 20)
            }
        }
    }
    
    // MARK: - 检查清单区域
    
    private var checklistCardView: some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack {
                Text("检查清单")
                    .font(.system(size: 15))
                    .foregroundColor(.secondary)
                
                Spacer()
            }
            .padding(.vertical, 12)
            
            // 检查清单项
            VStack(spacing: 8) {
                ForEach(viewModel.task.checklist?.sorted { !$0.isCompleted && $1.isCompleted } ?? [], id: \.id) { item in
                    checklistItemView(item)
                }
            }
            .animation(.spring(response: 0.3), value: viewModel.task.checklist)
            
            // 添加新的检查清单项
            HStack {
                Button(action: {
                    withAnimation {
                        viewModel.isAddingChecklistItem = true
                    }
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "plus")
                            .font(.system(size: 12))
                        Text("添加项目")
                            .font(.system(size: 14))
                    }
                    .foregroundColor(.accentColor)
                }
                .buttonStyle(.plain)
                .opacity(viewModel.isAddingChecklistItem ? 0 : 1)
                .frame(height: viewModel.isAddingChecklistItem ? 0 : nil)
                
                if viewModel.isAddingChecklistItem {
                    HStack {
                        TextField("添加检查项目...", text: $viewModel.newChecklistItemTitle)
                            .font(.system(size: 15))
                            .onSubmit {
                                if !viewModel.newChecklistItemTitle.isEmpty {
                                    _ = viewModel.addChecklistItem(viewModel.newChecklistItemTitle)
                                    viewModel.newChecklistItemTitle = ""
                                }
                                viewModel.isAddingChecklistItem = false
                            }
                        
                        Button("添加") {
                            if !viewModel.newChecklistItemTitle.isEmpty {
                                _ = viewModel.addChecklistItem(viewModel.newChecklistItemTitle)
                                viewModel.newChecklistItemTitle = ""
                            }
                            viewModel.isAddingChecklistItem = false
                        }
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.accentColor)
                    }
                }
                
                Spacer()
            }
            .padding(.vertical, 8)
            
            Divider()
        }
        .padding(.horizontal, 20)
    }
    
    /// 单个检查清单项视图
    private func checklistItemView(_ item: ChecklistItem) -> some View {
        HStack(spacing: 10) {
            // 完成状态勾选框
            Button {
                withAnimation {
                    viewModel.toggleChecklistItem(item)
                }
            } label: {
                ZStack {
                    Circle()
                        .strokeBorder(item.isCompleted ? Color.accentColor : Color.secondary.opacity(0.3), lineWidth: 1.5)
                        .frame(width: 20, height: 20)
                    
                    if item.isCompleted {
                        Image(systemName: "checkmark")
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(Color.accentColor)
                    }
                }
            }
            .buttonStyle(BorderlessButtonStyle())
            
            // 项目标题
            Text(item.title)
                .font(.system(size: 15))
                .strikethrough(item.isCompleted)
                .foregroundColor(item.isCompleted ? .secondary.opacity(0.7) : .primary)
                .lineLimit(2)
            
            Spacer()
            
            // 删除按钮
            Button {
                withAnimation {
                    viewModel.removeChecklistItem(item)
                }
            } label: {
                Image(systemName: "xmark")
                    .font(.system(size: 10))
                    .foregroundColor(.secondary.opacity(0.5))
                    .frame(width: 24, height: 24)
            }
            .buttonStyle(BorderlessButtonStyle())
            .opacity(0.6)
        }
        .padding(.vertical, 4)
    }
    
    // MARK: - 项目和标签区域
    
    private var projectTagsSectionView: some View {
        VStack(spacing: 0) {
            // 项目部分
            Button {
                viewModel.isShowingProjectSelector = true
            } label: {
                HStack {
                    Image(systemName: "folder")
                        .foregroundColor(.secondary)
                        .frame(width: 24)
                    
                    if viewModel.task.project == nil {
                        Text("无项目")
                            .font(.system(size: 15))
                            .foregroundColor(.secondary.opacity(0.7))
                    } else {
                        Text("! \(viewModel.getProjectName(for: viewModel.task.project))")
                            .font(.system(size: 15))
                            .foregroundColor(viewModel.getProjectColor(for: viewModel.task.project))
                    }
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.system(size: 13))
                        .foregroundColor(.secondary.opacity(0.7))
                }
                .padding(.vertical, 12)
            }
            .buttonStyle(PlainButtonStyle())
            .popover(isPresented: $viewModel.isShowingProjectSelector, attachmentAnchor: .point(.center)) {
                ProjectSelectorView(selectedProjectId: Binding(
                    get: { viewModel.task.project },
                    set: { projectId in
                        viewModel.updateProject(projectId)
                    }
                ), taskTitle: $viewModel.titleDraft)
                .presentationCompactAdaptation(.popover)
                .frame(width: 300, height: 400)
            }
            
            Divider()
            
            // 标签部分
            Button {
                viewModel.isShowingTagEditor = true
            } label: {
                HStack(alignment: .top) {
                    Image(systemName: "tag")
                        .foregroundColor(.secondary)
                        .frame(width: 24)
                    
                    if viewModel.task.tags.isEmpty {
                        Text("无标签")
                            .font(.system(size: 15))
                            .foregroundColor(.secondary.opacity(0.7))
                        
                        Spacer()
                    } else {
                        // 显示所有标签
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 8) {
                                ForEach(viewModel.task.tags, id: \.self) { tag in
                                    Text("#\(tag)")
                                        .font(.system(size: 14))
                                        .foregroundColor(viewModel.getTagColor(for: tag))
                                        .padding(.horizontal, 8)
                                        .padding(.vertical, 4)
                                        .background(
                                            viewModel.getTagColor(for: tag).opacity(0.1)
                                        )
                                        .cornerRadius(4)
                                }
                            }
                        }
                        .frame(height: 30)
                        
                        Spacer()
                    }
                    
                    Image(systemName: "chevron.right")
                        .font(.system(size: 13))
                        .foregroundColor(.secondary.opacity(0.7))
                }
                .padding(.vertical, 12)
            }
            .buttonStyle(PlainButtonStyle())
            .popover(isPresented: $viewModel.isShowingTagEditor, attachmentAnchor: .point(.center)) {
                TagEditorView(selectedTags: Binding(
                    get: { viewModel.task.tags },
                    set: { newTags in
                        viewModel.updateTags(newTags)
                    }
                ))
                .presentationCompactAdaptation(.popover)
                .frame(width: 300, height: 400)
            }
            
            Divider()
            
            // 删除按钮
            Button(action: {
                showingDeleteAlert = true
            }) {
                HStack {
                    Text("删除行动")
                        .font(.system(size: 15))
                        .foregroundColor(.secondary)
                    
                    Spacer()
                }
                .padding(.vertical, 12)
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.horizontal, 20)
    }
    
    // MARK: - 辅助属性
    
    /// 底部安全区域高度
    private var safeAreaBottomInset: CGFloat {
        // 使用更新的API获取安全区域
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return 0
        }
        return window.safeAreaInsets.bottom
    }
    
    /// 获取状态对应的颜色
    private var statusColor: Color {
        // 根据字符串状态获取对应的TaskStatus枚举并返回颜色
        if let taskStatus = TaskStatus(rawValue: viewModel.task.status) {
            return taskStatus.statusColor
        }
        // 默认颜色
        return .gray
    }
    
    /// 验证当前ActionFocus状态是否有效
    private var isValidCurrentActionFocusState: Bool {
        guard let state = viewModel.currentActionFocusState else { return false }
        return isValidActionFocusState(state)
    }
    
    /// 验证ActionFocus状态是否仍然有效
    private func isValidActionFocusState(_ state: ActionFocusState) -> Bool {
        let task = viewModel.task
        
        // 1. 检查任务是否未完成
        guard task.status != TaskStatus.done.rawValue else {
            return false
        }
        
        // 2. 检查小行动是否存在且未完成
        guard let checklist = task.checklist,
              let checklistItem = checklist.first(where: { $0.id == state.checklistItemId }),
              !checklistItem.isCompleted else {
            return false
        }
        
        // 3. 如果有子步骤路径，验证子步骤路径是否仍然有效
        if !state.subStepPath.isEmpty {
            return isValidSubStepPath(state.subStepPath, in: checklistItem.subStepsList)
        }
        
        return true
    }
    
    /// 递归验证子步骤路径是否有效
    private func isValidSubStepPath(_ path: [UUID], in steps: [SubStep]) -> Bool {
        guard !path.isEmpty else { return true }
        
        let currentStepId = path[0]
        let remainingPath = Array(path.dropFirst())
        
        // 查找当前步骤
        guard let currentStep = steps.first(where: { $0.id == currentStepId }),
              !currentStep.isCompleted else {
            return false
        }
        
        // 如果还有更深的路径，递归验证
        if !remainingPath.isEmpty {
            return isValidSubStepPath(remainingPath, in: currentStep.subSteps)
        }
        
        return true
    }
    
    /// 清除无效的ActionFocus状态
    private func clearInvalidActionFocusState(for taskId: UUID) {
        let defaults = UserDefaults.standard
        guard let data = defaults.data(forKey: "actionFocusStates"),
              var manager = try? JSONDecoder().decode(ActionFocusStateManager.self, from: data) else {
            return
        }
        
        manager.removeState(for: taskId)
        
        if let encoded = try? JSONEncoder().encode(manager) {
            defaults.set(encoded, forKey: "actionFocusStates")
        }
        
        print("[TaskDetailView] 清除无效的ActionFocus状态: 任务 \(taskId)")
    }
}

// MARK: - View Extensions

struct CardStyle: ViewModifier {
    @Environment(\.colorScheme) private var colorScheme
    
    func body(content: Content) -> some View {
        content
            .background(AppColors.UI.card(for: colorScheme))
            .cornerRadius(12)
            .shadow(
                color: Color.black.opacity(colorScheme == .dark ? 0.3 : 0.05),
                radius: colorScheme == .dark ? 4 : 2,
                x: 0,
                y: colorScheme == .dark ? 2 : 1
            )
            .padding(.horizontal, 16)
    }
}

extension View {
    func cardStyle() -> some View {
        modifier(CardStyle())
    }
}
