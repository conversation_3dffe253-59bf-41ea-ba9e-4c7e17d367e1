import SwiftUI
import Foundation

/// 简化版的全局焦点管理器
/// 专注于解决快速点击时的焦点问题
@MainActor
class SimpleFocusManager: ObservableObject {
    static let shared = SimpleFocusManager()

    @Published var activeTextFieldId: String? = nil
    private var pendingFocusRequests: [String: Date] = [:]

    private init() {}

    /// 请求焦点（带防抖动）
    func requestFocus(for textFieldId: String) {
        print("[SimpleFocusManager] 请求焦点: \(textFieldId)")

        // 记录请求时间
        pendingFocusRequests[textFieldId] = Date()

        // 立即设置活跃状态
        activeTextFieldId = textFieldId

        // 延迟确认，防止快速点击导致的冲突
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
            // 检查这个请求是否还是最新的
            if let requestTime = self.pendingFocusRequests[textFieldId],
               Date().timeIntervalSince(requestTime) < 0.1 {
                // 确认焦点设置
                self.activeTextFieldId = textFieldId
                print("[SimpleFocusManager] 确认焦点: \(textFieldId)")
            }
        }
    }

    /// 清除焦点
    func clearFocus(for textFieldId: String) {
        if activeTextFieldId == textFieldId {
            activeTextFieldId = nil
            pendingFocusRequests.removeValue(forKey: textFieldId)
            print("[SimpleFocusManager] 清除焦点: \(textFieldId)")
        }
    }

    /// 检查是否应该获得焦点
    func shouldBeFocused(_ textFieldId: String) -> Bool {
        return activeTextFieldId == textFieldId
    }
}

/// 简化版的受管理文本输入框
struct SimpleManagedTextField: View {
    let textFieldId: String
    let placeholder: String
    @Binding var text: String
    @StateObject private var focusManager = SimpleFocusManager.shared

    // 可选配置
    var submitLabel: SubmitLabel = .done
    var onSubmit: (() -> Void)?
    var onFocusChange: ((Bool) -> Void)?

    // 内部焦点状态
    @FocusState private var isFocused: Bool

    var body: some View {
        TextField(placeholder, text: $text)
            .focused($isFocused)
            .submitLabel(submitLabel)
            .onSubmit {
                onSubmit?()
            }
            .onChange(of: focusManager.activeTextFieldId) { _, newActiveId in
                let shouldBeFocused = (newActiveId == textFieldId)

                if isFocused != shouldBeFocused {
                    print("[SimpleManagedTextField] 焦点状态变化 \(textFieldId): \(isFocused) -> \(shouldBeFocused)")

                    // 直接设置焦点状态，不使用异步
                    isFocused = shouldBeFocused
                    onFocusChange?(shouldBeFocused)
                }
            }
            .onChange(of: isFocused) { _, newValue in
                if newValue {
                    // 获得焦点时，确保全局状态同步
                    if focusManager.activeTextFieldId != textFieldId {
                        focusManager.requestFocus(for: textFieldId)
                    }
                } else {
                    // 失去焦点时，清除全局状态
                    if focusManager.activeTextFieldId == textFieldId {
                        focusManager.clearFocus(for: textFieldId)
                    }
                }
            }
            .onAppear {
                // 视图出现时，如果应该获得焦点，立即设置
                if focusManager.shouldBeFocused(textFieldId) {
                    print("[SimpleManagedTextField] 视图出现，立即获得焦点: \(textFieldId)")
                    isFocused = true
                }
            }
    }
}

/// 便利方法扩展
extension SimpleManagedTextField {
    /// 创建用于添加节点的 TextField
    static func forAddingNode(
        to parentNodeId: UUID,
        text: Binding<String>,
        placeholder: String = "输入内容...",
        onSubmit: (() -> Void)? = nil
    ) -> SimpleManagedTextField {
        let textFieldId = "adding_\(parentNodeId.uuidString)"
        return SimpleManagedTextField(
            textFieldId: textFieldId,
            placeholder: placeholder,
            text: text,
            onSubmit: onSubmit
        )
    }

    /// 创建用于编辑节点的 TextField
    static func forEditingNode(
        _ nodeId: UUID,
        text: Binding<String>,
        placeholder: String = "节点内容",
        onSubmit: (() -> Void)? = nil
    ) -> SimpleManagedTextField {
        let textFieldId = "editing_\(nodeId.uuidString)"
        return SimpleManagedTextField(
            textFieldId: textFieldId,
            placeholder: placeholder,
            text: text,
            onSubmit: onSubmit
        )
    }
}

/// 预览提供器
struct SimpleManagedTextField_Previews: PreviewProvider {
    static var previews: some View {
        @State var text = ""

        VStack {
            SimpleManagedTextField(
                textFieldId: "test_field",
                placeholder: "测试输入框",
                text: $text
            )
            .textFieldStyle(RoundedBorderTextFieldStyle())
            .padding()
        }
    }
}
