import SwiftUI

/// 支持Markdown的文本编辑器组件
struct MarkdownTextEditor: View {
    // MARK: - 属性
    @Binding var text: String
    var placeholder: String
    var onSave: (() -> Void)?
    
    @State private var isEditMode = false // 默认为预览模式
    @FocusState private var isFocused: Bool
    
    // 文本样式参数 - 更优化的排版
    let fontSize: CGFloat = 16
    let lineHeight: CGFloat = 1.4 // 标准行高倍数
    let lineSpacing: CGFloat = 8 // 约1.5倍行高
    let paragraphSpacing: CGFloat = 12 // 段落间距，约行高的0.8倍
    
    // MARK: - 环境变量
    @Environment(\.colorScheme) private var colorScheme
    
    // MARK: - 计算属性
    private var textColor: Color {
        colorScheme == .dark ? .white.opacity(0.95) : .black.opacity(0.87) // 优化对比度
    }
    
    // MARK: - 主视图
    var body: some View {
        ZStack(alignment: .bottomTrailing) {
            // 编辑区域
            if isEditMode {
                TextEditor(text: $text)
                    .font(.system(size: fontSize, weight: .light, design: .rounded))
                    .foregroundColor(textColor)
                    .lineSpacing(lineSpacing)
                    .scrollContentBackground(.hidden)
                    .focused($isFocused)
                    .placeholder(when: text.isEmpty) {
                        Text(placeholder)
                            .foregroundColor(Color(.systemGray3))
                            .font(.system(size: fontSize, weight: .light, design: .rounded))
                            .padding(.leading, 4)
                            .padding(.top, 0)
                    }
                    .padding(16)
                    .onAppear {
                        // 进入编辑模式时自动获取焦点
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            isFocused = true
                        }
                    }
                
                // 保存按钮 - 仅在编辑模式+有焦点时显示
                if isFocused {
                    Button {
                        // 先取消焦点（收起键盘）
                        isFocused = false
                        // 切换回预览模式
                        withAnimation(.easeInOut(duration: 0.2)) {
                            isEditMode = false
                        }
                        // 调用保存回调
                        onSave?()
                    } label: {
                        Text("保存")
                            .font(.system(size: 14, weight: .regular, design: .rounded))
                            .foregroundColor(Color(red: 0.2, green: 0.5, blue: 0.9)) // 与链接颜色一致
                    }
                    .padding(.trailing, 16)
                    .padding(.bottom, 16)
                }
            } else {
                // 预览区域 - 点击进入编辑模式
                ScrollView {
                    VStack(alignment: .leading, spacing: 0) {
                        // 使用系统Markdown支持
                        if text.isEmpty {
                            Text(placeholder)
                                .foregroundColor(Color(.systemGray3))
                                .font(.system(size: fontSize, weight: .light, design: .rounded))
                                .padding(.top, 4)
                        } else {
                            // 使用iOS 15+的系统Markdown支持
                            if let attributedString = try? AttributedString(markdown: text) {
                                Text(attributedString)
                                    .font(.system(size: fontSize, weight: .light))
                                    .lineSpacing(lineSpacing)
                            } else {
                                // 回退至普通文本
                                Text(text)
                                    .font(.system(size: fontSize, weight: .light))
                                    .lineSpacing(lineSpacing)
                            }
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(16)
                }
                .contentShape(Rectangle()) // 确保整个区域可点击
                .onTapGesture {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        isEditMode = true
                    }
                }
            }
        }
    }
}

// MARK: - 预览
#Preview {
    VStack {
        MarkdownTextEditor(
            text: .constant("# 标题\n\n这是一段**加粗文本**，这是*斜体文本*。\n\n- 列表项1\n- 列表项2\n\n[点击这里](https://apple.com)"),
            placeholder: "添加项目描述...",
            onSave: {}
        )
        .frame(height: 300)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .padding()
    }
} 