import SwiftUI
import Foundation

struct FilterButton: View {
    let filter: SearchFilter
    @Binding var selectedFilter: SearchFilter
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        Button(action: {
            selectedFilter = filter
        }) {
            Text(filter.displayName)
                .padding(.horizontal, 10)
                .padding(.vertical, 4)
                .background(selectedFilter == filter ? AppColors.UI.primary(for: colorScheme).opacity(0.1) : Color.clear)
                .foregroundColor(selectedFilter == filter ? AppColors.UI.primary(for: colorScheme) : AppColors.UI.primaryText(for: colorScheme))
                .cornerRadius(8)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(selectedFilter == filter ? AppColors.UI.primary(for: colorScheme) : AppColors.UI.secondaryText(for: colorScheme).opacity(0.3), lineWidth: 1)
                )
        }
    }
}