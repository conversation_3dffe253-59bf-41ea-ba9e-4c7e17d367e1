import SwiftUI
import WebKit

/// Markdown文本显示视图
/// 可选使用WebView或系统AttributedString渲染Markdown内容
struct MarkdownView: View {
    /// Markdown文本
    let markdownString: String
    
    // 是否使用系统渲染 (iOS 15+)
    var useSystemRendering: Bool = true
    
    // 文本样式
    var fontSize: CGFloat = 15
    var lineSpacing: CGFloat = 8
    
    var body: some View {
        if useSystemRendering {
            // 使用系统AttributedString渲染
            ScrollView {
                if let attributedString = try? AttributedString(markdown: markdownString) {
                    Text(attributedString)
                        .font(.system(size: fontSize, weight: .light))
                        .lineSpacing(lineSpacing)
                        .padding(16)
                        .frame(maxWidth: .infinity, alignment: .leading)
                } else {
                    // 回退到普通文本
                    Text(markdownString)
                        .padding(16)
                }
            }
        } else {
            // 使用原有的WebView渲染
            MarkdownWebView(markdownString: markdownString)
        }
    }
}

/// Markdown文本显示视图 - 使用WebView渲染Markdown内容
struct MarkdownWebView: UIViewRepresentable {
    /// Markdown文本
    let markdownString: String
    
    func makeUIView(context: Context) -> WKWebView {
        let webView = WKWebView()
        webView.backgroundColor = .systemBackground
        webView.isOpaque = false
        webView.scrollView.contentInsetAdjustmentBehavior = .automatic
        return webView
    }
    
    func updateUIView(_ webView: WKWebView, context: Context) {
        // 将Markdown转换为HTML
        let cssStyle = """
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', sans-serif;
                font-size: 15px;
                line-height: 1.6;
                font-weight: 300;
                letter-spacing: 0.01em;
                color: #333;
                margin: 0;
                padding: 16px;
            }
            h1 {
                font-size: 22px;
                font-weight: 500;
                margin-top: 28px;
                margin-bottom: 18px;
                letter-spacing: -0.02em;
                line-height: 1.3;
            }
            h2 {
                font-size: 18px;
                font-weight: 500;
                margin-top: 24px;
                margin-bottom: 14px;
                letter-spacing: -0.01em;
                line-height: 1.35;
            }
            p {
                margin-top: 0;
                margin-bottom: 12px;
                font-weight: 300;
            }
            ul, ol {
                padding-left: 20px;
                margin-bottom: 16px;
            }
            li {
                margin-bottom: 8px;
                font-weight: 300;
            }
            hr {
                border: 0;
                height: 1px;
                background: #e6e6e6;
                margin: 28px 0;
            }
            blockquote {
                margin: 16px 0;
                padding: 12px 16px;
                background-color: rgba(0,0,0,0.03);
                border-left: 4px solid rgba(0,0,0,0.1);
                font-style: italic;
                color: rgba(0,0,0,0.7);
            }
            @media (prefers-color-scheme: dark) {
                body {
                    color: rgba(255, 255, 255, 0.9);
                    background-color: #000;
                }
                hr {
                    background: rgba(255, 255, 255, 0.2);
                }
                blockquote {
                    background-color: rgba(255,255,255,0.05);
                    border-left: 4px solid rgba(255,255,255,0.15);
                    color: rgba(255,255,255,0.75);
                }
            }
        </style>
        """
        
        // 更精确的Markdown转HTML处理
        var processedMD = markdownString
        
        // 处理标题: 必须在行首
        let h1Regex = try! NSRegularExpression(pattern: "^# (.+)$", options: [.anchorsMatchLines])
        processedMD = h1Regex.stringByReplacingMatches(
            in: processedMD,
            options: [],
            range: NSRange(location: 0, length: processedMD.count),
            withTemplate: "<h1>$1</h1>"
        )
        
        let h2Regex = try! NSRegularExpression(pattern: "^## (.+)$", options: [.anchorsMatchLines])
        processedMD = h2Regex.stringByReplacingMatches(
            in: processedMD,
            options: [],
            range: NSRange(location: 0, length: processedMD.count),
            withTemplate: "<h2>$1</h2>"
        )
        
        // 处理分隔线: 必须在行首
        let hrRegex = try! NSRegularExpression(pattern: "^---$", options: [.anchorsMatchLines])
        processedMD = hrRegex.stringByReplacingMatches(
            in: processedMD,
            options: [],
            range: NSRange(location: 0, length: processedMD.count),
            withTemplate: "<hr>"
        )
        
        // 处理引用块: 以 > 开头
        let quoteRegex = try! NSRegularExpression(pattern: "^\\s*>\\s*(.+)$", options: [.anchorsMatchLines])
        processedMD = quoteRegex.stringByReplacingMatches(
            in: processedMD,
            options: [],
            range: NSRange(location: 0, length: processedMD.count),
            withTemplate: "</p><blockquote>$1</blockquote><p>"
        )
        
        // 处理段落
        processedMD = processedMD.replacingOccurrences(of: "\n\n", with: "</p><p>")
        
        // 替换无序列表
        let listRegex = try! NSRegularExpression(pattern: "\n- (.+)", options: [])
        processedMD = listRegex.stringByReplacingMatches(
            in: processedMD,
            options: [],
            range: NSRange(location: 0, length: processedMD.count),
            withTemplate: "</p><ul><li>$1</li></ul><p>"
        )
        
        // 替换加粗文本
        let boldRegex = try! NSRegularExpression(pattern: "\\*\\*(.+?)\\*\\*", options: [])
        processedMD = boldRegex.stringByReplacingMatches(
            in: processedMD,
            options: [],
            range: NSRange(location: 0, length: processedMD.count),
            withTemplate: "<strong>$1</strong>"
        )
        
        // 包装HTML
        let html = "<html><head>\(cssStyle)<meta name='viewport' content='width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no'></head><body><p>\(processedMD)</p></body></html>"
        
        webView.loadHTMLString(html, baseURL: nil)
    }
} 