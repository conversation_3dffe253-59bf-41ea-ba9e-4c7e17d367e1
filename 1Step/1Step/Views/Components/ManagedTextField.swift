import SwiftUI
import Foundation

/// 全局文本输入框焦点管理器
/// 确保在整个应用中只有一个 TextField 获得焦点
@MainActor
class GlobalTextFieldFocusManager: ObservableObject {
    static let shared = GlobalTextFieldFocusManager()

    @Published var activeTextFieldId: String? = nil

    // 跟踪视图状态，避免在视图重建时错误清除焦点
    private var viewStates: [String: ViewState] = [:]

    private struct ViewState {
        var isVisible: Bool = false
        var lastFocusRequest: Date = Date()
    }

    private init() {}

    /// 请求焦点
    func requestFocus(for textFieldId: String) {
        print("[GlobalFocusManager] 请求焦点: \(textFieldId)")

        // 更新视图状态
        viewStates[textFieldId] = ViewState(isVisible: true, lastFocusRequest: Date())

        // 如果当前已经有活跃的输入框且不是同一个，先清除
        if let currentActive = activeTextFieldId, currentActive != textFieldId {
            print("[GlobalFocusManager] 清除当前活跃焦点: \(currentActive)")
        }

        activeTextFieldId = textFieldId
        print("[GlobalFocusManager] 焦点已设置为: \(textFieldId)")

        // 确保焦点设置后立即生效，避免时序问题
        DispatchQueue.main.async {
            // 再次确认焦点状态
            if self.activeTextFieldId == textFieldId {
                print("[GlobalFocusManager] 确认焦点状态: \(textFieldId)")
            }
        }
    }

    /// 清除焦点（只有在确实需要时才清除）
    func clearFocus(for textFieldId: String, force: Bool = false) {
        print("[GlobalFocusManager] 尝试清除焦点: \(textFieldId), force: \(force)")

        // 如果不是强制清除，检查是否是最近的焦点请求
        if !force {
            if let viewState = viewStates[textFieldId] {
                let timeSinceLastRequest = Date().timeIntervalSince(viewState.lastFocusRequest)
                // 如果是最近1秒内的焦点请求，可能是视图重建导致的，不清除
                if timeSinceLastRequest < 1.0 {
                    print("[GlobalFocusManager] 跳过清除焦点（最近的请求）: \(textFieldId)")
                    return
                }
            }
        }

        if activeTextFieldId == textFieldId {
            activeTextFieldId = nil
            print("[GlobalFocusManager] 焦点已清除: \(textFieldId)")
        }

        // 清理视图状态
        viewStates.removeValue(forKey: textFieldId)
    }

    /// 标记视图出现
    func markViewAppeared(for textFieldId: String) {
        if var viewState = viewStates[textFieldId] {
            viewState.isVisible = true
            viewStates[textFieldId] = viewState
        } else {
            viewStates[textFieldId] = ViewState(isVisible: true, lastFocusRequest: Date.distantPast)
        }
    }

    /// 标记视图消失
    func markViewDisappeared(for textFieldId: String) {
        if var viewState = viewStates[textFieldId] {
            viewState.isVisible = false
            viewStates[textFieldId] = viewState
        }
    }

    /// 检查是否应该获得焦点
    func shouldBeFocused(_ textFieldId: String) -> Bool {
        return activeTextFieldId == textFieldId
    }
}

/// 全局焦点管理的 TextField 组件
/// 解决递归视图中多个 @FocusState 竞争的问题
struct ManagedTextField: View {
    let textFieldId: String
    let placeholder: String
    @Binding var text: String
    @StateObject private var globalFocusManager = GlobalTextFieldFocusManager.shared

    // 可选配置
    var submitLabel: SubmitLabel = .done
    var onSubmit: (() -> Void)?
    var onFocusChange: ((Bool) -> Void)?

    // 内部焦点状态 - 由全局管理器控制
    @FocusState private var isFocused: Bool

    var body: some View {
        TextField(placeholder, text: $text)
            .focused($isFocused)
            .submitLabel(submitLabel)
            .onSubmit {
                onSubmit?()
            }
            .onChange(of: globalFocusManager.activeTextFieldId) { _, newActiveId in
                // 当全局活跃输入框改变时，更新本地焦点状态
                let shouldBeFocused = (newActiveId == textFieldId)

                if isFocused != shouldBeFocused {
                    print("[ManagedTextField] 更新焦点状态 \(textFieldId): \(isFocused) -> \(shouldBeFocused)")

                    // 使用更稳定的焦点设置方式
                    if shouldBeFocused {
                        // 获得焦点时，确保在下一个运行循环中设置
                        DispatchQueue.main.async {
                            if globalFocusManager.activeTextFieldId == textFieldId {
                                isFocused = true
                                onFocusChange?(true)
                            }
                        }
                    } else {
                        // 失去焦点时立即设置
                        isFocused = false
                        onFocusChange?(false)
                    }
                }
            }
            .onChange(of: isFocused) { _, newValue in
                // 当本地焦点状态改变时，通知全局管理器
                if newValue {
                    // 获得焦点时，请求全局焦点
                    if globalFocusManager.activeTextFieldId != textFieldId {
                        print("[ManagedTextField] 本地获得焦点，请求全局焦点: \(textFieldId)")
                        globalFocusManager.requestFocus(for: textFieldId)
                    }
                } else {
                    // 失去焦点时，如果是当前活跃的输入框，清除全局焦点
                    if globalFocusManager.activeTextFieldId == textFieldId {
                        print("[ManagedTextField] 本地失去焦点，清除全局焦点: \(textFieldId)")
                        globalFocusManager.clearFocus(for: textFieldId)
                    }
                }

                onFocusChange?(newValue)
            }
            .onAppear {
                // 标记视图出现
                globalFocusManager.markViewAppeared(for: textFieldId)

                // 视图出现时，检查是否应该获得焦点
                if globalFocusManager.shouldBeFocused(textFieldId) {
                    print("[ManagedTextField] 视图出现，应该获得焦点: \(textFieldId)")
                    // 使用更短的延迟，确保视图完全渲染后设置焦点
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.01) {
                        if globalFocusManager.shouldBeFocused(textFieldId) {
                            isFocused = true
                        }
                    }
                }
            }
            .onDisappear {
                // 标记视图消失
                globalFocusManager.markViewDisappeared(for: textFieldId)

                // 视图消失时，谨慎清除焦点（避免视图重建时的误清除）
                if globalFocusManager.activeTextFieldId == textFieldId {
                    print("[ManagedTextField] 视图消失，尝试清除焦点: \(textFieldId)")
                    // 延迟一点时间，如果是视图重建，新视图会很快出现
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        // 再次检查是否还应该清除焦点
                        if globalFocusManager.activeTextFieldId == textFieldId {
                            globalFocusManager.clearFocus(for: textFieldId)
                        }
                    }
                }
            }
    }
}

/// 便利方法扩展
extension ManagedTextField {
    /// 创建用于添加节点的 TextField
    static func forAddingNode(
        to parentNodeId: UUID,
        text: Binding<String>,
        placeholder: String = "输入内容...",
        onSubmit: (() -> Void)? = nil
    ) -> ManagedTextField {
        let textFieldId = "adding_\(parentNodeId.uuidString)"
        return ManagedTextField(
            textFieldId: textFieldId,
            placeholder: placeholder,
            text: text,
            onSubmit: onSubmit
        )
    }

    /// 创建用于编辑节点的 TextField
    static func forEditingNode(
        _ nodeId: UUID,
        text: Binding<String>,
        placeholder: String = "节点内容",
        onSubmit: (() -> Void)? = nil
    ) -> ManagedTextField {
        let textFieldId = "editing_\(nodeId.uuidString)"
        return ManagedTextField(
            textFieldId: textFieldId,
            placeholder: placeholder,
            text: text,
            onSubmit: onSubmit
        )
    }
}

/// 预览提供器
struct ManagedTextField_Previews: PreviewProvider {
    static var previews: some View {
        @State var text = ""

        VStack {
            ManagedTextField(
                textFieldId: "test_field",
                placeholder: "测试输入框",
                text: $text
            )
            .textFieldStyle(RoundedBorderTextFieldStyle())
            .padding()
        }
    }
}
