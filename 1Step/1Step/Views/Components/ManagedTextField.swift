import SwiftUI
import Foundation

/// 全局文本输入框焦点管理器
/// 确保在整个应用中只有一个 TextField 获得焦点
@MainActor
class GlobalTextFieldFocusManager: ObservableObject {
    static let shared = GlobalTextFieldFocusManager()

    @Published var activeTextFieldId: String? = nil

    private init() {}

    /// 请求焦点
    func requestFocus(for textFieldId: String) {
        print("[GlobalFocusManager] 请求焦点: \(textFieldId)")

        // 如果当前已经有活跃的输入框且不是同一个，先清除
        if let currentActive = activeTextFieldId, currentActive != textFieldId {
            print("[GlobalFocusManager] 清除当前活跃焦点: \(currentActive)")
        }

        activeTextFieldId = textFieldId
        print("[GlobalFocusManager] 焦点已设置为: \(textFieldId)")
    }

    /// 清除焦点
    func clearFocus(for textFieldId: String) {
        print("[GlobalFocusManager] 清除焦点: \(textFieldId)")

        if activeTextFieldId == textFieldId {
            activeTextFieldId = nil
        }
    }

    /// 检查是否应该获得焦点
    func shouldBeFocused(_ textFieldId: String) -> Bool {
        return activeTextFieldId == textFieldId
    }
}

/// 全局焦点管理的 TextField 组件
/// 解决递归视图中多个 @FocusState 竞争的问题
struct ManagedTextField: View {
    let textFieldId: String
    let placeholder: String
    @Binding var text: String
    @StateObject private var globalFocusManager = GlobalTextFieldFocusManager.shared

    // 可选配置
    var submitLabel: SubmitLabel = .done
    var onSubmit: (() -> Void)?
    var onFocusChange: ((Bool) -> Void)?

    // 内部焦点状态 - 由全局管理器控制
    @FocusState private var isFocused: Bool

    var body: some View {
        TextField(placeholder, text: $text)
            .focused($isFocused)
            .submitLabel(submitLabel)
            .onSubmit {
                onSubmit?()
            }
            .onChange(of: globalFocusManager.activeTextFieldId) { _, newActiveId in
                // 当全局活跃输入框改变时，更新本地焦点状态
                let shouldBeFocused = (newActiveId == textFieldId)

                if isFocused != shouldBeFocused {
                    print("[ManagedTextField] 更新焦点状态 \(textFieldId): \(isFocused) -> \(shouldBeFocused)")
                    isFocused = shouldBeFocused
                    onFocusChange?(shouldBeFocused)
                }
            }
            .onChange(of: isFocused) { _, newValue in
                // 当本地焦点状态改变时，通知全局管理器
                if newValue {
                    // 获得焦点时，请求全局焦点
                    if globalFocusManager.activeTextFieldId != textFieldId {
                        print("[ManagedTextField] 本地获得焦点，请求全局焦点: \(textFieldId)")
                        globalFocusManager.requestFocus(for: textFieldId)
                    }
                } else {
                    // 失去焦点时，如果是当前活跃的输入框，清除全局焦点
                    if globalFocusManager.activeTextFieldId == textFieldId {
                        print("[ManagedTextField] 本地失去焦点，清除全局焦点: \(textFieldId)")
                        globalFocusManager.clearFocus(for: textFieldId)
                    }
                }

                onFocusChange?(newValue)
            }
            .onAppear {
                // 视图出现时，检查是否应该获得焦点
                if globalFocusManager.shouldBeFocused(textFieldId) {
                    print("[ManagedTextField] 视图出现，应该获得焦点: \(textFieldId)")
                    DispatchQueue.main.async {
                        isFocused = true
                    }
                }
            }
            .onDisappear {
                // 视图消失时，清除焦点
                if globalFocusManager.activeTextFieldId == textFieldId {
                    print("[ManagedTextField] 视图消失，清除焦点: \(textFieldId)")
                    globalFocusManager.clearFocus(for: textFieldId)
                }
            }
    }
}

/// 便利方法扩展
extension ManagedTextField {
    /// 创建用于添加节点的 TextField
    static func forAddingNode(
        to parentNodeId: UUID,
        text: Binding<String>,
        placeholder: String = "输入内容...",
        onSubmit: (() -> Void)? = nil
    ) -> ManagedTextField {
        let textFieldId = "adding_\(parentNodeId.uuidString)"
        return ManagedTextField(
            textFieldId: textFieldId,
            placeholder: placeholder,
            text: text,
            onSubmit: onSubmit
        )
    }

    /// 创建用于编辑节点的 TextField
    static func forEditingNode(
        _ nodeId: UUID,
        text: Binding<String>,
        placeholder: String = "节点内容",
        onSubmit: (() -> Void)? = nil
    ) -> ManagedTextField {
        let textFieldId = "editing_\(nodeId.uuidString)"
        return ManagedTextField(
            textFieldId: textFieldId,
            placeholder: placeholder,
            text: text,
            onSubmit: onSubmit
        )
    }
}

/// 预览提供器
struct ManagedTextField_Previews: PreviewProvider {
    static var previews: some View {
        @State var text = ""

        VStack {
            ManagedTextField(
                textFieldId: "test_field",
                placeholder: "测试输入框",
                text: $text
            )
            .textFieldStyle(RoundedBorderTextFieldStyle())
            .padding()
        }
    }
}
