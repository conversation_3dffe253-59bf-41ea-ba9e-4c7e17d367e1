import SwiftUI

/// 触觉反馈管理器
/// 提供一致的触觉反馈体验
class HapticManager {
    static let shared = HapticManager()
    
    private init() {}
    
    /// 触觉反馈 - 冲击
    func impact(style: UIImpactFeedbackGenerator.FeedbackStyle) {
        let generator = UIImpactFeedbackGenerator(style: style)
        generator.prepare()
        generator.impactOccurred()
    }
    
    /// 触觉反馈 - 通知
    func notification(type: UINotificationFeedbackGenerator.FeedbackType) {
        let generator = UINotificationFeedbackGenerator()
        generator.prepare()
        generator.notificationOccurred(type)
    }
}
