import SwiftUI

/// 可折叠的行动章节标题
struct TaskSectionHeader: View {
    var title: String
    var count: Int
    var titleColor: Color?
    var isExpanded: Binding<Bool>
    var showExpandButton: Bool = true
    
    var body: some View {
        // 如果标题为空，则不显示任何内容
        if title.isEmpty {
            EmptyView()
        } else {
            Button {
                // 点击整个标题区域切换展开/收缩状态
                withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                    isExpanded.wrappedValue.toggle()
                }
            } label: {
                HStack {
                    // 章节标题
                    Text(title)
                        .font(.subheadline) // 使用更小的字体
                        .foregroundColor(.secondary) // 统一使用灰色字体
                    
                    Spacer()
                    
                    // 展开/折叠图标
                    if showExpandButton {
                        Image(systemName: isExpanded.wrappedValue ? "chevron.up" : "chevron.down")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.secondary)
                    }
                }
            }
            .buttonStyle(PlainButtonStyle()) // 使用普通按钮样式，不显示点击效果
            .padding(.horizontal)
            .padding(.top, 16)
            .padding(.bottom, 8)
        }
    }
}
