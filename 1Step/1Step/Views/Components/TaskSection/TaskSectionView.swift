import SwiftUI
import SwiftData
import SwipeActions
import UIKit // 添加UIKit以支持触觉反馈

/// 行动章节视图 - 包含标题、展开/折叠和行动列表
struct TaskSectionView: View {
    // 必要属性
    var title: String
    var tasks: [Task]
    var status: String
    
    // 数据仓库
    var taskRepository: TaskRepository
    var projectRepository: ProjectRepository
    var tagRepository: TagRepository
    
    // 可选配置
    var titleColor: Color? = nil
    @Binding var isExpanded: Bool
    var showExpandButton: Bool = true
    var showEmptyState: Bool = true
    var style: TaskCardStyle = .standard
    
    // 环境变量
    @Environment(\.self) private var environment
    
    // 内部状态
    @State private var deletingTaskIds: Set<UUID> = []
    // 添加退出动画状态
    @State private var exitingTaskIds: Set<UUID> = []
    // 添加任务位置偏移量
    @State private var taskOffsets: [UUID: CGFloat] = [:]
    // 添加任务透明度
    @State private var taskOpacities: [UUID: CGFloat] = [:]
    // 添加任务缩放比例
    @State private var taskScales: [UUID: CGFloat] = [:]
    
    // 批量操作相关
    var isBatchMode: Bool = false
    var selectedTasks: Set<UUID> = []
    var onTaskSelected: ((UUID) -> Void)? = nil
    var isSelectionDisabled: Bool = false
    var disabledSelectionMessage: String? = nil
    
    // 任务意图回调
    var onTaskIntent: ((TaskIntent) -> Void)
    
    // 自定义构建器
    var customTaskView: ((Task) -> AnyView)? = nil
    
    // 新API - 支持三个数据仓库
    init(
        title: String,
        tasks: [Task],
        status: String,
        taskRepository: TaskRepository,
        projectRepository: ProjectRepository,
        tagRepository: TagRepository,
        titleColor: Color? = nil,
        style: TaskCardStyle = .standard,
        isExpanded: Binding<Bool> = .constant(true),
        showExpandButton: Bool = true,
        showEmptyState: Bool = true,
        isBatchMode: Bool = false,
        selectedTasks: Set<UUID> = [],
        onTaskSelected: ((UUID) -> Void)? = nil,
        isSelectionDisabled: Bool = false,
        disabledSelectionMessage: String? = nil,
        onTaskIntent: @escaping ((TaskIntent) -> Void),
        customTaskView: ((Task) -> AnyView)? = nil
    ) {
        self.title = title
        self.tasks = tasks
        self.status = status
        self.taskRepository = taskRepository
        self.projectRepository = projectRepository
        self.tagRepository = tagRepository
        self.titleColor = titleColor
        self.style = style
        self._isExpanded = isExpanded
        self.showExpandButton = showExpandButton
        self.showEmptyState = showEmptyState
        self.isBatchMode = isBatchMode
        self.selectedTasks = selectedTasks
        self.onTaskSelected = onTaskSelected
        self.isSelectionDisabled = isSelectionDisabled
        self.disabledSelectionMessage = disabledSelectionMessage
        self.onTaskIntent = onTaskIntent
        self.customTaskView = customTaskView
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 章节标题
            TaskSectionHeader(
                title: title,
                count: tasks.count,
                titleColor: titleColor,
                isExpanded: $isExpanded,
                showExpandButton: showExpandButton
            )
            
            // 行动列表内容
            if isExpanded {
                if tasks.isEmpty && showEmptyState {
                    // 空状态视图
                    TaskSectionEmptyState(
                        status: status,
                        statusColor: titleColor ?? statusColor(for: status)
                    )
                    .transition(.opacity.animation(.easeInOut(duration: 0.3).delay(0.3)))
                } else {
                    // 行动列表
                    VStack(spacing: 10) {
                        SwipeViewGroup {
                            ForEach(tasks) { task in
                                HStack(spacing: 12) {
                                    if isBatchMode {
                                        // 选择按钮
                                        Button {
                                            if isSelectionDisabled && !selectedTasks.contains(task.id) {
                                                // 如果选择被禁用且任务未被选中，显示提示
                                                if let message = disabledSelectionMessage {
                                                    DependencyContainer.toastManager().showWarning(message)
                                                } else {
                                                    DependencyContainer.toastManager().showWarning("已达到选择上限")
                                                }
                                            } else {
                                                onTaskSelected?(task.id)
                                            }
                                        } label: {
                                            Image(systemName: selectedTasks.contains(task.id) ? 
                                                "checkmark.circle.fill" : "circle")
                                                .font(.system(size: 18))
                                                .foregroundColor(selectedTasks.contains(task.id) ? 
                                                    .accentColor : (isSelectionDisabled && !selectedTasks.contains(task.id) ? .secondary.opacity(0.5) : .secondary))
                                        }
                                        .transition(.scale)
                                    }
                                    
                                    if let customView = customTaskView {
                                        customView(task)
                                            .opacity(taskOpacities[task.id] ?? (deletingTaskIds.contains(task.id) ? 0.5 : 1.0))
                                            .scaleEffect(taskScales[task.id] ?? (deletingTaskIds.contains(task.id) ? 0.95 : 1.0))
                                            .offset(x: taskOffsets[task.id] ?? 0)
                                            .animation(.easeOut(duration: 0.4), value: taskOpacities[task.id])
                                            .animation(.easeOut(duration: 0.4), value: taskScales[task.id])
                                            .animation(.easeOut(duration: 0.4), value: taskOffsets[task.id])
                                            .animation(.easeOut(duration: 0.4), value: exitingTaskIds.contains(task.id))
                                    } else {
                                        defaultTaskCard(for: task)
                                            .opacity(taskOpacities[task.id] ?? (deletingTaskIds.contains(task.id) ? 0.5 : 1.0))
                                            .scaleEffect(taskScales[task.id] ?? (deletingTaskIds.contains(task.id) ? 0.95 : 1.0))
                                            .offset(x: taskOffsets[task.id] ?? 0)
                                            .animation(.easeOut(duration: 0.4), value: taskOpacities[task.id])
                                            .animation(.easeOut(duration: 0.4), value: taskScales[task.id])
                                            .animation(.easeOut(duration: 0.4), value: taskOffsets[task.id])
                                            .animation(.easeOut(duration: 0.4), value: exitingTaskIds.contains(task.id))
                                    }
                                }
                                .id(task.id) // 使用ID确保视图能正确更新
                            }
                        }
                    }
                    .padding(.horizontal)
                    .padding(.top, 8)
                    .padding(.bottom, 16)
                }
            }
        }
        .background(Color(.systemBackground))
    }
    
    // 默认行动卡片
    private func defaultTaskCard(for task: Task) -> some View {
        // 判断是否是标签页面的已完成任务
        let isEmptyCallback = String(describing: onTaskIntent).contains("_") && String(describing: onTaskIntent).count < 5
        let isTagPageCompletedTask = task.status == TaskStatus.done.rawValue && isEmptyCallback
        
        // 如果是标签页面的已完成任务，不显示滑动操作
        let leadingActions = isTagPageCompletedTask ? [] : createLeadingSwipeActions(for: task)
        let trailingActions = isTagPageCompletedTask ? [] : createTrailingSwipeActions(for: task)
        
        return TaskCardView(
            task: task,
            taskRepository: taskRepository,
            projectRepository: projectRepository,
            tagRepository: tagRepository,
            style: style,
            leadingSwipeActions: leadingActions,
            trailingSwipeActions: trailingActions,
            onTaskIntent: { intent in
                // 向上传递任务意图
                onTaskIntent(intent)
            },
            onExitRequested: { task, action in
                // 记录正在退出的任务
                exitingTaskIds.insert(task.id)
                
                // 应用动画效果
                withAnimation(.easeOut(duration: 0.4)) {
                    taskOffsets[task.id] = UIScreen.main.bounds.width * 0.8
                    taskOpacities[task.id] = 0
                    taskScales[task.id] = 0.95
                }
                
                // 延迟清理状态
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    // 清理所有临时状态变量
                    exitingTaskIds.remove(task.id)
                    taskOffsets.removeValue(forKey: task.id)
                    taskOpacities.removeValue(forKey: task.id)
                    taskScales.removeValue(forKey: task.id)
                }
            }
        )
    }
    
    // 状态颜色
    private func statusColor(for status: String) -> Color {
        switch status {
        case TaskStatus.na.rawValue:
            return .blue
        case TaskStatus.waiting.rawValue:
            return .orange
        case TaskStatus.inbox.rawValue:
            return .gray
        case TaskStatus.doing.rawValue:
            return .green
        case TaskStatus.smb.rawValue:
            return .purple
        case TaskStatus.done.rawValue:
            return .gray
        default:
            return .secondary
        }
    }
    
    // 创建左滑操作
    private func createLeadingSwipeActions(for task: Task) -> [TaskSwipeAction] {
        var actions: [TaskSwipeAction] = []
        
        // 始终添加完成操作作为第一个
        switch task.status {
        case TaskStatus.na.rawValue, TaskStatus.waiting.rawValue, TaskStatus.inbox.rawValue, TaskStatus.smb.rawValue:
            actions.append(
                createSwipeAction(title: "完成", statusValue: TaskStatus.done.rawValue, icon: "checkmark", color: .green)
            )
        default:
            break
        }
        
        // 根据状态添加其他两个操作按钮
        switch task.status {
        case TaskStatus.na.rawValue:
            // 添加"一步"和"等待中"
            actions.append(
                createSwipeAction(title: "一步", statusValue: TaskStatus.doing.rawValue, icon: "figure.walk", color: .green)
            )
            
            actions.append(
                createSwipeAction(title: "等待中", statusValue: TaskStatus.waiting.rawValue, icon: "hourglass", color: .orange)
            )
            
        case TaskStatus.waiting.rawValue:
            // 添加"下一步"和"未来也许"
            actions.append(
                createSwipeAction(title: "下一步", statusValue: TaskStatus.na.rawValue, icon: "arrow.right", color: .blue)
            )
            
            actions.append(
                createSwipeAction(title: "未来也许", statusValue: TaskStatus.smb.rawValue, icon: "calendar", color: .purple)
            )
            
        case TaskStatus.inbox.rawValue:
            // 添加"下一步"和"未来也许"
            actions.append(
                createSwipeAction(title: "下一步", statusValue: TaskStatus.na.rawValue, icon: "arrow.right", color: .blue)
            )
            
            actions.append(
                createSwipeAction(title: "未来也许", statusValue: TaskStatus.smb.rawValue, icon: "calendar", color: .purple)
            )
            
        case TaskStatus.smb.rawValue:
            // 添加"下一步"和"等待中"
            actions.append(
                createSwipeAction(title: "下一步", statusValue: TaskStatus.na.rawValue, icon: "arrow.right", color: .blue)
            )
            
            actions.append(
                createSwipeAction(title: "等待中", statusValue: TaskStatus.waiting.rawValue, icon: "hourglass", color: .orange)
            )
            
        default:
            break
        }
        
        return actions
    }
    
    // 创建右滑操作
    private func createTrailingSwipeActions(for task: Task) -> [TaskSwipeAction] {
        var actions: [TaskSwipeAction] = []
        
        // 如果是已完成任务，添加恢复操作
        if task.status == TaskStatus.done.rawValue {
            // 如果是项目页面的已完成任务，才添加恢复操作
            // 判断方式：如果 onTaskIntent 是空函数，则表示是标签页面，不添加恢复操作
            let isEmptyCallback = String(describing: onTaskIntent).contains("_") && String(describing: onTaskIntent).count < 5
            if !isEmptyCallback {
                // 恢复到下一步
                let recoverAction = createSwipeAction(title: "恢复为行动", statusValue: TaskStatus.na.rawValue, icon: "arrow.uturn.backward", color: .blue)
                actions.append(recoverAction)
            }
        }
        
        // 如果是标签页面的已完成任务，不添加删除操作
        let isEmptyCallback = String(describing: onTaskIntent).contains("_") && String(describing: onTaskIntent).count < 5
        if !(task.status == TaskStatus.done.rawValue && isEmptyCallback) {
            // 删除操作
            let deleteAction = createSwipeAction(title: "删除", statusValue: "delete", icon: "trash", color: .red)
            actions.append(deleteAction)
        }
        
        return actions
    }
    
    // 创建滑动操作辅助方法
    private func createSwipeAction(title: String, statusValue: String? = nil, icon: String, color: Color) -> TaskSwipeAction {
        return TaskSwipeAction(
            title: title,
            statusValue: statusValue ?? title, // 使用明确的状态值或默认使用标题
            icon: icon,
            color: color
        ) {
            // 不执行实际操作，只用于UI显示
            // 实际操作通过onTaskIntent回调处理
            return true
        }
    }
}
