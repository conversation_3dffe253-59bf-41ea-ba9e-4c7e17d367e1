import SwiftUI

/// 行动章节空状态视图
struct TaskSectionEmptyState: View {
    var status: String
    var statusColor: Color
    
    var body: some View {
        // 简化空状态显示，只显示简单的文字
        Text(emptyMessageFor(status: status))
            .font(.system(size: 14))
            .foregroundColor(Color.secondary.opacity(0.7))
            .frame(maxWidth: .infinity, alignment: .center)
            .padding(.vertical, 16)
    }
    
    // 根据不同状态显示极简单的空状态提示
    private func emptyMessageFor(status: String) -> String {
        switch status {
        case TaskStatus.na.rawValue:
            return "空"
        case TaskStatus.waiting.rawValue:
            return "空"
        case TaskStatus.inbox.rawValue:
            return "空"
        case TaskStatus.doing.rawValue:
            return "空"
        case TaskStatus.smb.rawValue:
            return "空"
        case TaskStatus.done.rawValue:
            return "空"
        default:
            return "空"
        }
    }
}
