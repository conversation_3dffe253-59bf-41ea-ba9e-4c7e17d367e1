import SwiftUI

/// 测试焦点管理的视图
struct FocusTestView: View {
    @State private var text1 = ""
    @State private var text2 = ""
    @State private var text3 = ""
    
    var body: some View {
        VStack(spacing: 20) {
            Text("焦点管理测试")
                .font(.title)
                .padding()
            
            VStack(spacing: 10) {
                Text("快速点击不同输入框，测试焦点管理")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                ManagedTextField(
                    textFieldId: "test_field_1",
                    placeholder: "输入框 1",
                    text: $text1
                )
                .textFieldStyle(RoundedBorderTextFieldStyle())
                
                ManagedTextField(
                    textFieldId: "test_field_2",
                    placeholder: "输入框 2",
                    text: $text2
                )
                .textFieldStyle(RoundedBorderTextFieldStyle())
                
                ManagedTextField(
                    textFieldId: "test_field_3",
                    placeholder: "输入框 3",
                    text: $text3
                )
                .textFieldStyle(RoundedBorderTextFieldStyle())
            }
            .padding()
            
            VStack(spacing: 10) {
                Text("当前活跃输入框:")
                    .font(.caption)
                
                Text(GlobalTextFieldFocusManager.shared.activeTextFieldId ?? "无")
                    .font(.caption)
                    .foregroundColor(.blue)
            }
            
            HStack(spacing: 20) {
                Button("请求焦点 1") {
                    GlobalTextFieldFocusManager.shared.requestFocus(for: "test_field_1")
                }
                
                Button("请求焦点 2") {
                    GlobalTextFieldFocusManager.shared.requestFocus(for: "test_field_2")
                }
                
                Button("请求焦点 3") {
                    GlobalTextFieldFocusManager.shared.requestFocus(for: "test_field_3")
                }
            }
            .padding()
            
            Button("清除所有焦点") {
                if let activeId = GlobalTextFieldFocusManager.shared.activeTextFieldId {
                    GlobalTextFieldFocusManager.shared.clearFocus(for: activeId)
                }
            }
            .foregroundColor(.red)
            
            Spacer()
        }
        .padding()
    }
}

/// 预览提供器
struct FocusTestView_Previews: PreviewProvider {
    static var previews: some View {
        FocusTestView()
    }
}
