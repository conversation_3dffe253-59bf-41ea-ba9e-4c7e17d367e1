import SwiftUI

/// 休息模式视图 - 显示动画并支持双击退出
struct RestModeView: View {
    @Binding var isActive: Bool
    @Environment(\.toastManager) private var toastManager
    
    var body: some View {
        ZStack {
            // 背景
            Color(UIColor.systemBackground)
                .edgesIgnoringSafeArea(.all)
            
            VStack {
                Spacer()
                
                // Lottie动画
                LottieView(name: "1746683054657")
                    .frame(width: 280, height: 280)
                
                Spacer()
                
                Text("双击屏幕退出")
                    .font(.footnote)
                    .foregroundColor(.secondary)
                    .padding(.bottom, 40)
            }
        }
        // 双击退出
        .onTapGesture(count: 2) {
            withAnimation(.easeInOut(duration: 0.5)) {
                isActive = false
                // 显示轻量级toast提示
                toastManager.showSuperLightInfo("期待再会")
            }
        }
    }
} 