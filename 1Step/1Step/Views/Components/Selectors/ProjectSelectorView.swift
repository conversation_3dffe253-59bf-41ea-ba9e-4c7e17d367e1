import SwiftUI
import SwiftData

struct ProjectSelectorView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    @Binding var selectedProjectId: UUID?
    @Binding var taskTitle: String
    
    @StateObject private var viewModel: ProjectSelectorViewModel
    @State private var searchText: String = ""
    
    init(
        selectedProjectId: Binding<UUID?>, 
        taskTitle: Binding<String>,
        projectRepository: ProjectRepository = DependencyContainer.projectRepository()
    ) {
        self._selectedProjectId = selectedProjectId
        self._taskTitle = taskTitle
        _viewModel = StateObject(wrappedValue: ProjectSelectorViewModel(projectRepository: projectRepository))
    }
    
    var body: some View {
        NavigationStack {
            VStack {
                // 搜索栏
                searchBar
                
                // 项目列表
                projectList
            }
            .navigationTitle("选择项目")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarLeading) {
                    But<PERSON>("取消") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    // 搜索栏
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(AppColors.UI.gray(for: colorScheme))
            
            TextField("搜索项目", text: $searchText)
                .autocapitalization(.none)
                .disableAutocorrection(true)
            
            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(AppColors.UI.gray(for: colorScheme))
                }
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(AppColors.UI.background(for: colorScheme))
        .cornerRadius(10)
        .padding(.horizontal)
        .padding(.bottom, 8)
    }
    
    // 过滤后的项目列表
    private var filteredProjects: [Project] {
        return viewModel.filteredProjects(searchText: searchText)
    }
    
    // 项目列表
    private var projectList: some View {
        List {
            // 移除项目选项
            Button {
                selectedProjectId = nil
                dismiss()
            } label: {
                HStack {
                    Text("无项目")
                        .foregroundColor(AppColors.UI.primary(for: colorScheme))
                    
                    Spacer()
                    
                    if selectedProjectId == nil {
                        Image(systemName: "checkmark")
                            .foregroundColor(AppColors.UI.accent(for: colorScheme))
                    }
                }
            }
            .padding(.vertical, 4)
            
            // 项目列表
            ForEach(filteredProjects) { project in
                Button {
                    selectedProjectId = project.id
                    dismiss()
                } label: {
                    HStack {
                        // 项目标识
                        Text("!")
                            .font(.system(size: 16, weight: .bold))
                            .foregroundColor(AppColors.UI.projectColor(for: colorScheme, project: project))
                            .frame(width: 24, height: 24)
                        
                        Text(project.name)
                            .foregroundColor(AppColors.UI.projectColor(for: colorScheme, project: project))
                        
                        Spacer()
                        
                        if selectedProjectId == project.id {
                            Image(systemName: "checkmark")
                                .foregroundColor(AppColors.UI.accent(for: colorScheme))
                        }
                    }
                }
                .padding(.vertical, 4)
            }
            
            // 创建新项目选项
            Section {
                createProjectButton
            }
        }
    }
    
    // 创建新项目按钮
    private var createProjectButton: some View {
        Button(action: createProject) {
            HStack {
                Image(systemName: "plus.circle.fill")
                    .foregroundColor(AppColors.UI.accent(for: colorScheme))
                    .font(.system(size: 20))
                
                Text("创建新项目")
                    .foregroundColor(AppColors.UI.accent(for: colorScheme))
            }
        }
        .padding(.vertical, 8)
    }
    
    // 创建新项目
    private func createProject() {
        guard !searchText.isEmpty else { return }
        
        // 创建新项目
        if let newProject = viewModel.createProject(name: searchText) {
            // 更新选中的项目ID
            selectedProjectId = newProject.id
            
            // 清空搜索文本
            searchText = ""
            
            // 关闭选择器
            dismiss()
        }
    }
}
