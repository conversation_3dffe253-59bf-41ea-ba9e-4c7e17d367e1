import SwiftUI
import SwiftData

struct PopupSelectorView: View {
    enum SelectorType {
        case project
        case tag
    }
    
    let type: SelectorType
    @Binding var selectedItem: UUID?  // 项目ID
    @Binding var selectedTags: [String]
    @Binding var isPresented: Bool
    var initialSearchText: String = ""
    var returnFocusToTitle: (() -> Void)?
    
    @StateObject private var viewModel: PopupSelectorViewModel
    @State private var searchText = ""
    @FocusState private var isSearchFocused: Bool
    @Environment(\.colorScheme) private var colorScheme
    
    init(
        type: SelectorType,
        selectedItem: Binding<UUID?>,
        selectedTags: Binding<[String]>,
        isPresented: Binding<Bool>,
        initialSearchText: String = "",
        returnFocusToTitle: (() -> Void)? = nil,
        taskManager: TaskManager = DependencyContainer.taskManager()
    ) {
        self.type = type
        self._selectedItem = selectedItem
        self._selectedTags = selectedTags
        self._isPresented = isPresented
        self.initialSearchText = initialSearchText
        self.returnFocusToTitle = returnFocusToTitle
        _viewModel = StateObject(wrappedValue: PopupSelectorViewModel(
            taskManager: taskManager
        ))
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 顶部栏：搜索框和取消按钮
            HStack(spacing: 8) {
                // 搜索栏
                HStack(spacing: 8) {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(AppColors.UI.secondaryText(for: colorScheme))
                    
                    TextField(type == .project ? "搜索项目" : "搜索标签", text: $searchText)
                        .focused($isSearchFocused)
                        .textFieldStyle(PlainTextFieldStyle())
                        .font(.body)
                    
                    if !searchText.isEmpty {
                        Button(action: {
                            searchText = ""
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(AppColors.UI.secondaryText(for: colorScheme))
                        }
                    }
                }
                .padding(8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(AppColors.UI.systemGray6(for: colorScheme))
                )
                
                // 取消按钮
                Button("取消") {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                        isPresented = false
                    }
                    // 直接返回焦点到标题输入框
                    returnFocusToTitle?()
                }
                .foregroundColor(AppColors.UI.accent(for: colorScheme))
            }
            .padding(.horizontal)
            .padding(.vertical, 8)
            
            // 列表内容
            ScrollView {
                LazyVStack(alignment: .leading, spacing: 4) {
                    // 如果是标签选择器且有搜索文本，显示"创建新标签"选项
                    if type == .tag && !searchText.isEmpty && !viewModel.tagNames.contains(searchText) {
                        Button(action: {
                            createNewTag(searchText)
                        }) {
                            HStack {
                                Image(systemName: "plus.circle.fill")
                                    .foregroundColor(AppColors.UI.accent(for: colorScheme))
                                    .padding(.trailing, 4)
                                
                                Text("创建标签 \"\(searchText)\"")
                                    .foregroundColor(AppColors.UI.secondaryText(for: colorScheme))
                                
                                Spacer()
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .contentShape(Rectangle())
                        }
                        .buttonStyle(SelectionButtonStyle(isSelected: false, colorScheme: colorScheme))
                    }
                    
                    // 如果是项目选择器且有搜索文本，显示"创建新项目"选项
                    if type == .project && !searchText.isEmpty && !viewModel.projects.contains(where: { $0.name.lowercased() == searchText.lowercased() }) {
                        Button(action: {
                            createNewTag(searchText)
                        }) {
                            HStack {
                                Image(systemName: "plus.circle.fill")
                                    .foregroundColor(AppColors.UI.accent(for: colorScheme))
                                    .padding(.trailing, 4)
                                
                                Text("创建项目 \"\(searchText)\"")
                                    .foregroundColor(AppColors.UI.secondaryText(for: colorScheme))
                                
                                Spacer()
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .contentShape(Rectangle())
                        }
                        .buttonStyle(SelectionButtonStyle(isSelected: false, colorScheme: colorScheme))
                    }
                    
                    ForEach(filteredItems, id: \.self) { item in
                        Button(action: {
                            selectItem(item)
                        }) {
                            HStack {
                                if type == .project {
                                    // 项目前缀
                                    Text("!")
                                        .font(.system(size: 16, weight: .bold))
                                        .foregroundColor(getItemColor(item))
                                        .padding(.trailing, 4)
                                } else {
                                    // 标签前缀
                                    Text("#")
                                        .font(.system(size: 16, weight: .bold))
                                        .foregroundColor(getItemColor(item))
                                        .padding(.trailing, 4)
                                }
                                
                                Text(item)
                                    .foregroundColor(getItemColor(item))
                                
                                Spacer()
                                
                                if isItemSelected(item) {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(AppColors.UI.accent(for: colorScheme))
                                }
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .contentShape(Rectangle())
                        }
                        .buttonStyle(SelectionButtonStyle(isSelected: isItemSelected(item), colorScheme: colorScheme))
                    }
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
            }
            .frame(height: min(CGFloat(filteredItems.count + (type == .tag && !searchText.isEmpty && !viewModel.tagNames.contains(searchText) ? 1 : 0) + (type == .project && !searchText.isEmpty && !viewModel.projects.contains(where: { $0.name.lowercased() == searchText.lowercased() }) ? 1 : 0)) * 44 + 16, 300))
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(AppColors.UI.systemBackground(for: colorScheme))
                .shadow(
                    color: colorScheme == .dark ? .black : .black.opacity(0.1),
                    radius: 10,
                    x: 0,
                    y: 5
                )
        )
        .padding(.horizontal)
        .onAppear {
            loadItems()
            searchText = initialSearchText
            isSearchFocused = true
            
            // 初始化本地标签状态
            if type == .tag {
                // selectedTagsLocal = selectedTags
            }
        }
    }
    
    private var filteredItems: [String] {
        if type == .project {
            let projectNames = viewModel.projects.map { $0.name }
            if searchText.isEmpty {
                return projectNames
            }
            return projectNames.filter { $0.localizedCaseInsensitiveContains(searchText) }
        } else {
            if searchText.isEmpty {
                return viewModel.tagNames
            }
            return viewModel.tagNames.filter { $0.localizedCaseInsensitiveContains(searchText) }
        }
    }
    
    private func isItemSelected(_ item: String) -> Bool {
        if type == .project {
            return viewModel.projects.first(where: { $0.name == item })?.id == selectedItem
        } else {
            // Directly check the bound selectedTags
            return selectedTags.contains(item)
        }
    }
    
    private func selectItem(_ item: String) {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            if type == .project {
                // 根据名称查找项目并获取其ID
                if let project = viewModel.projects.first(where: { $0.name == item }) {
                    // 如果当前已选中该项目，则取消选择
                    if selectedItem == project.id {
                        selectedItem = nil
                    } else {
                        // 否则选择该项目
                        selectedItem = project.id
                    }
                }
                isPresented = false
            } else {
                // Directly manipulate the bound selectedTags
                if selectedTags.contains(item) {
                    // 如果标签已选中，则移除它
                    selectedTags.removeAll { $0 == item }
                } else {
                    // 否则添加该标签
                    selectedTags.append(item)
                }
                isPresented = false
            }
        }
        
        // 延迟执行，确保状态更新后再操作
        DispatchQueue.main.async { 
            returnFocusToTitle?()
        }
    }
    
    private func loadItems() {
        viewModel.loadItems(type: type)
    }
    
    // 获取项目或标签的颜色
    private func getItemColor(_ item: String) -> Color {
        return viewModel.getItemColor(item: item, type: type, colorScheme: colorScheme)
    }
    
    // 创建新标签或项目
    private func createNewTag(_ tagName: String) {
        let newItem = tagName.trimmingCharacters(in: .whitespacesAndNewlines)
        
        if newItem.isEmpty {
            return
        }
        
        if type == .project {
            // 创建新项目
            if let newProject = viewModel.createProject(name: newItem) {
                selectedItem = newProject.id
            }
        } else {
            // 创建新标签
            if let newTagName = viewModel.createTag(name: newItem) {
                // Directly add to the bound selectedTags if not present
                if !selectedTags.contains(newTagName) {
                    selectedTags.append(newTagName)
                }
            }
        }
        
        searchText = ""
        isPresented = false
        
        // 延迟执行，确保状态更新后再操作
        DispatchQueue.main.async { 
            returnFocusToTitle?()
        }
    }
}

// 自定义按钮样式
struct SelectionButtonStyle: ButtonStyle {
    let isSelected: Bool
    let colorScheme: ColorScheme
    @Environment(\.colorScheme) private var environmentColorScheme
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(
                        configuration.isPressed ?
                            AppColors.UI.accent(for: environmentColorScheme).opacity(0.2) :
                            (isSelected ?
                                AppColors.UI.accent(for: environmentColorScheme).opacity(0.1) :
                                Color.clear)
                    )
            )
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(.spring(response: 0.2, dampingFraction: 0.7), value: configuration.isPressed)
    }
}
