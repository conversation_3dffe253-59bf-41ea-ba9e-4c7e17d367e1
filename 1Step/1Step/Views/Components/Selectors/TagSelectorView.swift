import SwiftUI
import SwiftData

struct TagSelectorView: View {
    @Environment(\.dismiss) private var dismiss
    @Binding var selectedTags: [String]
    @Binding var taskTitle: String
    @State private var tags: [String] = []
    @State private var searchText: String = ""
    
    var body: some View {
        NavigationStack {
            VStack {
                // 搜索栏
                searchBar
                
                // 已选标签
                selectedTagsView
                
                // 标签列表
                tagList
            }
            .navigationTitle("选择标签")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .topBarTrailing) {
                    Button("完成") {
                        applyTagsToTitle()
                        dismiss()
                    }
                }
            }
            .onAppear {
                loadTags()
            }
        }
    }
    
    // 搜索栏
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.gray)
            
            TextField("搜索或创建标签", text: $searchText)
                .autocapitalization(.none)
                .disableAutocorrection(true)
                .submitLabel(.done)
                .onSubmit {
                    if !searchText.isEmpty {
                        addTag(searchText)
                    }
                }
            
            if !searchText.isEmpty {
                Button(action: {
                    if !searchText.isEmpty {
                        addTag(searchText)
                    }
                }) {
                    Image(systemName: "plus.circle.fill")
                        .foregroundColor(.blue)
                }
                .padding(.trailing, 4)
                
                Button(action: {
                    searchText = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.gray)
                }
            }
        }
        .padding(8)
        .background(Color(.systemGray6))
        .cornerRadius(8)
        .padding(.horizontal)
        .padding(.top, 8)
    }
    
    // 已选标签视图
    private var selectedTagsView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack {
                ForEach(selectedTags, id: \.self) { tag in
                    HStack(spacing: 4) {
                        Text(tag)
                            .font(.subheadline)
                        
                        Button(action: {
                            removeTag(tag)
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                    }
                    .padding(.vertical, 4)
                    .padding(.horizontal, 8)
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                }
            }
            .padding(.horizontal)
            .padding(.vertical, 8)
        }
        .frame(height: selectedTags.isEmpty ? 0 : 50)
        .opacity(selectedTags.isEmpty ? 0 : 1)
    }
    
    // 标签列表
    private var tagList: some View {
        List {
            // 创建新标签选项
            if !searchText.isEmpty && !tags.contains(where: { $0.lowercased() == searchText.lowercased() }) {
                Button(action: {
                    addTag(searchText)
                }) {
                    HStack {
                        Image(systemName: "plus.circle.fill")
                            .foregroundColor(.blue)
                        Text("创建标签 \"\(searchText)\"")
                    }
                }
            }
            
            // 现有标签列表
            ForEach(filteredTags, id: \.self) { tag in
                Button(action: {
                    toggleTag(tag)
                }) {
                    HStack {
                        Image(systemName: "tag")
                            .foregroundColor(.blue)
                        Text(tag)
                        Spacer()
                        if selectedTags.contains(tag) {
                            Image(systemName: "checkmark")
                                .foregroundColor(.blue)
                        }
                    }
                }
            }
        }
        .listStyle(PlainListStyle())
    }
    
    // 过滤后的标签列表
    private var filteredTags: [String] {
        if searchText.isEmpty {
            return tags
        } else {
            return tags.filter { $0.localizedCaseInsensitiveContains(searchText) }
        }
    }
    
    // 加载标签列表
    private func loadTags() {
        // 这里应该从数据库中加载所有已有的标签
        // 简单起见，先使用一些示例标签
        tags = ["重要", "紧急", "工作", "个人", "家庭", "学习", "健康", "电话", "邮件", "会议"]
    }
    
    // 添加标签
    private func addTag(_ tag: String) {
        let trimmedTag = tag.trimmingCharacters(in: .whitespacesAndNewlines)
        if !trimmedTag.isEmpty && !selectedTags.contains(trimmedTag) {
            selectedTags.append(trimmedTag)
            
            // 如果是新标签，添加到标签列表
            if !tags.contains(trimmedTag) {
                tags.append(trimmedTag)
            }
            
            searchText = ""
        }
    }
    
    // 移除标签
    private func removeTag(_ tag: String) {
        selectedTags.removeAll { $0 == tag }
    }
    
    // 切换标签选择状态
    private func toggleTag(_ tag: String) {
        if selectedTags.contains(tag) {
            removeTag(tag)
        } else {
            addTag(tag)
        }
    }
    
    // 将标签应用到标题
    private func applyTagsToTitle() {
        // 先移除已有的标签
        var title = taskTitle
        
        // 正则表达式匹配所有 #标签 格式
        let tagPattern = "#[^\\s]+"
        let regex = try? NSRegularExpression(pattern: tagPattern, options: [])
        
        // 从后向前替换，以避免替换过程中索引变化
        if let matches = regex?.matches(in: title, options: [], range: NSRange(title.startIndex..., in: title)) {
            for match in matches.reversed() {
                if let range = Range(match.range, in: title) {
                    title.removeSubrange(range)
                }
            }
        }
        
        // 清理多余的空格
        title = title.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 添加所有选中的标签
        for tag in selectedTags {
            title += " #\(tag)"
        }
        
        taskTitle = title
    }
}
