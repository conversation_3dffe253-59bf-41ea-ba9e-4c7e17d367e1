import SwiftUI
import Combine

/// 自适应键盘的视图修饰符
struct KeyboardAdaptive: ViewModifier {
    @State private var keyboardHeight: CGFloat = 0
    
    func body(content: Content) -> some View {
        content
            .padding(.bottom, keyboardHeight)
            .onAppear {
                NotificationCenter.default.addObserver(forName: UIResponder.keyboardWillShowNotification, object: nil, queue: .main) { notification in
                    let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect ?? .zero
                    // 添加一点额外空间确保内容完全可见
                    self.keyboardHeight = keyboardFrame.height + 16
                }
                
                NotificationCenter.default.addObserver(forName: UIResponder.keyboardWillHideNotification, object: nil, queue: .main) { _ in
                    self.keyboardHeight = 0
                }
            }
    }
}

/// 键盘自适应扩展
extension View {
    func keyboardAdaptive() -> some View {
        modifier(KeyboardAdaptive())
    }
} 