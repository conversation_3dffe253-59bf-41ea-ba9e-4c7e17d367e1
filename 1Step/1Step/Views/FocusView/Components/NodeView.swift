import SwiftUI
import SwipeActions

/// 通用节点视图组件 - 统一处理Task、ChecklistItem、SubStep的显示
struct NodeView: View {
    let node: Node
    let style: NodeStyle
    let context: DisplayContext
    @ObservedObject var focusManager: FocusManager
    @ObservedObject var viewModel: FocusViewModel

    // 编辑相关参数
    let isEditing: Bool
    @Binding var editingText: String
    @FocusState.Binding var isEditingFocused: Bool
    let onStartEditing: () -> Void
    let onSaveEdit: () -> Void
    let onCancelEdit: () -> Void
    let onDeleteNode: () -> Void

    // 滑动状态管理 - 传入的全局滑动状态
    @Binding var activeSwipeContext: SwipeContext?

    // 任务详情状态 - 与TaskCardView保持一致
    @State private var showingTaskDetail = false

    // 计算属性
    private var canFocus: Bool {
        node is Task
    }

    // 节点位置
    @State private var nodeFrame: CGRect = .zero

    // 检查节点是否在完成过程中
    private var isNodeCompleting: Bool {
        if let task = node as? Task {
            // 对于任务，检查pendingCompletionTaskIDs
            return viewModel.pendingCompletionTaskIDs.contains(task.id)
        } else {
            // 对于小行动和子步骤，检查animatingNodes
            return focusManager.isNodeAnimating(node.id)
        }
    }



    // MARK: - 初始化方法

    /// 便利初始化方法 - 不带编辑功能
    init(
        node: Node,
        style: NodeStyle,
        context: DisplayContext,
        focusManager: FocusManager,
        viewModel: FocusViewModel,
        activeSwipeContext: Binding<SwipeContext?> = .constant(nil)
    ) {
        self.node = node
        self.style = style
        self.context = context
        self.focusManager = focusManager
        self.viewModel = viewModel

        // 编辑相关的默认值
        self.isEditing = false
        self._editingText = .constant("")
        self._isEditingFocused = FocusState<Bool>().projectedValue
        self.onStartEditing = {}
        self.onSaveEdit = {}
        self.onCancelEdit = {}
        self.onDeleteNode = {}
        self._activeSwipeContext = activeSwipeContext
    }

    /// 完整初始化方法 - 带编辑功能
    init(
        node: Node,
        style: NodeStyle,
        context: DisplayContext,
        focusManager: FocusManager,
        viewModel: FocusViewModel,
        isEditing: Bool,
        editingText: Binding<String>,
        isEditingFocused: FocusState<Bool>.Binding,
        activeSwipeContext: Binding<SwipeContext?>,
        onStartEditing: @escaping () -> Void,
        onSaveEdit: @escaping () -> Void,
        onCancelEdit: @escaping () -> Void,
        onDeleteNode: @escaping () -> Void
    ) {
        self.node = node
        self.style = style
        self.context = context
        self.focusManager = focusManager
        self.viewModel = viewModel
        self.isEditing = isEditing
        self._editingText = editingText
        self._isEditingFocused = isEditingFocused
        self._activeSwipeContext = activeSwipeContext
        self.onStartEditing = onStartEditing
        self.onSaveEdit = onSaveEdit
        self.onCancelEdit = onCancelEdit
        self.onDeleteNode = onDeleteNode
    }

    var body: some View {
        SwipeView {
            VStack(spacing: 0) {
                // 根据上下文和样式使用不同的展现方式
                if style == .primary {
                    // 当前聚焦的根节点或列表中的任务，使用大标题样式
                    taskRowContent
                } else {
                    // 子项使用小行动样式
                    checklistItemRowContent
                }
            }
            // 只对任务应用缩放和透明度效果，不对小行动应用
            .scaleEffect(isNodeCompleting && node is Task ? 0.97 : 1.0)
            .opacity(isNodeCompleting && node is Task ? 0.7 : 1.0)
            .animation(.easeInOut(duration: 0.3), value: isNodeCompleting)
            .background(
                GeometryReader { geometry in
                    Color.clear.onAppear {
                        // 获取节点在全局坐标系中的位置
                        DispatchQueue.main.async {
                            let frame = geometry.frame(in: .global)
                            self.nodeFrame = frame
                        }
                    }
                }
            )
        } leadingActions: { swipeContext in
            // Set activeSwipeContext synchronously when leading actions are revealed
            // Using DispatchQueue.main.async to ensure UI updates are on the main thread if context is accessed later by UI
            DispatchQueue.main.async {
                self.activeSwipeContext = swipeContext
            }
            return leadingSwipeActions(swipeContext)
        } trailingActions: { swipeContext in
            // Set activeSwipeContext synchronously when trailing actions are revealed
            // Using DispatchQueue.main.async to ensure UI updates are on the main thread if context is accessed later by UI
            DispatchQueue.main.async {
                self.activeSwipeContext = swipeContext
            }
            return trailingSwipeActions(swipeContext)
        }
        .swipeActionsStyle(.cascade)
        .swipeActionsVisibleStartPoint(0)
        .swipeActionsVisibleEndPoint(0)
        .swipeActionCornerRadius(14)
        .swipeSpacing(6)
        .swipeActionsMaskCornerRadius(14)
        .swipeMinimumDistance(30) // 减少最小滑动距离，让滑动更敏感
        .swipeActionWidth(style == .primary ? 100 : 90) // 显著增加按钮宽度，让滑动距离更长
        .swipeOffsetTriggerAnimation(stiffness: 300, damping: 25) // 增加弹性
        .swipeOffsetCloseAnimation(stiffness: 200, damping: 20) // 更流畅的关闭动画
        .swipeReadyToTriggerPadding(60) // 增加触发区域，配合更长的滑动距离
        .swipeMinimumPointToTrigger(150) // 增加触发点，配合更长的按钮宽度
        .swipeEnableTriggerHaptics(true)
        .sheet(isPresented: $showingTaskDetail) {
            // 完全复制TaskCardView中的实现
            NavigationStack {
                if let task = node as? Task {
                    // 如果当前节点是任务，直接显示
                    TaskDetailView(
                        task: task,
                        sourcePageType: (style == .primary && context == .focusMode) ? .focus : .doing,
                        currentPathData: (style == .primary && context == .focusMode) ? createCurrentPathData() : nil,
                        onDeleteIntent: { taskToDelete in
                            viewModel.handleTaskDelete(taskToDelete)
                        },
                        onRestoreActionFocus: { taskId, checklistItemId, subStepPath in
                            // 关闭任务详情弹窗
                            showingTaskDetail = false

                            // 延迟一帧确保弹窗关闭后再恢复焦点
                            DispatchQueue.main.async {
                                viewModel.restoreActionFocusState(
                                    taskId: taskId,
                                    checklistItemId: checklistItemId,
                                    subStepPath: subStepPath
                                )
                            }
                        }
                    )
                } else if let rootTaskId = focusManager.focusPath.first,
                          let rootTask = viewModel.doingTasks.first(where: { $0.id == rootTaskId }) {
                    // 如果是子项，显示其所属的根任务
                    TaskDetailView(
                        task: rootTask,
                        sourcePageType: (style == .primary && context == .focusMode) ? .focus : .doing,
                        currentPathData: (style == .primary && context == .focusMode) ? createCurrentPathData() : nil,
                        onDeleteIntent: { taskToDelete in
                            viewModel.handleTaskDelete(taskToDelete)
                        },
                        onRestoreActionFocus: { taskId, checklistItemId, subStepPath in
                            // 关闭任务详情弹窗
                            showingTaskDetail = false

                            // 延迟一帧确保弹窗关闭后再恢复焦点
                            DispatchQueue.main.async {
                                viewModel.restoreActionFocusState(
                                    taskId: taskId,
                                    checklistItemId: checklistItemId,
                                    subStepPath: subStepPath
                                )
                            }
                        }
                    )
                }
            }
            .presentationDetents([.medium, .large])
            .presentationDragIndicator(.visible)
        }
    }

    /// 创建当前路径数据
    private func createCurrentPathData() -> CurrentPathData? {
        return PathDataHelper.createCurrentPathData(with: focusManager) { level in
            _Concurrency.Task { @MainActor in
                PathDataHelper.navigateToTaskDetailLevel(
                    level,
                    focusManager: self.focusManager,
                    dismissAction: { self.showingTaskDetail = false }
                )
            }
        }
    }

    // MARK: - 任务行内容
    private var taskRowContent: some View {
        VStack(spacing: 0) {
            HStack(spacing: 16) { // 恢复16像素间距，保持原有布局
                // 勾选框
                completionButton

                // 内容区
                contentArea
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .frame(minHeight: 60)
            .background(Color(.systemBackground))
            .contentShape(Rectangle())
            .onTapGesture {
                // 编辑模式下不处理点击
                if !isEditing {
                    // 修改为点击卡片显示详情
                    handleTitleTap()
                }
            }

            // 任务行的分隔线
            HStack {
                Spacer().frame(width: 72) // 与文本开始位置对齐: 16(左边距) + 44(勾选框触摸区域) + 12(间距) = 72
                Rectangle()
                    .frame(height: 0.6)
                    .foregroundColor(Color.gray.opacity(0.3))
            }
        }
    }

    // MARK: - 小行动行内容
    private var checklistItemRowContent: some View {
        HStack(spacing: 0) { // 使用0间距，让勾选框触摸区域紧贴文字区域
            // 勾选框部分
            completionButton

            // 文本和图标的容器，每个小行动有独立的背景
            HStack(spacing: 6) {
                // 根据编辑状态显示不同内容
                if isEditing {
                    // 编辑模式：显示文本输入框
                    TextField("节点内容", text: $editingText)
                        .font(.system(size: 15))
                        .foregroundColor(.primary)
                        .focused($isEditingFocused)
                        .submitLabel(.done)
                        .onSubmit {
                            onSaveEdit()
                        }
                        .onAppear {
                            isEditingFocused = true
                        }
                } else {
                    // 显示模式：显示文本
                    Text(node.title)
                        .font(.system(size: 15))
                        .foregroundColor((node.isCompleted || isNodeCompleting) ?
                                       .secondary.opacity(0.6) :
                                       .primary.opacity(0.75))
                        .strikethrough(node.isCompleted || isNodeCompleting, color: .secondary)
                        .lineLimit(2)
                        .truncationMode(.tail)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .contentShape(Rectangle())
                        .onTapGesture(count: 2) {
                            // 双击编辑未完成的子项，减少误触
                            if !node.isCompleted && !isNodeCompleting {
                                onStartEditing()
                            }
                        }

                    // 在一步模式下显示target图标（仅对非根节点的未完成子项）
                    if context == .focusMode && style != .primary && !node.isCompleted {
                        targetButton
                    }
                }

                // 编辑模式下显示操作按钮
                if isEditing {
                    HStack(spacing: 8) {
                        Button(action: onCancelEdit) {
                            Image(systemName: "xmark")
                                .font(.system(size: 12))
                                .foregroundColor(.secondary)
                        }
                        .buttonStyle(PlainButtonStyle())

                        Button(action: onSaveEdit) {
                            Image(systemName: "checkmark")
                                .font(.system(size: 12))
                                .foregroundColor(.accentColor)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
            }
            .padding(.vertical, 2) // 进一步减少垂直内边距
            .padding(.leading, 8) // 左边距控制与勾选框的视觉间距
            .padding(.trailing, 8)
            .background(
                // 每个小行动都有独立的灰色背景
                node.isCompleted ? Color.clear : Color.gray.opacity(0.03)
            )
            .cornerRadius(4)
        }
        .padding(.leading, style == .primary ? 22 : 56) // 统一：所有子项都与主任务文本左对齐（56像素）
        .padding(.trailing, 22) // 右侧保持一致
        .contentShape(Rectangle())
        .onTapGesture {
            // 阻止点击事件冒泡到父级SwipeView，防止误触发任务详情
        }
    }

    // MARK: - 勾选框

    private var completionButton: some View {
        Button {
            // 根据节点样式判断烟花大小，而不是节点类型
            if style == .primary {
                // 主要样式（大标题）使用大烟花
                if let task = node as? Task {
                    viewModel.handleTaskComplete(task)
                } else {
                    viewModel.handleNodeCompletion(node.id)
                }

                // 触发大烟花效果
                ConfettiManager.shared.trigger(at: nil, isLarge: true)
            } else {
                // 次要样式（展开项）使用小烟花
                if let task = node as? Task {
                    viewModel.handleTaskComplete(task)
                } else {
                    viewModel.handleNodeCompletion(node.id)
                }

                // 获取当前节点在屏幕上的位置
                let screenSize = UIScreen.main.bounds.size
                let position = CGPoint(x: screenSize.width * 0.25, y: screenSize.height * 0.35)

                // 触发小烟花效果
                ConfettiManager.shared.trigger(at: position, isLarge: false)
            }
        } label: {
            if style == .primary {
                // 根节点使用大勾选框，不管节点类型
                taskCheckbox
            } else {
                // 子节点使用小勾选框
                checklistItemCheckbox
            }
        }
        .buttonStyle(PlainButtonStyle())
        .frame(width: 44, height: 44) // 扩大触摸区域到苹果推荐的最小尺寸
        .contentShape(Rectangle()) // 确保整个44x44区域都可以点击
    }

    private var taskCheckbox: some View {
        ZStack {
            Circle()
                .strokeBorder(
                    node.isCompleted || isNodeCompleting ? Color.green : Color.gray.opacity(0.3),
                    lineWidth: 2
                )
                .frame(width: style.checkboxSize, height: style.checkboxSize)

            if node.isCompleted || isNodeCompleting {
                Image(systemName: "checkmark")
                    .font(.system(size: style.checkboxSize * 0.6, weight: .medium))
                    .foregroundColor(.green)
            }

            // 动画效果 - 对于正在完成的节点显示波纹
            if isNodeCompleting {
                Circle()
                    .fill(Color.green.opacity(0.3))
                    .frame(width: style.checkboxSize, height: style.checkboxSize)
                    .scaleEffect(isNodeCompleting ? 1.5 : 1.0)
                    .opacity(isNodeCompleting ? 0 : 1)
                    .animation(.easeOut(duration: 1.5), value: isNodeCompleting)
            }
        }
        // 对于正在完成的任务，添加缩小和透明度效果
        .scaleEffect(isNodeCompleting && node is Task ? 0.95 : 1.0)
        .opacity(isNodeCompleting && node is Task ? 0.8 : 1.0)
        .frame(width: 44, height: 44) // 确保在44x44区域内居中
    }

    private var checklistItemCheckbox: some View {
        Image(systemName: (node.isCompleted || isNodeCompleting) ? "checkmark.circle.fill" : "circle")
            .font(.system(size: 18)) // 稍微增大图标尺寸，提升可见度
            .foregroundColor((node.isCompleted || isNodeCompleting) ?
                            Color.green.opacity(0.8) :
                            Color.gray.opacity(0.7))
            // 对于正在完成的小行动/子步骤，添加缩小和透明度效果
            .scaleEffect(isNodeCompleting ? 0.95 : 1.0)
            .opacity(isNodeCompleting ? 0.8 : 1.0)
            .frame(width: 44, height: 44) // 确保在44x44区域内居中
    }

    // MARK: - 内容区

    private var contentArea: some View {
        ZStack(alignment: .trailing) { // 使用ZStack将展开按钮覆盖在右侧
            // 主要内容
            VStack(alignment: .leading, spacing: 4) {
                // 标题行 - 根据编辑状态显示
                if isEditing {
                    // 编辑模式：显示文本输入框
                    TextField("任务内容", text: $editingText)
                        .font(.system(size: 16))
                        .foregroundColor(.primary)
                        .focused($isEditingFocused)
                        .submitLabel(.done)
                        .onSubmit {
                            onSaveEdit()
                        }
                        .onAppear {
                            isEditingFocused = true
                        }
                } else {
                    // 显示模式：显示文本 - 限制最多为2行
                    Text(node.title)
                        .font(.system(size: 16))
                        .foregroundColor(node.isCompleted || isNodeCompleting ?
                            Color.secondary :
                            Color.primary.opacity(0.8))
                        .multilineTextAlignment(.leading)
                        .lineLimit(2)
                        .strikethrough(node.isCompleted || isNodeCompleting, color: Color.secondary)
                }

                // 编辑模式下显示操作按钮（仅在任务编辑时显示，目前任务不支持直接编辑）
                if isEditing && !(node is Task) {
                    HStack(spacing: 8) {
                        Button(action: onCancelEdit) {
                            Image(systemName: "xmark")
                                .font(.system(size: 14))
                                .foregroundColor(.secondary)
                        }
                        .buttonStyle(PlainButtonStyle())

                        Button(action: onSaveEdit) {
                            Image(systemName: "checkmark")
                                .font(.system(size: 14))
                                .foregroundColor(.accentColor)
                        }
                        .buttonStyle(PlainButtonStyle())

                        Button(action: onDeleteNode) {
                            Image(systemName: "trash")
                                .font(.system(size: 14))
                                .foregroundColor(.red)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }

                // 项目信息 - 只在任务列表模式下显示，一步模式下不显示（因为面包屑已有）
                if context == .taskList,
                   let task = node as? Task, let projectId = task.project {
                    if let projectName = getProjectDisplayName(for: projectId),
                       let projectColor = getProjectColor(for: projectId) {
                        // 项目名称和彩色感叹号
                        HStack(spacing: 0) {
                            Text("!")
                                .font(.caption)
                                .foregroundColor(projectColor)
                            Text(projectName)
                                .font(.caption)
                                .foregroundColor(Color.gray)
                        }
                    }
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(.trailing, shouldShowExpandButton ? 28 : 0) // 给展开按钮留出足够空间

            // 展开图标放在右侧中间位置
            if shouldShowExpandButton {
                expandButton
            }
        }
    }

    /// 计算是否应该显示展开按钮
    private var shouldShowExpandButton: Bool {
        // 一步模式下始终显示展开按钮（用于添加子项）
        if context == .focusMode {
            return true
        }

        // 任务列表模式下，只有当节点有未完成子节点或正在动画的已完成子节点时才显示
        let hasVisibleChildren = node.children.contains { child in
            !child.isCompleted || focusManager.isNodeAnimating(child.id)
        }

        return hasVisibleChildren
    }

    // 获取项目名称
    private func getProjectDisplayName(for projectId: UUID) -> String? {
        // 从项目仓库中获取项目名称
        let projectRepository = DependencyContainer.projectRepository()
        if let project = projectRepository.getProject(byId: projectId) {
            return project.name
        }
        return nil
    }

    // 获取项目颜色
    private func getProjectColor(for projectId: UUID) -> Color? {
        // 从项目仓库中获取项目颜色
        let projectRepository = DependencyContainer.projectRepository()
        if let project = projectRepository.getProject(byId: projectId),
           let colorHex = project.color {
            return Color(hex: colorHex)
        }
        return Color.blue
    }

    // 子节点信息视图
    private var childrenInfoView: some View {
        HStack(spacing: 4) {
            let progress = node.completionProgress

            if progress.total > 0 {
                Text("\(progress.completed)/\(progress.total)")
                    .font(.caption2)
                    .foregroundColor(.gray)

                if progress.completed > 0 {
                    Circle()
                        .fill(Color.green)
                        .frame(width: 4, height: 4)
                }
            }
        }
    }

    // MARK: - 操作区

    // 展开按钮
    private var expandButton: some View {
        Button {
            focusManager.toggleNodeExpansion(node.id)
        } label: {
            Image(systemName: focusManager.isNodeExpanded(node.id) ? "chevron.up" : "chevron.down")
                .font(.system(size: 13))
                .foregroundColor(Color.secondary.opacity(0.5))
                .frame(width: 24, height: 24)
                .background(Color.clear)
                .contentShape(Rectangle()) // 确保整个区域可点击
                .frame(width: 44, height: 44) // 增大点击区域到44x44（苹果推荐的最小触摸区域）
        }
        .buttonStyle(PlainButtonStyle())
    }

    // 聚焦按钮
    private var focusButton: some View {
        Button {
            if let task = node as? Task {
                focusManager.focusTo(task.id)
            }
        } label: {
            Image(systemName: "scope")
                .font(.system(size: 13))
                .foregroundColor(style == .primary ? Color.blue : Color.gray)
                .frame(width: 24, height: 24)
        }
        .buttonStyle(PlainButtonStyle())
    }

    // Target按钮 - 在一步模式下点击可深入聚焦到该子项
    private var targetButton: some View {
        Button {
            focusManager.focusDeeper(to: node.id)
        } label: {
            Image(systemName: "target")
                .font(.system(size: 12))
                .foregroundColor(.blue.opacity(0.7))
                .frame(width: 20, height: 20)
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - 滑动操作

    /// 创建滑动操作的通用方法
    private func createSwipeAction(
        icon: String,
        title: String,
        color: Color,
        action: @escaping () -> Void,
        swipeContext: SwipeContext
    ) -> some View {
        SwipeAction(
            systemImage: icon,
            backgroundColor: color,
            highlightOpacity: 1
        ) {
            action()
            closeSwipeView()
        }
        .allowSwipeToTrigger(false) // 不允许滑动到底自动触发，需要点击按钮
        .foregroundColor(.white) // 确保图标颜色始终为白色
        .font(.title2) // 增加图标大小
        .onAppear {
            self.activeSwipeContext = swipeContext
        }
    }

    /// 右滑操作 (leadingSwipeActions) - 根据上下文调整
    private func leadingSwipeActions(_ swipeContext: SwipeContext) -> some View {
        Group {
            if context == .taskList {
                // 一步列表模式：右滑显示"一步模式"操作
                if node is Task {
                    createSwipeAction(
                        icon: "target",
                        title: "一步模式",
                        color: Color.orange,
                        action: handleFocusAction,
                        swipeContext: swipeContext
                    )
                } else {
                    // 展开项不支持右滑操作
                    EmptyView()
                }
            } else {
                // 一步模式：右滑删除（仅对次要节点）
                if style != .primary {
                    expandedItemTrailingActions(swipeContext)
                } else {
                    // 主要节点不支持右滑操作
                    EmptyView()
                }
            }
        }
    }

    /// 左滑操作 (trailingSwipeActions) - 根据上下文调整
    private func trailingSwipeActions(_ swipeContext: SwipeContext) -> some View {
        Group {
            if context == .taskList {
                // 一步列表模式：左滑显示"安排"操作
                if node is Task {
                    createSwipeAction(
                        icon: "calendar.badge.clock",
                        title: "安排",
                        color: Color.blue,
                        action: handleMoveAction,
                        swipeContext: swipeContext
                    )
                } else {
                    // 展开项：左滑删除
                    expandedItemTrailingActions(swipeContext)
                }
            } else {
                // 一步模式：左滑显示"退出一步模式"（仅对主要节点）
                if style == .primary {
                    createSwipeAction(
                        icon: "arrow.left",
                        title: "退出一步模式",
                        color: Color.gray,
                        action: handleExitFocusAction,
                        swipeContext: swipeContext
                    )
                } else {
                    // 次要节点：左滑删除
                    expandedItemTrailingActions(swipeContext)
                }
            }
        }
    }

    /// 展开项的右滑操作：只支持删除
    /// 适用于所有展开的子项（小行动、子步骤等）
    private func expandedItemTrailingActions(_ swipeContext: SwipeContext) -> some View {
        SwipeAction(
            action: {
                onDeleteNode()
                closeSwipeView()
            },
            label: { highlighted in
                Image(systemName: "trash")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.red.opacity(0.7))
                    .frame(width: 44, height: 32) // 适配当前行高，保持合理的触摸区域
                    .contentShape(Rectangle()) // 确保整个区域可点击
                    .scaleEffect(highlighted ? 0.9 : 1.0)
            },
            background: { highlighted in
                Color.clear
            }
        )
    }

    // MARK: - 交互处理

    private func handleTap() {
        // 整个卡片点击都显示任务详情
        handleTitleTap()
    }

    /// 关闭滑动视图方法
    private func closeSwipeView() {
        // 添加短暂延迟，确保UI状态已更新
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            if let context = self.activeSwipeContext {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    context.state.wrappedValue = .closed
                }
                // 不清空activeSwipeContext，让SwipeView自己管理
                // self.activeSwipeContext = nil // 不清空activeSwipeContext，让SwipeView自己管理
            } else {
                // activeSwipeContext is nil, nothing to close
            }
        }
    }

    /// 处理移动操作
    private func handleMoveAction() {

        // 触感反馈
        UIImpactFeedbackGenerator(style: .light).impactOccurred()

        if let task = node as? Task {
            guard let contextToClose = self.activeSwipeContext else {
                // activeSwipeContext is nil, cannot proceed with closing based on it.
                // This might happen if the swipe view was closed by another interaction before this action was fully processed.
                return
            }
            // 计算菜单显示位置
            var position: CGPoint

            if nodeFrame != .zero {
                // 使用节点的顶部中点，在其上方20pt显示
                position = CGPoint(
                    x: UIScreen.main.bounds.width / 2, // 水平居中
                    y: nodeFrame.minY - 20 // 垂直在节点上方20pt
                )
            } else {
                // 默认位置
                position = CGPoint(
                    x: UIScreen.main.bounds.width / 2,
                    y: UIScreen.main.bounds.height * 0.3
                )
            }

            // 确保不超出屏幕顶部安全区域
            position.y = max(position.y, 100)

            // 显示操作菜单
            viewModel.showTaskArrangementSheet(for: task, at: position) {
                // 当菜单关闭时，使用捕获的 contextToClose 关闭滑动状态
                if contextToClose.state.wrappedValue != .closed {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        contextToClose.state.wrappedValue = .closed
                    }
                } else {
                    // Context was already closed, nothing to do.
                }
            }
        }
    }

    /// 处理一步模式操作
    private func handleFocusAction() {
        // 添加触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .light)
        generator.impactOccurred()

        if let task = node as? Task {
            viewModel.focusTo(task.id)
        }
    }

    /// 处理退出一步模式操作
    private func handleExitFocusAction() {
        // 添加触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .light)
        generator.impactOccurred()

        viewModel.exitFocus()
    }

    /// 处理标题点击 - 打开任务详情
    private func handleTitleTap() {
        // 直接设置状态显示详情页，与TaskCardView保持一致
        showingTaskDetail = true
    }
}
