import SwiftUI

/// 面包屑导航视图
struct BreadcrumbView: View {
    @ObservedObject var focusManager: FocusManager
    @ObservedObject var viewModel: FocusViewModel
    @Binding var showingTaskDetailFromBreadcrumb: Bool
    
    var body: some View {
        ActionPathComponent(
            style: .navigation,
            focusManager: focusManager,
            onFocusNavigate: { level in
                focusManager.navigateToBreadcrumb(level)
                viewModel.loadTasks()
            }
        )
    }
}

#Preview {
    let taskManager = TaskManager(
        taskRepository: DependencyContainer.taskRepository(),
        projectRepository: DependencyContainer.projectRepository(),
        tagRepository: DependencyContainer.tagRepository()
    )
    let viewModel = FocusViewModel(taskManager: taskManager)
    
    // 模拟聚焦状态
    viewModel.focusManager.focusPath = [UUID(), UUID(), UUID()]
    
    return BreadcrumbView(
        focusManager: viewModel.focusManager,
        viewModel: viewModel,
        showingTaskDetailFromBreadcrumb: .constant(false)
    )
    .padding()
} 