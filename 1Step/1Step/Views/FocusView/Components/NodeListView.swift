import SwiftUI
import SwipeActions

/// 通用节点列表视图 - 支持递归显示和交互
struct NodeListView: View {
    let nodes: [Node]
    let context: DisplayContext
    @ObservedObject var focusManager: FocusManager
    @ObservedObject var viewModel: FocusViewModel

    private let maxDepth: Int
    private let currentDepth: Int

    // 编辑状态管理
    @State private var editingNodeId: UUID?
    @State private var editingText: String = ""
    @FocusState private var isEditingFocused: Bool

    // 添加子节点状态管理
    @State private var addingToNodeId: UUID?
    @State private var newNodeTitle: String = ""
    @FocusState private var isAddingFocused: Bool

    // 滑动状态管理 - SwipeViewGroup自动处理滑动互斥
    @State private var activeSwipeContext: SwipeContext? = nil

    init(
        nodes: [Node],
        context: DisplayContext,
        focusManager: FocusManager,
        viewModel: FocusViewModel,
        maxDepth: Int = 10,
        currentDepth: Int = 0
    ) {
        self.nodes = nodes
        self.context = context
        self.focusManager = focusManager
        self.viewModel = viewModel
        self.maxDepth = maxDepth
        self.currentDepth = currentDepth
    }

    var body: some View {
        LazyVStack(spacing: currentDepth > 0 ? -6 : 0) { // 子项使用更大的负间距，让展开项非常紧凑
            SwipeViewGroup {
                ForEach(nodes, id: \.id) { node in
                    VStack(spacing: 0) {
                        // 节点本身
                        NodeView(
                            node: node,
                            style: getNodeStyle(for: node),
                            context: context,
                            focusManager: focusManager,
                            viewModel: viewModel,
                            isEditing: editingNodeId == node.id && !(node is Task),
                            editingText: $editingText,
                            isEditingFocused: $isEditingFocused,
                            activeSwipeContext: $activeSwipeContext,
                            onStartEditing: {
                                // 只有非任务节点才能直接编辑
                                if !(node is Task) {
                                    startEditingNode(node)
                                }
                            },
                            onSaveEdit: { saveNodeEdit(node) },
                            onCancelEdit: { cancelNodeEdit() },
                            onDeleteNode: { deleteNode(node) }
                        )
                        .id(node.id)
                        .opacity(getNodeOpacity(for: node))

                        // 递归显示子节点
                        if shouldShowChildren(for: node) {
                            VStack(spacing: 0) {
                                // 子节点内容 - 添加左缩进
                                VStack(spacing: 0) {
                                    childrenView(for: node)

                                    // 添加输入框 - 在以下情况显示：
                                    // 1. 任务列表模式下：只有任务下显示
                                    // 2. 一步模式下：
                                    //    - 如果是根节点（最顶层）则显示
                                    //    - 如果是聚焦路径的最后一个节点并且明确展开了则显示
                                    if context == .taskList && node is Task {
                                        // 任务列表模式下只有任务显示添加入口
                                        addItemInputView(for: node)
                                    } else if context == .focusMode {
                                        // 判断是否是根节点（当前聚焦的节点）
                                        let isFocusRoot = currentDepth == 0

                                        // 判断是否是聚焦路径的最后一个节点
                                        let isInFocusPath = focusManager.isInFocusPath(node.id)
                                        let isLastInFocusPath = focusManager.focusPath.last == node.id

                                        // 是否已明确展开
                                        let isExplicitlyExpanded = focusManager.isNodeExpanded(node.id)

                                        // 根节点显示，或者聚焦路径上的节点且明确展开才显示
                                        if isFocusRoot || (isInFocusPath && isExplicitlyExpanded) {
                                            addItemInputView(for: node)
                                        }
                                    }
                                }
                            }
                            .transition(.asymmetric(
                                insertion: .scale(scale: 0.95).combined(with: .opacity),
                                removal: .scale(scale: 0.95).combined(with: .opacity)
                            ))
                        }
                    }
                    .animation(.spring(response: 0.3, dampingFraction: 0.8), value: focusManager.isNodeExpanded(node.id))
                }
            }
        }
        .padding(.top, context == .taskList ? 16 : 0)
    }

    // MARK: - 子节点视图

    private func childrenView(for node: Node) -> some View {
        // 在所有模式下，都显示未完成的子节点和正在动画中的已完成节点
        let filteredChildren = node.children.filter { !$0.isCompleted || focusManager.isNodeAnimating($0.id) }

        // 注意：子NodeListView将会有自己的activeSwipeContext状态，
        // 这样每个层级的滑动都是独立的，这是预期的行为
        return NodeListView(
            nodes: filteredChildren,
            context: context,
            focusManager: focusManager,
            viewModel: viewModel,
            maxDepth: maxDepth,
            currentDepth: currentDepth + 1
        )
        .padding(.top, 2) // 统一使用很小的上方间距
    }

    // MARK: - 添加输入框

    private func addItemInputView(for parentNode: Node) -> some View {
        HStack(spacing: 0) { // 改为0间距，与NodeView保持一致
            // 占位符勾选框 - 与NodeView的勾选框保持一致
            Image(systemName: "circle")
                .font(.system(size: 18)) // 与NodeView的checklistItemCheckbox一致
                .foregroundColor(Color.gray.opacity(0.4))
                .frame(width: 44, height: 44) // 与NodeView的勾选框触摸区域一致

            // 输入区域
            if addingToNodeId == parentNode.id {
                // 编辑模式：显示输入框
                HStack(spacing: 6) {
                    TextField("输入内容...", text: $newNodeTitle)
                        .font(.system(size: 15))
                        .foregroundColor(.primary)
                        .focused($isAddingFocused)
                        .submitLabel(.done)
                        .onSubmit {
                            saveNewNode(to: parentNode)
                        }
                        .onAppear {
                            print("[FOCUS_DEBUG] TextField onAppear，parentNode: \(parentNode.id)")
                            print("[FOCUS_DEBUG] TextField onAppear，当前 isAddingFocused: \(isAddingFocused)")
                        }
                        .onDisappear {
                            print("[FOCUS_DEBUG] TextField onDisappear，parentNode: \(parentNode.id)")
                        }
                        .onChange(of: isAddingFocused) { oldValue, newValue in
                            print("[FOCUS_DEBUG] isAddingFocused 变化：\(oldValue) -> \(newValue)，parentNode: \(parentNode.id)")
                        }
                        .padding(.vertical, 8)
                        .padding(.leading, 8)
                        .padding(.trailing, 8)

                    // 操作按钮 - 只在有内容时显示
                    if !newNodeTitle.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                        HStack(spacing: 8) {
                            Button(action: {
                                cancelAddingNode()
                            }) {
                                Image(systemName: "xmark")
                                    .font(.system(size: 12))
                                    .foregroundColor(.secondary)
                            }
                            .buttonStyle(PlainButtonStyle())

                            Button(action: {
                                saveNewNode(to: parentNode)
                            }) {
                                Image(systemName: "checkmark")
                                    .font(.system(size: 12))
                                    .foregroundColor(.accentColor)
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                }
                .background(Color.gray.opacity(0.02))
                .cornerRadius(4)
            } else {
                // 显示模式：显示提示文本
                HStack(spacing: 6) {
                    Text("添加一步...")
                        .font(.system(size: 15))
                        .foregroundColor(.secondary.opacity(0.6))
                        .frame(maxWidth: .infinity, alignment: .leading)

                    Spacer()
                }
                .padding(.vertical, 8)
                .padding(.leading, 8) // 与NodeView中文本容器的左边距保持一致
                .padding(.trailing, 8)
                .background(Color.gray.opacity(0.02))
                .cornerRadius(4)
                .contentShape(Rectangle())
                .onTapGesture {
                    print("[FOCUS_DEBUG] 用户点击添加一步按钮，parentNode: \(parentNode.id)")
                    startAddingNode(to: parentNode)
                }
            }
        }
        .padding(.leading, 56) // 统一：所有添加输入框都与主任务文本左对齐（56像素）
        .padding(.trailing, 22)
        .padding(.top, 4) // 减少上方间距
        .padding(.bottom, 6) // 减少下方间距
        .onChange(of: focusManager.focusPath) { oldValue, newValue in
            print("[FOCUS_DEBUG] NodeListView focusPath 变化：\(oldValue) -> \(newValue)")
            print("[FOCUS_DEBUG] 当前 addingToNodeId: \(String(describing: addingToNodeId))")
            print("[FOCUS_DEBUG] 当前 isAddingFocused: \(isAddingFocused)")
        }
    }

    // MARK: - 节点编辑方法

    /// 开始编辑节点
    private func startEditingNode(_ node: Node) {
        // 只能编辑未完成的节点
        guard !node.isCompleted else { return }

        editingNodeId = node.id
        editingText = node.title
        // 焦点将在NodeView中设置

        // 触感反馈
        UIImpactFeedbackGenerator(style: .light).impactOccurred()
    }

    /// 保存节点编辑
    private func saveNodeEdit(_ node: Node) {
        let newTitle = editingText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !newTitle.isEmpty else {
            cancelNodeEdit()
            return
        }

        // 根据节点类型调用相应的更新方法
        if let task = node as? Task {
            viewModel.updateTaskTitle(task.id, newTitle: newTitle)
        } else if let checklistItem = node as? ChecklistItem {
            viewModel.updateChecklistItemTitle(checklistItem.id, newTitle: newTitle)
        } else if let subStep = node as? SubStep {
            viewModel.updateSubStepTitle(subStep.id, newTitle: newTitle)
        }

        // 清除编辑状态
        editingNodeId = nil
        editingText = ""
        isEditingFocused = false
    }

    /// 取消节点编辑
    private func cancelNodeEdit() {
        editingNodeId = nil
        editingText = ""
        isEditingFocused = false
    }

    /// 删除节点
    private func deleteNode(_ node: Node) {
        // 根据节点类型调用相应的删除方法
        if let task = node as? Task {
            viewModel.deleteTask(task.id)
        } else if let checklistItem = node as? ChecklistItem {
            viewModel.deleteChecklistItem(checklistItem.id)
        } else if let subStep = node as? SubStep {
            viewModel.deleteSubStep(subStep.id)
        }

        // 如果正在编辑这个节点，取消编辑状态
        if editingNodeId == node.id {
            cancelNodeEdit()
        }
    }

    // MARK: - 添加子节点方法

    /// 开始添加子节点
    private func startAddingNode(to parentNode: Node) {
        print("[FOCUS_DEBUG] startAddingNode 开始，parentNode: \(parentNode.id)")
        print("[FOCUS_DEBUG] 当前 addingToNodeId: \(String(describing: addingToNodeId))")
        print("[FOCUS_DEBUG] 当前 isAddingFocused: \(isAddingFocused)")

        addingToNodeId = parentNode.id
        newNodeTitle = ""

        print("[FOCUS_DEBUG] 设置 addingToNodeId = \(parentNode.id)")
        print("[FOCUS_DEBUG] 准备延迟设置焦点...")

        // 确保焦点设置在渲染后执行，增加小延迟确保视图完全稳定
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            print("[FOCUS_DEBUG] 延迟执行：设置 isAddingFocused = true")
            isAddingFocused = true
            print("[FOCUS_DEBUG] 焦点设置完成，isAddingFocused: \(isAddingFocused)")
        }

        // 触感反馈
        UIImpactFeedbackGenerator(style: .light).impactOccurred()
    }

    /// 保存新子节点
    private func saveNewNode(to parentNode: Node) {
        let title = newNodeTitle.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !title.isEmpty else {
            cancelAddingNode()
            return
        }

        // 根据父节点类型添加相应的子节点
        if let task = parentNode as? Task {
            // 添加小行动
            viewModel.addChecklistItem(to: task.id, title: title)
        } else if let checklistItem = parentNode as? ChecklistItem {
            // 添加子步骤
            viewModel.addSubStep(to: checklistItem.id, title: title)
        } else if let subStep = parentNode as? SubStep {
            // 添加子子步骤
            viewModel.addSubStep(to: subStep.id, title: title)
        }

        // 清空输入框并保持添加状态，以便连续添加
        newNodeTitle = ""

        // 触感反馈
        UIImpactFeedbackGenerator(style: .light).impactOccurred()

        // 延迟重新聚焦
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            isAddingFocused = true
        }
    }

    /// 取消添加子节点
    private func cancelAddingNode() {
        print("[FOCUS_DEBUG] cancelAddingNode 开始")
        print("[FOCUS_DEBUG] 当前 addingToNodeId: \(String(describing: addingToNodeId))")
        print("[FOCUS_DEBUG] 当前 isAddingFocused: \(isAddingFocused)")

        addingToNodeId = nil
        newNodeTitle = ""
        isAddingFocused = false

        print("[FOCUS_DEBUG] cancelAddingNode 完成，isAddingFocused: \(isAddingFocused)")
    }

    // MARK: - 辅助方法

    /// 是否应该显示子节点
    private func shouldShowChildren(for node: Node) -> Bool {
        // 如果超过最大深度，不显示
        guard currentDepth < maxDepth else { return false }

        switch context {
        case .taskList:
            // 任务列表模式：必须有可显示的子节点（未完成或正在动画的已完成子节点）且用户手动展开
            let hasVisibleChildren = node.children.contains { child in
                !child.isCompleted || focusManager.isNodeAnimating(child.id)
            }
            return hasVisibleChildren && focusManager.isNodeExpanded(node.id)

        case .focusMode:
            // 一步模式：默认展开（即使没有子节点也要显示添加区域），点击可以收缩/展开
            return focusManager.isNodeExpanded(node.id)
        }
    }

    /// 获取节点样式
    private func getNodeStyle(for node: Node) -> NodeStyle {
        // 使用样式计算器
        let calculatedStyle = NodeStyleCalculator.calculateStyle(
            nodeId: node.id,
            focusPath: focusManager.focusPath,
            context: context
        )

        // 确保聚焦的根节点始终是primary
        if context == .focusMode && currentDepth == 0 {
            return .primary
        }

        // 根据深度微调
        switch currentDepth {
        case 0:
            return calculatedStyle
        case 1:
            return calculatedStyle == .primary ? .secondary : calculatedStyle
        default:
            return .tertiary
        }
    }

    /// 计算节点的透明度
    private func getNodeOpacity(for node: Node) -> Double {
        // 只在任务列表模式下且在顶层任务时应用透明度效果
        guard context == .taskList && currentDepth == 0 else {
            return 1.0
        }

        // 如果没有任务展开，所有任务都不透明
        if focusManager.expandedNodes.isEmpty {
            return 1.0
        }

        // 如果当前任务是展开的，保持不透明
        if focusManager.isNodeExpanded(node.id) {
            return 1.0
        }

        // 其他任务变暗
        return 0.7
    }
}

// MARK: - 专用列表视图

/// 任务列表视图
struct TaskListView: View {
    let tasks: [Task]
    @ObservedObject var focusManager: FocusManager
    @ObservedObject var viewModel: FocusViewModel

    var body: some View {
        ScrollView {
            VStack(spacing: 0) {
                // 使用 NodeListView 来支持递归展开
                NodeListView(
                    nodes: tasks.map { $0 as Node },
                    context: .taskList,
                    focusManager: focusManager,
                    viewModel: viewModel
                )
                .opacity(getListOpacity())
                .animation(.spring(response: 0.3, dampingFraction: 0.8), value: focusManager.expandedNodes)
            }
            .padding(.bottom, 100) // 为底部按钮留出空间
        }
        .scrollIndicators(.hidden)
        .keyboardAdaptive()
    }

    /// 计算列表的透明度（应用到每个任务的包装视图）
    private func getListOpacity() -> Double {
        return 1.0 // 透明度将在NodeListView内部的单个任务上应用
    }
}

/// 聚焦节点树视图
struct FocusedNodeTreeView: View {
    @ObservedObject var focusManager: FocusManager
    @ObservedObject var viewModel: FocusViewModel

    private var currentNodeId: UUID? {
        focusManager.focusPath.last  // 显示当前聚焦的节点
    }

    var body: some View {
        ScrollView {
            VStack(spacing: 0) {
                if let currentNodeId = currentNodeId,
                   let currentNode = findRootNode(currentNodeId) {
                    NodeListView(
                        nodes: [currentNode],
                        context: focusManager.getCurrentDisplayContext(),
                        focusManager: focusManager,
                        viewModel: viewModel
                    )
                    .padding(.bottom, 100) // 为底部按钮留出空间
                } else {
                    EmptyStateView()
                }
            }
        }
        .scrollIndicators(.hidden)
        .keyboardAdaptive()
        .onAppear {

        }
        .onChange(of: focusManager.focusPath) { oldValue, newValue in
            print("[FOCUS_DEBUG] focusPath 变化：\(oldValue) -> \(newValue)")
        }
    }

    private func findRootNode(_ nodeId: UUID) -> Node? {
        // 从ViewModel的任务中查找根节点
        let allTasks = viewModel.doingTasks

        for task in allTasks {
            if task.id == nodeId {
                return task
            }

            // 在小行动中查找
            if let checklist = task.checklist {
                for item in checklist {
                    if item.id == nodeId {
                        return item
                    }

                    // 在子步骤中查找
                    if let found = findNodeInSubSteps(nodeId, in: item.subStepsList) {
                        return found
                    }
                }
            }
        }

        return nil
    }

    private func findNodeInSubSteps(_ nodeId: UUID, in steps: [SubStep]) -> Node? {
        for step in steps {
            if step.id == nodeId {
                return step
            }

            if let found = findNodeInSubSteps(nodeId, in: step.subSteps) {
                return found
            }
        }
        return nil
    }
}

/// 空状态视图
struct EmptyStateView: View {
    var body: some View {
        ScrollView {
            VStack(spacing: 16) {
                Image(systemName: "checkmark.circle.badge.questionmark")
                    .font(.system(size: 48))
                    .foregroundColor(.gray.opacity(0.6))

                Text("没有一步任务")
                    .font(.title3)
                    .fontWeight(.medium)
                    .foregroundColor(.gray)

                Text("点击右上角的 + 开始添加任务")
                    .font(.body)
                    .foregroundColor(.gray.opacity(0.8))
                    .multilineTextAlignment(.center)
            }
            .padding(.top, 80)
            .padding(.bottom, 100) // 为底部按钮留出空间
            .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
        .scrollIndicators(.hidden)
        .keyboardAdaptive()
    }
}


