import SwiftUI

// MARK: - 烟花效果管理协议
protocol ConfettiManaging {
    func trigger(at position: CGPoint?, isLarge: Bool)
    func hide()
}

/// 烟花效果管理器 - 单例
class ConfettiManager: ObservableObject, ConfettiManaging {
    static let shared = ConfettiManager()
    
    // 发布状态
    @Published var triggerCount: Int = 0  // 新增：触发计数器
    @Published var position: CGPoint = .zero
    @Published var isLarge: Bool = true
    @Published var isVisible: Bool = false // 保留此属性以兼容现有代码
    
    private init() {}
    
    /// 触发烟花效果
    func trigger(at position: CGPoint? = nil, isLarge: Bool = true) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            // 设置大小烟花标志
            self.isLarge = isLarge
            
            // 设置位置，默认为屏幕中心
            if let position = position {
                self.position = position
            } else {
                let screenSize = UIScreen.main.bounds.size
                self.position = CGPoint(x: screenSize.width / 2, y: screenSize.height / 2)
            }
            
            // 显示烟花
            self.isVisible = true
            
            // 增加触发计数
            self.triggerCount += 1
            
            // 3秒后自动隐藏
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                self.hide()
            }
        }
    }
    
    /// 隐藏烟花效果
    func hide() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            withAnimation {
                self.isVisible = false
            }
        }
    }
    
    // 添加新的粒子
    func addParticle() {
        // 获取屏幕尺寸
        _ = UIScreen.main.bounds.width
        _ = UIScreen.main.bounds.height
        
        // 创建新粒子
        // ... existing code ...
    }
}

// MARK: - 视图扩展
extension View {
    /// 添加全局烟花效果支持
    func withConfetti() -> some View {
        self.modifier(ConfettiViewModifier())
    }
}

// MARK: - 烟花视图修饰器
struct ConfettiViewModifier: ViewModifier {
    @StateObject private var confettiManager = ConfettiManager.shared
    
    func body(content: Content) -> some View {
        ZStack {
            content
            
            if confettiManager.isVisible {
                ConfettiView(position: confettiManager.position)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .edgesIgnoringSafeArea(.all)
                    .allowsHitTesting(false)
                    .transition(.opacity)
                    .zIndex(999) // 确保在最顶层
            }
        }
    }
}

struct ConfettiView: View {
    @State private var particles = [Particle]()
    var position: CGPoint
    @ObservedObject private var confettiManager = ConfettiManager.shared
    
    init(position: CGPoint = CGPoint(x: UIScreen.main.bounds.width / 2, 
                                   y: UIScreen.main.bounds.height / 2)) {
        self.position = position
    }
    
    var body: some View {
        ZStack {
            ForEach(particles) { particle in
                Circle()
                    .fill(particle.color)
                    .frame(width: particle.size, height: particle.size)
                    .position(particle.position)
                    .opacity(particle.opacity)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .allowsHitTesting(false) // 防止干扰其他交互
        .onAppear {
            generateParticles()
        }
        .onChange(of: confettiManager.triggerCount) { _, _ in
            generateParticles()
        }
    }
    
    private func generateParticles() {
        let colors: [Color] = [
            AppColors.UI.error(for: .light),   // 红色
            AppColors.UI.blue(for: .light),    // 蓝色
            AppColors.UI.success(for: .light), // 绿色
            AppColors.UI.yellow(for: .light),  // 黄色
            AppColors.UI.pink(for: .light),    // 粉色
            AppColors.UI.special(for: .light), // 紫色
            AppColors.UI.warning(for: .light)  // 橙色
        ]
        var newParticles = [Particle]()
        
        // 根据烟花大小调整粒子数量和范围
        let particleCount = confettiManager.isLarge ? 200 : 100
        let horizontalRange = confettiManager.isLarge ? CGFloat(100) : CGFloat(50)
        let verticalRange = confettiManager.isLarge ? CGFloat(80) : CGFloat(40)
        let sizeRange = confettiManager.isLarge ? (CGFloat(3)...CGFloat(8)) : (CGFloat(2)...CGFloat(5))
        
        for _ in 0..<particleCount {
            // 初始位置集中在指定位置周围
            let centerX = confettiManager.position.x
            let centerY = confettiManager.position.y
            let randomX = centerX + CGFloat.random(in: -horizontalRange...horizontalRange)
            let randomY = centerY + CGFloat.random(in: -verticalRange...verticalRange)
            
            let randomSize = CGFloat.random(in: sizeRange)
            let randomColor = colors.randomElement() ?? AppColors.UI.blue(for: .light)
            let randomOpacity = Double.random(in: 0.7...1.0)
            
            let particle = Particle(id: UUID(), position: CGPoint(x: randomX, y: randomY), color: randomColor, size: randomSize, opacity: randomOpacity)
            newParticles.append(particle)
        }
        
        self.particles = newParticles
        
        // 添加动画
        for i in 0..<particles.count {
            let index = i
            DispatchQueue.main.asyncAfter(deadline: .now() + Double.random(in: 0...0.3)) {
                withAnimation(.easeOut(duration: Double.random(in: 0.8...2.5))) {
                    let screenWidth = UIScreen.main.bounds.width
                    let screenHeight = UIScreen.main.bounds.height
                    
                    // 根据烟花大小调整散开距离
                    let minDistance = confettiManager.isLarge ? CGFloat(150) : CGFloat(80)
                    let maxDistance = confettiManager.isLarge ? max(screenWidth, screenHeight) : CGFloat(150)
                    
                    let angle = Double.random(in: 0...(2 * .pi))
                    let distance = CGFloat.random(in: minDistance...maxDistance)
                    let newX = confettiManager.position.x + cos(angle) * distance
                    let newY = confettiManager.position.y + sin(angle) * distance
                    
                    particles[index].position = CGPoint(x: newX, y: newY)
                    particles[index].opacity = Double.random(in: 0...0.3)
                    
                    // 根据烟花大小调整最终粒子大小
                    if Bool.random() {
                        particles[index].size = CGFloat.random(in: confettiManager.isLarge ? CGFloat(1)...CGFloat(3) : CGFloat(0.5)...CGFloat(2))
                    } else {
                        particles[index].size = CGFloat.random(in: confettiManager.isLarge ? CGFloat(4)...CGFloat(6) : CGFloat(2)...CGFloat(4))
                    }
                }
            }
        }
    }
}

struct Particle: Identifiable {
    var id: UUID
    var position: CGPoint
    var color: Color
    var size: CGFloat
    var opacity: Double
}

// MARK: - 预览
struct ConfettiView_Previews: PreviewProvider {
    static var previews: some View {
        ConfettiView(position: CGPoint(x: 150, y: 150))
            .frame(width: 300, height: 300)
            .background(Color.black.opacity(0.1))
    }
}
