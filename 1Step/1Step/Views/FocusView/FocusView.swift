import SwiftUI
import SwiftData
import Combine
import SwipeActions

// Define the new navigation target for BrowseView
struct FocusBrowseNavigationTarget: Hashable, Codable { // Make it Codable for NavigationPath
    // No properties needed, its type is its identity
}

// 用于发布键盘事件的通知管理类
class KeyboardObserver: ObservableObject {
    @Published var isKeyboardVisible = false
    
    var cancellables = Set<AnyCancellable>()
    
    init() {
        let showPublisher = NotificationCenter.default.publisher(for: UIResponder.keyboardWillShowNotification)
            .map { _ in true }
        
        let hidePublisher = NotificationCenter.default.publisher(for: UIResponder.keyboardWillHideNotification)
            .map { _ in false }
            
        // 添加Did通知监听
        let didShowPublisher = NotificationCenter.default.publisher(for: UIResponder.keyboardDidShowNotification)
            .map { _ in true }
        
        let didHidePublisher = NotificationCenter.default.publisher(for: UIResponder.keyboardDidHideNotification)
            .map { _ in false }
        
        // 合并所有发布者
        Publishers.MergeMany([showPublisher, hidePublisher, didShowPublisher, didHidePublisher])
            .receive(on: RunLoop.main)
            .assign(to: \.isKeyboardVisible, on: self)
            .store(in: &cancellables)
    }
}

struct FocusView: View {
    // MARK: - 依赖
    let taskManager: TaskManager
    
    // MARK: - 状态管理
    @StateObject internal var viewModel: FocusViewModel
    @State var showingClearConfirmation = false
    @State var showingTaskSelector = false
    @State var showingGuideSheet = false
    @State var editMode = EditMode.inactive
    // 这些状态现在由FocusManager统一管理
    // @State var expandedTaskId: UUID? = nil  // -> focusManager.expandedNodes
    // @State var expandedStepIds: Set<UUID> = []  // -> focusManager.expandedNodes
    // @State var newSubStepTitle: String = ""  // -> focusManager.newItemText
    // @State var editingStepId: UUID? = nil  // -> focusManager.editingNodeId
    // @State var editingText: String = ""  // -> 在NodeView中本地管理
    @StateObject var formCoordinator = TaskFormCoordinator.shared
    @StateObject var taskFormViewModel: TaskFormViewModel
    @Environment(\.toastManager) var toastManager
    @Environment(\.confettiManager) internal var confettiManager
    @Environment(\.colorScheme) var colorScheme
    @FocusState var isEditingFocused: Bool
    @FocusState var isAddingSubStepFocused: Bool
    
    // 休息模式状态
    @State var isRestModeActive = false
    
    // 键盘观察者
    @StateObject var keyboardObserver = KeyboardObserver()
    
    // 新增状态
    @State var isNavBarHiddenForEchoDrop = false
    @State var isNavBarHiddenForFocus = false
    @State internal var showingTaskDetailFromBreadcrumb = false
    
    var browseCoordinatorInstance: BrowseCoordinator { 
        NavigationStateManager.shared.browseCoordinator
    }
    
    @State private var focusNavigationPath = NavigationPath()

    // MARK: - 初始化方法
    init(
        taskManager: TaskManager = DependencyContainer.taskManager()
    ) {
        self.taskManager = taskManager
        
        _viewModel = StateObject(wrappedValue: FocusViewModel(
            taskManager: taskManager
        ))
        
        // 初始化 taskFormViewModel，并传入 "doing" 作为初始状态
        _taskFormViewModel = StateObject(wrappedValue: TaskFormViewModel(
            initialStatus: TaskStatus.doing.rawValue, // FocusView 中新建任务默认为 "doing"
            taskManager: taskManager
        ))
    }
    
    var body: some View {
        NavigationStack(path: $focusNavigationPath) { 
            mainContentView
                .navigationBarTitleDisplayMode(.inline)
                .navigationBarBackButtonHidden(viewModel.focusManager.isInActionFocusMode)
            .onAppear {
                viewModel.loadTasks()
            }
            .sheet(isPresented: $showingGuideSheet) {
                GuideView()
            }
            .sheet(isPresented: $showingTaskSelector) {
                FocusTaskSelectorView(currentDoingCount: viewModel.doingTasks.count) {
                    viewModel.loadTasks()
                }
            }
            .alert("清空一步", isPresented: $showingClearConfirmation) {
                Button("取消", role: .cancel) { }
                Button("确定", role: .destructive) {
                    // viewModel.clearAllDoingTasks() // TODO: Re-evaluate this function in new architecture
                }
            } message: {
                Text("确定要将所有行动移回下一步吗？")
            }
            .toolbar {
                    toolbarContent
                }
                .navigationDestination(for: FocusBrowseNavigationTarget.self) { _ in
                    BrowseViewContainer(coordinator: self.browseCoordinatorInstance)
                }
                .navigationDestination(for: SearchNavigationTarget.self) { _ in
                    SearchView()
                }
                .toolbarBackground(Material.regular, for: .navigationBar)
                .sheet(isPresented: $showingTaskDetailFromBreadcrumb) {
                    taskDetailSheet
                }
        }
        .onChange(of: viewModel.showingEchoDrop) { oldValue, newValue in
            if !newValue && oldValue {
                withAnimation {
                    isNavBarHiddenForEchoDrop = false
                }
            }
        }
        .onChange(of: isRestModeActive) { _, newValue in
            if !newValue && !viewModel.showingEchoDrop {
                isNavBarHiddenForEchoDrop = false
            }
        }
        .onChange(of: viewModel.focusManager.isFocused) { _, newValue in
            withAnimation {
                isNavBarHiddenForFocus = newValue
            }
        }
    }
    
    // MARK: - 主要内容视图
    private var mainContentView: some View {
        ZStack {
            Color(.systemBackground).edgesIgnoringSafeArea(.all)
            
            VStack(spacing: 0) {
                contentBasedOnMode
                    .edgesIgnoringSafeArea(.bottom)
                    .animation(.easeInOut(duration: 0.25), value: viewModel.focusManager.isFocused)
            }
            .opacity(isRestModeActive ? 0 : 1)
            
            overlayViews
        }
        .animation(.easeInOut(duration: 0.2), value: keyboardObserver.isKeyboardVisible)
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: viewModel.focusManager.expandedNodes)
        .animation(.easeInOut(duration: 0.5), value: isRestModeActive)
        .animation(.easeInOut(duration: 0.3), value: isNavBarHiddenForFocus)
        .onReceive(NotificationCenter.default.publisher(for: .taskAdded)) { _ in
            // 收到任务添加通知后刷新任务列表
            viewModel.loadTasks()
        }
    }
    
    // MARK: - 根据模式显示内容
    @ViewBuilder
    private var contentBasedOnMode: some View {
        if viewModel.focusManager.isFocused {
            // 聚焦模式：显示面包屑 + 节点树
            VStack(spacing: 0) {
                // 面包屑导航
                BreadcrumbView(
                    focusManager: viewModel.focusManager,
                    viewModel: viewModel,
                    showingTaskDetailFromBreadcrumb: $showingTaskDetailFromBreadcrumb
                )
                
                // 节点树内容
                FocusedNodeTreeView(
                    focusManager: viewModel.focusManager,
                    viewModel: viewModel
                )
                .id(viewModel.focusManager.focusPath) // 确保视图在focusPath变化时重新渲染
            }
            .transition(.asymmetric(
                insertion: .opacity.combined(with: .scale(scale: 0.95)),
                removal: .opacity
            ))
        } else if viewModel.doingTasks.isEmpty {
            // 空状态 - 只有在非聚焦模式且真的没有任务时才显示
            emptyStateView
                .transition(.opacity)
        } else {
            // 列表模式：显示所有任务
            TaskListView(
                tasks: viewModel.doingTasks,
                focusManager: viewModel.focusManager,
                viewModel: viewModel
            )
            .transition(.asymmetric(
                insertion: .opacity.combined(with: .scale(scale: 0.95)),
                removal: .opacity
            ))
        }
    }
    
    // MARK: - 浮层视图
    @ViewBuilder
    private var overlayViews: some View {
        // 底部按钮
        if !(keyboardObserver.isKeyboardVisible && !viewModel.focusManager.expandedNodes.isEmpty) && !isRestModeActive && !formCoordinator.isShowing {
            bottomButtonsView
                .transition(.opacity.combined(with: .move(edge: .bottom)))
                .animation(.easeInOut(duration: 0.3), value: keyboardObserver.isKeyboardVisible || !viewModel.focusManager.expandedNodes.isEmpty || formCoordinator.isShowing)
        }
        
        // 回声视图
        if viewModel.showingEchoDrop && !isRestModeActive {
            FocusEchoDropView(viewModel: viewModel)
        }
        
        // 任务表单浮层
        if formCoordinator.isShowing && !isRestModeActive {
            taskFormOverlay
        }
        
        // 休息模式视图
        if isRestModeActive {
            RestModeView(isActive: $isRestModeActive)
                .transition(.opacity)
        }
    }
    
    // MARK: - 工具栏内容
    @ToolbarContentBuilder
    private var toolbarContent: some ToolbarContent {
        ToolbarItem(placement: .navigationBarLeading) {
            if !isRestModeActive && !isNavBarHiddenForEchoDrop && !isNavBarHiddenForFocus {
                Button {
                    focusNavigationPath.append(SearchNavigationTarget())
                } label: {
                    Image("search")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 22, height: 22)
                        .foregroundColor(Color.primary)
                }
            }
        }
        
        ToolbarItem(placement: .principal) {
            Text("1Step")
                .font(.headline)
                .foregroundColor(Color.primary)
                .onTapGesture(count: 2) {
                    withAnimation(.easeInOut(duration: 0.5)) {
                        isRestModeActive = true
                        toastManager.showSuperLightInfo("欢迎来到 宁静一隅")
                    }
                }
        }
        
        ToolbarItem(placement: .navigationBarTrailing) {
            if !isRestModeActive && !isNavBarHiddenForEchoDrop && !isNavBarHiddenForFocus {
                Button {
                    focusNavigationPath.append(FocusBrowseNavigationTarget())
                } label: {
                    Image(systemName: "square.grid.2x2")
                        .foregroundColor(Color.primary)
                }
            }
        }
    }
    
    // MARK: - 任务详情弹窗
    @ViewBuilder
    private var taskDetailSheet: some View {
        Group {
            if let focusedTask = viewModel.getFocusedTask() {
                TaskDetailView(
                    task: focusedTask,
                    sourcePageType: .focus, // 来自一步模式的主任务（点击大标题）
                    currentPathData: createCurrentPathData(),
                    onDeleteIntent: { taskToDelete in
                        viewModel.handleTaskDelete(taskToDelete)
                        showingTaskDetailFromBreadcrumb = false // 关闭自身
                    },
                    onRestoreActionFocus: { taskId, checklistItemId, subStepPath in
                        // 关闭任务详情弹窗
                        showingTaskDetailFromBreadcrumb = false
                        
                        // 延迟一帧确保弹窗关闭后再恢复焦点
                        DispatchQueue.main.async {
                            viewModel.restoreActionFocusState(
                                taskId: taskId,
                                checklistItemId: checklistItemId,
                                subStepPath: subStepPath
                            )
                        }
                    }
                )
                // Add onDismiss to handle actions previously in onComplete/onChangeStatus
                .onAppear {
                    // Optional: Any setup needed when TaskDetailView appears
                }
                // We might need to trigger a refresh or specific logic when the sheet is dismissed
                // For now, let's assume TaskDetailView handles its own state changes and FocusView will react to data changes.
            } else {
                Text("无法加载任务详情")
            }
        }
    }
    
    /// 创建当前路径数据
    private func createCurrentPathData() -> CurrentPathData? {
        return PathDataHelper.createCurrentPathData(with: viewModel.focusManager) { level in
            _Concurrency.Task { @MainActor in
                PathDataHelper.navigateToTaskDetailLevel(
                    level,
                    focusManager: self.viewModel.focusManager,
                    dismissAction: { self.showingTaskDetailFromBreadcrumb = false }
                )
            }
        }
    }
    
    // MARK: - 底部按钮视图
    var bottomButtonsView: some View {
        BottomButtonsView(
            showingGuideSheet: $showingGuideSheet, // Pass FocusView's state
            showingTaskSelector: $showingTaskSelector,
            showingClearConfirmation: $showingClearConfirmation,
            editMode: $editMode, // Pass FocusView's state
            doingTasksCount: viewModel.doingTasks.count, // Get from viewModel
            isFocusMode: viewModel.focusManager.isFocused, // Get from focusManager
            onExitFocus: viewModel.exitFocus, // Pass viewModel method
            onShowEchoDrop: viewModel.showEchoDrop // Pass viewModel method
        )
    }
    
    // MARK: - 任务表单浮层
    private var taskFormOverlay: some View {
        AddTaskFormView(
            viewModel: taskFormViewModel
        )
        .id(formCoordinator.formId) // 重要：确保表单在重用时能正确重置
    }
    
    // MARK: - 子步骤添加功能迁移到NodeView中统一处理
    // 这些方法将在NodeView中重新实现
    
    private var emptyStateView: some View {
        GeometryReader { geometry in
            ZStack {
                // 主内容区域 - 考虑导航栏的实际可视区域
                VStack(spacing: 0) {
                    // 顶部空间 - 减少空间让内容更往上，使文字居中
                    Spacer()
                        .frame(height: max(40, geometry.size.height * 0.05))
                    
                    // 主视觉内容
                    VStack(spacing: 20) {
                        // 气球图片 - 适当放大
                        Image("empty")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 300, height: 300)
                        
                        // 文字内容区域 - 平静的状态描述
                        VStack(spacing: 10) {
                            Text("没有行动")
                                .font(.system(size: 17, weight: .regular))
                                .foregroundColor(AppColors.UI.secondaryText(for: colorScheme))
                            
                            Text("享受这份宁静")
                                .font(.system(size: 15, weight: .regular))
                                .foregroundColor(AppColors.UI.secondaryText(for: colorScheme))
                                .opacity(0.7)
                        }
                    }
                    
                    // 底部弹性空间
                    Spacer()
                }
                .frame(maxWidth: .infinity)
                
                // 引导内容 - 作为overlay不影响主内容位置
                if !formCoordinator.isShowing && !keyboardObserver.isKeyboardVisible {
                    VStack {
                        Spacer()
                        
                        VStack(spacing: 10) {
                            HStack(spacing: 6) {
                                Image(systemName: "plus.circle")
                                    .font(.system(size: 14, weight: .medium))
                                    .foregroundColor(AppColors.UI.accent(for: colorScheme))
                                    .opacity(0.5)
                                
                                Text("轻点下方按钮开始行动")
                                    .font(.system(size: 13, weight: .regular))
                                    .foregroundColor(AppColors.UI.secondaryText(for: colorScheme))
                                    .opacity(0.5)
                            }
                            
                            HStack(spacing: 6) {
                                Image(systemName: "square.grid.2x2")
                                    .font(.system(size: 12, weight: .medium))
                                    .foregroundColor(AppColors.UI.secondaryText(for: colorScheme))
                                    .opacity(0.3)
                                
                                Text("或去下一步看看")
                                    .font(.system(size: 12, weight: .regular))
                                    .foregroundColor(AppColors.UI.secondaryText(for: colorScheme))
                                    .opacity(0.3)
                            }
                        }
                        .padding(.bottom, 100) // 为底部按钮留出空间
                    }
                    .transition(.opacity)
                }
            }
            .frame(height: geometry.size.height)
        }
        .animation(.easeInOut(duration: 0.3), value: formCoordinator.isShowing)
        .animation(.easeInOut(duration: 0.2), value: keyboardObserver.isKeyboardVisible)
    }
}

// MARK: - 拖放代理
struct TaskDropDelegate: DropDelegate {
    let item: Task
    let items: [Task]
    @Binding var draggedItem: Task?
    let viewModel: FocusViewModel
    
    func performDrop(info: DropInfo) -> Bool {
        draggedItem = nil
        return true
    }
    
    func dropEntered(info: DropInfo) {
        guard let draggedItem = draggedItem else { return }
        viewModel.handleTaskDrop(draggedTask: draggedItem, toTask: item)
    }
}

// MARK: - 预览
struct FocusView_Previews: PreviewProvider {
    static var previews: some View {
        FocusView()
    }
}