import SwiftUI

struct BottomButtonsView: View {
    // 使用绑定来访问主视图的状态变量
    @Binding var showingGuideSheet: <PERSON><PERSON>
    @Binding var showingTaskSelector: <PERSON><PERSON>
    @Binding var showingClearConfirmation: Bool
    @Binding var editMode: EditMode
    var doingTasksCount: Int
    @Environment(\.colorScheme) private var colorScheme
    
    // 添加聚焦模式相关属性
    var isFocusMode: Bool = false
    var onExitFocus: (() -> Void)? = nil
    // 添加回声回调
    var onShowEchoDrop: (() -> Void)? = nil
    
    // 添加状态变量控制按钮旋转
    @State private var buttonRotation: Double = 0
    @State private var buttonHovered: Bool = false
    
    var body: some View {
        VStack {
            Spacer()
            HStack {
              
                
                Spacer()
                
                // 根据聚焦模式显示不同按钮
                if isFocusMode {
                    exitFocusButton
                        .padding(.trailing, 20)
                        .padding(.bottom, 20)
                } else {
                    magicButtonMenu
                        .padding(.trailing, 20)
                        .padding(.bottom, 20)
                }
            }
        }
    }
    
    // 退出聚焦按钮
    var exitFocusButton: some View {
        Button(action: {
            onExitFocus?()
            
            // 添加按钮点击动画
            withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                buttonRotation += 360
            }
        }) {
            Image(systemName: "arrow.left")
                .font(.system(size: 20))
                .foregroundColor(.white)
                .padding(14)
                .background(AppColors.UI.primary(for: colorScheme))
                .clipShape(Circle())
                .shadow(radius: 3)
                .rotationEffect(Angle(degrees: buttonRotation))
                .animation(.spring(response: 0.3, dampingFraction: 0.6), value: buttonRotation)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // 魔法按钮菜单
    var magicButtonMenu: some View {
        ZStack {
            Image(systemName: "plus")  // 使用加号图标
                .font(.system(size: 20))
                .foregroundColor(.white)
                .padding(14)
                .background(AppColors.UI.primary(for: colorScheme))
                .clipShape(Circle())
                .shadow(radius: 3)
                .rotationEffect(Angle(degrees: buttonRotation))
                .animation(.spring(response: 0.3, dampingFraction: 0.6), value: buttonRotation)
        }
        .onLongPressGesture {
            HapticManager.shared.impact(style: .medium)
            onShowEchoDrop?()
        }
        .onTapGesture {
            // 使用表单协调器显示添加任务表单，而不是显示任务选择器
            TaskFormCoordinator.shared.showForm()
            
            // 保留原有的按钮旋转动画
            withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                buttonRotation += 360
            }
        }
    }
}

// MARK: - 预览
struct BottomButtonsView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // 正常模式预览
            BottomButtonsView(
                showingGuideSheet: .constant(false),
                showingTaskSelector: .constant(false),
                showingClearConfirmation: .constant(false),
                editMode: .constant(.inactive),
                doingTasksCount: 2
            )
            .previewDisplayName("正常模式")
            .previewLayout(.sizeThatFits)
            .padding()
            
            // 聚焦模式预览
            BottomButtonsView(
                showingGuideSheet: .constant(false),
                showingTaskSelector: .constant(false),
                showingClearConfirmation: .constant(false),
                editMode: .constant(.inactive),
                doingTasksCount: 2,
                isFocusMode: true,
                onExitFocus: {}
            )
            .previewDisplayName("聚焦模式")
            .previewLayout(.sizeThatFits)
            .padding()
        }
    }
}
