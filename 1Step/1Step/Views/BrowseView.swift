import SwiftUI
import SwiftData

// 无需单独导入子页面视图，它们都属于同一个模块

/// 浏览标签页的根视图
struct BrowseViewContainer: View {
    @StateObject private var coordinator: BrowseCoordinator
    // 从环境中获取依赖
    @Environment(\.taskRepository) private var taskRepository
    @Environment(\.projectRepository) private var projectRepository
    @Environment(\.tagRepository) private var tagRepository

    init(coordinator: BrowseCoordinator) {
        _coordinator = StateObject(wrappedValue: coordinator)
    }

    var body: some View {
        BrowseView(
            taskRepository: taskRepository,
            projectRepository: projectRepository,
            tagRepository: tagRepository
        )
        .withNavigation(coordinator: coordinator)
        .id("BrowseView_Container")
    }
}

// 主要的 BrowseView 结构体（保持不变，但移除 Coordinator 相关逻辑）
struct BrowseView: View {
    // MARK: - 依赖注入 (由容器传入)
    let taskRepository: TaskRepository
    let projectRepository: ProjectRepository
    let tagRepository: TagRepository

    // MARK: - 环境变量
    @Environment(\.colorScheme) private var colorScheme
    // 从环境中获取 Coordinator
    @EnvironmentObject private var coordinator: BrowseCoordinator

    // MARK: - 视图模型
    @StateObject private var viewModel: BrowseViewModel

    // MARK: - 状态变量
    @State private var showDeveloperMenu = false
    @State private var showDemoLoadConfirm = false
    
    // MARK: - 初始化方法 (简化)
    init(
        taskRepository: TaskRepository,
        projectRepository: ProjectRepository,
        tagRepository: TagRepository
    ) {
        self.taskRepository = taskRepository
        self.projectRepository = projectRepository
        self.tagRepository = tagRepository
        _viewModel = StateObject(wrappedValue: BrowseViewModel(
            taskManager: DependencyContainer.taskManager()
        ))
    }
    
    var body: some View {
        ScrollView {
            // Updated section for the new list layout
            VStack(spacing: 0) {
                ForEach(BrowseSection.allCases) { section in
                    NavigationLink(value: mapBrowseSectionToDestination(section)) {
                        HStack {
                            Image(systemName: section.systemImage)
                                .font(.system(size: 18, weight: .regular))
                                .frame(width: 24, alignment: .center) // Ensure icon alignment
                                .foregroundColor(section.getColor(for: colorScheme))
                            Text(section.title)
                                .foregroundColor(AppColors.UI.primaryText(for: colorScheme))
                            Spacer()
                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(AppColors.UI.secondaryText(for: colorScheme))
                         }
                        .padding(.vertical, 12)
                        .padding(.horizontal)
                        .contentShape(Rectangle())
                    }
                    .buttonStyle(.plain)
                    if section != BrowseSection.allCases.last {
                        Divider().padding(.leading)
                }
            }
            }
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .padding()
            
            VStack(spacing: 0) {                    
                NavigationLink(value: NavigationDestination.projectList) {
                    HStack {
                        Image(systemName: "folder")
                            .font(.system(size: 18, weight: .light))
                            .foregroundColor(AppColors.UI.primaryText(for: colorScheme))
                        Text("项目")
                            .foregroundColor(AppColors.UI.primaryText(for: colorScheme))
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(AppColors.UI.secondaryText(for: colorScheme))
                     }
                    .padding(.vertical, 12)
                    .padding(.horizontal)
                    .contentShape(Rectangle())
                }
                .buttonStyle(.plain)
                Divider()
                    .padding(.leading)
                
                NavigationLink(value: NavigationDestination.tagList) {
                    HStack {
                        Image(systemName: "tag")
                            .font(.system(size: 18, weight: .light))
                            .foregroundColor(AppColors.UI.primaryText(for: colorScheme))
                        Text("标签")
                            .foregroundColor(AppColors.UI.primaryText(for: colorScheme))
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(AppColors.UI.secondaryText(for: colorScheme))
                    }
                    .padding(.vertical, 12)
                    .padding(.horizontal)
                    .contentShape(Rectangle())
                }
                .buttonStyle(.plain)
                Divider()
                    .padding(.leading)
                
                NavigationLink(value: NavigationDestination.settingsPage) {
                    HStack {
                        Image(systemName: "gear")
                            .font(.system(size: 18, weight: .light))
                            .foregroundColor(AppColors.UI.primaryText(for: colorScheme))
                        Text("设置")
                            .foregroundColor(AppColors.UI.primaryText(for: colorScheme))
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(AppColors.UI.secondaryText(for: colorScheme))
                    }
                    .padding(.vertical, 12)
                    .padding(.horizontal)
                    .contentShape(Rectangle())
                }
                .buttonStyle(.plain)
            }
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .padding()
            
            // 社区与交流
            VStack(spacing: 0) {
                Button(action: {
                    // 打开反馈问卷
                    if let url = URL(string: "https://wj.qq.com/s2/19701989/08ba/") {
                        UIApplication.shared.open(url)
                    }
                }) {
                    HStack {
                        Image(systemName: "square.and.pencil")
                            .font(.system(size: 18, weight: .light))
                            .foregroundColor(AppColors.UI.primaryText(for: colorScheme))
                        Text("我要反馈")
                            .foregroundColor(AppColors.UI.primaryText(for: colorScheme))
                        Spacer()
                        Image(systemName: "arrow.up.right")
                            .font(.caption)
                            .foregroundColor(AppColors.UI.secondaryText(for: colorScheme))
                    }
                    .padding(.vertical, 12)
                    .padding(.horizontal)
                }
                Divider()
                    .padding(.leading)
                    
                Button(action: {
                    // 打开行动哲学页面
                    if let url = URL(string: "http://way.1step.run") {
                        UIApplication.shared.open(url)
                    }
                }) {
                    HStack {
                        Image(systemName: "lightbulb")
                            .font(.system(size: 18, weight: .light))
                            .foregroundColor(AppColors.UI.primaryText(for: colorScheme))
                        Text("1Step行动哲学")
                            .foregroundColor(AppColors.UI.primaryText(for: colorScheme))
                        Spacer()
                        Image(systemName: "arrow.up.right")
                            .font(.caption)
                            .foregroundColor(AppColors.UI.secondaryText(for: colorScheme))
                    }
                    .padding(.vertical, 12)
                    .padding(.horizontal)
                }
            }
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .padding(.horizontal)
            .padding(.bottom)
        }
        .navigationTitle("浏览")
        .background(Color(.systemGroupedBackground).ignoresSafeArea())
        .sheet(isPresented: $showDeveloperMenu) {
            DeveloperMenuView()
        }
        .alert("加载示例数据", isPresented: $showDemoLoadConfirm) {
            Button("取消", role: .cancel) { }
            Button("确认") {
                // 使用Repository加载示例数据
                taskRepository.loadDemoData(forceLoad: true)
            }
        } message: {
            Text("这将加载所有示例项目、标签和行动，但不会影响您现有的数据。是否继续？")
        }
    }
    
    // MARK: - 辅助方法
    
    /// 根据浏览分类获取目标视图
    private func mapBrowseSectionToDestination(_ section: BrowseSection) -> NavigationDestination {
        let destination: NavigationDestination
        switch section {
        case .inbox: destination = .inboxTasks
        case .nextAction: destination = .nextTasks
        case .waiting: destination = .waitingTasks
        case .smb: destination = .smbTasks
        case .done: destination = .doneTasks
        }
        return destination
    }
}

// MARK: - 扩展
extension BrowseView {
    // 浏览部分枚举
    enum BrowseSection: String, Identifiable, CaseIterable {
        case inbox = "收集箱"
        case nextAction = "下一步"
        case waiting = "等待中"
        case smb = "将来也许"
        case done = "已完成"
        
        var id: String { self.rawValue }
        
        // Reorder allCases to reflect the new sequence
        static var allCases: [BrowseSection] {
            return [.inbox, .nextAction, .waiting, .smb, .done]
        }
        
        var title: String {
            self.rawValue
        }
        
        // 返回对应的行动状态类型
        var taskStatus: TaskStatus {
            switch self {
            case .inbox: return .inbox
            case .nextAction: return .na
            case .waiting: return .waiting
            case .smb: return .smb
            case .done: return .done
            }
        }
        
        // 获取系统图标名称
        var systemImage: String {
            switch self {
            case .inbox: return "tray"
            case .nextAction: return "list.star"
            case .waiting: return "hourglass"
            case .smb: return "sparkles"
            case .done: return "checkmark.circle"
            }
        }
        
        // 根据给定的colorScheme获取颜色
        func getColor(for colorScheme: ColorScheme) -> Color {
            switch self {
            case .inbox: return AppColors.Status.inbox(for: colorScheme)
            case .nextAction: return AppColors.Status.nextAction(for: colorScheme)
            case .waiting: return AppColors.Status.waiting(for: colorScheme)
            case .smb: return AppColors.Status.someday(for: colorScheme)
            case .done: return AppColors.Status.done(for: colorScheme)
            }
        }
    }
    
    // 给列表分区的头部视图
    struct BrowseSectionHeader: View {
        let section: BrowseSection
        @Environment(\.colorScheme) private var colorScheme
        
        var body: some View {
            HStack {
                Circle()
                    .fill(section.getColor(for: colorScheme))
                    .frame(width: 10, height: 10)
                
                Text(section.title)
                     .font(.headline)
                    .foregroundColor(AppColors.UI.primaryText(for: colorScheme))
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding()
        }
    }
}

// Wrapper view for ProjectDetailView if it needs to fetch Project by ID
struct ProjectDetailViewWrapper: View {
    let projectID: UUID
    // Pass necessary repositories
    let taskRepository: TaskRepository
    let projectRepository: ProjectRepository
    let tagRepository: TagRepository

    // Access Coordinator from environment (optional if not needed directly here)
    // @EnvironmentObject var coordinator: BrowseCoordinator 
    @State private var project: Project?

    // Add the required body property
    var body: some View {
        if let project = project {
            ProjectDetailView(
                project: project,
                taskRepository: taskRepository,
                projectRepository: projectRepository,
                tagRepository: tagRepository
            )
            // Coordinator should be inherited from environment
        } else {
            // Show loading state and trigger fetch
            ProgressView("加载中...")
                .onAppear {
                    // Explicitly use Swift Concurrency Task
                    _Concurrency.Task { @MainActor in
                         self.project = projectRepository.getProjectById(projectID)
                        if self.project == nil {
                            print("[ProjectDetailViewWrapper] Warning: Project with ID \(projectID) not found.")
                        }
                    }
                }
        }
    }
}
