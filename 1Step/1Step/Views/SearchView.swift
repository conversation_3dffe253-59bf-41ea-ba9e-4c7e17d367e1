import SwiftUI
import Flow
/// 搜索导航目标标识符
struct SearchNavigationTarget: Hashable, Codable { 
    // 无需属性，类型即为其标识
}

/// 搜索视图 - 作为导航目标
struct SearchView: View {
    // MARK: - 依赖
    private let taskRepository: TaskRepository
    private let projectRepository: ProjectRepository
    private let tagRepository: TagRepository
    
    // MARK: - 状态
    @StateObject private var viewModel: SearchViewModel
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    @FocusState private var isSearchFieldFocused: Bool
    
    // MARK: - 初始化
    init(
        taskRepository: TaskRepository = DependencyContainer.taskRepository(),
        projectRepository: ProjectRepository = DependencyContainer.projectRepository(),
        tagRepository: TagRepository = DependencyContainer.tagRepository()
    ) {
        self.taskRepository = taskRepository
        self.projectRepository = projectRepository
        self.tagRepository = tagRepository
        
        _viewModel = StateObject(wrappedValue: SearchViewModel(
            taskManager: DependencyContainer.taskManager()
        ))
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 搜索栏区域（包含返回按钮和搜索框）
            HStack(spacing: 8) {
                // 返回按钮
                Button(action: {
                    dismiss()
                }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 20))
                        .foregroundColor(.blue)
                }
                
                // 搜索框 - 使用之前更好的样式
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)
                        .padding(.leading, 8)
                    
                    TextField("搜索行动、项目、备注...", text: $viewModel.searchText)
                        .focused($isSearchFieldFocused)
                        .textFieldStyle(PlainTextFieldStyle())
                        .padding(.vertical, 10)
                        .onChange(of: isSearchFieldFocused) { _, newValue in
                            viewModel.isSearchFocused = newValue
                        }
                        .onSubmit {
                            if !viewModel.searchText.isEmpty {
                                viewModel.addSearchHistory(viewModel.searchText)
                            }
                        }
                    
                    if !viewModel.searchText.isEmpty {
                        Button(action: { viewModel.searchText = "" }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.secondary)
                                .padding(.trailing, 8)
                        }
                    }
                }
                .background(
                    searchBarBackground
                )
                .cornerRadius(10)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            
            // 内容区域 - 根据状态显示不同内容
            if viewModel.isSearchFocused && viewModel.searchText.isEmpty {
                // 搜索历史 - 此处无需分割线
                VStack(alignment: .leading, spacing: 0) {
                    HStack {
                        Text("搜索历史")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        Button(action: {
                            viewModel.clearSearchHistory()
                        }) {
                            Image(systemName: "trash")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .padding(4)
                        }
                        .buttonStyle(.plain)
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 14)
                    .padding(.bottom, 8)
                    
                    if viewModel.searchHistory.isEmpty {
                        Text("暂无搜索历史")
                            .font(.system(size: 15))
                            .foregroundColor(.secondary)
                            .padding(.horizontal, 16)
                            .padding(.top, 8)
                    } else {
                        Flow(alignment: .topLeading, spacing: 8) {
                            ForEach(viewModel.searchHistory.prefix(12), id: \.self) { term in
                                Button(action: {
                                    viewModel.searchText = term
                                }) {
                                    Text(term)
                                        .font(.caption)
                                        .foregroundColor(Color(.label))
                                        .padding(.horizontal, 8)
                                        .padding(.vertical, 4)
                                        .background(Color(.systemGray5))
                                        .cornerRadius(6)
                                }
                                .buttonStyle(.plain)
                            }
                        }
                        .padding(.horizontal, 16)
                    }
                }
            } else if !viewModel.searchText.isEmpty {
                // 搜索结果
                VStack(spacing: 0) {
                    // 过滤器
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            NAFilterButton(filter: .all, selectedFilter: $viewModel.selectedFilter)
                            NAFilterButton(filter: .tasks, selectedFilter: $viewModel.selectedFilter)
                            NAFilterButton(filter: .projects, selectedFilter: $viewModel.selectedFilter)
                            NAFilterButton(filter: .tags, selectedFilter: $viewModel.selectedFilter)
                            NAFilterButton(filter: .notes, selectedFilter: $viewModel.selectedFilter)
                            NAFilterButton(filter: .smb, selectedFilter: $viewModel.selectedFilter)
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                    }
                    
                    Divider()
                    
                    // 搜索结果列表
                    if viewModel.searchResults.isEmpty {
                        VStack {
                            Spacer()
                            Text("没有找到匹配的结果")
                                .foregroundColor(.secondary)
                                .padding(.bottom, 100)
                            Spacer()
                        }
                    } else {
                        ScrollView {
                            LazyVStack(spacing: 12) {
                                ForEach(viewModel.searchResults) { task in
                                    TaskCardView(
                                        task: task,
                                        taskRepository: DependencyContainer.taskRepository(),
                                        projectRepository: DependencyContainer.projectRepository(),
                                        tagRepository: DependencyContainer.tagRepository(),
                                        style: .standard,
                                        onTaskIntent: { intent in
                                            viewModel.handleTaskIntent(intent)
                                        },
                                        onExitRequested: { task, action in
                                            // 搜索结果不需要特殊的退出动画
                                        }
                                    )
                                    .padding(.horizontal)
                                    .frame(height: 65)
                                }
                            }
                            .padding(.vertical, 8)
                        }
                    }
                }
            } else {
                // 空状态
                Spacer()
            }
            
            Spacer(minLength: 0)
        }
        .navigationBarHidden(true)
        .onAppear {
            // 自动聚焦搜索框
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                isSearchFieldFocused = true
                viewModel.isSearchFocused = true
            }
            
            // 添加埋点 - 搜索视图进入事件
            AnalyticsService.shared.trackEvent(AnalyticsService.EventNames.searchUsed)
        }
    }
    
    // 根据颜色主题提供适当的搜索栏背景
    private var searchBarBackground: some View {
        Group {
            if colorScheme == .dark {
                // 黑暗主题下的背景
                Color(UIColor.systemGray6)
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.gray.opacity(0.2), lineWidth: 0.5)
                    )
            } else {
                // 浅色主题下的背景
                Color(red: 0.95, green: 0.95, blue: 0.95)
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color(red: 0.8, green: 0.8, blue: 0.8).opacity(0.2), lineWidth: 0.5)
                    )
            }
        }
    }
}

// MARK: - 预览
struct SearchView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationStack {
            SearchView()
        }
    }
} 
