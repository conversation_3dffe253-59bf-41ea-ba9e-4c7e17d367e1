import SwiftUI

// 应用主题模式
enum ThemeMode: String, CaseIterable, Identifiable {
    case auto = "自动"
    case light = "亮色"
    case dark = "暗色"
    
    var id: String { self.rawValue }
    
    // 获取对应的系统颜色模式
    func getColorScheme() -> ColorScheme? {
        switch self {
        case .auto:
            return nil // 使用系统默认
        case .light:
            return .light
        case .dark:
            return .dark
        }
    }
}

// 主题管理类
class ThemeManager: ObservableObject {
    static let shared = ThemeManager()
    
    @Published var themeMode: ThemeMode {
        didSet {
            UserDefaults.standard.set(themeMode.rawValue, forKey: "ThemeMode")
        }
    }
    
    private init() {
        // 从UserDefaults读取主题设置，默认为自动
        let savedTheme = UserDefaults.standard.string(forKey: "ThemeMode") ?? ThemeMode.auto.rawValue
        if let themeMode = ThemeMode.allCases.first(where: { $0.rawValue == savedTheme }) {
            self.themeMode = themeMode
        } else {
            self.themeMode = .auto
        }
    }
}

// 提供颜色主题的环境修饰符
struct ThemeModeModifier: ViewModifier {
    @ObservedObject var themeManager = ThemeManager.shared
    
    func body(content: Content) -> some View {
        content
            .preferredColorScheme(themeManager.themeMode.getColorScheme())
    }
}

// View扩展，便于应用主题
extension View {
    func withThemeMode() -> some View {
        self.modifier(ThemeModeModifier())
    }
}
