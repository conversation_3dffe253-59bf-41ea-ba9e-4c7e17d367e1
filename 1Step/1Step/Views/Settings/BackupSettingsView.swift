import SwiftUI
import SwiftData
import UniformTypeIdentifiers
import UIKit

/// 备份与恢复设置视图
struct BackupSettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @Environment(\.colorScheme) private var colorScheme
    
    // 状态
    @State private var isBackupInProgress = false
    @State private var isRestoreInProgress = false
    @State private var alertMessage = ""
    @State private var showAlert = false
    
    var body: some View {
        NavigationView {
            ZStack(alignment: .top) {
                // 背景色
                Color(.systemGroupedBackground)
                    .edgesIgnoringSafeArea(.all)
                
                ScrollView {
                    VStack(spacing: 20) {
                        // 备份选项
                        backupSection
                        
                        // 恢复选项
                        restoreSection
                        
                        // 高级选项
                        advancedSection
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 16)
                    .padding(.bottom, 30)
                }
            }
            .navigationBarTitle("备份与恢复", displayMode: .inline)
            .navigationBarItems(trailing: But<PERSON>("完成") { dismiss() })
            .alert(alertMessage, isPresented: $showAlert) {
                Button("确定", role: .cancel) { }
            }
        }
    }
    
    // MARK: - 页面区块
    
    // 备份选项区块
    private var backupSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题
            Text("备份")
                .font(.headline)
                .foregroundColor(.secondary)
                .padding(.leading, 8)
            
            // 按钮
            Button(action: createBackup) {
                HStack {
                    // 图标
                    Image(systemName: "arrow.up.doc")
                        .font(.system(size: 20))
                        .foregroundColor(.blue)
                        .frame(width: 24, height: 24)
                    
                    // 文本
                    VStack(alignment: .leading, spacing: 2) {
                        Text("创建备份")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.primary)
                        
                        Text("将所有数据导出为备份文件")
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    // 加载中指示器
                    if isBackupInProgress {
                        ProgressView()
                            .controlSize(.small)
                    } else {
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.vertical, 14)
                .padding(.horizontal, 16)
                .background(Color(.secondarySystemGroupedBackground))
                .cornerRadius(12)
            }
            .buttonStyle(PlainButtonStyle())
            .disabled(isBackupInProgress || isRestoreInProgress)
            
            // 说明文本
            Text("备份会导出应用中的所有任务、项目、标签和检查项，用于数据保护和迁移。")
                .font(.system(size: 13))
                .foregroundColor(.secondary)
                .padding(.leading, 8)
                .padding(.trailing, 12)
                .padding(.top, 4)
        }
    }
    
    // 恢复选项区块
    private var restoreSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题
            Text("恢复")
                .font(.headline)
                .foregroundColor(.secondary)
                .padding(.leading, 8)
            
            // 按钮
            Button(action: importBackup) {
                HStack {
                    // 图标
                    Image(systemName: "arrow.down.doc")
                        .font(.system(size: 20))
                        .foregroundColor(.green)
                        .frame(width: 24, height: 24)
                    
                    // 文本
                    VStack(alignment: .leading, spacing: 2) {
                        Text("导入备份")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.primary)
                        
                        Text("从备份文件中还原数据")
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    // 加载中指示器
                    if isRestoreInProgress {
                        ProgressView()
                            .controlSize(.small)
                    } else {
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.vertical, 14)
                .padding(.horizontal, 16)
                .background(Color(.secondarySystemGroupedBackground))
                .cornerRadius(12)
            }
            .buttonStyle(PlainButtonStyle())
            .disabled(isBackupInProgress || isRestoreInProgress)
            
            // 说明文本
            Text("导入备份将完全替换当前数据，建议先创建当前数据的备份。")
                .font(.system(size: 13))
                .foregroundColor(.secondary)
                .padding(.leading, 8)
                .padding(.trailing, 12)
                .padding(.top, 4)
        }
    }
    
    // 高级选项区块
    private var advancedSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题
            Text("高级选项")
                .font(.headline)
                .foregroundColor(.secondary)
                .padding(.leading, 8)
            
            // 按钮
            Button(action: confirmClearData) {
                HStack {
                    // 图标
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 20))
                        .foregroundColor(.red)
                        .frame(width: 24, height: 24)
                    
                    // 文本
                    VStack(alignment: .leading, spacing: 2) {
                        Text("恢复出厂设置")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.primary)
                        
                        Text("清除所有数据并重置应用")
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    // 箭头
                    Image(systemName: "chevron.right")
                        .font(.system(size: 14))
                        .foregroundColor(.secondary)
                }
                .padding(.vertical, 14)
                .padding(.horizontal, 16)
                .background(Color(.secondarySystemGroupedBackground))
                .cornerRadius(12)
            }
            .buttonStyle(PlainButtonStyle())
            .disabled(isBackupInProgress || isRestoreInProgress)
            
            // 警告文本
            Text("恢复出厂设置将清除所有数据，并将应用重置为初始状态。此操作不可撤销，请谨慎操作。")
                .font(.system(size: 13))
                .foregroundColor(.red.opacity(0.8))
                .padding(.leading, 8)
                .padding(.trailing, 12)
                .padding(.top, 4)
        }
    }
    
    // 基本操作
    func dismissView() {
        dismiss()
    }
    
    private func createBackup() {
        guard !isBackupInProgress && !isRestoreInProgress else { return }
        isBackupInProgress = true
        
        _Concurrency.Task {
            do {
                let backupData = try await BackupService.shared.createBackup(modelContext: modelContext)
                let fileURL = try BackupService.shared.saveBackupToDocuments(backupData)
                
                await MainActor.run {
                    isBackupInProgress = false
                    shareFile(fileURL)
                }
            } catch {
                await MainActor.run {
                    isBackupInProgress = false
                    showErrorAlert("创建备份失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    func importBackup() {
        guard !isBackupInProgress && !isRestoreInProgress else { return }
        
        if let rootViewController = getRootViewController() {
            let documentTypes = [UTType.json]
            let documentPicker = UIDocumentPickerViewController(forOpeningContentTypes: documentTypes)
            
            let delegate = SimpleDocumentPickerDelegate()
            delegate.onPickDocument = { url in
                self.handleSelectedFile(url)
            }
            
            documentPicker.delegate = delegate
            documentPicker.allowsMultipleSelection = false
            
            // 保持代理引用
            objc_setAssociatedObject(documentPicker, "delegate", delegate, .OBJC_ASSOCIATION_RETAIN)
            
            rootViewController.present(documentPicker, animated: true, completion: nil)
        }
    }
    
    private func handleSelectedFile(_ url: URL) {
        guard url.startAccessingSecurityScopedResource() else {
            showErrorAlert("无法访问选择的文件")
            return
        }
        
        isRestoreInProgress = true
        
        _Concurrency.Task {
            do {
                let summary = try await BackupService.shared.importBackup(from: url, modelContext: modelContext)
                await MainActor.run {
                    isRestoreInProgress = false
                    showSuccessAlert(summary)
                }
            } catch {
                await MainActor.run {
                    isRestoreInProgress = false
                    showErrorAlert("导入失败: \(error.localizedDescription)")
                }
            }
            
            url.stopAccessingSecurityScopedResource()
        }
    }
    
    func confirmClearData() {
        guard !isBackupInProgress && !isRestoreInProgress else { return }
        
        if let rootViewController = getRootViewController() {
            let alert = UIAlertController(
                title: "确定要清除所有数据吗？",
                message: "此操作将删除所有任务、项目、标签和检查项。此操作不可撤销。",
                preferredStyle: .alert
            )
            
            alert.addAction(UIAlertAction(title: "取消", style: .cancel, handler: nil))
            
            let clearAction = UIAlertAction(
                title: "清除所有数据",
                style: .destructive,
                handler: { _ in self.clearAllData() }
            )
            alert.addAction(clearAction)
            
            rootViewController.present(alert, animated: true, completion: nil)
        }
    }
    
    private func clearAllData() {
        isRestoreInProgress = true
        
        _Concurrency.Task {
            do {
                try await BackupService.shared.clearAllData(modelContext)
                await MainActor.run {
                    isRestoreInProgress = false
                    showSuccessAlert("所有数据已清除")
                }
            } catch {
                await MainActor.run {
                    isRestoreInProgress = false
                    showErrorAlert("清除数据失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    func shareFile(_ fileURL: URL) {
        if let rootViewController = getRootViewController() {
            let activityVC = UIActivityViewController(
                activityItems: [fileURL],
                applicationActivities: nil
            )
            
            if let popover = activityVC.popoverPresentationController,
               UIDevice.current.userInterfaceIdiom == .pad {
                popover.sourceView = rootViewController.view
                popover.sourceRect = CGRect(
                    x: rootViewController.view.bounds.midX,
                    y: rootViewController.view.bounds.midY,
                    width: 0, height: 0
                )
                popover.permittedArrowDirections = []
            }
            
            rootViewController.present(activityVC, animated: true, completion: nil)
        }
    }
    
    // 辅助方法
    func getRootViewController() -> UIViewController? {
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first,
           let rootVC = window.rootViewController {
            return findTopViewController(from: rootVC)
        }
        return nil
    }
    
    func findTopViewController(from controller: UIViewController) -> UIViewController {
        if let presented = controller.presentedViewController {
            return findTopViewController(from: presented)
        }
        
        if let tabBarController = controller as? UITabBarController,
           let selected = tabBarController.selectedViewController {
            return findTopViewController(from: selected)
        }
        
        if let navigationController = controller as? UINavigationController,
           let top = navigationController.topViewController {
            return findTopViewController(from: top)
        }
        
        return controller
    }
    
    func showSuccessAlert(_ message: String) {
        alertMessage = message
        showAlert = true
    }
    
    func showErrorAlert(_ message: String) {
        alertMessage = message
        showAlert = true
    }
}

// MARK: - 辅助类型和扩展

// 简单按钮组件
struct SimpleButton: View {
    let title: String
    let icon: String
    let iconColor: Color
    let isLoading: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(iconColor)
                Text(title)
                
                if isLoading {
                    Spacer()
                    ProgressView()
                        .controlSize(.small)
                }
            }
        }
    }
}

// 简单文本按钮
struct SimpleTextButton: View {
    let title: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
        }
    }
}

// 简单Section包装器
struct SimpleSection<Content: View>: View {
    let header: String
    let footer: String
    let content: Content
    
    init(header: String, footer: String, content: Content) {
        self.header = header
        self.footer = footer
        self.content = content
    }
    
    var body: some View {
        Section(
            content: { self.content },
            header: { Text(self.header) },
            footer: { Text(self.footer) }
        )
    }
}

// 简单文档选择器代理
class SimpleDocumentPickerDelegate: NSObject, UIDocumentPickerDelegate {
    var onPickDocument: ((URL) -> Void)?
    
    func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
        if let url = urls.first {
            onPickDocument?(url)
        }
    }
    
    func documentPickerWasCancelled(_ controller: UIDocumentPickerViewController) {
        // 取消操作，无需处理
    }
}

// 简单Alert视图扩展 - 避免尾随闭包
extension View {
    func simpleAlert(isPresented: Binding<Bool>, title: String, message: String) -> some View {
        return self.alert(title, isPresented: isPresented) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(message)
        }
    }
}

// MARK: - Preview
struct BackupSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        BackupSettingsView()
    }
} 




