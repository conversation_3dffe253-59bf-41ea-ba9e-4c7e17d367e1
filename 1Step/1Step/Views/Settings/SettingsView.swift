import SwiftUI
import SwiftData

/// 设置页面 - 应用配置和信息
struct SettingsView: View {
    // MARK: - 依赖属性
    @ObservedObject private var themeManager = ThemeManager.shared
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.dismiss) private var dismiss
    @Environment(\.inviteRepository) private var inviteRepository
    @Environment(\.toastManager) private var toastManager
    @Environment(\.taskRepository) private var taskRepository
    
    // 状态属性
    @State private var showingInviteManagement = false
    @State private var showUserAgreement = false
    @State private var showPrivacyPolicy = false
    @State private var showDeveloperMenu = false
    @State private var developerModeCount = 0
    @State private var showBackupSettings = false
    
    var onAccepted: (() -> Void)?
    var onRejected: (() -> Void)?
    
    // MARK: - 主视图
    var body: some View {
        ZStack(alignment: .top) {
            // 背景色
            AppColors.UI.background(for: colorScheme)
                .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // 设置列表
                ScrollView {
                    settingsContent
                }
            }
            .navigationTitle("设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbarBackground(Color(.systemBackground), for: .navigationBar)
            .toolbarBackground(.visible, for: .navigationBar)
        }
        .sheet(isPresented: $showingInviteManagement) {
            InviteManagementView()
        }
        .sheet(isPresented: $showUserAgreement) {
            NavigationView {
                UserAgreementDisplayView()
            }
        }
        .sheet(isPresented: $showPrivacyPolicy) {
            NavigationView {
                PrivacyPolicyDisplayView()
            }
        }
        .sheet(isPresented: $showDeveloperMenu) {
            developerMenuView
        }
        .sheet(isPresented: $showBackupSettings) {
            BackupSettingsView()
        }
    }
    
    // MARK: - 子视图组件
    
    /// 设置内容
    private var settingsContent: some View {
        VStack(spacing: 24) {
            // 顶部空白占位
            Color.clear.frame(height: 10)
            
            // 主题设置
            themeSettingsSection
            
            // 数据管理
            dataManagementSection
            
            // 关于应用
            aboutAppSection
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 30)
    }
    
    /// 主题设置部分
    private var themeSettingsSection: some View {
        settingSection(title: "外观") {
            themeSelectionView
        }
    }
    
    /// 主题选择视图
    private var themeSelectionView: some View {
        VStack(spacing: 0) {
            ForEach(ThemeMode.allCases) { mode in
                themeButton(for: mode)
                
                if mode != ThemeMode.allCases.last {
                    themeDivider
                }
            }
        }
        .background(AppColors.UI.card(for: colorScheme))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(AppColors.UI.cardBorder(for: colorScheme), lineWidth: 0.5)
        )
    }
    
    /// 主题按钮
    private func themeButton(for mode: ThemeMode) -> some View {
        Button(action: {
            themeManager.themeMode = mode
        }) {
            HStack(spacing: 12) {
                Text(mode.displayName)
                    .font(.system(size: 16))
                    .foregroundColor(.primary)
                
                Spacer()
                
                if themeManager.themeMode == mode {
                    Image(systemName: "checkmark")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(AppColors.UI.accent(for: colorScheme))
                }
            }
            .padding(.vertical, 14)
            .padding(.horizontal, 16)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    /// 主题分隔线
    private var themeDivider: some View {
        Divider()
            .padding(.leading, 16)
            .background(Color.clear)
            .overlay(
                Rectangle()
                    .frame(height: 0.5)
                    .foregroundColor(AppColors.UI.divider(for: colorScheme))
            )
    }
    
    /// 数据管理部分
    private var dataManagementSection: some View {
        settingSection(title: "数据管理") {
            VStack(spacing: 0) {
                // 备份与恢复
                backupRestoreRow
            }
            .background(AppColors.UI.card(for: colorScheme))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(AppColors.UI.cardBorder(for: colorScheme), lineWidth: 0.5)
            )
        }
    }
    
    /// 备份与恢复行
    private var backupRestoreRow: some View {
        Button(action: { showBackupSettings = true }) {
            HStack(spacing: 12) {
                Text("备份与恢复")
                    .font(.system(size: 16))
                    .foregroundColor(.primary)
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 14)
            .padding(.horizontal, 16)
        }
    }
    
    /// 关于应用部分
    private var aboutAppSection: some View {
        settingSection(title: "关于") {
            VStack(spacing: 0) {
                // 版本信息
                versionInfoRow
                
                // 分隔线
                standardDivider
                
                // 用户协议
                userAgreementRow
                
                standardDivider
                
                // 隐私政策
                privacyPolicyRow
                
                standardDivider
            }
            .background(AppColors.UI.card(for: colorScheme))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(AppColors.UI.cardBorder(for: colorScheme), lineWidth: 0.5)
            )
        }
    }
    
    /// 版本信息行
    private var versionInfoRow: some View {
        HStack(spacing: 12) {
            Text("版本")
                .font(.system(size: 16))
                .foregroundColor(.primary)
            
            Spacer()
            
            Text("\(Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "未知") (\(Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "未知"))")
                .font(.system(size: 16))
                .foregroundColor(.secondary)
                .onTapGesture {
                    developerModeCount += 1
                    if developerModeCount >= 7 {
                        developerModeCount = 0
                        showDeveloperMenu = true
                        toastManager.show(
                            "已进入开发者模式", 
                            type: .info, 
                            position: .top, 
                            action: nil, 
                            onDismiss: nil
                        )
                    }
                }
        }
        .padding(.vertical, 14)
        .padding(.horizontal, 16)
    }
    
    /// 用户协议行
    private var userAgreementRow: some View {
        Button(action: { showUserAgreement = true }) {
            HStack(spacing: 12) {
                Text("用户协议")
                    .font(.system(size: 16))
                    .foregroundColor(.primary)
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 14)
        .padding(.horizontal, 16)
    }
    
    /// 隐私政策行
    private var privacyPolicyRow: some View {
        Button(action: { showPrivacyPolicy = true }) {
            HStack(spacing: 12) {
                Text("隐私政策")
                    .font(.system(size: 16))
                    .foregroundColor(.primary)
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 14)
        .padding(.horizontal, 16)
    }
    
    /// 标准分隔线
    private var standardDivider: some View {
        Divider()
            .padding(.leading, 16)
            .background(Color.clear)
            .overlay(
                Rectangle()
                    .frame(height: 0.5)
                    .foregroundColor(AppColors.UI.divider(for: colorScheme))
            )
    }
    
    /// 设置分区
    private func settingSection<Content: View>(title: String, @ViewBuilder content: () -> Content) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.secondary)
                .padding(.leading, 16)
            
            content()
        }
    }
    
    // MARK: - 开发者菜单
    
    // 开发者菜单视图 - 使用独立组件
    private var developerMenuView: some View {
        DeveloperMenuView()
    }
}

// MARK: - 辅助扩展
extension ThemeMode {
    var displayName: String {
        switch self {
        case .auto:
            return "跟随系统"
        case .light:
            return "浅色模式"
        case .dark:
            return "深色模式"
        }
    }
}

// MARK: - 预览
#Preview {
    SettingsView()
}
