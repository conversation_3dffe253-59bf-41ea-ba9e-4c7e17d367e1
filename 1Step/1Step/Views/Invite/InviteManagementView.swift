import SwiftUI

/// 邀请管理视图 - 用于生成邀请码
struct InviteManagementView: View {
    // MARK: - 属性
    @StateObject private var viewModel: InviteCreationViewModel = InviteCreationViewModel()
    @Environment(\.dismiss) private var dismiss
    @Environment(\.toastManager) private var toastManager
    
    // MARK: - 状态
    @State private var isLoading = false
    
    // MARK: - 视图体
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 邀请码创建部分
                creationSection
                
                // 已创建邀请码列表
                inviteCodesList
            }
            .navigationTitle("邀请管理")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    closeButton
                }
            }
            .onAppear {
                self.setupCallbacks()
            }
        }
    }
    
    // MARK: - 子视图
    
    /// 关闭按钮
    private var closeButton: some View {
        let textLabel = Text("关闭")
        
        return But<PERSON>(action: dismissView, label: { textLabel })
            .buttonStyle(PlainButtonStyle())
    }
    
    /// 创建区域
    private var creationSection: some View {
        VStack(spacing: 16) {
            // 输入框和创建按钮
            HStack {
                TextField("自定义邀请码", text: $viewModel.customCode)
                    .font(.system(.body, design: .rounded))
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
                    .keyboardType(.asciiCapable)
                
                // 创建按钮
                createButton
            }
            .padding(.horizontal, 16)
            .padding(.top, 16)
            
            // 最大使用次数选择器
            HStack {
                Text("最大使用次数")
                    .font(.system(.subheadline, design: .rounded))
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Picker("", selection: $viewModel.maxUses) {
                    ForEach(1...10, id: \.self) { number in
                        Text("\(number)").tag(number)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .frame(width: 180)
            }
            .padding(.horizontal, 16)
            
            // 错误消息
            if viewModel.creationState == .failed {
                Text(viewModel.errorMessage)
                    .font(.system(.caption, design: .rounded))
                    .foregroundColor(.red)
                    .padding(.horizontal, 16)
            }
        }
    }
    
    /// 邀请码列表
    private var inviteCodesList: some View {
        List {
            ForEach(viewModel.ownCodes) { code in
                InviteCodeRow(code: code)
            }
        }
        .listStyle(InsetGroupedListStyle())
    }
    
    /// 创建按钮
    private var createButton: some View {
        let isDisabled = viewModel.customCode.isEmpty || viewModel.creationState == .creating
        
        // 使用不同的button初始化方式
        let buttonBody = ZStack {
            RoundedRectangle(cornerRadius: 10)
                .fill(isDisabled ? Color.accentColor.opacity(0.5) : Color.accentColor)
            
            if viewModel.creationState == .creating {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle())
                    .tint(.white)
            } else {
                Text("创建")
                    .font(.system(.body, design: .rounded))
                    .fontWeight(.medium)
                    .foregroundColor(.white)
            }
        }
        .frame(width: 80, height: 44)
        
        return Button(action: self.createInviteCode, label: { buttonBody })
            .buttonStyle(PlainButtonStyle())
            .disabled(isDisabled)
            .opacity(isDisabled ? 0.7 : 1.0)
    }
    
    // MARK: - 私有方法
    
    /// 设置回调
    private func setupCallbacks() {
        viewModel.onCreationSuccess = handleCreationSuccess
    }
    
    /// 处理创建成功的回调
    private func handleCreationSuccess() {
        toastManager.showSuperLightInfo("邀请码创建成功")
        viewModel.loadOwnCodes()
    }
    
    /// 创建邀请码
    private func createInviteCode() {
        let isDisabled = viewModel.customCode.isEmpty || viewModel.creationState == .creating
        if !isDisabled {
            // 设置状态
            viewModel.creationState = .creating
            
            // 定义更新UI的闭包
            let updateUIBlock: () -> Void = {
                // 设置成功状态
                self.viewModel.creationState = .success
                self.viewModel.customCode = ""
                
                // 调用成功回调
                if let successCallback = self.viewModel.onCreationSuccess {
                    successCallback()
                }
            }
            
            // 定义后台任务闭包
            let backgroundBlock: () -> Void = {
                // 模拟网络操作
                Thread.sleep(forTimeInterval: 1.0)
                
                // 回到主线程更新UI
                DispatchQueue.main.async { updateUIBlock() }
            }
            
            // 启动后台任务
            DispatchQueue.global(qos: .userInitiated).async { backgroundBlock() }
        }
    }
    
    /// 关闭视图
    private func dismissView() {
        dismiss()
    }
}

// MARK: - 辅助视图

/// 邀请码列表行
struct InviteCodeRow: View {
    let code: InviteCode
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                // 邀请码
                Text(code.code)
                    .font(.system(.body, design: .rounded))
                    .fontWeight(.medium)
                
                // 状态、使用信息和日期
                VStack(alignment: .leading, spacing: 2) {
                    HStack {
                        StatusBadge(status: InviteCodeStatus(rawValue: code.status) ?? .expired)
                        
                        Text(formattedDate)
                            .font(.system(.caption, design: .rounded))
                            .foregroundColor(.secondary)
                    }
                    
                    // 使用次数信息
                    Text("\(code.currentUses)/\(code.maxUses) 次使用")
                        .font(.system(.caption, design: .rounded))
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            // 分享按钮
            shareButton
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle()) // 确保整行可点击
        .onTapGesture(perform: showInviteDetails)
    }
    
    // 格式化日期显示
    private var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter.string(from: code.createdAt)
    }
    
    // 分享按钮
    private var shareButton: some View {
        let imageLabel = Image(systemName: "square.and.arrow.up")
            .foregroundColor(.accentColor)
        
        return Button(action: shareInviteCode, label: { imageLabel })
            .buttonStyle(PlainButtonStyle())
            .padding(8) // 增加点击区域
    }
    
    // 分享邀请码
    private func shareInviteCode() {
        // 实际项目中，这里会调用系统分享功能
        let activityVC = UIActivityViewController(
            activityItems: [code.code],
            applicationActivities: nil
        )
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first,
           let rootVC = window.rootViewController {
            rootVC.present(activityVC, animated: true)
        }
    }
    
    // 显示邀请码详情
    private func showInviteDetails() {
        // 此处未实现，将来可实现查看详情的功能
        // 例如：展示使用记录等
    }
}

/// 状态标签
struct StatusBadge: View {
    let status: InviteCodeStatus
    
    var body: some View {
        Text(statusText)
            .font(.system(.caption, design: .rounded))
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(
                RoundedRectangle(cornerRadius: 4)
                    .fill(statusColor.opacity(0.2))
            )
            .foregroundColor(statusColor)
    }
    
    // 状态文本
    private var statusText: String {
        switch status {
        case .active:
            return "有效"
        case .used:
            return "已使用"
        case .expired:
            return "已过期"
        }
    }
    
    // 状态颜色
    private var statusColor: Color {
        switch status {
        case .active:
            return .green
        case .used:
            return .blue
        case .expired:
            return .gray
        }
    }
}

// MARK: - 预览
struct InviteManagementView_Previews: PreviewProvider {
    static var previews: some View {
        InviteManagementView()
    }
} 