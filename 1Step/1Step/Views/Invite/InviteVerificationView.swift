import SwiftUI
import WebKit

/// 口令验证视图 - 用于首次启动时要求用户输入口令
struct InviteVerificationView: View {
    // MARK: - 属性
    @StateObject private var viewModel: InviteVerificationViewModel
    @Environment(\.toastManager) private var toastManager
    @State private var agreedToTerms = false
    @State private var showUserAgreement = false
    @State private var showPrivacyPolicy = false
    @State private var userAgreementContent: AttributedString = AttributedString("")
    @State private var privacyPolicyContent: AttributedString = AttributedString("")
    
    // MARK: - 初始化
    init(viewModel: InviteVerificationViewModel = InviteVerificationViewModel()) {
        _viewModel = StateObject(wrappedValue: viewModel)
    }
    
    // MARK: - 视图体
    var body: some View {
        VStack(spacing: 24) {
            // 顶部标题区域
            headerView
            
            // 口令输入区域
            inputSection
            
            Spacer()
            
            // 底部说明文字
            footerView
        }
        .onAppear {
            setupCallbacks()
            loadMarkdownContents()
        }
        .sheet(isPresented: $showUserAgreement) {
            NavigationView {
                ScrollView {
                    if let agreementURL = Bundle.main.url(forResource: "UserAgreement", withExtension: "md"),
                       let agreementData = try? Data(contentsOf: agreementURL),
                       let agreementString = String(data: agreementData, encoding: .utf8) {
                        MarkdownWebView(markdownString: agreementString)
                            .frame(minHeight: UIScreen.main.bounds.height * 0.8)
                            .padding(.horizontal, 8)
                    } else {
                        VStack(spacing: 16) {
                            Text("无法加载用户协议")
                                .font(.headline)
                            Text("文件位置：Resources/UserAgreement.md")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding()
                    }
                }
                .navigationTitle("用户协议")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("关闭") { showUserAgreement = false }
                    }
                }
            }
        }
        .sheet(isPresented: $showPrivacyPolicy) {
            NavigationView {
                ScrollView {
                    if let policyURL = Bundle.main.url(forResource: "PrivacyPolicy", withExtension: "md"),
                       let policyData = try? Data(contentsOf: policyURL),
                       let policyString = String(data: policyData, encoding: .utf8) {
                        MarkdownWebView(markdownString: policyString)
                            .frame(minHeight: UIScreen.main.bounds.height * 0.8)
                            .padding(.horizontal, 8)
                    } else {
                        VStack(spacing: 16) {
                            Text("无法加载隐私政策")
                                .font(.headline)
                            Text("文件位置：Resources/PrivacyPolicy.md")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding()
                    }
                }
                .navigationTitle("隐私政策")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("关闭") { showPrivacyPolicy = false }
                    }
                }
            }
        }
    }
    
    // MARK: - 子视图
    
    /// 顶部标题区域
    private var headerView: some View {
        VStack(spacing: 16) {
            Image(systemName: "lock.shield")
                .font(.system(size: 60))
                .foregroundColor(.accentColor)
                .padding(.bottom, 8)
            
            Text("1Step")
                .font(.system(size: 28, weight: .bold, design: .rounded))
            
            Text("请输入一步口令继续使用")
                .font(.system(size: 16, design: .rounded))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.horizontal, 24)
        .padding(.top, 40)
    }
    
    /// 输入区域
    private var inputSection: some View {
        VStack(spacing: 16) {
            // 文本输入框
            TextField("输入口令", text: $viewModel.inviteCode)
                .font(.system(size: 16, design: .rounded))
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6))
                )
                .padding(.horizontal, 20)
                .autocapitalization(.none)
                .disableAutocorrection(true)
            
            // 错误消息
            if viewModel.verificationState == .failed {
                Text(viewModel.errorMessage)
                    .font(.system(.caption, design: .rounded))
                    .foregroundColor(.red)
            }
            
            // 验证按钮
            verifyButton
                .padding(.horizontal, 20)
                .padding(.top, 8)
                
            // 用户协议和隐私政策同意选项
            HStack(spacing: 4) {
                Button(action: {
                    agreedToTerms.toggle()
                }) {
                    Image(systemName: agreedToTerms ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(agreedToTerms ? .accentColor : .secondary)
                        .font(.system(size: 18))
                }
                
                Text("我已阅读并同意")
                    .font(.system(size: 12, design: .rounded))
                    .foregroundColor(.secondary)
                
                Button("《用户协议》") {
                    showUserAgreement = true
                }
                .font(.system(size: 12, design: .rounded))
                .foregroundColor(.accentColor)
                
                Text("和")
                    .font(.system(size: 12, design: .rounded))
                    .foregroundColor(.secondary)
                
                Button("《隐私政策》") {
                    showPrivacyPolicy = true
                }
                .font(.system(size: 12, design: .rounded))
                .foregroundColor(.accentColor)
            }
            .padding(.top, 12)
            .padding(.horizontal, 20)
        }
    }
    
    /// 底部说明
    private var footerView: some View {
        VStack(spacing: 10) {
            // 剩余使用次数信息
            if viewModel.verificationState == .success, let remaining = viewModel.remainingUses, remaining > 0 {
                Text("此口令还可使用\(remaining)次")
                    .font(.system(size: 13, design: .rounded))
                    .foregroundColor(.green)
                    .padding(.bottom, 8)
            }
            
            Text("1Step是一款为真实人类创造的行动系统\n只通过邀请获得使用资格")
                .font(.system(size: 12, design: .rounded))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            // 添加获取口令的提示
            Link(destination: URL(string: "http://way.1step.run")!) {
                Text("没有一步口令？")
                    .font(.system(size: 12, design: .rounded))
                    .foregroundColor(.accentColor)
            }
            .padding(.top, 8)
        }
        .padding(.bottom, 32)
    }
    
    /// 验证按钮
    private var verifyButton: some View {
        let isDisabled = viewModel.inviteCode.isEmpty || viewModel.verificationState == .verifying || !agreedToTerms
        
        let content = ZStack {
            RoundedRectangle(cornerRadius: 12)
                .fill(isDisabled ? Color.accentColor.opacity(0.5) : Color.accentColor)
            
            if viewModel.verificationState == .verifying {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle())
                    .tint(.white)
            } else {
                Text("验证口令")
                    .font(.system(.body, design: .rounded))
                    .fontWeight(.medium)
                    .foregroundColor(.white)
            }
        }
        .frame(height: 50)
        .frame(maxWidth: .infinity)
        
        return Button(
            action: verifyInviteCode,
            label: { content }
        )
        .buttonStyle(PlainButtonStyle())
        .disabled(isDisabled)
        .opacity(isDisabled ? 0.7 : 1.0)
    }
    
    // MARK: - 私有方法
    
    /// 设置回调
    private func setupCallbacks() {
        // 使用不同的赋值方式来避免可能的编译器问题
        let successHandler: () -> Void = self.handleVerificationSuccess
        viewModel.onVerificationSuccess = successHandler
    }
    
    /// 处理验证成功的回调
    private func handleVerificationSuccess() {
        toastManager.showSuperLightInfo("口令验证成功")
    }
    
    /// 验证口令
    private func verifyInviteCode() {
        let isDisabled = viewModel.inviteCode.isEmpty || viewModel.verificationState == .verifying || !agreedToTerms
        if !isDisabled {
            // 设置状态
            viewModel.verificationState = .verifying
            
            // 使用 DispatchQueue 启动验证进程
            DispatchQueue.global(qos: .userInitiated).async {
                // 模拟网络延迟
                Thread.sleep(forTimeInterval: 1.0)
                
                DispatchQueue.main.async {
                    // 检查日期是否在9月30号之前
                    let calendar = Calendar.current
                    let now = Date()
                    let September30 = calendar.date(from: DateComponents(year: 2025, month: 9, day: 30))!
                    
                    if now > September30 {
                        self.viewModel.verificationState = .failed
                        self.viewModel.errorMessage = "口令已过期"
                        return
                    }
                    
                    // 验证口令
                    let normalizedInput = self.viewModel.inviteCode
                        .trimmingCharacters(in: .whitespacesAndNewlines)
                        .replacingOccurrences(of: " ", with: "")
                        .lowercased()
                    
                    // 检查是否为有效口令
                    let validCodes = ["1step先驱", "最小行动", "无压启动", "内在动机", "1step","1Step"]
                    if validCodes.contains(normalizedInput) {
                        // 验证成功
                        self.viewModel.verificationState = .success
                        self.viewModel.remainingUses = 1
                        
                        // 保存本地邀请状态
                        let inviteRepository = DependencyContainer.inviteRepository()
                        let status = inviteRepository.getLocalInviteStatus()
                        let newStatus = DeviceInviteStatus(
                            isVerified: true,
                            deviceId: status.deviceId,
                            usedInviteCode: normalizedInput, // 保存用户实际使用的口令
                            verifiedAt: Date(),
                            ownInviteCodes: status.ownInviteCodes
                        )
                        inviteRepository.saveLocalInviteStatus(newStatus)
                        
                        // 保存用户同意隐私政策和用户协议的状态
                        UserDefaults.standard.set(true, forKey: "hasAcceptedPrivacyPolicy")
                        UserDefaults.standard.set(true, forKey: "UMengPrivacyPolicyAccepted")
                        
                        // 调用成功回调
                        if let successCallback = self.viewModel.onVerificationSuccess {
                            successCallback()
                        }
                    } else {
                        // 验证失败
                        self.viewModel.verificationState = .failed
                        self.viewModel.errorMessage = "无效的口令"
                    }
                }
            }
        }
    }
    
    /// 加载Markdown文件内容
    private func loadMarkdownContents() {
        // 加载用户协议
        if let agreementURL = Bundle.main.url(forResource: "UserAgreement", withExtension: "md"),
           let agreementData = try? Data(contentsOf: agreementURL),
           let agreementString = String(data: agreementData, encoding: .utf8) {
            do {
                userAgreementContent = try AttributedString(markdown: agreementString)
            } catch {
                print("无法解析用户协议Markdown: \(error)")
                // 回退到基本文本
                userAgreementContent = AttributedString("无法加载用户协议，请稍后再试。")
            }
        } else {
            print("无法加载用户协议文件")
            userAgreementContent = AttributedString("无法加载用户协议，请稍后再试。")
        }
        
        // 加载隐私政策
        if let policyURL = Bundle.main.url(forResource: "PrivacyPolicy", withExtension: "md"),
           let policyData = try? Data(contentsOf: policyURL),
           let policyString = String(data: policyData, encoding: .utf8) {
            do {
                privacyPolicyContent = try AttributedString(markdown: policyString)
            } catch {
                print("无法解析隐私政策Markdown: \(error)")
                // 回退到基本文本
                privacyPolicyContent = AttributedString("无法加载隐私政策，请稍后再试。")
            }
        } else {
            print("无法加载隐私政策文件")
            privacyPolicyContent = AttributedString("无法加载隐私政策，请稍后再试。")
        }
    }
}

// MARK: - 预览
struct InviteVerificationView_Previews: PreviewProvider {
    static var previews: some View {
        InviteVerificationView()
    }
} 