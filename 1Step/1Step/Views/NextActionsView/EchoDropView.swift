import SwiftUI
import Combine
import AVFoundation
import AudioToolbox

// 添加一个字符视图组件用于单字符动画
struct CharacterView: View {
    let character: String
    let delay: Double
    var isReleasing: Bool
    
    @State private var opacity: Double = 1
    @State private var offset: CGFloat = 0
    @State private var scale: CGFloat = 1
    
    var body: some View {
        Text(character)
            .opacity(opacity)
            .offset(y: offset)
            .scaleEffect(scale)
            .onChange(of: isReleasing) { oldValue, newValue in
                if newValue {
                    withAnimation(
                        .easeOut(duration: 0.8)
                        .delay(delay)
                    ) {
                        opacity = 0
                        offset = -10 - CGFloat.random(in: 0...10)
                        scale = 1.0 + CGFloat.random(in: 0...0.08)
                    }
                }
            }
    }
}

// 字符释放容器视图
struct ReleasingTextView: View {
    let text: String
    var isReleasing: Bool
    var startFromBeginning: Bool = true // 决定从开头还是结尾开始释放
    
    var body: some View {
        ZStack(alignment: .topLeading) {
            // 创建一个不可见的背景文本用于确保布局与原文本一致
            Text(text)
                .font(.body)
                .foregroundColor(.clear)
                .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
                .padding(10)
            
            // 拆分的字符视图
            HStack(spacing: 0) {
                ForEach(Array(text.enumerated()), id: \.offset) { index, character in
                    let delayFactor = startFromBeginning ? 
                        Double(index) : Double(text.count - index)
                    
                    CharacterView(
                        character: String(character),
                        delay: Double(delayFactor) * 0.03, // 每个字符延迟约30毫秒
                        isReleasing: isReleasing
                    )
                }
            }
            .padding(10)
            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
        }
    }
}

struct EchoDropView: View {
    // 表单协调器
    @StateObject private var formCoordinator = TaskFormCoordinator.shared
    
    private func dismissView() {
        isTextFieldFocused = false
        
        withAnimation(.easeOut(duration: 0.25)) {
            contentOpacity = 0
            backgroundOpacity = 0
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.25) {
            // viewModel.showingEchoDrop = false // CLEANED: Property removed from viewModel
            formCoordinator.hideForm()
        }
    }
    
    @ObservedObject var viewModel: NextActionsViewModel
    @FocusState private var isTextFieldFocused: Bool
    @Environment(\.colorScheme) private var colorScheme
    
    // 键盘通知监听者
    @State private var keyboardObserver: AnyCancellable?
    @State private var keyboardHeight: CGFloat = 0
    
    // 入场和释放动画状态
    @State private var contentOpacity: Double = 0
    @State private var backgroundOpacity: Double = 0
    @State private var contentOffset: CGFloat = 120
    
    // 释放动画状态
    @State private var isReleasing: Bool = false
    @State private var textOpacity: Double = 1
    @State private var containersOpacity: Double = 1
    @State private var positionOffset: CGFloat = 0
    
    // 波纹状态
    @State private var ripples: [Ripple] = []
    
    // 波纹数据结构
    struct Ripple: Identifiable {
        let id = UUID()
        var scale: CGFloat
        var opacity: Double
    }
    
    // 在EchoDropView中添加音频播放器
    @State private var audioPlayer: AVAudioPlayer?
    
    // 将输入区域提取为单独视图函数
    private func inputAreaView() -> some View {
        ZStack(alignment: .topLeading) {
            // 输入框背景
            RoundedRectangle(cornerRadius: 16)
                .fill(colorScheme == .dark ? 
                      Color(UIColor.systemGray6) : 
                      Color(UIColor.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(colorScheme == .dark ? 
                                Color.gray.opacity(0.2) : 
                                Color.gray.opacity(0.3),
                                lineWidth: 0.5)
                )
                .shadow(color: Color.black.opacity(0.15), radius: 8, x: 0, y: 2)
                .opacity(containersOpacity)
            
            // 文本编辑器
            // ZStack { // CLEANED: Entire ZStack with TextEditor and ReleaseEffectView commented out
            //     TextEditor(text: $viewModel.echoText) // CLEANED: Property removed from viewModel
            //         .focused($isTextFieldFocused)
            //         .scrollContentBackground(.hidden)
            //         .background(Color.clear)
            //         .font(.body)
            //         .foregroundColor(colorScheme == .dark ? 
            //                          Color.white : 
            //                          Color.black)
            //         .padding(10)
            //         .opacity(isReleasing ? 0 : 1)
            //     
            //     // 释放效果视图
            //     if isReleasing {
            //         ReleaseEffectView(
            //             text: viewModel.echoText, // CLEANED: Property removed from viewModel
            //             textOpacity: textOpacity,
            //             colorScheme: colorScheme,
            //             ripples: ripples
            //         )
            //         .padding(10)
            //     }
            // }
            Text("回声输入区已禁用") // ADDED: Placeholder
                    .font(.body)
                .foregroundColor(.gray)
                    .padding(10)
                .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .center)
        }
        .frame(height: 150)
        .padding(.horizontal, 16)
    }
    
    // 背景视图
    private var backgroundView: some View {
        Rectangle()
            .fill(colorScheme == .dark ? Color.black : Color.white)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .edgesIgnoringSafeArea(.all)
            .contentShape(Rectangle())
            .opacity(backgroundOpacity)
            .onTapGesture {
                dismissView()
            }
    }
    
    // 内容浮窗视图
    private var contentOverlayView: some View {
        GeometryReader { geometry in
            ZStack {
                // 主界面内容
                VStack(spacing: 16) {
                    // 顶部提示文字
                    Text("写下你想说的，也许并不需要留下")
                        .font(.footnote)
                        .foregroundColor(Color.gray)
                        .padding(.top, 16)
                        .opacity(containersOpacity)
                    
                    // 使用提取的输入区域视图
                    inputAreaView()
                    
                    // 按钮区域
                    buttonAreaView
                }
                .padding(.vertical, 4)
                .frame(width: geometry.size.width * 0.85)
                .background(colorScheme == .dark ? Color(UIColor.systemGray6).opacity(0.8) : Color(UIColor.systemBackground).opacity(0.8))
                .cornerRadius(20)
            }
            .opacity(contentOpacity)
            .offset(y: contentOffset)
            .position(
                x: geometry.size.width / 2,
                y: geometry.size.height * 0.5 - (isReleasing ? positionOffset : (keyboardHeight > 0 ? keyboardHeight * 0.4 : 0))
            )
        }
    }
    
    // 按钮区域视图
    private var buttonAreaView: some View {
        HStack(spacing: 20) {
            // 转为行动按钮
            Button(action: {
                isTextFieldFocused = false
                
                let generator = UIImpactFeedbackGenerator(style: .medium)
                generator.impactOccurred()
                
                withAnimation(.easeOut(duration: 0.25)) {
                    contentOpacity = 0
                    backgroundOpacity = 0
                }
                
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.25) {
                    // viewModel.handleEchoDropText() // CLEANED: Method removed from viewModel
                }
            }) {
                Text("点击转为行动")
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(colorScheme == .dark ? 
                                    Color.white.opacity(0.7) : 
                                    Color.black.opacity(0.6))
            }
            .padding(.vertical, 6)
            .opacity(containersOpacity)
            
            Spacer()
            
            // 释放按钮
            Button(action: {
                // 记录位置偏移
                let currentKeyboardOffset = keyboardHeight > 0 ? keyboardHeight * 0.4 : 0
                positionOffset = currentKeyboardOffset
                
                // 取消焦点
                isTextFieldFocused = false
                
                // 触觉反馈 - 初始反馈
                let generator = UIImpactFeedbackGenerator(style: .soft)
                generator.impactOccurred(intensity: 0.7)
                
                isReleasing = true
                
                // 隐藏容器和按钮
                withAnimation(.easeOut(duration: 0.3)) {
                    containersOpacity = 0
                }
                
                // 第一个波纹
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    createRipple(initialScale: 0.05, finalScale: 4.0, duration: 0.8)
                    playHapticFeedback(style: .soft, intensity: 0.4)
                }
                
                // 第二个波纹
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    createRipple(initialScale: 0.1, finalScale: 3.9, duration: 0.9)
                    playHapticFeedback(style: .soft, intensity: 0.5)
                }
                
                // 第三个波纹 - 在这里播放音效
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
                    createRipple(initialScale: 0.15, finalScale: 3.8, duration: 1.0)
                    playHapticFeedback(style: .soft, intensity: 0.6)
                    
                    // 开始文字淡出
                    withAnimation(.easeOut(duration: 1.0)) {
                        textOpacity = 0
                    }
                    
                    // 播放水滴音效 - 根据用户要求，保留在第三个波纹处
                    playSoundEffect("drop")
                }
                
                // 第四个波纹
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    createRipple(initialScale: 0.2, finalScale: 3.7, duration: 1.1)
                    playHapticFeedback(style: .soft, intensity: 0.4)
                }
                
                // 结束动画
                DispatchQueue.main.asyncAfter(deadline: .now() + 2.6) {
                    playHapticFeedback(style: .medium, intensity: 0.6)
                    withAnimation(.easeOut(duration: 0.3)) {
                        backgroundOpacity = 0
                        contentOpacity = 0
                    }
                    
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        // viewModel.releaseEcho() // CLEANED: Method removed from viewModel
                        DependencyContainer.toastManager().showSuperLightInfo("回声已释放了")
                    }
                }
            }) {
                Text("释放")
                    .font(.system(size: 15, weight: .medium))
                    .foregroundColor(.white)
                    .padding(.horizontal, 22)
                    .padding(.vertical, 7)
                    .background(AppColors.UI.primary(for: colorScheme))
                    .cornerRadius(8)
            }
            .opacity(containersOpacity)
        }
        .padding(.horizontal, 24)
        .padding(.bottom, 20)
    }
    
    var body: some View {
        ZStack {
            backgroundView
            contentOverlayView
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .edgesIgnoringSafeArea(.all)
        .onAppear {
            // 键盘监听
            NotificationCenter.default.addObserver(forName: UIResponder.keyboardWillShowNotification, object: nil, queue: .main) { notification in
                if let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect {
                    self.keyboardHeight = keyboardFrame.height
                }
            }
            
            NotificationCenter.default.addObserver(forName: UIResponder.keyboardWillHideNotification, object: nil, queue: .main) { _ in
                self.keyboardHeight = 0
            }
            
            // 入场动画
            withAnimation(
                .spring(
                    response: 0.65,
                    dampingFraction: 0.7,
                    blendDuration: 0
                )
            ) {
                contentOpacity = 1.0
                contentOffset = 0
                backgroundOpacity = 1.0
            }
            
            // 聚焦文本框
            DispatchQueue.main.async {
                isTextFieldFocused = true
            }
            
            // 键盘通知
            keyboardObserver = NotificationCenter.default
                .publisher(for: .requestKeyboardFocus)
                .sink { _ in
                    isTextFieldFocused = true
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        isTextFieldFocused = false
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            isTextFieldFocused = true
                        }
                    }
                }
            
            NotificationCenter.default.post(name: .requestKeyboardFocus, object: nil)
        }
        .onDisappear {
            keyboardObserver?.cancel()
            formCoordinator.hideForm()
            
            NotificationCenter.default.removeObserver(self, name: UIResponder.keyboardWillShowNotification, object: nil)
            NotificationCenter.default.removeObserver(self, name: UIResponder.keyboardWillHideNotification, object: nil)
        }
    }
    
    // 修改波纹创建函数，添加持续时间参数
    private func createRipple(initialScale: CGFloat = 0.1, finalScale: CGFloat = 4.0, duration: Double = 0.9) {
        let newRipple = Ripple(
            scale: initialScale, 
            opacity: colorScheme == .dark ? 0.25 : 0.3
        )
        ripples.append(newRipple)
        
        let rippleIndex = ripples.count - 1
        
        // 动画扩散 - 可调整持续时间
        withAnimation(.easeOut(duration: duration)) {
            ripples[rippleIndex].scale = finalScale
            ripples[rippleIndex].opacity = 0
        }
        
        // 动画完成后移除
        DispatchQueue.main.asyncAfter(deadline: .now() + duration + 0.1) {
            if ripples.count > rippleIndex {
                ripples.remove(at: rippleIndex)
            }
        }
    }
    
    // 添加触觉反馈函数
    private func playHapticFeedback(style: UIImpactFeedbackGenerator.FeedbackStyle, intensity: CGFloat) {
        let generator = UIImpactFeedbackGenerator(style: style)
        generator.impactOccurred(intensity: intensity)
    }
    
    // 简化的音效播放函数
    private func playSoundEffect(_ name: String) {
        guard let soundURL = Bundle.main.url(forResource: name, withExtension: "WAV") else {
            print("找不到音效文件: \(name).WAV")
            // 如果找不到，使用系统音效作为备选
            AudioServicesPlaySystemSound(1057)
            return
        }
        
        do {
            // 停止任何现有播放
            audioPlayer?.stop()
            
            // 创建并配置新的播放器
            audioPlayer = try AVAudioPlayer(contentsOf: soundURL)
            audioPlayer?.volume = 0.5  // 将音量设置为50%
            audioPlayer?.prepareToPlay()
            audioPlayer?.play()
            
        } catch {
            print("播放音效出错: \(error.localizedDescription)")
            // 发生错误时使用系统音效
            AudioServicesPlaySystemSound(1057)
        }
    }
}

// 波纹效果组件
struct RippleEffectView: View {
    let colorScheme: ColorScheme
    let ripples: [EchoDropView.Ripple]
    let innerGlowOpacity: Double
    let innerGlowScale: CGFloat
    
    var body: some View {
        ZStack {
            // 中心光晕
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            colorScheme == .dark ? 
                                Color.white.opacity(0.18) : 
                                Color.blue.opacity(0.12),
                            Color.clear
                        ]),
                        center: .center,
                        startRadius: 1,
                        endRadius: 70
                    )
                )
                .frame(width: 140, height: 140)
                .scaleEffect(innerGlowScale)
                .opacity(innerGlowOpacity)
            
            // 波纹圆圈
            ForEach(ripples) { ripple in
                Circle()
                    .stroke(
                        colorScheme == .dark ? 
                            Color.white.opacity(ripple.opacity) : 
                            Color.blue.opacity(ripple.opacity), 
                        lineWidth: 1.2
                    )
                    .scaleEffect(ripple.scale)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// 文字效果组件
struct TextEffectView: View {
    let text: String
    let textOpacity: Double
    let colorScheme: ColorScheme
    let textScale: CGFloat
    let textOffset: CGFloat
    let textBlur: CGFloat
    
    var body: some View {
        Text(text)
            .font(.body)
            .foregroundColor(colorScheme == .dark ? Color.white : Color.black)
            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
            .opacity(textOpacity)
            .scaleEffect(textScale)
            .offset(y: textOffset)
            .blur(radius: textBlur)
    }
}

// 修改ReleaseEffectView
struct ReleaseEffectView: View {
    let text: String
    let textOpacity: Double
    let colorScheme: ColorScheme
    let ripples: [EchoDropView.Ripple]
    
    @State private var textScale: CGFloat = 1.0
    @State private var textOffset: CGFloat = 0
    @State private var innerGlowOpacity: Double = 0
    @State private var innerGlowScale: CGFloat = 0.8
    @State private var textBlur: CGFloat = 0
    
    var body: some View {
        ZStack {
            // 波纹效果
            RippleEffectView(
                colorScheme: colorScheme, 
                ripples: ripples,
                innerGlowOpacity: innerGlowOpacity,
                innerGlowScale: innerGlowScale
            )
            
            // 文字效果
            TextEffectView(
                text: text,
                textOpacity: textOpacity,
                colorScheme: colorScheme,
                textScale: textScale,
                textOffset: textOffset,
                textBlur: textBlur
            )
        }
        .onAppear {
            // 光晕出现动画
            withAnimation(.easeIn(duration: 0.4).delay(0.1)) {
                innerGlowOpacity = 1.0
                innerGlowScale = 1.0
            }
            
            // 光晕脉动动画
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true)) {
                    innerGlowScale = 1.1
                }
            }
            
            // 文字动画序列
            withAnimation(.easeOut(duration: 0.6).delay(0.3)) {
                textScale = 1.02
                textOffset = -2
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.9) {
                withAnimation(.easeOut(duration: 0.8)) {
                    textScale = 1.04
                    textOffset = -4
                    textBlur = 0.5
                }
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.7) {
                withAnimation(.easeOut(duration: 1.0)) {
                    textScale = 1.06
                    textOffset = -6
                    textBlur = 1.0
                }
            }
        }
    }
}

// 粒子效果视图
struct ParticlesView: View {
    @State private var particles: [Particle] = []
    
    var body: some View {
        ZStack {
            ForEach(0..<20, id: \.self) { index in
                Circle()
                    .fill(Color.blue.opacity(0.5))
                    .frame(width: CGFloat.random(in: 3...8), height: CGFloat.random(in: 3...8))
                    .offset(x: CGFloat.random(in: 10...100) * (Bool.random() ? 1 : -1),
                            y: CGFloat.random(in: -120...0))
                    .opacity(Double.random(in: 0.2...0.8))
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// 如果需要预览，可以取消下面的注释
/*
#Preview {
    EchoDropView(viewModel: NextActionsViewModel())
}
*/
