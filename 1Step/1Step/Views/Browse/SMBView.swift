import SwiftUI
import SwiftData

/// 未来也许页面
struct SMBView: View {
    // MARK: - 依赖
    private let taskRepository: TaskRepository
    private let projectRepository: ProjectRepository
    private let tagRepository: TagRepository
    
    // MARK: - 环境变量
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - 状态变量
    @StateObject private var viewModel: SMBViewModel
    @StateObject private var formCoordinator = TaskFormCoordinator.shared
    @FocusState private var isTitleFocused: Bool
    
    // 初始化方法
    init(
        taskRepository: TaskRepository = DependencyContainer.taskRepository(),
        projectRepository: ProjectRepository = DependencyContainer.projectRepository(),
        tagRepository: TagRepository = DependencyContainer.tagRepository()
    ) {
        self.taskRepository = taskRepository
        self.projectRepository = projectRepository
        self.tagRepository = tagRepository
        
        _viewModel = StateObject(wrappedValue: SMBViewModel())
    }
    
    // MARK: - 视图体
    var body: some View {
        ZStack {
            VStack(spacing: 0) {
                // 搜索栏 - 仅在搜索模式下显示
                if viewModel.isSearchFocused {
                    searchBarView
                }
                
                // 任务列表
                ScrollView {
                    smbTasksView
                        .padding(.bottom, formCoordinator.isShowing ? 0 : 10)
                }
                .refreshable {
                    await viewModel.refreshTasks()
                }
            }
            .navigationTitle("未来也许")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItemGroup(placement: .navigationBarTrailing) {
                    // 搜索按钮
                    Button {
                        withAnimation {
                            viewModel.isSearchFocused.toggle()
                            if !viewModel.isSearchFocused {
                                viewModel.searchText = ""
                                viewModel.hideKeyboard()
                                viewModel.loadTasks()
                            }
                        }
                    } label: {
                        Image(systemName: viewModel.isSearchFocused ? "xmark" : "magnifyingglass")
                            .foregroundColor(viewModel.isSearchFocused ? .accentColor : .primary)
                    }
                    
                    // 批量操作按钮
                    Button {
                        withAnimation {
                            if viewModel.isBatchMode {
                                viewModel.exitBatchMode()
                            } else {
                                viewModel.enterBatchMode()
                            }
                        }
                    } label: {
                        Image(systemName: viewModel.isBatchMode ? "checkmark.circle.fill" : "circle")
                            .foregroundColor(viewModel.isBatchMode ? .accentColor : .primary)
                    }
                }
            }
            .toolbarBackground(Color(.systemBackground), for: .navigationBar)
            .toolbarBackground(.automatic, for: .navigationBar)
            .onAppear {
                // 视图出现时刷新任务数据
                viewModel.loadTasks()
                // 添加埋点 - 未来也许视图进入事件
                AnalyticsService.shared.trackEvent(AnalyticsService.EventNames.somedayViewEntered)
            }
            
            // 浮层内容 - 只显示一个添加按钮
            if !formCoordinator.isShowing {
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        Button(action: { formCoordinator.showForm() }) {
                            Image(systemName: "plus")
                                .foregroundColor(.white)
                                .frame(width: 56, height: 56)
                                .background(Color.purple)
                                .clipShape(Circle())
                                .shadow(radius: 4)
                        }
                        .padding()
                    }
                }
            }
            
            // 添加任务浮层
            if formCoordinator.isShowing {
                taskFormOverlay
            }
            
            // Toast提示
            if viewModel.showToast {
                toastOverlay
            }
        }
    }
    
    // MARK: - 子视图
    
    /// 搜索栏
    private var searchBarView: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("搜索任务", text: $viewModel.searchText)
                .textFieldStyle(PlainTextFieldStyle())
                .autocapitalization(.none)
                .disableAutocorrection(true)
            
            if !viewModel.searchText.isEmpty {
                Button(action: {
                    viewModel.searchText = ""
                    viewModel.loadTasks()
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(8)
        .background(Color.secondarySystemBackground)
        .cornerRadius(8)
        .padding(.horizontal)
        .padding(.bottom, 8)
    }
    
    /// 未来也许任务列表
    private var smbTasksView: some View {
        VStack(spacing: 0) {
            // 批量操作工具栏
            if viewModel.isBatchMode {
                batchOperationToolbar
            }
            
            // 任务列表
            if viewModel.isSearchFocused && !viewModel.searchText.isEmpty {
                // 搜索结果视图
                smbSearchResultsView
            } else {
                // 标准任务列表 - 使用 TaskSectionView
                TaskSectionView(
                    title: "", // 未来也许页面不需要章节标题
                    tasks: viewModel.smbTasks,
                    status: TaskStatus.smb.rawValue,
                    taskRepository: taskRepository,
                    projectRepository: projectRepository,
                    tagRepository: tagRepository,
                    titleColor: .purple, // 或适合 SMB 的颜色
                    isExpanded: .constant(true), // SMB 页面默认展开
                    showExpandButton: false, // 不需要展开/折叠按钮
                    showEmptyState: true, // 显示空状态
                    isBatchMode: viewModel.isBatchMode,
                    selectedTasks: viewModel.selectedTasks,
                    onTaskSelected: { taskId in
                        viewModel.toggleTaskSelection(taskId)
                    },
                    onTaskIntent: { [viewModel] intent in // 确保使用捕获列表
                        viewModel.handleTaskIntent(intent)
                    }
                )
            }
        }
    }
    
    // 批量操作底部工具栏
    private var batchOperationToolbar: some View {
        VStack(spacing: 0) {
            Divider()
            
            HStack(spacing: 20) {
                // 取消按钮
                Button {
                    withAnimation {
                        viewModel.exitBatchMode()
                    }
                } label: {
                    Text("取消")
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // 已选数量
                Text("已选择 \(viewModel.selectedTasks.count) 项")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                // 移动按钮
                Menu {
                    Button(action: {
                        viewModel.moveSelectedTasksToNextActions()
                    }) {
                        Label("移动到下一步", systemImage: "arrow.right")
                    }
                    
                    Button(action: {
                        viewModel.moveSelectedTasksToWaiting()
                    }) {
                        Label("移动到等待中", systemImage: "hourglass")
                    }
                    
                    Button(action: {
                        viewModel.moveSelectedTasksToInbox()
                    }) {
                        Label("移动到收集箱", systemImage: "tray")
                    }
                    
                    Button(action: {
                        viewModel.deleteSelectedTasks()
                    }) {
                        Label("删除", systemImage: "trash")
                            .foregroundColor(.red)
                    }
                } label: {
                    Text("移动到")
                        .foregroundColor(.accentColor)
                }
                .disabled(viewModel.selectedTasks.isEmpty)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(Color(.systemBackground))
        }
    }
    
    // 搜索结果视图
    private var smbSearchResultsView: some View {
        VStack(spacing: 0) {
            // 在搜索结果中，我们仍然直接使用 ForEach 和 TaskCardView
            // 但需要手动添加滑动操作
            if viewModel.smbTasks.isEmpty {
                Text("没有找到相关任务")
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
                    .padding()
            } else {
                ForEach(viewModel.smbTasks) { (task: Task) in
                    HStack(spacing: 12) {
                        if viewModel.isBatchMode {
                            // 选择按钮
                            Button {
                                viewModel.toggleTaskSelection(task.id)
                            } label: {
                                Image(systemName: viewModel.selectedTasks.contains(task.id) ? "checkmark.circle.fill" : "circle")
                                    .font(.system(size: 18))
                                    .foregroundColor(viewModel.selectedTasks.contains(task.id) ? .accentColor : .secondary)
                            }
                            .transition(.scale)
                        }
                        
                        // 任务卡片 - 手动添加滑动操作
                        TaskCardView(
                            task: task,
                            taskRepository: taskRepository,
                            projectRepository: projectRepository,
                            tagRepository: tagRepository,
                            style: .standard,
                            // 调用辅助方法生成并传递滑动操作
                            leadingSwipeActions: createLeadingSwipeActions(for: task),
                            trailingSwipeActions: createTrailingSwipeActions(for: task),
                            onTaskIntent: { [viewModel] intent in
                                viewModel.handleTaskIntent(intent)
                            },
                            onExitRequested: { task, direction in
                                // 处理动画退出 (如果需要，保留或实现此逻辑)
                            }
                        )
                    }
                    .padding(.horizontal)
                    .padding(.vertical, 4)
                }
            }
        }
    }
    
    // 空状态视图 (现在由 TaskSectionView 处理，可以移除或保留以备他用)
    // private var emptyStateView: some View { ... }
    
    // 任务添加表单浮层
    private var taskFormOverlay: some View {
        // 使用新的初始化方法，直接设置初始状态为未来也许
        let taskFormViewModel = TaskFormViewModel(
            initialStatus: TaskStatus.smb.rawValue,
            taskManager: DependencyContainer.taskManager()
        )
        
        // 设置回调
        taskFormViewModel.onTaskAdded = { task, message in
            // 触感反馈
            UIImpactFeedbackGenerator(style: .medium).impactOccurred()
            
            // 刷新任务列表，但不关闭添加任务界面
            viewModel.loadTasks()
            
            // 重新请求键盘聚焦，便于用户继续输入
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                NotificationCenter.default.post(name: .requestKeyboardFocus, object: nil)
            }
        }
        
        taskFormViewModel.onDismiss = {
            formCoordinator.hideForm()
        }
        
        return AddTaskFormView(viewModel: taskFormViewModel)
    }
    
    // Toast提示浮层
    private var toastOverlay: some View {
        VStack {
            Spacer()
            
            Text(viewModel.toastMessage)
                .font(.system(size: 14))
                .padding(.horizontal, 16)
                .padding(.vertical, 10)
                .background(Color.secondarySystemBackground)
                .cornerRadius(8)
                .shadow(radius: 2)
                .transition(.move(edge: .bottom).combined(with: .opacity))
                .padding(.bottom, 16)
        }
    }
}

#Preview {
    NavigationView {
        SMBView()
    }
}

// MARK: - 滑动操作创建 (私有辅助方法)

private func createLeadingSwipeActions(for task: Task) -> [TaskSwipeAction] {
    var actions: [TaskSwipeAction] = []
    
    // 始终添加完成操作作为第一个
    switch task.status {
    case TaskStatus.na.rawValue, TaskStatus.waiting.rawValue, TaskStatus.inbox.rawValue, TaskStatus.smb.rawValue:
        actions.append(
            createSwipeAction(title: "完成", statusValue: TaskStatus.done.rawValue, icon: "checkmark", color: .green)
        )
    default:
        break
    }
    
    // 根据状态添加其他两个操作按钮
    switch task.status {
    case TaskStatus.na.rawValue:
        // 添加"一步"和"等待中"
        actions.append(
            createSwipeAction(title: "一步", statusValue: TaskStatus.doing.rawValue, icon: "figure.walk", color: .green)
        )
        
        actions.append(
            createSwipeAction(title: "等待中", statusValue: TaskStatus.waiting.rawValue, icon: "hourglass", color: .orange)
        )
        
    case TaskStatus.waiting.rawValue:
        // 添加"下一步"和"未来也许"
        actions.append(
            createSwipeAction(title: "下一步", statusValue: TaskStatus.na.rawValue, icon: "arrow.right", color: .blue)
        )
        
        actions.append(
            createSwipeAction(title: "未来也许", statusValue: TaskStatus.smb.rawValue, icon: "calendar", color: .purple)
        )
        
    case TaskStatus.inbox.rawValue:
        // 添加"下一步"和"未来也许"
        actions.append(
            createSwipeAction(title: "下一步", statusValue: TaskStatus.na.rawValue, icon: "arrow.right", color: .blue)
        )
        
        actions.append(
            createSwipeAction(title: "未来也许", statusValue: TaskStatus.smb.rawValue, icon: "calendar", color: .purple)
        )
        
    case TaskStatus.smb.rawValue:
        // 添加"下一步"和"等待中"
        actions.append(
            createSwipeAction(title: "下一步", statusValue: TaskStatus.na.rawValue, icon: "arrow.right", color: .blue)
        )
        
        actions.append(
            createSwipeAction(title: "等待中", statusValue: TaskStatus.waiting.rawValue, icon: "hourglass", color: .orange)
        )
        
    default:
        break
    }
    
    return actions
}

// 创建右滑操作 (从 TaskSectionView 复制并适配)
private func createTrailingSwipeActions(for task: Task) -> [TaskSwipeAction] {
    // 左滑操作只包含删除按钮
    let deleteAction = createSwipeAction(title: "删除", statusValue: "delete", icon: "trash", color: .red)
    
    // 左滑只显示删除操作
    return [deleteAction]
}

// 创建滑动操作辅助方法 (从 TaskSectionView 复制并适配)
private func createSwipeAction(title: String, statusValue: String? = nil, icon: String, color: Color) -> TaskSwipeAction {
    return TaskSwipeAction(
        title: title,
        statusValue: statusValue ?? title, // 使用明确的状态值或默认使用标题
        icon: icon,
        color: color
    ) {
        // 这个闭包在 TaskCardView 内部不会被直接使用，因为实际操作由 onTaskIntent 处理
        // 但 TaskSwipeAction 结构需要它
        return true 
    }
}
