import SwiftUI
import SwiftData

/// 标签详情视图 V2 - 展示与标签关联的任务
struct TagDetailViewV2: View {
    // MARK: - 依赖属性
    @StateObject private var viewModel: TagDetailViewModel
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    @State private var showingDeleteConfirmation = false
    @State private var showingRenameTag = false
    @State private var newTagName = ""
    
    // MARK: - 状态管理
    @State private var naExpanded: Bool = true
    @State private var waitingExpanded: Bool = true
    @State private var inboxExpanded: Bool = true
    @State private var smbExpanded: Bool = true
    @State private var doneExpanded: Bool = false
    
    // MARK: - 初始化
    init(
        tag: Tag,
        taskManager: TaskManager = DependencyContainer.taskManager()
    ) {
        _viewModel = StateObject(wrappedValue: TagDetailViewModel(
            tag: tag,
            taskManager: taskManager
        ))
        _newTagName = State(initialValue: tag.name)
    }
    
    // MARK: - 主视图
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 0) {
                Divider()
                
                if viewModel.naTasks.isEmpty &&
                   viewModel.waitingTasks.isEmpty &&
                   viewModel.inboxTasks.isEmpty &&
                   viewModel.smbTasks.isEmpty /* && viewModel.doneTasks.isEmpty */ {
                    emptyTasksView
                        .padding(.top, 16)
                } else {
                    taskListView
                        .padding(.top, 16)
                }
            }
            .padding(.bottom, 80)
        }
        .refreshable {
            await viewModel.refreshTasks()
        }
        .navigationTitle("#\(viewModel.tag.name)")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                tagOptionsMenu
            }
            
            ToolbarItem(placement: .principal) {
                Text("#\(viewModel.tag.name)")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(Color(hex: viewModel.tag.color ?? "6366F1"))
            }
        }
        .toolbarBackground(Color(.systemBackground), for: .navigationBar)
        .toolbarBackground(.automatic, for: .navigationBar)
        .alert("重命名标签", isPresented: $showingRenameTag) {
            TextField("标签名称", text: $newTagName)
            Button("取消", role: .cancel) {}
            Button("确定") {
                if !newTagName.isEmpty && newTagName != viewModel.tag.name {
                    viewModel.renameTag(newName: newTagName)
                    // 显示重命名成功的 Toast 通知
                    DependencyContainer.toastManager().showSuperLightInfo("标签已重命名为 \"\(newTagName)\"")
                }
            }
        }
        .alert(isPresented: $showingDeleteConfirmation) {
            Alert(
                title: Text("删除标签"),
                message: Text("确定要删除这个标签吗？此操作不可撤销，但不会删除关联的任务。"),
                primaryButton: .destructive(Text("删除")) {
                    viewModel.deleteTag(dismissAction: dismiss)
                    // 显示删除标签的 Toast 通知
                    DependencyContainer.toastManager().showSuperLightInfo("\"\(viewModel.tag.name)\" 标签已删除")
                },
                secondaryButton: .cancel()
            )
        }
        .onAppear {
            viewModel.loadTasks()
            // 添加埋点 - 标签详情视图进入事件
            AnalyticsService.shared.trackEvent(AnalyticsService.EventNames.tagViewEntered)
        }
    }
    
    // MARK: - 子视图组件
    
    // 创建隐藏标签的任务卡片样式
    private var tagDetailCardStyle: TaskCardStyle {
        var style = TaskCardStyle.standard
        style.showTags = false // 在标签详情页面中隐藏标签信息
        return style
    }
    
    /// 任务列表视图
    private var taskListView: some View {
        VStack(spacing: 16) {
            // 下一步任务
            if !viewModel.naTasks.isEmpty {
                TaskSectionView(
                    title: "下一步",
                    tasks: viewModel.naTasks,
                    status: TaskStatus.na.rawValue,
                    taskRepository: DependencyContainer.taskRepository(),
                    projectRepository: DependencyContainer.projectRepository(),
                    tagRepository: DependencyContainer.tagRepository(),
                    style: tagDetailCardStyle,
                    isExpanded: $naExpanded,
                    onTaskIntent: handleTaskIntent
                )
            }
            
            // 等待中任务
            if !viewModel.waitingTasks.isEmpty {
                TaskSectionView(
                    title: "等待中",
                    tasks: viewModel.waitingTasks,
                    status: TaskStatus.waiting.rawValue,
                    taskRepository: DependencyContainer.taskRepository(),
                    projectRepository: DependencyContainer.projectRepository(),
                    tagRepository: DependencyContainer.tagRepository(),
                    style: tagDetailCardStyle,
                    isExpanded: $waitingExpanded,
                    onTaskIntent: handleTaskIntent
                )
            }
            
            // 收集箱任务
            if !viewModel.inboxTasks.isEmpty {
                TaskSectionView(
                    title: "收集箱",
                    tasks: viewModel.inboxTasks,
                    status: TaskStatus.inbox.rawValue,
                    taskRepository: DependencyContainer.taskRepository(),
                    projectRepository: DependencyContainer.projectRepository(),
                    tagRepository: DependencyContainer.tagRepository(),
                    style: tagDetailCardStyle,
                    isExpanded: $inboxExpanded,
                    onTaskIntent: handleTaskIntent
                )
            }
            
            // 未来也许任务
            if !viewModel.smbTasks.isEmpty {
                TaskSectionView(
                    title: "未来也许",
                    tasks: viewModel.smbTasks,
                    status: TaskStatus.smb.rawValue,
                    taskRepository: DependencyContainer.taskRepository(),
                    projectRepository: DependencyContainer.projectRepository(),
                    tagRepository: DependencyContainer.tagRepository(),
                    style: tagDetailCardStyle,
                    isExpanded: $smbExpanded,
                    onTaskIntent: handleTaskIntent
                )
            }
            
            // 已完成任务
            if !viewModel.doneTasks.isEmpty && viewModel.showCompletedTasks {
                TaskSectionView(
                    title: "已完成",
                    tasks: viewModel.doneTasks,
                    status: TaskStatus.done.rawValue,
                    taskRepository: DependencyContainer.taskRepository(),
                    projectRepository: DependencyContainer.projectRepository(),
                    tagRepository: DependencyContainer.tagRepository(),
                    style: {
                        var style = TaskCardStyle.completed
                        style.showTags = false
                        return style
                    }(),
                    isExpanded: $doneExpanded,
                    // 已完成任务只允许删除操作
                    onTaskIntent: { intent in
                        if case .delete = intent {
                            viewModel.handleTaskIntent(intent)
                        }
                    }
                )
            }
        }
    }
    
    // 添加处理任务意图的辅助方法
    private func handleTaskIntent(_ intent: TaskIntent) {
        viewModel.handleTaskIntent(intent)
    }
    
    /// 空任务视图
    private var emptyTasksView: some View {
        VStack(spacing: 12) {
            Image(systemName: "tray")
                .font(.system(size: 36))
                .foregroundColor(.secondary)
            
            Text("没有关联的行动")
                .font(.headline)
                .foregroundColor(.primary)
            
            Text("为行动添加此标签，它们将显示在这里")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 24)
    }
    
    /// 标签选项菜单
    private var tagOptionsMenu: some View {
        Menu {
            Button(action: {
                showingRenameTag = true
            }) {
                Label("重命名", systemImage: "pencil")
            }
            
            Toggle(isOn: $viewModel.showCompletedTasks) {
                Label("显示已完成", systemImage: "checkmark.circle")
            }
            
            Button(role: .destructive, action: {
                showingDeleteConfirmation = true
            }) {
                Label("删除", systemImage: "trash")
            }
        } label: {
            Image(systemName: "ellipsis")
                .font(.system(size: 16))
                .foregroundColor(.primary)
                .frame(width: 30, height: 30)
        }
    }
}

// MARK: - 预览
#Preview {
    let tag = Tag(name: "工作", color: "6366F1")
    return TagDetailViewV2(tag: tag)
}
