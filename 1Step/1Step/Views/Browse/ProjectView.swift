import SwiftUI
import SwiftData

struct ProjectView: View {
    // MARK: - 依赖
    private let projectRepository: ProjectRepository
    private let taskRepository: TaskRepository
    private let tagRepository: TagRepository
    
    // MARK: - 环境变量
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - 状态管理
    @State private var projects: [Project] = []
    @State private var newProjectName: String = ""
    @State private var projectToRename: Project?
    @State private var renamedProjectName: String = ""
    @State private var isShowingRenameProject: Bool = false
    @State private var isShowingAddProject: Bool = false
    @State private var searchText: String = ""
    @State private var isSearching: Bool = false
    
    // MARK: - 初始化方法
    init(
        projectRepository: ProjectRepository = DependencyContainer.projectRepository(),
        taskRepository: TaskRepository = DependencyContainer.taskRepository(),
        tagRepository: TagRepository = DependencyContainer.tagRepository()
    ) {
        self.projectRepository = projectRepository
        self.taskRepository = taskRepository
        self.tagRepository = tagRepository
    }
    
    var body: some View {
        ZStack(alignment: .bottom) {
            VStack(spacing: 0) {
                // 搜索栏
                if isSearching {
                    searchBarView
                        .transition(.move(edge: .top).combined(with: .opacity))
                }
                
                // 项目列表
                ScrollView {
                    LazyVStack(spacing: 0) {
                        ForEach(filteredProjects) { project in
                            projectRow(project: project)
                        }
                        
                        // 空白占位，为了底部按钮留出空间
                        Color.clear.frame(height: 80)
                    }
                    .padding(.top, 8)
                }
                .refreshable {
                    await refreshProjects()
                }
            }
            .navigationTitle("项目")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    // 搜索按钮
                    Button(action: {
                        withAnimation {
                            isSearching.toggle()
                            if !isSearching {
                                searchText = ""
                            }
                        }
                    }) {
                        Image(systemName: isSearching ? "xmark" : "magnifyingglass")
                            .foregroundColor(isSearching ? .accentColor : .primary)
                    }
                }
            }
            .toolbarBackground(Color(.systemBackground), for: .navigationBar)
            .toolbarBackground(.automatic, for: .navigationBar)
            
            // 添加项目按钮
            addButton
        }
        .sheet(isPresented: $isShowingRenameProject) {
            renameProjectSheet
        }
        .sheet(isPresented: $isShowingAddProject) {
            addProjectSheet
        }
        .onAppear {
            loadProjects()
        }
    }
    
    // MARK: - 子视图组件
    
    // 搜索栏
    private var searchBarView: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("搜索项目", text: $searchText)
                .font(.system(size: 15))
                .autocapitalization(.none)
                .disableAutocorrection(true)
            
            if !searchText.isEmpty {
                Button {
                    searchText = ""
                } label: {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 8)
        .background(Color(.systemGray6).opacity(0.5))
        .cornerRadius(8)
        .padding(.horizontal, 20)
        .padding(.bottom, 10)
    }
    
    // 项目行
    private func projectRow(project: Project) -> some View {
        NavigationLink(destination: ProjectDetailView(
            project: project,
            taskRepository: taskRepository,
            projectRepository: projectRepository,
            tagRepository: tagRepository
        )) {
            HStack(spacing: 16) {
                // 项目颜色标记
                Circle()
                    .fill(Color(hex: project.color ?? "6366F1"))
                    .frame(width: 12, height: 12)
                
                // 项目名称
                Text(project.name)
                    .font(.system(size: 16))
                    .foregroundColor(.primary)
                    .lineLimit(1)
                
                Spacer()
                
                // 导航箭头
                Image(systemName: "chevron.right")
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 14)
        }
        .contentShape(Rectangle())
        .contextMenu {
            Button(action: {
                projectToRename = project
                renamedProjectName = project.name
                isShowingRenameProject = true
            }) {
                Label("重命名", systemImage: "pencil")
            }
            
            Button(role: .destructive, action: {
                deleteProject(project)
            }) {
                Label("删除", systemImage: "trash")
            }
        }
        .swipeActions(edge: .trailing) {
            Button(action: {
                projectToRename = project
                renamedProjectName = project.name
                isShowingRenameProject = true
            }) {
                Label("重命名", systemImage: "pencil")
            }
            .tint(.blue)
            
            Button(role: .destructive, action: {
                deleteProject(project)
            }) {
                Label("删除", systemImage: "trash")
            }
        }
    }
    
    // 添加按钮
    private var addButton: some View {
        Button(action: {
            isShowingAddProject = true
        }) {
            ZStack {
                Circle()
                    .fill(AppColors.UI.primary(for: colorScheme))
                    .frame(width: 50, height: 50)
                    .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                
                Image(systemName: "plus")
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(.white)
            }
        }
        .padding(.bottom, 20)
    }
    
    // 添加项目弹窗
    private var addProjectSheet: some View {
        NavigationView {
            VStack(spacing: 16) {
                TextField("项目名称", text: $newProjectName)
                    .padding(.horizontal)
                    .padding(.vertical, 10)
                    .background(Color(.systemGray6).opacity(0.3))
                    .cornerRadius(8)
                
                Spacer()
            }
            .padding()
            .navigationTitle("新建项目")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("取消") {
                        isShowingAddProject = false
                        newProjectName = ""
                    }
                }
                
                ToolbarItem(placement: .confirmationAction) {
                    Button("添加") {
                        addProject()
                        isShowingAddProject = false
                    }
                    .disabled(newProjectName.isEmpty)
                }
            }
        }
        .presentationDetents([.height(200)])
    }
    
    // 重命名项目弹窗
    private var renameProjectSheet: some View {
        VStack(spacing: 20) {
            Text("重命名项目")
                .font(.headline)
                .padding(.top, 20)
            
            TextField("项目名称", text: $renamedProjectName)
                .padding()
                .background(Color(.systemGray6).opacity(0.5))
                .cornerRadius(8)
                .padding(.horizontal)
            
            HStack(spacing: 20) {
                Button("取消") {
                    isShowingRenameProject = false
                }
                .foregroundColor(.secondary)
                
                Button("保存") {
                    if let project = projectToRename {
                        let oldName = project.name
                        project.name = renamedProjectName
                        projectRepository.updateProject(project)
                        loadProjects()
                        isShowingRenameProject = false
                        
                        // 显示重命名成功的 Toast 通知
                        DependencyContainer.toastManager().showSuperLightInfo("项目已重命名为 \"\(project.name)\"")
                    }
                }
                .disabled(renamedProjectName.isEmpty)
                .foregroundColor(renamedProjectName.isEmpty ? .gray : .accentColor)
            }
            .padding(.bottom, 20)
        }
        .frame(maxWidth: .infinity)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .padding()
        .presentationDetents([.medium])
    }
    
    // MARK: - 辅助方法
    
    private func loadProjects() {
        projects = projectRepository.getAllProjects()
    }
    
    @MainActor
    private func refreshProjects() async {
        loadProjects()
        // 显示轻量级刷新提示
        DependencyContainer.toastManager().showSuperLightInfo("已刷新")
    }
    
    private func addProject() {
        guard !newProjectName.isEmpty else { return }
        
        let project = Project(name: newProjectName, color: randomColor())
        projectRepository.addProject(project)
        newProjectName = ""
        loadProjects()
        
        // 显示添加项目成功的 Toast 通知
        DependencyContainer.toastManager().showSuperLightInfo("\"\(project.name)\" 项目已创建")
    }
    
    private func deleteProject(_ project: Project) {
        projectRepository.deleteProject(project)
        loadProjects()
        // 显示删除项目的 Toast 通知
        DependencyContainer.toastManager().showSuperLightInfo("\"\(project.name)\" 项目已删除")
    }
    
    // 随机颜色生成
    private func randomColor() -> String {
        let colors = ["6366F1", "F43F5E", "10B981", "3B82F6", "8B5CF6"]
        return colors.randomElement() ?? "6366F1"
    }
    
    // 过滤后的项目列表
    private var filteredProjects: [Project] {
        if searchText.isEmpty {
            return projects
        } else {
            return projects.filter { $0.name.localizedCaseInsensitiveContains(searchText) }
        }
    }
    
    // 移除不再使用的 getTaskCountForProject 方法
}

// MARK: - 预览
struct ProjectView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            ProjectView()
                .modelContainer(for: [Task.self, Project.self])
        }
    }
}
