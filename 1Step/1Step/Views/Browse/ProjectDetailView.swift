import SwiftUI
import SwiftData
import UIKit
// 确保 Markdown 组件可以被正确引用
@_implementationOnly import class UIKit.UIImpactFeedbackGenerator

/// 项目详情视图 - 使用上下展开列表替代左右页签
struct ProjectDetailView: View {
    // MARK: - 依赖属性
    @StateObject private var viewModel: ProjectDetailViewModel
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    @State private var showingDeleteConfirmation = false
    @State private var taskToRecover: Task? = nil
    @State private var showingRecoverConfirmation = false
    @State private var isDescriptionExpanded = false
    @FocusState private var isTextEditorFocused: Bool // 跟踪 TextEditor 焦点状态

    
    // MARK: - 初始化
    init(
        project: Project,
        taskRepository: TaskRepository,
        projectRepository: ProjectRepository,
        tagRepository: TagRepository
    ) {
        _viewModel = StateObject(wrappedValue: ProjectDetailViewModel(
            project: project,
            taskManager: DependencyContainer.taskManager()
        ))
    }
    
    // MARK: - 主视图
    var body: some View {
        mainContentView
            .navigationTitle("")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .principal) {
                    Text(viewModel.project.name)
                        .font(.headline)
                        .foregroundColor(Color(hex: viewModel.project.color ?? "6366F1"))
                }
            }
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    toolbarContent
                }
            }
            .toolbarBackground(Color(.systemBackground), for: .navigationBar)
            .toolbarBackground(.automatic, for: .navigationBar)
            .sheet(isPresented: viewModel.editProjectBinding) {
                EditProjectSheetView(
                    project: viewModel.project, 
                    isPresented: viewModel.editProjectBinding
                )
                .presentationDetents([.medium])
            }
            .overlay {
                overlayContent
            }
            .alert(isPresented: $showingDeleteConfirmation) {
                deleteAlert
            }
            .alert("恢复这一步？", isPresented: $showingRecoverConfirmation) {
                recoverAlert
            } message: {
                Text("该行动将回到「下一步」，你可以重新推进它。")
            }
            .onAppear {
                AnalyticsService.shared.trackEvent(AnalyticsService.EventNames.projectViewEntered)
            }
    }
    
    // MARK: - 子视图组件
    
    private var mainContentView: some View {
        ZStack(alignment: .top) {
            scrollContentView
            
            if isDescriptionExpanded {
                descriptionOverlayView
            }
        }
    }
    
    private var scrollContentView: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 0) {
                expandButton
                
                taskSectionView
                    .padding(.top, 16)
            }
            .padding(.bottom, 80)
        }
        .refreshable {
            await viewModel.refreshTasks()
        }
    }
    
    private var expandButton: some View {
        Button(action: {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                isDescriptionExpanded.toggle()
            }
        }) {
            HStack {
                Spacer()
                Image(systemName: isDescriptionExpanded ? "chevron.up" : "chevron.down")
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(.secondary)
                Spacer()
            }
            .frame(height: 32)
            .contentShape(Rectangle())
        }
        .buttonStyle(.plain)
    }
    
    private var descriptionOverlayView: some View {
        VStack(spacing: 0) {
            MarkdownTextEditor(
                text: projectNotesBinding,
                placeholder: "添加项目描述...",
                onSave: {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                        isDescriptionExpanded = false
                    }
                }
            )
            .focused($isTextEditorFocused)
        }
        .background(descriptionBackground)
        .overlay(descriptionBorder)
        .padding(.horizontal, 16)
        .padding(.top, 36)
        .padding(.bottom, 8)
        .transition(.move(edge: .top).combined(with: .opacity))
        .zIndex(1)
        .onTapGesture {}
        .gesture(dismissGesture)
    }
    
    private var projectNotesBinding: Binding<String> {
        Binding(
            get: { viewModel.project.notes },
            set: { newValue in
                viewModel.project.notes = newValue
                viewModel.project.updatedAt = Date()
                viewModel.updateProject(viewModel.project)
            }
        )
    }
    
    private var descriptionBackground: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(Color(.systemBackground))
            .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 2)
    }
    
    private var descriptionBorder: some View {
        RoundedRectangle(cornerRadius: 12)
            .stroke(Color(.systemGray5), lineWidth: 0.5)
    }
    
    private var dismissGesture: some Gesture {
        DragGesture(minimumDistance: 20, coordinateSpace: .local)
            .onEnded { value in
                if value.translation.height > 0 {
                    withAnimation(.spring(response: 0.3)) {
                        isDescriptionExpanded = false
                    }
                }
            }
    }
    
    private var toolbarContent: some View {
        HStack(spacing: 16) {
            NavigationLink(destination: ProjectBackboardView(project: viewModel.project)) {
                Image(systemName: "tornado")
                    .font(.system(size: 17))
                    .foregroundColor(.primary)
            }
            
            projectOptionsMenu
        }
    }
    
    private var deleteAlert: Alert {
        Alert(
            title: Text("删除项目"),
            message: Text("确定要删除这个项目吗？该项目下的所有任务也会被删除。此操作不可撤销。"),
            primaryButton: .destructive(Text("删除")) {
                viewModel.deleteProject(dismissAction: dismiss)
                DependencyContainer.toastManager().showSuperLightInfo("\"\(viewModel.project.name)\" 项目已删除")
            },
            secondaryButton: .cancel()
        )
    }
    
    private var recoverAlert: some View {
        Group {
            Button("取消", role: .cancel) { }
            Button("恢复") {
                if let task = taskToRecover {
                    viewModel.recoverTask(task)
                    DependencyContainer.toastManager().showSuperLightInfo("已恢复，已放入下一步")
                }
            }
        }
    }
    
    private var taskSectionView: some View {
        VStack(spacing: 16) {
            if viewModel.hasNoTasks {
                emptyTasksView
            }
            
            // 下一步任务
            if !viewModel.naTasks.isEmpty {
                TaskSectionView(
                    title: "下一步",
                    tasks: viewModel.naTasks,
                    status: TaskStatus.na.rawValue,
                    taskRepository: DependencyContainer.taskRepository(),
                    projectRepository: DependencyContainer.projectRepository(),
                    tagRepository: DependencyContainer.tagRepository(),
                    style: projectDetailCardStyle,
                    isExpanded: $viewModel.naExpanded,
                    onTaskIntent: handleTaskIntent
                )
            }
            
            // 等待中任务
            if !viewModel.waitingTasks.isEmpty {
                TaskSectionView(
                    title: "等待中",
                    tasks: viewModel.waitingTasks,
                    status: TaskStatus.waiting.rawValue,
                    taskRepository: DependencyContainer.taskRepository(),
                    projectRepository: DependencyContainer.projectRepository(),
                    tagRepository: DependencyContainer.tagRepository(),
                    style: projectDetailCardStyle,
                    isExpanded: $viewModel.waitingExpanded,
                    onTaskIntent: handleTaskIntent
                )
            }
            
            // 收集箱任务
            if !viewModel.inboxTasks.isEmpty {
                TaskSectionView(
                    title: "收集箱",
                    tasks: viewModel.inboxTasks,
                    status: TaskStatus.inbox.rawValue,
                    taskRepository: DependencyContainer.taskRepository(),
                    projectRepository: DependencyContainer.projectRepository(),
                    tagRepository: DependencyContainer.tagRepository(),
                    style: projectDetailCardStyle,
                    isExpanded: $viewModel.inboxExpanded,
                    onTaskIntent: handleTaskIntent
                )
            }
            
            // 将来也许任务
            if !viewModel.smbTasks.isEmpty {
                TaskSectionView(
                    title: "将来也许",
                    tasks: viewModel.smbTasks,
                    status: TaskStatus.smb.rawValue,
                    taskRepository: DependencyContainer.taskRepository(),
                    projectRepository: DependencyContainer.projectRepository(),
                    tagRepository: DependencyContainer.tagRepository(),
                    style: projectDetailCardStyle,
                    isExpanded: $viewModel.smbExpanded,
                    onTaskIntent: handleTaskIntent
                )
            }
            
            // 已完成任务
            if !viewModel.doneTasks.isEmpty && viewModel.showCompletedTasks {
                VStack(spacing: 8) {
                    TaskSectionView(
                        title: "已完成",
                        tasks: viewModel.doneTasks,
                        status: TaskStatus.done.rawValue,
                        taskRepository: DependencyContainer.taskRepository(),
                        projectRepository: DependencyContainer.projectRepository(),
                        tagRepository: DependencyContainer.tagRepository(),
                        style: {
                            var style = TaskCardStyle.completed
                            style.showProject = false
                            return style
                        }(),
                        isExpanded: $viewModel.doneExpanded,
                        onTaskIntent: handleCompletedTaskIntent
                    )
                    
                    if viewModel.hasMoreCompletedTasks {
                        Button(action: {
                            viewModel.loadMoreCompletedTasks()
                        }) {
                            HStack {
                                if viewModel.isLoadingMoreTasks {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                }
                                Text("加载更多")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 8)
                            }
                        .buttonStyle(.plain)
                    }
                }
            }
        }
    }
    
    // 添加处理任务意图的辅助方法
    private func handleTaskIntent(_ intent: TaskIntent) {
        viewModel.handleTaskIntent(intent)
    }
    
    // 处理已完成任务的意图
    private func handleCompletedTaskIntent(_ intent: TaskIntent) {
        switch intent {
        case .changeStatus(let task, let newStatus):
            if newStatus == TaskStatus.na.rawValue {
                if viewModel.isTaskRecoverable(task) {
                    // 显示确认弹窗
                    taskToRecover = task
                    showingRecoverConfirmation = true
                } else {
                    // 显示无法恢复提示
                    DependencyContainer.toastManager().showWarning("已经太久啦，无法恢复了")
                }
            }
        default:
            break
        }
    }
    
    private var projectOptionsMenu: some View {
        Menu {
            Button(action: { viewModel.editProject() }) {
                Label("编辑项目", systemImage: "pencil")
            }
            
            Toggle(isOn: $viewModel.showCompletedTasks) {
                Label("显示已完成", systemImage: "checkmark.circle")
            }
            
            Button(role: .destructive, action: {
                showingDeleteConfirmation = true
            }) {
                Label("删除项目", systemImage: "trash")
            }
        } label: {
            Image(systemName: "ellipsis")
        }
    }
    
    private var overlayContent: some View {
        ZStack {
            // Echo Drop 浮层
            if viewModel.showingEchoDrop {
                EchoDropView(viewModel: viewModel.createNextActionsViewModel())
            }

            // 添加任务浮层
            if viewModel.showingAddTask {
                addTaskOverlay
            }

            // 添加按钮或返回按钮 - 固定在右下角
            if !viewModel.showingAddTask && !viewModel.showingEchoDrop {
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        // 根据状态显示不同按钮
                        if isDescriptionExpanded && !isTextEditorFocused {
                            // 返回按钮
                            Button {
                                withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                    isDescriptionExpanded = false
                                }
                            } label: {
                                ZStack {
                                    Circle()
                                        .fill(Color.orange)
                                        .frame(width: 56, height: 56)
                                        .shadow(radius: 4)
                                    
                                    Image(systemName: "chevron.backward")
                                        .foregroundColor(.white)
                                        .font(.system(size: 20, weight: .bold))
                                }
                            }
                            .transition(.opacity)
                            .padding(.trailing, 20)
                            .padding(.bottom, 20)
                        } else if !isDescriptionExpanded {
                            // 添加按钮（仅在描述未展开时显示）
                            addButton
                                .padding(.trailing, 20)
                                .padding(.bottom, 20)
                        }
                    }
                }
                .animation(.easeInOut(duration: 0.2), value: isDescriptionExpanded)
                .animation(.easeInOut(duration: 0.2), value: isTextEditorFocused)
            }
        }
    }
    
    private var addTaskOverlay: some View {
        let taskFormViewModel = TaskFormViewModel(
            taskManager: DependencyContainer.taskManager()
        )
        
        taskFormViewModel.taskProject = viewModel.project.id
        taskFormViewModel.onTaskAdded = { _, message in
            // 显示 Toast 消息
            // 注意：这里不使用 Toast 消息，因为 ProjectDetailViewModel 可能没有相关属性
            
            // 触感反馈
            UIImpactFeedbackGenerator(style: .medium).impactOccurred()
            
            // 重新加载任务列表，但不关闭添加任务界面
            viewModel.loadTasks()
            
            // 重新请求键盘聚焦，便于用户继续输入
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                NotificationCenter.default.post(name: .requestKeyboardFocus, object: nil)
            }
        }
        taskFormViewModel.onDismiss = {
            viewModel.showingAddTask = false
        }
        
        return AddTaskFormView(viewModel: taskFormViewModel)
    }
    
    private var addButton: some View {
        ZStack {
            Image(systemName: "plus")
                .font(.system(size: 20, weight: .medium))
                .foregroundColor(.white)
                .frame(width: 50, height: 50)
                .background(AppColors.UI.primary(for: colorScheme))
                .clipShape(Circle())
                .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        }
        .gesture(
            LongPressGesture(minimumDuration: 1.0)
                .onEnded { _ in
                    HapticManager.shared.impact(style: .medium)
                    withAnimation {
                        viewModel.showEchoDrop()
                    }
                }
        )
        .onTapGesture {
            withAnimation {
                viewModel.showingAddTask = true
            }
        }
    }
    
    // MARK: - 辅助方法
    
    private var emptyTasksView: some View {
        VStack(spacing: 12) {
            Image(systemName: "tray")
                .font(.system(size: 36))
                .foregroundColor(.secondary)
            
            Text("没有关联的行动")
                .font(.headline)
                .foregroundColor(.primary)
            
            Text("添加行动到这个项目，它们将显示在这里")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 24)
    }
    
    // 创建隐藏项目的任务卡片样式
    private var projectDetailCardStyle: TaskCardStyle {
        var style = TaskCardStyle.standard
        style.showProject = false // 在项目详情页面中隐藏项目信息
        return style
    }
}

// MARK: - View 扩展
extension View {
    func placeholder<Content: View>(
        when shouldShow: Bool,
        alignment: Alignment = .leading,
        @ViewBuilder placeholder: () -> Content) -> some View {

        ZStack(alignment: alignment) {
            placeholder().opacity(shouldShow ? 1 : 0)
            self
        }
    }
}

// MARK: - 项目编辑弹窗视图
struct EditProjectSheetView: View {
    var project: Project
    @Binding var isPresented: Bool
    
    private let projectRepository: ProjectRepository
    @State private var projectName: String
    @State private var showDeleteConfirmation = false
    
    init(
        project: Project, 
        isPresented: Binding<Bool>,
        projectRepository: ProjectRepository = DependencyContainer.projectRepository()
    ) {
        self.project = project
        self._isPresented = isPresented
        self._projectName = State(initialValue: project.name)
        self.projectRepository = projectRepository
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("项目信息")) {
                    TextField("项目名称", text: $projectName)
                }
                
                Section {
                    Button(action: {
                        project.isArchived.toggle()
                        projectRepository.saveProject(project)
                        isPresented = false
                    }) {
                        HStack {
                            Text(project.isArchived ? "取消归档" : "归档项目")
                            Spacer()
                            Image(systemName: project.isArchived ? "tray.and.arrow.up" : "archivebox")
                        }
                    }
                    .foregroundColor(.blue)
                    
                    Button(action: {
                        showDeleteConfirmation = true
                    }) {
                        HStack {
                            Text("删除项目")
                            Spacer()
                            Image(systemName: "trash")
                        }
                    }
                    .foregroundColor(.red)
                }
            }
            .navigationTitle("编辑项目")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("取消") {
                        isPresented = false
                    }
                }
                
                ToolbarItem(placement: .confirmationAction) {
                    Button("保存") {
                        project.name = projectName
                        project.updatedAt = Date()
                        projectRepository.saveProject(project)
                        isPresented = false
                        // 显示项目更新成功的 Toast 通知
                        DependencyContainer.toastManager().showSuperLightInfo("项目已更新")
                    }
                    .disabled(projectName.isEmpty)
                }
            }
            .alert("确认删除", isPresented: $showDeleteConfirmation) {
                Button("取消", role: .cancel) { }
                Button("删除", role: .destructive) {
                    // 删除项目
                    projectRepository.deleteProject(project)
                    isPresented = false
                    // 显示删除项目的 Toast 通知
                    DependencyContainer.toastManager().showSuperLightInfo("\"\(project.name)\" 项目已删除")
                }
            } message: {
                Text("删除项目将不会删除关联的行动，但它们将不再与此项目关联。")
            }
        }
    }
}
