import SwiftUI
import SwiftData

/// 下一步行动页面 (修改自 InboxView)
struct NextActionsView: View {
    // MARK: - 依赖
    private let taskRepository: TaskRepository
    private let projectRepository: ProjectRepository
    private let tagRepository: TagRepository
    
    // MARK: - 环境变量
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - 状态变量
    @StateObject private var viewModel: NextActionsViewModel
    @StateObject private var formCoordinator = TaskFormCoordinator.shared
    @FocusState private var isTitleFocused: Bool
    
    // 初始化方法
    init(
        taskRepository: TaskRepository = DependencyContainer.taskRepository(),
        projectRepository: ProjectRepository = DependencyContainer.projectRepository(),
        tagRepository: TagRepository = DependencyContainer.tagRepository()
    ) {
        self.taskRepository = taskRepository
        self.projectRepository = projectRepository
        self.tagRepository = tagRepository
        
        _viewModel = StateObject(wrappedValue: NextActionsViewModel())
    }
    
    // MARK: - 视图体
    var body: some View {
        ZStack {
            VStack(spacing: 0) {
                if viewModel.isSearchFocused {
                    searchBarView
                }
                
                ScrollView {
                    tasksListView
                        .padding(.bottom, formCoordinator.isShowing ? 0 : 10)
                }
                .refreshable {
                    await viewModel.refreshTasks()
                }
            }
            .navigationTitle("下一步")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItemGroup(placement: .navigationBarTrailing) {
                    Button {
                        withAnimation {
                            viewModel.isSearchFocused.toggle()
                        }
                    } label: {
                        Image(systemName: viewModel.isSearchFocused ? "xmark" : "magnifyingglass")
                            .foregroundColor(viewModel.isSearchFocused ? .accentColor : AppColors.UI.primaryText(for: colorScheme))
                    }
                }
            }
            .toolbarBackground(Color(.systemBackground), for: .navigationBar)
            .toolbarBackground(.automatic, for: .navigationBar)
            .tint(AppColors.UI.primaryText(for: colorScheme))
            .onAppear {
                viewModel.loadTasks()
                // 添加埋点 - 下一步视图进入事件
                AnalyticsService.shared.trackEvent(AnalyticsService.EventNames.nextViewEntered)
            }
            
            if !formCoordinator.isShowing {
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        Button(action: { formCoordinator.showForm() }) {
                            Image(systemName: "plus")
                                .foregroundColor(.white)
                                .frame(width: 56, height: 56)
                                .background(AppColors.Status.nextAction(for: colorScheme))
                                .clipShape(Circle())
                                .shadow(radius: 4)
                        }
                        .padding()
                    }
                }
            }
            
            if formCoordinator.isShowing {
                taskFormOverlay
            }
            
            if viewModel.showToast {
                toastOverlay
            }
        }
    }
    
    // MARK: - 子视图
    
    private var searchBarView: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("搜索下一步行动", text: $viewModel.searchText)
                .font(.system(size: 15))
                .autocapitalization(.none)
                .disableAutocorrection(true)
                .focused($isTitleFocused)
            
            if !viewModel.searchText.isEmpty {
                Button {
                    viewModel.searchText = ""
                } label: {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 8)
        .background(Color(.systemGray6).opacity(0.5))
        .cornerRadius(8)
        .padding(.horizontal, 20)
        .padding(.bottom, 10)
    }
    
    private var tasksListView: some View {
        VStack(spacing: 0) {
            TaskSectionView(
                title: "",
                tasks: viewModel.tasks,
                status: TaskStatus.na.rawValue,
                taskRepository: taskRepository,
                projectRepository: projectRepository,
                tagRepository: tagRepository,
                titleColor: AppColors.Status.nextAction(for: colorScheme),
                isExpanded: .constant(true),
                showExpandButton: false,
                showEmptyState: true,
                onTaskIntent: { intent in
                    viewModel.handleTaskIntent(intent)
                }
            )
        }
    }
    
    private var taskFormOverlay: some View {
        let taskFormViewModel = TaskFormViewModel(
            initialStatus: TaskStatus.na.rawValue,
            taskManager: DependencyContainer.taskManager()
        )
        
        taskFormViewModel.onTaskAdded = { task, message in
            UIImpactFeedbackGenerator(style: .medium).impactOccurred()
            viewModel.loadTasks()
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                NotificationCenter.default.post(name: .requestKeyboardFocus, object: nil)
            }
        }
        
        taskFormViewModel.onDismiss = {
            formCoordinator.hideForm()
        }
        
        return AddTaskFormView(viewModel: taskFormViewModel)
    }
    
    private var toastOverlay: some View {
        VStack {
            Spacer()
                .frame(height: 50)
            
            Text(viewModel.toastMessage)
                .font(.footnote)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(colorScheme == .dark ? 
                              Color.white.opacity(0.2) : 
                                Color.black.opacity(0.06))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(colorScheme == .dark ? 
                                        Color.white.opacity(0.2) : 
                                            Color.black.opacity(0.1),
                                        lineWidth: 0.5)
                        )
                )
                .foregroundColor(colorScheme == .dark ? .white : .black)
                .transition(.move(edge: .top).combined(with: .opacity))
            
            Spacer()
        }
        .animation(.spring(response: 0.4), value: viewModel.showToast)
        .zIndex(100)
    }
}

// MARK: - 预览
#Preview {
    NavigationView {
        NextActionsView()
            .modelContainer(for: [Task.self, Project.self, Tag.self])
    }
}
 