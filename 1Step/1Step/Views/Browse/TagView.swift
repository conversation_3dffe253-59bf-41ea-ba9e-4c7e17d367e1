import SwiftUI
import SwiftData

/// 标签管理页面
struct TagView: View {
    // MARK: - 依赖
    private let tagRepository: TagRepository
    
    // MARK: - 环境变量
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - 状态管理
    @StateObject private var viewModel: TagViewModel
    @State private var isShowingAddTag = false
    @State private var newTagName = ""
    @State private var isShowingRenameTag = false
    @State private var tagToRename: Tag?
    @State private var renamedTagName = ""
    @State private var searchText: String = ""
    @State private var isSearching: Bool = false
    @State private var errorMessage = ""
    @State private var showingError = false
    
    // MARK: - 初始化方法
    init(tagRepository: TagRepository = DependencyContainer.tagRepository()) {
        self.tagRepository = tagRepository
        _viewModel = StateObject(wrappedValue: TagViewModel(tagRepository: tagRepository))
    }
    
    // MARK: - 视图
    var body: some View {
        ZStack(alignment: .bottom) {
            VStack(spacing: 0) {
                // 搜索栏
                if isSearching {
                    searchBarView
                        .transition(.move(edge: .top).combined(with: .opacity))
                }
                
                // 标签列表
                ScrollView {
                    LazyVStack(spacing: 0) {
                        if filteredTags.isEmpty {
                            emptyStateView
                                .padding(.top, 40)
                        } else {
                            ForEach(filteredTags) { tag in
                                tagRow(tag: tag)
                            }
                        }
                        
                        // 空白占位，为了底部按钮留出空间
                        Color.clear.frame(height: 80)
                    }
                    .padding(.top, 8)
                }
                .refreshable {
                    await refreshTags()
                }
            }
            .navigationTitle("标签")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    // 搜索按钮
                    Button(action: {
                        withAnimation {
                            isSearching.toggle()
                            if !isSearching {
                                searchText = ""
                            }
                        }
                    }) {
                        Image(systemName: isSearching ? "xmark" : "magnifyingglass")
                            .foregroundColor(isSearching ? .accentColor : .primary)
                    }
                }
            }
            .toolbarBackground(Color(.systemBackground), for: .navigationBar)
            .toolbarBackground(.automatic, for: .navigationBar)
            
            // 添加标签按钮
            addButton
        }
        .sheet(isPresented: $isShowingAddTag) {
            addTagSheet
        }
        .alert("重命名标签", isPresented: $isShowingRenameTag) {
            TextField("标签名称", text: $renamedTagName)
            Button("取消", role: .cancel) {}
            Button("确定") {
                if let tag = tagToRename, !renamedTagName.isEmpty {
                    let success = viewModel.renameTag(tag, newName: renamedTagName)
                    if !success {
                        errorMessage = "已经有这个标签啦"
                        showingError = true
                    }
                }
            }
        }
        .onAppear {
            viewModel.loadTags()
        }
        .alert("错误", isPresented: $showingError) {
            Button("确定", role: .cancel) {}
        } message: {
            Text(errorMessage)
        }
    }
    
    // MARK: - 计算属性
    
    // 过滤后的标签列表
    private var filteredTags: [Tag] {
        if searchText.isEmpty {
            return viewModel.tags
        } else {
            return viewModel.tags.filter { $0.name.localizedCaseInsensitiveContains(searchText) }
        }
    }
    
    // MARK: - 子视图组件
    
    // 搜索栏
    private var searchBarView: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("搜索标签", text: $searchText)
                .font(.system(size: 16))
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 8)
        .background(Color(.systemGray6).opacity(0.5))
        .cornerRadius(8)
        .padding(.horizontal, 20)
        .padding(.bottom, 10)
    }
    
    // 标签行
    private func tagRow(tag: Tag) -> some View {
        NavigationLink(destination: TagDetailViewV2(tag: tag)) {
            HStack(spacing: 16) {
                // 标签颜色标记
                Circle()
                    .fill(Color(hex: tag.color ?? "6366F1"))
                    .frame(width: 12, height: 12)
                
                // 标签名称
                Text("#\(tag.name)")
                    .font(.system(size: 16))
                    .foregroundColor(.primary)
                    .lineLimit(1)
                
                Spacer()
                
                // 导航箭头
                Image(systemName: "chevron.right")
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 14)
        }
        .contentShape(Rectangle())
        .contextMenu {
            Button(action: {
                tagToRename = tag
                renamedTagName = tag.name
                isShowingRenameTag = true
            }) {
                Label("重命名", systemImage: "pencil")
            }
            
            Button(role: .destructive, action: {
                deleteTag(tag)
            }) {
                Label("删除", systemImage: "trash")
            }
        }
        .swipeActions(edge: .trailing) {
            Button(action: {
                tagToRename = tag
                renamedTagName = tag.name
                isShowingRenameTag = true
            }) {
                Label("重命名", systemImage: "pencil")
            }
            .tint(.blue)
            
            Button(role: .destructive, action: {
                deleteTag(tag)
            }) {
                Label("删除", systemImage: "trash")
            }
        }
    }
    
    // 添加按钮
    private var addButton: some View {
        Button(action: {
            isShowingAddTag = true
        }) {
            ZStack {
                Circle()
                    .fill(AppColors.UI.primary(for: colorScheme))
                    .frame(width: 50, height: 50)
                    .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                
                Image(systemName: "plus")
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(.white)
            }
        }
        .padding(.bottom, 20)
    }
    
    // 添加标签弹窗
    private var addTagSheet: some View {
        NavigationView {
            VStack(spacing: 16) {
                TextField("标签名称", text: $newTagName)
                    .padding(.horizontal)
                    .padding(.vertical, 10)
                    .background(Color(.systemGray6).opacity(0.3))
                    .cornerRadius(8)
                
                Spacer()
            }
            .padding()
            .navigationTitle("新建标签")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("取消") {
                        isShowingAddTag = false
                        newTagName = ""
                    }
                }
                
                ToolbarItem(placement: .confirmationAction) {
                    Button("添加") {
                        if !newTagName.isEmpty {
                            let success = viewModel.addTag(name: newTagName, color: randomColor())
                            if success {
                                isShowingAddTag = false
                                newTagName = ""
                            } else {
                                errorMessage = "已经有这个标签啦"
                                showingError = true
                            }
                        }
                    }
                    .disabled(newTagName.isEmpty)
                }
            }
        }
        .presentationDetents([.height(200)])
    }
    
    // 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 12) {
            Image(systemName: "tag")
                .font(.system(size: 36))
                .foregroundColor(.secondary)
            
            Text("没有标签")
                .font(.headline)
                .foregroundColor(.primary)
            
            Text("点击底部的加号按钮添加新标签")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
        }
        .frame(maxWidth: .infinity)
        .padding()
    }
    
    // MARK: - 标签操作
    
    /// 创建新标签
    private func createTag(name: String) {
        viewModel.addTag(name: name, color: randomColor())
    }
    
    @MainActor
    private func refreshTags() async {
        viewModel.loadTags()
        // 显示轻量级刷新提示
        DependencyContainer.toastManager().showSuperLightInfo("已刷新")
    }
    
    private func renameTag(_ tag: Tag, newName: String) {
        viewModel.renameTag(tag, newName: newName)
    }
    
    private func deleteTag(_ tag: Tag) {
        viewModel.deleteTag(tag)
    }
    
    // 生成随机颜色
    private func randomColor() -> String {
        // 使用更适合背景的柔和颜色
        let colors = [
            "6366F1", // Indigo
            "8B5CF6", // Violet
            "EC4899", // Pink
            "F43F5E", // Rose
            "F97316", // Orange
            "EAB308", // Yellow
            "22C55E", // Green
            "14B8A6", // Teal
            "0EA5E9", // Sky
            "3B82F6"  // Blue
        ]
        return colors.randomElement() ?? "6366F1"
    }
}

#Preview {
    NavigationView {
        TagView()
            .modelContainer(for: [Task.self, Tag.self, Project.self]) // 确保 Project 也包含在预览容器中
    }
}
