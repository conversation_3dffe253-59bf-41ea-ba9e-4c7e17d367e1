import SwiftUI
import SwiftData

/// 已完成行动页面
struct DoneView: View {
    // MARK: - 依赖注入
    private let taskRepository: TaskRepository
    private let projectRepository: ProjectRepository
    private let tagRepository: TagRepository
    
    // MARK: - 状态属性
    @StateObject private var viewModel: CompletedTasksViewModel
    @State private var isSearching: Bool = false
    @State private var isFilterSheetPresented: Bool = false
    @State private var hasActiveFilters: Bool = false
    
    // MARK: - 初始化方法
    init(
        taskRepository: TaskRepository = DependencyContainer.taskRepository(),
        projectRepository: ProjectRepository = DependencyContainer.projectRepository(),
        tagRepository: TagRepository = DependencyContainer.tagRepository()
    ) {
        self.taskRepository = taskRepository
        self.projectRepository = projectRepository
        self.tagRepository = tagRepository
        
        // 初始化视图模型
        _viewModel = StateObject(wrappedValue: CompletedTasksViewModel(
            taskManager: DependencyContainer.taskManager()
        ))
    }
    
    // MARK: - 视图主体
    var body: some View {
        ZStack {
            // 主内容视图
            completedTasksContent
                .navigationTitle("已完成")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItemGroup(placement: .navigationBarTrailing) {
                        // 搜索按钮
                        Button {
                            withAnimation {
                                isSearching = true
                            }
                        } label: {
                            Image(systemName: "magnifyingglass")
                                .foregroundColor(.primary)
                        }
                        
                        // 筛选按钮
                        Button {
                            withAnimation {
                                isFilterSheetPresented = true
                            }
                        } label: {
                            Image(systemName: hasActiveFilters ? "line.3.horizontal.decrease.circle.fill" : "line.3.horizontal.decrease.circle")
                                .foregroundColor(hasActiveFilters ? .accentColor : .primary)
                        }
                    }
                }
                .toolbarBackground(Color(.systemBackground), for: .navigationBar)
                .toolbarBackground(.automatic, for: .navigationBar)
                .sheet(isPresented: $isSearching) {
                    // 直接传递 viewModel 对象
                    SearchTasksView(viewModel: viewModel)
                }
                .sheet(isPresented: $isFilterSheetPresented) {
                    // 直接传递 viewModel 对象和状态绑定
                    FilterTasksView(viewModel: viewModel, hasActiveFilters: $hasActiveFilters)
                }
        }
        .onAppear {
            viewModel.loadInitialTasks()
            
            // 添加埋点 - 已完成视图进入事件
            AnalyticsService.shared.trackEvent(AnalyticsService.EventNames.completedViewEntered)
        }
        }
    
    // MARK: - 子视图
    
    /// 已完成行动内容视图
    private var completedTasksContent: some View {
        ScrollView {
            LazyVStack(spacing: 8) {
                // 检查是否有分组行动
                if viewModel.groupedTasks.isEmpty {
                    emptyStateView
                } else {
                    // 分组行动展示
                    ForEach(sortedGroups(), id: \.self) { group in
                        if let tasks = viewModel.groupedTasks[group], !tasks.isEmpty {
                            taskGroupSection(group: group, tasks: tasks)
                        }
                    }
                    
                    // 加载更多按钮
                    if self.viewModel.canLoadMore {
                        loadMoreButton
                    }
                }
            }
            .padding(.bottom, 20)
        }
        .refreshable {
            await viewModel.refreshTasks()
        }
    }
    
    /// 排序后的分组
    private func sortedGroups() -> [DateGroup] {
        return self.viewModel.groupedTasks.keys.sorted()
    }
    
    /// 行动组区块
    private func taskGroupSection(group: DateGroup, tasks: [Task]) -> some View {
        Section {
            ForEach(tasks) { task in
                CompletedTaskRow(
                    task: task, 
                    onRestore: { task in 
                        self.viewModel.restoreTask(task)
                        DependencyContainer.toastManager().showSuperLightInfo("已恢复到下一步")
                    }, 
                    onDelete: { task in self.viewModel.deleteTask(task) },
                    isRecoverable: viewModel.isTaskRecoverable(task: task)
                )
                    .padding(.horizontal)
            }
        } header: {
            taskGroupHeader(group: group, count: tasks.count)
        }
    }
    

    
    /// 活跃筛选器视图
    private var activeFiltersView: some View {
        HStack(spacing: 8) {
            // 筛选条件数量
            Text("已筛选 \(getActiveFiltersCount())")
                .font(.system(size: 13))
                .foregroundColor(.secondary)
            
            Spacer()
            
            Button("清除") {
                viewModel.applyFilter()
                hasActiveFilters = false
            }
            .font(.system(size: 13))
            .foregroundColor(.blue)
        }
        .padding(.horizontal)
        .padding(.vertical, 6)
        .background(Color(.systemBackground))
    }
    
    /// 获取活跃筛选器数量
    private func getActiveFiltersCount() -> Int {
        var count = 0
        if viewModel.filterTimeRange != .all { count += 1 }
        if viewModel.filterProject != nil { count += 1 }
        count += viewModel.filterTags.count
        return count
    }
    
    /// 加载更多按钮
    private var loadMoreButton: some View {
        Button {
            viewModel.loadMoreTasks()
        } label: {
            HStack {
                if viewModel.isLoading {
                    ProgressView()
                        .frame(width: 16, height: 16)
                } else {
                    Text("加载更多")
                        .font(.system(size: 14))
                        .foregroundColor(.primary.opacity(0.8))
                }
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(Color.secondary.opacity(0.1))
            .cornerRadius(8)
        }
        .disabled(viewModel.isLoading)
        .padding(.horizontal)
        .padding(.top, 8)
    }
    
    /// 空状态视图
    @ViewBuilder
    private var emptyStateView: some View {
        VStack(spacing: 12) {
            Spacer()
            
            Image(systemName: "checkmark.circle")
                .font(.system(size: 36))
                .foregroundColor(.secondary.opacity(0.3))
            
            Text(hasActiveFilters ? "没有符合条件的任务" : "暂无已完成任务")
                .font(.system(size: 15))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            if hasActiveFilters {
                Button("清除筛选") {
                    viewModel.applyFilter()
                    hasActiveFilters = false
                }
                .font(.system(size: 14))
                .foregroundColor(.blue)
                .padding(.top, 4)
            }
            
            Spacer()
        }
        .frame(minHeight: 240)
    }
}

// MARK: - 公共可复用组件

/// 行动组标题
func taskGroupHeader(group: DateGroup, count: Int) -> some View {
    HStack {
        Text(group.title)
            .font(.system(size: 13, weight: .medium))
            .foregroundColor(.secondary)
        
        Text("\(count)")
            .font(.system(size: 13, weight: .regular))
            .foregroundColor(.secondary.opacity(0.8))
        
        Spacer()
    }
    .padding(.horizontal)
    .padding(.vertical, 6)
    .background(Color(.systemBackground))
}

// MARK: - 已完成行动行
struct CompletedTaskRow: View {
    // 属性
    var task: Task
    var onRestore: (Task) -> Void
    var onDelete: (Task) -> Void
    var isRecoverable: Bool
    
    // 内部状态
    @State private var showingMenu = false
    
    var body: some View {
        HStack(alignment: .center, spacing: 10) {
            // 完成标记
            Image(systemName: "checkmark.circle.fill")
                .foregroundColor(.secondary)
                .font(.system(size: 16))
            
            // 行动信息
            HStack {
                Text(task.title)
                    .font(.system(size: 16))
                    .foregroundColor(.primary.opacity(0.8))
                    .strikethrough(true, color: .secondary.opacity(0.5))
                    .lineLimit(1)
                
                Spacer()
                
                // 完成时间
                Text(task.completedAt?.formatted(.relative(presentation: .named)) ?? "")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding(.vertical, 12)
        .contentShape(Rectangle())
        .background(Color(.systemBackground))
        // 添加长按手势
        .onLongPressGesture {
            showingMenu = true
        }
        // 滑动动作
        .swipeActions(edge: .trailing) {
            Button(role: .destructive) {
                onDelete(task)
            } label: {
                Label("删除", systemImage: "trash")
            }
            
            // 始终显示恢复按钮，但根据可恢复状态改变行为和样式
            Button {
                if isRecoverable {
                    onRestore(task)
                    DependencyContainer.toastManager().showSuperLightInfo("已恢复到下一步")
                } else {
                    DependencyContainer.toastManager().showWarning("只能恢复一小时内完成的行动")
                }
            } label: {
                Label("恢复", systemImage: "arrow.uturn.backward")
            }
            .tint(isRecoverable ? .blue : .gray)
        }
        // 上下文菜单
        .contextMenu {
            Button(action: { 
                if isRecoverable {
                    onRestore(task)
                    DependencyContainer.toastManager().showSuperLightInfo("已恢复到下一步")
                } else {
                    DependencyContainer.toastManager().showWarning("只能恢复一小时内完成的行动")
                }
            }) {
                Label("恢复为行动", systemImage: "arrow.uturn.backward")
            }
            
            Button(role: .destructive, action: { onDelete(task) }) {
                Label("删除", systemImage: "trash")
            }
        }
    }
    
    // 日期格式化函数
    private func formattedDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .none
        return formatter.string(from: date)
    }
    
    // 获取项目名称
    private func getProjectName(for projectId: UUID?) -> String {
        // 这里应该根据projectId获取项目名称
        // 简化处理，返回默认值
        return "项目"
    }
}

// MARK: - 筛选标签
struct FilterTag: View {
    var text: String
    
    var body: some View {
        Text(text)
            .font(.caption)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color.blue.opacity(0.1))
            .foregroundColor(.blue)
            .cornerRadius(4)
    }
}

// MARK: - 搜索视图
struct SearchTasksView: View {
    @ObservedObject var viewModel: CompletedTasksViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var searchText: String = ""
    
    var body: some View {
        NavigationView {
            VStack {
                // 搜索框
                searchBar
                
                // 搜索结果
                if viewModel.groupedTasks.isEmpty {
                    noResultsView
                } else {
                    searchResultsView
                }
            }
            .navigationTitle("搜索已完成行动")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
        }
        .refreshable {
            await viewModel.refreshTasks()
        }
    }
    
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("搜索行动标题、项目、备注", text: $searchText)
                .autocapitalization(.none)
                .disableAutocorrection(true)
                .submitLabel(.search)
                .onSubmit {
                    self.viewModel.applySearch(text: self.searchText)
                }
            
            if !searchText.isEmpty {
                Button {
                    self.searchText = ""
                    self.viewModel.applySearch(text: "")
                } label: {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(10)
        .background(Color(.systemGray6))
        .cornerRadius(10)
        .padding(.horizontal)
        .padding(.top, 8)
    }
    
    // 根据日期分组渲染已完成任务
    private func taskGroupView(group: DateGroup, tasks: [Task]) -> some View {
        Section {
            LazyVStack(spacing: 0) {
                ForEach(tasks) { task in
                    CompletedTaskRow(
                        task: task, 
                        onRestore: { t in 
                            viewModel.restoreTask(t)
                            DependencyContainer.toastManager().showSuperLightInfo("已恢复到下一步")
                        }, 
                        onDelete: { t in viewModel.deleteTask(t) },
                        isRecoverable: viewModel.isTaskRecoverable(task: task)
                    )
                    .padding(.horizontal)
                }
            }
        } header: {
            taskGroupHeader(group: group, count: tasks.count)
        }
    }
    
    // 加载更多按钮视图
    private var loadMoreButtonView: some View {
        Button(action: { viewModel.loadMoreTasks() }) {
            Text("加载更多...")
                .foregroundColor(.blue)
                .padding()
        }
    }
    
    // 搜索结果主视图
    private var searchResultsView: some View {
        ScrollView {
            LazyVStack(spacing: 0) {
                // 分组显示任务
                let sortedKeys = Array(viewModel.groupedTasks.keys.sorted())
                ForEach(sortedKeys, id: \.self) { group in
                    if let tasks = viewModel.groupedTasks[group], !tasks.isEmpty {
                        taskGroupView(group: group, tasks: tasks)
                    }
                }
                
                // 加载更多按钮
                if !viewModel.isLoading && viewModel.canLoadMore {
                    loadMoreButtonView
                }
            }
        }
        .refreshable {
            await viewModel.refreshTasks()
        }
    }
    
    private var noResultsView: some View {
        VStack(spacing: 16) {
            Spacer()
            
            Image(systemName: "magnifyingglass")
                .font(.system(size: 40))
                .foregroundColor(.secondary.opacity(0.5))
            
            Text(viewModel.searchText.isEmpty ? "输入关键词搜索行动" : "未找到匹配的行动")
                .font(.headline)
                .foregroundColor(.secondary)
            
            Spacer()
        }
    }
}

// MARK: - 筛选视图
struct FilterTasksView: View {
    @ObservedObject var viewModel: CompletedTasksViewModel
    @Binding var hasActiveFilters: Bool
    @Environment(\.dismiss) private var dismiss
    
    // 临时状态，用于在应用前临时存储
    @State private var selectedTimeRange: TaskTimeRange = .all
    @State private var selectedProject: Project? = nil
    @State private var selectedTags: [String] = []
    
    var body: some View {
        NavigationView {
            Form {
                // 时间范围筛选
                Section(header: Text("时间范围")) {
                    ForEach(TaskTimeRange.allCases) { range in
                        Button {
                            selectedTimeRange = range
                        } label: {
                            HStack {
                                Text(range.title)
                                    .foregroundColor(.primary)
                                
                                Spacer()
                                
                                if selectedTimeRange == range {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(.blue)
                                }
                            }
                        }
                    }
                }
                
                // 项目筛选 (简化版本)
                Section(header: Text("项目")) {
                    Text("项目筛选待实现")
                        .foregroundColor(.secondary)
                }
                
                // 标签筛选 (简化版本)
                Section(header: Text("标签")) {
                    Text("标签筛选待实现")
                        .foregroundColor(.secondary)
                }
            }
            .navigationTitle("筛选行动")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("重置") {
                        resetFilters()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("应用") {
                        applyFilters()
                    }
                }
            }
            .onAppear {
                // 初始化选择状态
                selectedTimeRange = viewModel.filterTimeRange
                selectedProject = viewModel.filterProject
                selectedTags = viewModel.filterTags
            }
        }
    }
    
    private func resetFilters() {
        selectedTimeRange = .all
        selectedProject = nil
        selectedTags = []
    }
    
    private func applyFilters() {
        // 应用筛选条件
        viewModel.applyFilters(
            project: selectedProject,
            tags: selectedTags,
            timeRange: selectedTimeRange
        )
        
        // 更新是否有活跃筛选
        hasActiveFilters = selectedTimeRange != .all || selectedProject != nil || !selectedTags.isEmpty
        
        // 关闭表单
        dismiss()
    }
}

#Preview {
    NavigationView {
        DoneView()
    }
}
