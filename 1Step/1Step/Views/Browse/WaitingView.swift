import SwiftUI
import SwiftData
import Combine
import UIKit

/// 等待中页面
struct WaitingView: View {
    // MARK: - 依赖
    private let taskRepository: TaskRepository
    private let projectRepository: ProjectRepository
    private let tagRepository: TagRepository
    
    // MARK: - 环境变量
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - 状态变量
    @StateObject private var viewModel: WaitingViewModel
    @StateObject private var formCoordinator = TaskFormCoordinator.shared
    @FocusState private var isTitleFocused: Bool
    
    // 初始化方法
    init(
        taskRepository: TaskRepository = DependencyContainer.taskRepository(),
        projectRepository: ProjectRepository = DependencyContainer.projectRepository(),
        tagRepository: TagRepository = DependencyContainer.tagRepository()
    ) {
        self.taskRepository = taskRepository
        self.projectRepository = projectRepository
        self.tagRepository = tagRepository
        
        _viewModel = StateObject(wrappedValue: WaitingViewModel())
    }
    
    // MARK: - 视图体
    var body: some View {
        ZStack {
            VStack(spacing: 0) {
                // 搜索栏 - 仅在搜索模式下显示
                if viewModel.isSearchFocused {
                    searchBarView
                }
                
                // 任务列表
                ScrollView {
                    waitingTasksView
                        .padding(.bottom, 10)
                }
                .refreshable {
                    await viewModel.refreshTasks()
                }
            }
            .navigationTitle("等待中")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItemGroup(placement: .navigationBarTrailing) {
                    // 搜索按钮
                    Button {
                        withAnimation {
                            viewModel.isSearchFocused.toggle()
                            if !viewModel.isSearchFocused {
                                viewModel.searchText = ""
                                viewModel.loadTasks()
                            }
                        }
                    } label: {
                        Image(systemName: viewModel.isSearchFocused ? "xmark" : "magnifyingglass")
                            .foregroundColor(viewModel.isSearchFocused ? .accentColor : .primary)
                    }
                }
            }
            .toolbarBackground(Color(.systemBackground), for: .navigationBar)
            .toolbarBackground(.automatic, for: .navigationBar)
            .onAppear {
                // 视图出现时刷新任务数据
                viewModel.loadTasks()
                // 添加埋点 - 等待中视图进入事件
                AnalyticsService.shared.trackEvent(AnalyticsService.EventNames.waitingViewEntered)
            }
            
            // 浮层内容 - 只显示一个添加按钮
            if !formCoordinator.isShowing {
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        Button(action: { formCoordinator.showForm() }) {
                            Image(systemName: "plus")
                                .foregroundColor(.white)
                                .frame(width: 56, height: 56)
                                .background(Color.blue)
                                .clipShape(Circle())
                                .shadow(radius: 4)
                        }
                        .padding()
                    }
                }
            }
            
            // 添加任务浮层
            if formCoordinator.isShowing {
                taskFormOverlay
            }
            
            // Toast提示
            if viewModel.showToast {
                toastOverlay
            }
        }
    }
    
    // MARK: - 子视图
    
    /// 搜索栏
    private var searchBarView: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("搜索等待中的行动", text: $viewModel.searchText)
                .textFieldStyle(PlainTextFieldStyle())
                .focused($isTitleFocused)
                .submitLabel(.search)
                .onSubmit {
                    viewModel.loadTasks()
                }
            
            if !viewModel.searchText.isEmpty {
                Button(action: {
                    viewModel.searchText = ""
                    viewModel.loadTasks()
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(8)
        .background(Color(.systemGray6))
        .cornerRadius(8)
        .padding(.horizontal)
        .padding(.bottom, 8)
    }
    
    /// 等待中任务列表
    private var waitingTasksView: some View {
        TaskSectionView(
            title: "",
            tasks: viewModel.filteredTasks,
            status: TaskStatus.waiting.rawValue,
            taskRepository: taskRepository,
            projectRepository: projectRepository,
            tagRepository: tagRepository,
            titleColor: .orange,
            isExpanded: .constant(true),
            showExpandButton: false,
            showEmptyState: true,
            onTaskIntent: { [viewModel] intent in
                viewModel.handleTaskIntent(intent)
            }
        )
    }
    
    // 任务添加表单浮层
    private var taskFormOverlay: some View {
        // 使用新的初始化方法，直接设置初始状态为等待中
        let taskFormViewModel = TaskFormViewModel(
            initialStatus: TaskStatus.waiting.rawValue,
            taskManager: DependencyContainer.taskManager()
        )
        
        // 设置回调
        taskFormViewModel.onTaskAdded = { task, message in
            // 触感反馈
            UIImpactFeedbackGenerator(style: .medium).impactOccurred()
            
            // 刷新任务列表，但不关闭添加任务界面
            viewModel.loadTasks()
            
            // 重新请求键盘聚焦，便于用户继续输入
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                NotificationCenter.default.post(name: .requestKeyboardFocus, object: nil)
            }
        }
        
        taskFormViewModel.onDismiss = {
            formCoordinator.hideForm()
        }
        
        return AddTaskFormView(viewModel: taskFormViewModel)
    }
    
    // Toast提示浮层
    private var toastOverlay: some View {
        VStack {
            Spacer()
                .frame(height: 50)
            
            Text(viewModel.toastMessage)
                .font(.footnote)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Environment(\.colorScheme).wrappedValue == .dark ? 
                              Color.white.opacity(0.2) : 
                                Color.black.opacity(0.06))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Environment(\.colorScheme).wrappedValue == .dark ? 
                                        Color.white.opacity(0.2) : 
                                            Color.black.opacity(0.1),
                                        lineWidth: 0.5)
                        )
                )
                .foregroundColor(Environment(\.colorScheme).wrappedValue == .dark ? .white : .black)
                .transition(.move(edge: .top).combined(with: .opacity))
            
            Spacer()
        }
        .animation(.spring(response: 0.4), value: viewModel.showToast)
        .zIndex(100)
    }
}

// MARK: - 预览
#Preview {
    NavigationView {
        WaitingView()
    }
}
