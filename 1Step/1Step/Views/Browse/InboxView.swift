import SwiftUI
import SwiftData

/// 收集箱页面
struct InboxView: View {
    // MARK: - 依赖
    private let taskRepository: TaskRepository
    private let projectRepository: ProjectRepository
    private let tagRepository: TagRepository
    
    // MARK: - 环境变量
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - 状态变量
    @StateObject private var viewModel: InboxViewModel
    @StateObject private var formCoordinator = TaskFormCoordinator.shared
    @FocusState private var isTitleFocused: Bool
    
    // 初始化方法
    init(
        taskRepository: TaskRepository = DependencyContainer.taskRepository(),
        projectRepository: ProjectRepository = DependencyContainer.projectRepository(),
        tagRepository: TagRepository = DependencyContainer.tagRepository()
    ) {
        self.taskRepository = taskRepository
        self.projectRepository = projectRepository
        self.tagRepository = tagRepository
        
        _viewModel = StateObject(wrappedValue: InboxViewModel())
    }
    
    // MARK: - 视图体
    var body: some View {
        ZStack {
            VStack(spacing: 0) {
                // 搜索栏 - 仅在搜索模式下显示
                if viewModel.isSearchFocused {
                    searchBarView
                }
                
                // 任务列表
                ScrollView {
                    inboxTasksView
                        .padding(.bottom, formCoordinator.isShowing ? 0 : 10)
                }
                .refreshable {
                    await viewModel.refreshTasks()
                }
            }
            .navigationTitle("收集箱")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItemGroup(placement: .navigationBarTrailing) {
                    // 搜索按钮
                    Button {
                        withAnimation {
                            viewModel.isSearchFocused.toggle()
                        }
                    } label: {
                        Image(systemName: viewModel.isSearchFocused ? "xmark" : "magnifyingglass")
                            .foregroundColor(viewModel.isSearchFocused ? .accentColor : .primary)
                    }
                    
                    // 批量操作按钮
                    Button {
                        withAnimation {
                            viewModel.isBatchMode.toggle()
                        }
                    } label: {
                        Image(systemName: viewModel.isBatchMode ? "checkmark.circle.fill" : "circle")
                            .foregroundColor(viewModel.isBatchMode ? .accentColor : .primary)
                    }
                }
            }
            .toolbarBackground(Color(.systemBackground), for: .navigationBar)
            .toolbarBackground(.automatic, for: .navigationBar)
            .tint(AppColors.UI.primaryText(for: colorScheme))
            .onAppear {
                // 视图出现时刷新任务数据
                viewModel.loadTasks()
                
                // 添加埋点 - 收集箱视图进入事件
                AnalyticsService.shared.trackEvent(AnalyticsService.EventNames.inboxViewEntered)
            }
            

            
            // 浮层内容 - 只显示一个添加按钮
            if !formCoordinator.isShowing {
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        Button(action: { formCoordinator.showForm() }) {
                            Image(systemName: "plus")
                                .foregroundColor(.white)
                                .frame(width: 56, height: 56)
                                .background(Color.blue)
                                .clipShape(Circle())
                                .shadow(radius: 4)
                        }
                        .padding()
                    }
                }
            }
            
            // 添加任务浮层
            if formCoordinator.isShowing {
                taskFormOverlay
            }
            
            // Toast提示
            if viewModel.showToast {
                toastOverlay
            }
        }
    }
    
    // MARK: - 子视图
    
    /// 搜索栏
    private var searchBarView: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("搜索收集箱任务", text: $viewModel.searchText)
                .font(.system(size: 15))
                .autocapitalization(.none)
                .disableAutocorrection(true)
            
            if !viewModel.searchText.isEmpty {
                Button {
                    viewModel.searchText = ""
                } label: {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 8)
        .background(Color(.systemGray6).opacity(0.5))
        .cornerRadius(8)
        .padding(.horizontal, 20)
        .padding(.bottom, 10)
    }
    
    // 收集箱任务列表
    private var inboxTasksView: some View {
        VStack(spacing: 0) {
            if viewModel.isSearchFocused || !viewModel.searchText.isEmpty {
                // 搜索结果
                inboxSearchResultsView
            } else {
                // 普通收集箱列表
                TaskSectionView(
                    title: "",  // 移除重复的标题
                    tasks: viewModel.inboxTasks,
                    status: TaskStatus.inbox.rawValue,
                    taskRepository: taskRepository,
                    projectRepository: projectRepository,
                    tagRepository: tagRepository,
                    titleColor: .gray,
                    isExpanded: .constant(true),
                    showExpandButton: false,
                    isBatchMode: viewModel.isBatchMode,
                    selectedTasks: viewModel.selectedTasks,
                    onTaskSelected: { taskId in
                        viewModel.toggleTaskSelection(taskId)
                    },
                    onTaskIntent: { intent in
                        // 处理任务意图
                        viewModel.handleTaskIntent(intent)
                    }
                )
            }
            
            // 批量操作底部工具栏
            if viewModel.isBatchMode {
                VStack(spacing: 0) {
                    Divider()
                    
                    HStack(spacing: 20) {
                        // 取消按钮
                        Button {
                            withAnimation {
                                viewModel.cancelBatchOperation()
                            }
                        } label: {
                            Text("取消")
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        // 已选数量
                        Text("已选择 \(viewModel.selectedTasks.count) 项")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        // 移动按钮
                        Menu {
                            Button(action: {
                                viewModel.moveSelectedTasksToNextActions()
                            }) {
                                Label("移动到下一步", systemImage: "arrow.right")
                            }
                            
                            Button(action: {
                                viewModel.moveSelectedTasksToSomeday()
                            }) {
                                Label("移动到将来也许", systemImage: "clock")
                            }
                        } label: {
                            Text("移动到")
                                .foregroundColor(.accentColor)
                        }
                        .disabled(viewModel.selectedTasks.isEmpty)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(Color(.systemBackground))
                }
            }
        }
    }
    

    
    // 任务添加表单浮层
    private var taskFormOverlay: some View {
        let taskFormViewModel = TaskFormViewModel(
            taskManager: DependencyContainer.taskManager()
        )
        
        // 设置回调
        taskFormViewModel.onTaskAdded = { task, message in
            // 不再显示本地 Toast，因为已经在 AddTaskFormView 中使用全局 ToastManager 显示了
            
            // 触感反馈
            UIImpactFeedbackGenerator(style: .medium).impactOccurred()
            
            // 刷新任务列表，但不关闭添加任务界面
            viewModel.loadTasks()
            
            // 重新请求键盘聚焦，便于用户继续输入
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                NotificationCenter.default.post(name: .requestKeyboardFocus, object: nil)
            }
        }
        
        taskFormViewModel.onDismiss = {
            formCoordinator.hideForm()
        }
        
        return AddTaskFormView(viewModel: taskFormViewModel)
    }
    
    // 搜索结果视图
    private var inboxSearchResultsView: some View {
        VStack(spacing: 0) {
            VStack(alignment: .leading, spacing: 10) {
                if viewModel.inboxTasks.isEmpty {
                    Text("没有找到相关任务")
                        .font(.system(size: 14))
                        .foregroundColor(.secondary)
                        .padding()
                } else {
                    LazyVStack {
                        ForEach(viewModel.inboxTasks, id: \.id) { task in
                            HStack(spacing: 12) {
                                if viewModel.isBatchMode {
                                    // 选择按钮
                                    Button {
                                        viewModel.toggleTaskSelection(task.id)
                                    } label: {
                                        Image(systemName: viewModel.selectedTasks.contains(task.id) ? 
                                              "checkmark.circle.fill" : "circle")
                                        .font(.system(size: 18))
                                        .foregroundColor(viewModel.selectedTasks.contains(task.id) ? 
                                            .accentColor : .secondary)
                                    }
                                    .transition(.scale)
                                }
                                
                                TaskCardView(
                                    task: task,
                                    taskRepository: self.taskRepository,
                                    projectRepository: self.projectRepository,
                                    tagRepository: self.tagRepository,
                                    style: .standard,
                                    onTaskIntent: { intent in
                                        // 处理任务意图
                                        viewModel.handleTaskIntent(intent)
                                    },
                                    onExitRequested: { task, direction in
                                        // 动画退出处理
                                    }
                                )
                            }
                        }
                    }
                }
            }
            .padding(.horizontal)
            
            // 批量操作底部工具栏
            if viewModel.isBatchMode {
                VStack(spacing: 0) {
                    Divider()
                    
                    HStack(spacing: 20) {
                        // 取消按钮
                        Button {
                            withAnimation {
                                viewModel.cancelBatchOperation()
                            }
                        } label: {
                            Text("取消")
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        // 已选数量
                        Text("已选择 \(viewModel.selectedTasks.count) 项")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        // 移动按钮
                        Menu {
                            Button(action: {
                                viewModel.moveSelectedTasksToNextActions()
                            }) {
                                Label("移动到下一步", systemImage: "arrow.right")
                            }
                            
                            Button(action: {
                                viewModel.moveSelectedTasksToSomeday()
                            }) {
                                Label("移动到将来也许", systemImage: "clock")
                            }
                        } label: {
                            Text("移动到")
                                .foregroundColor(.accentColor)
                        }
                        .disabled(viewModel.selectedTasks.isEmpty)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(Color(.systemBackground))
                }
            }
        }
    }
    
    // 已移除 - 浮层内容直接在body中实现
    
    // 删除重复的 taskFormOverlay 方法
    
    // Toast提示浮层
    private var toastOverlay: some View {
        VStack {
            Spacer()
                .frame(height: 50)
            
            Text(viewModel.toastMessage)
                .font(.footnote)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Environment(\.colorScheme).wrappedValue == .dark ? 
                              Color.white.opacity(0.2) : 
                                Color.black.opacity(0.06))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Environment(\.colorScheme).wrappedValue == .dark ? 
                                        Color.white.opacity(0.2) : 
                                            Color.black.opacity(0.1),
                                        lineWidth: 0.5)
                        )
                )
                .foregroundColor(Environment(\.colorScheme).wrappedValue == .dark ? .white : .black)
                .transition(.move(edge: .top).combined(with: .opacity))
            
            Spacer()
        }
        .animation(.spring(response: 0.4), value: viewModel.showToast)
        .zIndex(100)
    }
    
    // 已移除
    
    // MARK: - 预览
    #Preview {
        InboxView()
            .modelContainer(for: [Task.self, Project.self, Tag.self])
    }
}
