import SwiftUI
import SwiftData

struct TaskSelectorView: View {
    // MARK: - 依赖
    private let taskRepository: TaskRepository
    
    // MARK: - 环境变量
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - 状态管理
    @State private var availableTasks: [Task] = []
    
    // 添加任务后的回调函数
    var onTaskAdded: (() -> Void)?
    
    // MARK: - 初始化方法
    init(
        taskRepository: TaskRepository = DependencyContainer.taskRepository(),
        onTaskAdded: (() -> Void)? = nil
    ) {
        self.taskRepository = taskRepository
        self.onTaskAdded = onTaskAdded
    }
    
    var body: some View {
        NavigationView {
            List {
                Section(header: Text("可添加的行动 (\(availableTasks.count))")) {
                    ForEach(availableTasks) { task in
                        HStack {
                            Text(task.title)
                                .lineLimit(2)
                            
                            Spacer()
                            
                            Button(action: {
                                addToDoing(task: task)
                            }) {
                                Image(systemName: "plus.circle.fill")
                                    .foregroundColor(.blue)
                            }
                        }
                    }
                }
            }
            .navigationTitle("选择行动")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                loadAvailableTasks()
            }
        }
    }
    
    // MARK: - 私有方法
    private func loadAvailableTasks() {
        availableTasks = taskRepository.getTasksByStatus("NA")
            .sorted { $0.updatedAt > $1.updatedAt }
    }
    
    private func addToDoing(task: Task) {
        // 检查当前一步行动数量
        let doingTasksCount = taskRepository.getTasksByStatus("Doing").count
        
        // 限制一步行动数量为3个
        if doingTasksCount < 3 {
            task.status = "Doing"
            task.updatedAt = Date()
            
            // 使用仓储更新任务状态
            taskRepository.updateTask(task)
            
            // 重新加载可用任务
            loadAvailableTasks()
            
            // 调用回调函数通知FocusView刷新
            onTaskAdded?()
        } else {
            // TODO: 添加用户提示，提示已达到最大行动数量
            print("已达到最大行动数量")
        }
    }
}
