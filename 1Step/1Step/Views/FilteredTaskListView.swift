import SwiftUI
import SwiftData

/// 通用的过滤行动列表视图，用于显示特定状态的行动
struct FilteredTaskListView: View {
    // MARK: - 属性
    
    /// 行动状态
    var status: TaskStatus
    
    /// 排序方式
    var sortOrder: SortOrder
    
    /// 是否支持批量操作
    var supportsBatchOperations: Bool
    
    /// 显示的字段
    var displayFields: DisplayFields
    
    /// 环境变量
    @Environment(\.dismiss) private var dismiss
    
    /// 依赖注入
    private let taskRepository: TaskRepository
    private let projectRepository: ProjectRepository
    private let tagRepository: TagRepository
    
    /// 视图模型
    @StateObject private var viewModel: FilteredTaskListViewModel
    
    /// 状态
    @State private var isMultiSelectMode: Bool = false
    @State private var selectedTasks: Set<Task> = []
    @State private var searchText: String = ""
    
    // MARK: - 初始化方法
    
    init(
        status: TaskStatus, 
        sortOrder: SortOrder = .dateCreatedDesc, 
        supportsBatchOperations: Bool = false,
        displayFields: DisplayFields = .titleOnly,
        taskRepository: TaskRepository = DependencyContainer.taskRepository(),
        projectRepository: ProjectRepository = DependencyContainer.projectRepository(),
        tagRepository: TagRepository = DependencyContainer.tagRepository()
    ) {
        self.status = status
        self.sortOrder = sortOrder
        self.supportsBatchOperations = supportsBatchOperations
        self.displayFields = displayFields
        self.taskRepository = taskRepository
        self.projectRepository = projectRepository
        self.tagRepository = tagRepository
        
        // 初始化视图模型
        _viewModel = StateObject(wrappedValue: FilteredTaskListViewModel(
            status: status,
            sortOrder: sortOrder,
            taskManager: DependencyContainer.taskManager()
        ))
    }
    
    // MARK: - 视图体
    
    var body: some View {
        VStack(spacing: 0) {
            // 页面标题和操作栏
            headerView
            
            if status == .done && !viewModel.searchText.isEmpty {
                // 已完成页面的搜索框
                searchFieldView
            }
            
            if viewModel.tasks.isEmpty {
                // 空状态视图
                emptyStateView
            } else {
                // 行动列表
                taskListView
            }
        }
        .navigationBarHidden(true)
        // 底部批量操作栏
        .overlay(alignment: .bottom) {
            if isMultiSelectMode && supportsBatchOperations {
                batchOperationBar
            }
        }
    }
    
    // MARK: - 子视图
    
    /// 顶部标题和操作栏
    private var headerView: some View {
        HStack {
            // 返回按钮
            Button {
                dismiss()
            } label: {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.primary)
                    .frame(width: 30, height: 30)
            }
            
            // 页面标题
            Text(statusTitle)
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(.primary)
            
            Spacer()
            
            // 右侧操作按钮
            if supportsBatchOperations {
                Button {
                    withAnimation {
                        isMultiSelectMode.toggle()
                        if !isMultiSelectMode {
                            selectedTasks.removeAll()
                        }
                    }
                } label: {
                    Image(systemName: isMultiSelectMode ? "xmark" : "checkmark.circle")
                        .font(.system(size: 16))
                        .foregroundColor(.primary)
                        .frame(width: 30, height: 30)
                }
            }
            
            if status == .done {
                // 搜索按钮
                Button {
                    // 实现搜索功能
                } label: {
                    Image(systemName: "magnifyingglass")
                        .font(.system(size: 16))
                        .foregroundColor(.primary)
                        .frame(width: 30, height: 30)
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(Color(.systemBackground))
    }
    
    /// 行动列表视图
    private var taskListView: some View {
        ScrollView {
            LazyVStack(spacing: 0) {
                if status == .done {
                    // 已完成行动使用分组显示
                    doneTasksGroupedView
                } else {
                    // 其他状态使用普通列表
                    ForEach(viewModel.tasks) { task in
                        taskRowView(for: task)
                        
                        if task.id != viewModel.tasks.last?.id {
                            Divider()
                                .padding(.leading, isMultiSelectMode ? 55 : 20)
                        }
                    }
                }
            }
            .padding(.bottom, isMultiSelectMode ? 60 : 0)
        }
    }
    
    /// 单个行动行视图
    private func taskRowView(for task: Task) -> some View {
        HStack(spacing: 12) {
            // 多选模式选择框
            if isMultiSelectMode {
                Button {
                    toggleTaskSelection(task)
                } label: {
                    Image(systemName: selectedTasks.contains(task) ? "checkmark.circle.fill" : "circle")
                        .font(.system(size: 18))
                        .foregroundColor(selectedTasks.contains(task) ? .accentColor : .secondary)
                }
                .padding(.leading, 20)
            }
            
            // 行动内容
            VStack(alignment: .leading, spacing: displayFields.contains(.projectTag) ? 6 : 0) {
                // 行动标题
                HStack {
                    if status == .done {
                        Image(systemName: "checkmark")
                            .font(.system(size: 12))
                            .foregroundColor(.secondary.opacity(0.7))
                    }
                    
                    Text(task.title)
                        .font(.system(size: 16))
                        .foregroundColor(status == .done ? .secondary : .primary)
                        .lineLimit(2)
                    
                    Spacer()
                }
                
                // 项目和标签（仅在相应模式下显示）
                if displayFields.contains(.projectTag) {
                    HStack {
                        // 标签
                        if !task.tags.isEmpty {
                            Text(task.tags.joined(separator: ", "))
                                .font(.system(size: 12))
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        // 项目
                        if let projectId = task.project {
                            Text(getProjectName(for: projectId))
                                .font(.system(size: 12))
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
            .contentShape(Rectangle())
            .padding(.vertical, 12)
            .padding(.horizontal, isMultiSelectMode ? 0 : 20)
            .background(Color(.systemBackground))
            // 点击事件
            .onTapGesture {
                if isMultiSelectMode {
                    toggleTaskSelection(task)
                } else {
                    // 打开行动详情
                    // 这里需要实现行动详情的展示
                }
            }
            // 左滑操作
            .swipeActions(edge: .trailing) {
                swipeActionsForTask(task)
            }
        }
    }
    
    /// 已完成行动的分组显示
    private var doneTasksGroupedView: some View {
        // 实现分组逻辑
        VStack(spacing: 0) {
            Text("待实现：已完成行动的分组显示")
                .foregroundColor(.secondary)
                .padding()
        }
    }
    
    /// 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Spacer()
            
            Image(systemName: emptyStateIcon)
                .font(.system(size: 40))
                .foregroundColor(.secondary.opacity(0.5))
                .padding(.bottom, 16)
            
            Text(emptyStateMessage)
                .font(.system(size: 16))
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
                .padding(.horizontal, 40)
            
            Spacer()
        }
    }
    
    /// 批量操作栏
    private var batchOperationBar: some View {
        HStack(spacing: 20) {
            // 取消按钮
            Button {
                withAnimation {
                    isMultiSelectMode = false
                    selectedTasks.removeAll()
                }
            } label: {
                Text("取消")
                    .font(.system(size: 15))
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 移到下一步
            Button {
                moveSelectedTasksToNextAction()
            } label: {
                Text("下一步")
                    .font(.system(size: 15))
                    .foregroundColor(.blue)
            }
            .disabled(selectedTasks.isEmpty)
            
            // 移到将来也许（仅收集箱显示）
            if status == .inbox {
                Button {
                    moveSelectedTasksToSMB()
                } label: {
                    Text("将来也许")
                        .font(.system(size: 15))
                        .foregroundColor(.purple)
                }
                .disabled(selectedTasks.isEmpty)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 14)
        .background(
            Rectangle()
                .fill(Color(.systemBackground).opacity(0.95))
                .shadow(color: Color.black.opacity(0.05), radius: 3, x: 0, y: -2)
        )
    }
    
    /// 搜索框视图
    private var searchFieldView: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("搜索已完成行动", text: $viewModel.searchText)
                .font(.system(size: 15))
            
            if !viewModel.searchText.isEmpty {
                Button {
                    viewModel.searchText = ""
                    viewModel.loadTasks()
                } label: {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 8)
        .background(Color(.systemGray6).opacity(0.5))
        .cornerRadius(8)
        .padding(.horizontal, 20)
        .padding(.bottom, 10)
    }
    
    // MARK: - 辅助方法
    
    /// 根据行动状态生成左滑操作
    @ViewBuilder
    private func swipeActionsForTask(_ task: Task) -> some View {
        switch status {
        case .inbox:
            // 收集箱：移到下一步、移到将来也许、删除
            moveToNextAction(task)
            moveToSMB(task)
            deleteButton(task)
            
        case .waiting:
            // 等待中：移到下一步、删除
            moveToNextAction(task)
            deleteButton(task)
            
        case .smb:
            // 将来也许：移到下一步、删除
            moveToNextAction(task)
            deleteButton(task)
            
        case .done:
            // 已完成：恢复到下一步、删除
            Button {
                withAnimation {
                    viewModel.updateTaskStatus(task, newStatus: TaskStatus.na.rawValue)
                }
            } label: {
                Label("恢复", systemImage: "arrow.counterclockwise")
            }
            .tint(.blue)
            
            deleteButton(task)
            
        default:
            EmptyView()
        }
    }
    
    /// "移到下一步"操作按钮
    private func moveToNextAction(_ task: Task) -> some View {
        Button {
            withAnimation {
                viewModel.updateTaskStatus(task, newStatus: TaskStatus.na.rawValue)
            }
        } label: {
            Label("下一步", systemImage: "arrow.right")
        }
        .tint(.blue)
    }
    
    /// "移到将来也许"操作按钮
    private func moveToSMB(_ task: Task) -> some View {
        Button {
            withAnimation {
                viewModel.updateTaskStatus(task, newStatus: TaskStatus.smb.rawValue)
            }
        } label: {
            Label("将来也许", systemImage: "sparkles")
        }
        .tint(.purple)
    }
    
    /// "删除"操作按钮
    private func deleteButton(_ task: Task) -> some View {
        Button(role: .destructive) {
            withAnimation {
                viewModel.deleteTask(task)
            }
        } label: {
            Label("删除", systemImage: "trash")
        }
    }
    
    /// 切换行动选择状态
    private func toggleTaskSelection(_ task: Task) {
        withAnimation {
            if selectedTasks.contains(task) {
                selectedTasks.remove(task)
            } else {
                selectedTasks.insert(task)
            }
        }
    }
    
    /// 将选中行动移到下一步
    private func moveSelectedTasksToNextAction() {
        withAnimation {
            for task in selectedTasks {
                viewModel.updateTaskStatus(task, newStatus: TaskStatus.na.rawValue)
            }
            isMultiSelectMode = false
            selectedTasks.removeAll()
        }
    }
    
    /// 将选中行动移到将来也许
    private func moveSelectedTasksToSMB() {
        withAnimation {
            for task in selectedTasks {
                viewModel.updateTaskStatus(task, newStatus: TaskStatus.smb.rawValue)
            }
            isMultiSelectMode = false
            selectedTasks.removeAll()
        }
    }
    
    // MARK: - 计算属性
    
    /// 页面标题
    private var statusTitle: String {
        switch status {
        case .inbox: return "收集箱"
        case .waiting: return "等待中"
        case .smb: return "将来也许"
        case .done: return "已完成"
        default: return status.description
        }
    }
    
    /// 空状态图标
    private var emptyStateIcon: String {
        switch status {
        case .inbox: return "tray"
        case .waiting: return "hourglass"
        case .smb: return "sparkles"
        case .done: return "checkmark.circle"
        default: return "doc"
        }
    }
    
    /// 空状态提示信息
    private var emptyStateMessage: String {
        switch status {
        case .inbox: return "收集箱中没有行动\n你可以添加想法和灵感"
        case .waiting: return "没有等待中的行动\n可以把依赖他人的行动放在这里"
        case .smb: return "将来也许中没有行动\n可以把将来有可能做的事情收集在这里"
        case .done: return "没有已完成的行动\n完成的行动会显示在这里"
        default: return "没有行动"
        }
    }
    
    /// 根据项目ID获取项目名称
    private func getProjectName(for projectId: UUID) -> String {
        return viewModel.getProjectNameById(projectId: projectId)
    }
}

// MARK: - 辅助类型

/// 排序方式
enum SortOrder {
    case dateCreatedDesc   // 创建时间降序（新的在前）
    case dateCreatedAsc    // 创建时间升序（旧的在前）
    case byProject         // 按项目排序
}

/// 显示字段选项集
struct DisplayFields: OptionSet {
    let rawValue: Int
    
    static let title = DisplayFields(rawValue: 1 << 0)
    static let projectTag = DisplayFields(rawValue: 1 << 1)
    static let completedDate = DisplayFields(rawValue: 1 << 2)
    
    static let titleOnly: DisplayFields = [.title]
    static let standard: DisplayFields = [.title, .projectTag]
    static let completed: DisplayFields = [.title, .completedDate]
}

// MARK: - 预览
struct FilteredTaskListView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            FilteredTaskListView(status: .inbox, 
                                 supportsBatchOperations: true,
                                 displayFields: .titleOnly)
        }
    }
}
