import SwiftUI

struct GuideView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    Text("一步理念")
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding(.bottom, 4)
                    
                    VStack(alignment: .leading, spacing: 16) {
                        Text("你可能有几十个行动，但真正能做的，只有眼前这一两个。")
                            .font(.body)
                        
                        Text("「一步」让你从思考里抽身出来，回到行动中去。")
                            .font(.body)
                        
                        Text("它不是清单，而是你当下的舞台。")
                            .font(.body)
                            .padding(.bottom, 8)
                    }
                    .padding(16)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    
                    Text("使用方式")
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding(.top, 8)
                        .padding(.bottom, 4)
                    
                    VStack(alignment: .leading, spacing: 16) {
                        HStack(alignment: .top, spacing: 12) {
                            Text("1")
                                .font(.headline)
                                .foregroundColor(.white)
                                .frame(width: 24, height: 24)
                                .background(Circle().fill(Color.blue))
                            
                            Text("选三个以内行动放到这里，开始做。")
                                .font(.body)
                        }
                        
                        HStack(alignment: .top, spacing: 12) {
                            Text("2")
                                .font(.headline)
                                .foregroundColor(.white)
                                .frame(width: 24, height: 24)
                                .background(Circle().fill(Color.blue))
                            
                            Text("做完就打钩，不用管其他。")
                                .font(.body)
                        }
                        
                        HStack(alignment: .top, spacing: 12) {
                            Text("3")
                                .font(.headline)
                                .foregroundColor(.white)
                                .frame(width: 24, height: 24)
                                .background(Circle().fill(Color.blue))
                            
                            Text("每完成一项，就是一小步，也是一个胜利。")
                                .font(.body)
                        }
                    }
                    .padding(16)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                }
                .padding()
            }
            .navigationTitle("引导")
            .navigationBarItems(trailing: Button("关闭") {
                presentationMode.wrappedValue.dismiss()
            })
        }
    }
}

struct GuideView_Previews: PreviewProvider {
    static var previews: some View {
        GuideView()
    }
}