import SwiftUI
import UIKit

/// 共享的Markdown富文本编辑器组件
/// 可用于全屏模式和半屏模式
struct SharedMarkdownEditor: View {
    // MARK: - 属性
    @Binding var text: String
    var placeholder: String
    var onSave: () -> Void
    var onCancel: (() -> Void)?
    var isCompactMode: Bool = false // 是否为紧凑模式（半屏使用）
    var hideKeyboardToolbar: Bool = false // 是否隐藏键盘上方的工具栏
    var onViewCreated: ((RichTextView) -> Void)? // 添加视图创建回调
    var keyboardHeight: CGFloat = 0 // Keyboard height IS passed in
    
    // MARK: - 状态
    // Initial height doesn't matter as much now, frame logic controls it.
    @State private var calculatedHeight: CGFloat = 130 // Default to compact min height
    @State private var editorView: RichTextView?
    
    // MARK: - Constants for Compact Mode Resizing
    private let compactMinHeight: CGFloat = 130
    // Calculate max height based on keyboardHeight
    private var compactMaxHeight: CGFloat {
        let screenHeight = UIScreen.main.bounds.height
        let safeAreaBottomInset = UIApplication.shared.connectedScenes
            .compactMap { $0 as? UIWindowScene }
            .first?.windows.first?.safeAreaInsets.bottom ?? 0
        let safeAreaTopInset = UIApplication.shared.connectedScenes
            .compactMap { $0 as? UIWindowScene }
            .first?.windows.first?.safeAreaInsets.top ?? 44
            
        // Calculate available space above keyboard, considering safe areas
        let spaceAboveKeyboard = screenHeight - keyboardHeight - safeAreaBottomInset - safeAreaTopInset
        
        // Use 75% of the space above keyboard, with padding
        let padding: CGFloat = 20
        let calculatedMax = (spaceAboveKeyboard - padding) * 0.75 // Apply percentage here
        
        
        // Ensure max height is at least min height
        return max(compactMinHeight, calculatedMax)
    }
    
    // MARK: - 主视图
    var body: some View {
        let editor = RichTextEditor(
            text: $text,
            calculatedHeight: $calculatedHeight,
            placeholder: placeholder,
            onViewCreated: { view in
                editorView = view
                onViewCreated?(view)
            },
            onSave: onSave,
            hideKeyboardToolbar: hideKeyboardToolbar,
            isCompactMode: isCompactMode
            // keyboardHeight is now a property, not passed to RichTextEditor
        )
        .background(Color(.systemBackground))
        
        // Apply dynamic frame ONLY in compact mode
        if isCompactMode {
            editor
                .frame(height: max(compactMinHeight, min(calculatedHeight, compactMaxHeight)))
                .animation(.smooth(duration: 0.2), value: calculatedHeight)
        } else {
            editor
        }
    }
}

#Preview {
    // Preview needs adjustment if we rely on GeometryReader height
    // Wrapping in a fixed frame container for preview
    VStack {
        Spacer() // Push to bottom to simulate keyboard up
        NoteInputView(
            noteText: .constant("这是一条测试笔记\n第二行\n第三行"),
            onSave: {},
            onCancel: {}
        )
        .frame(height: UIScreen.main.bounds.height * 0.7) // Simulate container height
    }
    .background(Color.gray.opacity(0.2))
    .ignoresSafeArea(.keyboard)
}