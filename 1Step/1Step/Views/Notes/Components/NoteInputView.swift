import SwiftUI
import UIKit
import Combine // Import Combine for keyboard publisher

/// flomo风格半屏笔记输入视图组件
struct NoteInputView: View {
    // MARK: - 属性
    @Environment(\.colorScheme) private var colorScheme
    @Binding var noteText: String
    var onSave: () -> Void
    var onCancel: () -> Void
    
    // MARK: - 状态
    @FocusState private var isInputFocused: Bool
    @State private var selectedRange: NSRange? = nil
    
    // 添加状态标记自动聚焦
    @State private var editorView: RichTextView?
    @State private var hasFocusRequested = false // 跟踪是否已请求焦点
    @State private var keyboardHeight: CGFloat = 0 // State to store keyboard height
    
    // MARK: - 主视图
    var body: some View {
        VStack(spacing: 0) {
            // Top drag bar (unchanged)
            HStack {
                Rectangle()
                    .fill(Color(.systemGray5))
                    .frame(width: 36, height: 4)
                    .cornerRadius(2)
            }
            .frame(height: 24)
            .padding(.top, 8)
            
            // SharedMarkdownEditor passing keyboard height
            SharedMarkdownEditor(
                text: $noteText,
                placeholder: "记录你的想法...",
                onSave: onSave,
                onCancel: onCancel,
                isCompactMode: true,
                onViewCreated: { view in
                    self.editorView = view
                    requestKeyboardFocus(view)
                },
                keyboardHeight: keyboardHeight
            )
        }
        .background(Color(.systemBackground))
        .cornerRadius(16, corners: [.topLeft, .topRight])
        .onTapGesture {} // Absorb taps
        .onAppear {
            hasFocusRequested = false
            // Subscribe to keyboard notifications
            subscribeToKeyboardEvents()
        }
        .onDisappear {
            // Unsubscribe from keyboard notifications
            unsubscribeFromKeyboardEvents()
        }
    }
    
    // MARK: - 私有方法
    
    /// 请求键盘聚焦 - 参考AddTaskFormView的实现
    private func requestKeyboardFocus(_ textView: RichTextView) {
        guard !hasFocusRequested else { return }
        
        // 添加震动反馈
        UIImpactFeedbackGenerator(style: .light).impactOccurred()
        
        hasFocusRequested = true
        
        // 使用更复杂的延迟模式确保键盘显示
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
            // 短暂取消第一响应者状态
            textView.resignFirstResponder()
            
            // 然后再次设置为第一响应者
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                textView.becomeFirstResponder()
                
                // 打印调试信息
            }
        }
    }
    
    // MARK: - Keyboard Handling
    
    private func subscribeToKeyboardEvents() {
        NotificationCenter.default.addObserver(forName: UIResponder.keyboardWillShowNotification, object: nil, queue: .main) { notification in
            handleKeyboardShow(notification: notification)
        }
        
        NotificationCenter.default.addObserver(forName: UIResponder.keyboardWillHideNotification, object: nil, queue: .main) { notification in
            handleKeyboardHide(notification: notification)
        }
    }

    private func unsubscribeFromKeyboardEvents() {
        NotificationCenter.default.removeObserver(self, name: UIResponder.keyboardWillShowNotification, object: nil)
        NotificationCenter.default.removeObserver(self, name: UIResponder.keyboardWillHideNotification, object: nil)
    }

    private func handleKeyboardShow(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let keyboardFrame = userInfo[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect else { return }
        
        let newHeight = keyboardFrame.height
        // Avoid unnecessary updates if height is the same
        if newHeight != keyboardHeight {
             keyboardHeight = newHeight
        }
    }

    private func handleKeyboardHide(notification: Notification) {
        if keyboardHeight != 0 {
            keyboardHeight = 0
        }
    }
}

// Preview might need adjustment again
#Preview {
    // Simulate presenting in a sheet-like container
    VStack {
        Spacer()
        NoteInputView(
            noteText: .constant("Test"),
            onSave: {},
            onCancel: {}
        )
    }
    .background(Color.blue.opacity(0.2)) // Container background
    .ignoresSafeArea(.keyboard)
} 