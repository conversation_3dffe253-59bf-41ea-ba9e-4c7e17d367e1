import SwiftUI
import UIKit

/// 笔记富文本编辑器 (全屏)
struct MarkdownNoteEditor: View {
    // MARK: - 属性
    @Binding var text: String
    var placeholder: String
    var onSave: () -> Void
    var onCancel: () -> Void
    
    // MARK: - 状态
    @State private var calculatedHeight: CGFloat = 200
    @State private var currentDate = Date()
    
    var body: some View {
        VStack(spacing: 0) {
            // 顶部栏
            topNavigationBar
            
            // 分隔线
            Rectangle()
                .fill(Color(.systemGray5))
                .frame(height: 1)
            
            // 使用共享的Markdown编辑器组件 - 填充剩余空间
            SharedMarkdownEditor(
                text: $text,
                placeholder: placeholder,
                onSave: onSave,
                onCancel: onCancel,
                isCompactMode: false
            )
            .frame(maxHeight: .infinity) // 强制填充剩余空间
        }
        .background(Color(.systemBackground))
        .onAppear {
            currentDate = Date()
        }
    }
    
    // 顶部导航栏
    private var topNavigationBar: some View {
        HStack {
            // 左侧返回按钮
            Button(action: onCancel) {
                Image(systemName: "chevron.down")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.secondary)
            }
            .frame(width: 44, height: 44)
            
            // 时间显示居中
            Text(currentDate.formatted(date: .numeric, time: .shortened))
                .font(.system(size: 14))
                .foregroundColor(Color(.systemGray))
            
            Spacer()
        }
        .padding(.horizontal, 8)
    }
}

/// 富文本编辑器 (UIKit包装)
struct RichTextEditor: UIViewRepresentable {
    @Binding var text: String
    @Binding var calculatedHeight: CGFloat
    var placeholder: String
    var onViewCreated: ((RichTextView) -> Void)?
    var onSave: (() -> Void)?
    
    // 添加参数控制工具栏显示
    var hideKeyboardToolbar: Bool = false
    var isCompactMode: Bool = false
    
    func makeUIView(context: Context) -> RichTextView {
        let textView = RichTextView()
        textView.delegate = context.coordinator
        
        // Setup based on mode
        if isCompactMode {
            textView.setup(placeholder: placeholder, isCompactMode: true)
        } else {
            textView.setup(placeholder: placeholder)
        }
        if !text.isEmpty { textView.text = text }
        
        // Revert: Add toolbar UNLESS hideKeyboardToolbar is true
        if !hideKeyboardToolbar {
            let toolbar = createToolbar(textView: textView)
            textView.inputAccessoryView = toolbar
        } else {
            textView.inputAccessoryView = nil
        }
        
        textView.onMarkdownUpdated = { newText in
            DispatchQueue.main.async {
                self.text = newText
            }
        }
        textView.onSave = onSave
        
        // Add gesture recognizer dependency for sheet dismissal
        // Delay slightly to ensure the view is in the hierarchy
        DispatchQueue.main.async {
            if let sheetGesture = textView.findSheetPanGestureRecognizer() {
                textView.panGestureRecognizer.require(toFail: sheetGesture)
            }
        }
        
        onViewCreated?(textView)
        return textView
    }
    
    func updateUIView(_ uiView: RichTextView, context: Context) {
        // 只在文本真正变化时更新，避免循环
        if text != uiView.storedMarkdownText && !uiView.isUpdatingText {
            uiView.setCustomMarkdownText(text)
        }
    }
    
    func makeCoordinator() -> Coordinator {
        return Coordinator(self)
    }
    
    // 创建贴合键盘的工具栏 (Flomo风格)
    private func createToolbar(textView: RichTextView) -> UIView {
        // 创建自定义工具栏容器，更接近Flomo风格
        let containerView = UIView(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 44))
        containerView.backgroundColor = UIColor.systemBackground
        
        // 添加上边框线
        let borderView = UIView(frame: CGRect(x: 0, y: 0, width: containerView.frame.width, height: 0.5))
        borderView.backgroundColor = UIColor.systemGray5
        containerView.addSubview(borderView)
        
        // 创建工具栏按钮容器
        let buttonContainer = UIView(frame: CGRect(x: 0, y: 0, width: containerView.frame.width, height: 44))
        buttonContainer.backgroundColor = .clear
        
        // 粗体按钮
        let boldButton = createToolbarButton(icon: "bold", action: #selector(textView.toggleBold), target: textView)
        
        // 列表按钮
        let listButton = createToolbarButton(icon: "list.bullet", action: #selector(textView.toggleList), target: textView)
        
        // 保存按钮 - 调整为更小的尺寸，更符合Flomo风格
        let saveButton = UIButton(type: .system)
        saveButton.backgroundColor = UIColor.systemGreen
        saveButton.tintColor = UIColor.white
        saveButton.layer.cornerRadius = 16 // 从20改为16
        saveButton.setImage(UIImage(systemName: "checkmark")?.withConfiguration(
            UIImage.SymbolConfiguration(pointSize: 14, weight: .medium)), for: .normal) // 从16pt改为14pt
        saveButton.addTarget(textView, action: #selector(textView.saveAction), for: .touchUpInside)
        
        // 添加常规按钮到容器
        let buttons = [boldButton, listButton]
        let buttonWidth: CGFloat = 44
        let spacing: CGFloat = 16
        
        var xPosition: CGFloat = 16
        for button in buttons {
            button.frame = CGRect(x: xPosition, y: 0, width: buttonWidth, height: 44)
            buttonContainer.addSubview(button)
            xPosition += buttonWidth + spacing
        }
        
        // 添加保存按钮到右侧 - 调整大小
        let saveButtonWidth: CGFloat = 32 // 从44改为32
        let saveButtonHeight: CGFloat = 32 // 从40改为32
        let saveButtonX = containerView.frame.width - saveButtonWidth - 16
        let saveButtonY = (44 - saveButtonHeight) / 2 // 垂直居中
        saveButton.frame = CGRect(x: saveButtonX, y: saveButtonY, width: saveButtonWidth, height: saveButtonHeight)
        buttonContainer.addSubview(saveButton)
        
        containerView.addSubview(buttonContainer)
        
        return containerView
    }
    
    // 创建Flomo风格的工具栏按钮
    private func createToolbarButton(icon: String, action: Selector, target: Any?) -> UIButton {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: icon)?
            .withConfiguration(UIImage.SymbolConfiguration(pointSize: 17, weight: .medium)), for: .normal)
        button.backgroundColor = UIColor.clear
        button.tintColor = UIColor.systemGray
        
        // 添加按钮点击时的视觉反馈
        button.showsTouchWhenHighlighted = true
        
        button.addTarget(target, action: action, for: .touchUpInside)
        return button
    }
    
    class Coordinator: NSObject, UITextViewDelegate {
        var parent: RichTextEditor
        
        init(_ parent: RichTextEditor) {
            self.parent = parent
        }
        
        // MARK: - UITextViewDelegate Methods
        
        func textViewDidChange(_ textView: UITextView) {
            // Cast to RichTextView to access its properties
            guard let richTextView = textView as? RichTextView else { return }
            
            // Update height calculation
            let newSize = textView.sizeThatFits(CGSize(width: textView.frame.size.width, height: CGFloat.greatestFiniteMagnitude))
            if parent.calculatedHeight != newSize.height {
                DispatchQueue.main.async {
                    // Check if parent still exists
                    guard self.parent != nil else { return }
                    self.parent.calculatedHeight = newSize.height
                }
            }
            
            // Trigger markdown update using the text view's debouncer
            richTextView.textDidChange() // Call the existing @objc func
        }
        
        func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
            guard let richTextView = textView as? RichTextView else { return true }
            
            // 检测到回车键时重置isBoldMode
            if text == "\n" {
                print("[BoldReset] Enter key pressed. Resetting isBoldMode.")
                richTextView.isBoldMode = false
                // 同时立即重置typingAttributes
                richTextView.typingAttributes[.font] = richTextView.defaultFont
                richTextView.typingAttributes.removeValue(forKey: RichTextView.AttributeKey.isBold)
            }
            
            return true
        }
        
        func textViewDidChangeSelection(_ textView: UITextView) {
            // 1. 检查中文输入，如果是则返回
            guard textView.markedTextRange == nil else {
                return
            }
            
            guard let richTextView = textView as? RichTextView else { return }
            
            // 2. 移除所有行首判断逻辑，始终根据 isBoldMode 同步 typingAttributes
            // 确保光标移动后，后续输入的样式与当前模式一致。
            if richTextView.isBoldMode {
                richTextView.typingAttributes[.font] = richTextView.boldFont
                richTextView.typingAttributes[RichTextView.AttributeKey.isBold] = true
            } else {
                richTextView.typingAttributes[.font] = richTextView.defaultFont
                richTextView.typingAttributes.removeValue(forKey: RichTextView.AttributeKey.isBold)
            }
        }
    }
}

/// 富文本编辑视图
class RichTextView: UITextView {
    // MARK: - 属性
    // 标识符，用于属性字符串中的属性键
    struct AttributeKey {
        static let isBold = NSAttributedString.Key("isBold")
        static let isList = NSAttributedString.Key("isList")
        static let isOrderedList = NSAttributedString.Key("isOrderedList") // 有序列表标记
        static let listItemNumber = NSAttributedString.Key("listItemNumber") // 列表项编号
    }
    
    // 列表模式枚举
    enum ListMode {
        case none
        case unordered // 无序列表 (-)
        case ordered   // 有序列表 (1.)
        
        func next() -> ListMode {
            switch self {
            case .none: return .unordered
            case .unordered: return .ordered
            case .ordered: return .none
            }
        }
    }
    
    // 当前编辑状态
    var isBoldMode = false
    var listMode = ListMode.none
    
    var placeholderText: String = ""
    var storedMarkdownText: String = ""
    var isUpdatingText: Bool = false
    private var preventTextChanges = false  // 防止文本更新循环
    
    // 检测中文输入状态
    private var previousHadMarkedText = false
    
    // 样式 - 优化为更接近flomo的样式
    let defaultFont = UIFont.systemFont(ofSize: 17, weight: .regular)
    let boldFont = UIFont.systemFont(ofSize: 17, weight: .semibold)
    
    // 防止循环更新计时器
    private var textUpdateTimer: Timer?
    
    // 文本更新回调 - 优雅的数据同步解决方案
    var onMarkdownUpdated: ((String) -> Void)?
    
    // 保存回调
    var onSave: (() -> Void)?
    
    // MARK: - 生命周期
    override init(frame: CGRect, textContainer: NSTextContainer?) {
        super.init(frame: frame, textContainer: textContainer)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - 生命周期方法
    override func didMoveToSuperview() {
        super.didMoveToSuperview()
        if let superview = superview {
            NotificationCenter.default.addObserver(self, selector: #selector(textDidChange), name: UITextView.textDidChangeNotification, object: self)
        }
    }
    
    override func removeFromSuperview() {
        super.removeFromSuperview()
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - 方法
    func setup(placeholder: String) {
        self.placeholderText = placeholder
        self.font = UIFont.systemFont(ofSize: 17, weight: .regular)
        self.isSelectable = true
        self.isUserInteractionEnabled = true
        self.isEditable = true
        self.isScrollEnabled = true
        self.backgroundColor = UIColor.clear
        self.returnKeyType = .default
        self.keyboardType = .default
        self.autocapitalizationType = .sentences
        self.autocorrectionType = .yes
        
        self.textContainerInset = UIEdgeInsets(top: 16, left: 16, bottom: 16, right: 16)
        
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = 8
        self.typingAttributes[.paragraphStyle] = paragraphStyle
        self.typingAttributes[.foregroundColor] = UIColor(red: 51/255, green: 51/255, blue: 51/255, alpha: 1)
        
        self.textColor = UIColor(red: 51/255, green: 51/255, blue: 51/255, alpha: 1)
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(textDidChange),
            name: UITextView.textDidChangeNotification,
            object: self
        )
    }
    
    func setup(placeholder: String, isCompactMode: Bool = false) {
        self.placeholderText = placeholder
        self.font = UIFont.systemFont(ofSize: 17, weight: .regular)
        self.isSelectable = true
        self.isUserInteractionEnabled = true
        self.isEditable = true
        self.isScrollEnabled = true
        self.backgroundColor = UIColor.clear
        self.returnKeyType = .default
        self.keyboardType = .default
        self.autocapitalizationType = .sentences
        self.autocorrectionType = .yes
        
        if isCompactMode {
            self.textContainerInset = UIEdgeInsets(top: 8, left: 16, bottom: 8, right: 16)
        } else {
            self.textContainerInset = UIEdgeInsets(top: 16, left: 16, bottom: 16, right: 16)
        }
        
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = 8
        self.typingAttributes[.paragraphStyle] = paragraphStyle
        self.typingAttributes[.foregroundColor] = UIColor(red: 51/255, green: 51/255, blue: 51/255, alpha: 1)
        
        self.textColor = UIColor(red: 51/255, green: 51/255, blue: 51/255, alpha: 1)
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(textDidChange),
            name: UITextView.textDidChangeNotification,
            object: self
        )
    }
    
    @objc func textDidChange() {
        // *** 恢复：检查是否有标记文本 (中文输入中) ***
        guard markedTextRange == nil else {
            print("[textDidChange] Ignoring change due to marked text.")
            return
        }
        
        if preventTextChanges {
            return
        }
        
        print("NoteSaveDebug: textDidChange通知触发")
        
        // 移除中文输入检测和特殊处理
        // 简化逻辑，只保持格式同步
        
        // 应用当前格式设置
        if isBoldMode {
            typingAttributes[.font] = boldFont
            typingAttributes[AttributeKey.isBold] = true
            print("[BoldSync] 应用粗体格式 (isBoldMode: \(isBoldMode))")
        } else {
            typingAttributes[.font] = defaultFont
            typingAttributes.removeValue(forKey: AttributeKey.isBold)
            print("[BoldSync] 应用普通格式 (isBoldMode: \(isBoldMode))")
        }
        
        textUpdateTimer?.invalidate()
        
        textUpdateTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: false) { [weak self] _ in
            print("NoteSaveDebug: 防抖计时器触发")
            self?.updateMarkdownFromAttributedText()
        }
    }
    
    func setCustomMarkdownText(_ markdown: String) {
        guard !isUpdatingText else { return }
        isUpdatingText = true
        
        storedMarkdownText = markdown
        
        if markdown.isEmpty || markdown == placeholderText {
            text = markdown
            isUpdatingText = false
            return
        }
        
        let selectedRange = self.selectedRange
        let contentOffset = self.contentOffset
        
        // 确保换行符被正确处理
        let processedMarkdown = ensureProperLineBreaks(in: markdown)
        
        // 使用系统API将Markdown转换为AttributedString
        do {
            let systemAttributedString = try AttributedString(markdown: processedMarkdown)
            let nsAttributedString = NSAttributedString(systemAttributedString)
            
            // *** 核心修复：应用字体和段落样式 ***
            let mutableAttrString = NSMutableAttributedString(attributedString: nsAttributedString)
            let fullRange = NSRange(location: 0, length: mutableAttrString.length)
            
            // 获取默认字体和段落样式
            let baseFont = self.defaultFont // 17pt 字体
            let paragraphStyle = NSMutableParagraphStyle()
            paragraphStyle.lineSpacing = 8
            
            // 遍历并应用基础样式，但保留特殊样式(如加粗)
            mutableAttrString.beginEditing()
            mutableAttrString.enumerateAttributes(in: fullRange, options: []) { attributes, range, _ in
                var newAttributes = attributes
                // 如果没有字体属性，或者不是粗体，则应用基础字体
                if newAttributes[.font] == nil || (newAttributes[.font] as? UIFont) != self.boldFont {
                    newAttributes[.font] = baseFont
                }
                // 应用基础段落样式
                newAttributes[.paragraphStyle] = paragraphStyle
                mutableAttrString.setAttributes(newAttributes, range: range)
            }
            mutableAttrString.endEditing()
            
            // 保存选择范围和滚动位置
            preventTextChanges = true
            
            // 应用带样式的富文本
            self.attributedText = mutableAttrString
            
            // 恢复选择范围和滚动位置
            if selectedRange.location <= self.text.count {
                self.selectedRange = selectedRange
                self.contentOffset = contentOffset
            }
            
            preventTextChanges = false
        } catch {
            // 如果系统API失败，使用简单文本
            print("Markdown解析错误: \(error)")
            
            // *** 核心修复：纯文本时也应用字体和段落样式 ***
            let mutableAttrString = NSMutableAttributedString(string: markdown)
            let fullRange = NSRange(location: 0, length: mutableAttrString.length)
            
            // 应用基础样式
            let paragraphStyle = NSMutableParagraphStyle()
            paragraphStyle.lineSpacing = 8
            mutableAttrString.addAttribute(.font, value: self.defaultFont, range: fullRange)
            mutableAttrString.addAttribute(.paragraphStyle, value: paragraphStyle, range: fullRange)
            
            preventTextChanges = true
            self.attributedText = mutableAttrString
            preventTextChanges = false
        }
        
        isUpdatingText = false
    }
    
    // 确保Markdown中换行符被正确处理
    private func ensureProperLineBreaks(in text: String) -> String {
        // 在Markdown中，单个换行不会产生新行，需要两个换行或行尾加两个空格
        var result = text
        
        // 查找所有单独的换行符（不是两个连续的）
        let pattern = "([^\n])\n([^\n])"
        if let regex = try? NSRegularExpression(pattern: pattern, options: []) {
            // 从后向前替换，以避免替换位置偏移
            let nsString = NSString(string: result)
            let matches = regex.matches(in: result, options: [], range: NSRange(location: 0, length: nsString.length))
            
            for match in matches.reversed() {
                let range = match.range
                let matchText = nsString.substring(with: range)
                // 在换行前添加两个空格，确保Markdown识别为换行
                let replacement = matchText.replacingOccurrences(of: "\n", with: "  \n")
                result = (result as NSString).replacingCharacters(in: range, with: replacement)
            }
        }
        
        return result
    }
    
    @objc func updateMarkdownFromAttributedText() {
        guard !isUpdatingText else { return }
        isUpdatingText = true
        
        // 构建带有Markdown标记的文本
        if let attributedText = self.attributedText {
            let mutableAttributedText = NSMutableAttributedString(attributedString: attributedText)
            let fullRange = NSRange(location: 0, length: mutableAttributedText.length)
            
            // 处理加粗文本
            mutableAttributedText.enumerateAttribute(.font, in: fullRange, options: []) { (value, range, stop) in
                if let font = value as? UIFont, font == self.boldFont {
                    // 检查是否已经有**标记
                    let rangeText = mutableAttributedText.attributedSubstring(from: range).string
                    if !rangeText.hasPrefix("**") && !rangeText.hasSuffix("**") {
                        // 应用Markdown标记
                        let boldText = "**" + rangeText + "**"
                        let newText = NSAttributedString(string: boldText)
                        mutableAttributedText.replaceCharacters(in: range, with: newText)
                    }
                }
            }
            
            storedMarkdownText = mutableAttributedText.string
        } else {
            storedMarkdownText = self.text
        }
        
        onMarkdownUpdated?(storedMarkdownText)
        isUpdatingText = false
    }
    
    @objc func insertTag() {
        insertTextAtCursor("#")
    }
    
    @objc func insertImage() {
        insertTextAtCursor("![图片]")
    }
    
    private func insertTextAtCursor(_ text: String) {
        let currentRange = self.selectedRange
        
        guard var currentText = self.text else { return }
        
        currentText.insert(contentsOf: text, at: currentText.index(currentText.startIndex, offsetBy: currentRange.location))
        self.text = currentText
        
        self.selectedRange = NSRange(location: currentRange.location + text.count, length: 0)
        
        self.textDidChange()
    }
    
    @objc func toggleBold() {
        print("[BoldToggle] Toggle button pressed. Current isBoldMode: \(isBoldMode)")
        isBoldMode = !isBoldMode
        print("[BoldToggle] New isBoldMode: \(isBoldMode)")
        
        if let selectedTextRange = self.selectedTextRange {
            if !selectedTextRange.isEmpty {
                // 处理选中文本的情况
                if let nsRange = nsRange(from: selectedTextRange) {
                    // 使用NSString处理substring操作，避免Range<String.Index>转换问题
                    let nsString = text as NSString
                    let selectedText = nsString.substring(with: nsRange)
                    
                    // 检查选中文本是否已经被**包围
                    if selectedText.hasPrefix("**") && selectedText.hasSuffix("**") {
                        // 移除包围的**标记
                        let plainText = String(selectedText.dropFirst(2).dropLast(2))
                        let newText = NSMutableAttributedString(string: plainText)
                        
                        // 保存状态
                        let selectedRangeBefore = self.selectedRange
                        let contentOffset = self.contentOffset
                        preventTextChanges = true
                        
                        // 替换文本
                        if let attributedText = self.attributedText {
                            let mutableAttributedText = NSMutableAttributedString(attributedString: attributedText)
                            mutableAttributedText.replaceCharacters(in: nsRange, with: newText)
                            self.attributedText = mutableAttributedText
                            
                            // 调整选择范围以考虑去除的**标记
                            let newLength = plainText.count
                            self.selectedRange = NSRange(location: selectedRangeBefore.location, length: newLength)
                        }
                        
                        preventTextChanges = false
                        self.contentOffset = contentOffset
                    } else {
                        // 添加**标记使文本变粗
                        let boldText = "**\(selectedText)**"
                        let newText = NSMutableAttributedString(string: boldText)
                        
                        // 保存状态
                        let selectedRangeBefore = self.selectedRange
                        let contentOffset = self.contentOffset
                        preventTextChanges = true
                        
                        // 替换文本
                        if let attributedText = self.attributedText {
                            let mutableAttributedText = NSMutableAttributedString(attributedString: attributedText)
                            mutableAttributedText.replaceCharacters(in: nsRange, with: newText)
                            self.attributedText = mutableAttributedText
                            
                            // 调整选择范围以包含新添加的**标记
                            let newLength = boldText.count
                            self.selectedRange = NSRange(location: selectedRangeBefore.location, length: newLength)
                        }
                        
                        preventTextChanges = false
                        self.contentOffset = contentOffset
                    }
                    
                    // 触发更新
                    textUpdateTimer?.invalidate()
                    textUpdateTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: false) { [weak self] _ in
                        self?.updateMarkdownFromAttributedText()
                    }
                }
            } else {
                // 光标位置，没有选中文本
                // 不插入任何Markdown标记，只改变文本样式和记录模式状态
                if isBoldMode {
                    // 启用粗体模式，应用粗体样式
                    typingAttributes[.font] = boldFont
                    typingAttributes[AttributeKey.isBold] = true
                } else {
                    // 关闭粗体模式，恢复普通字体样式
                    typingAttributes[.font] = defaultFont
                    typingAttributes.removeValue(forKey: AttributeKey.isBold)
                }
                
                print("[BoldToggle] TypingAttributes updated. Font: \(String(describing: typingAttributes[.font])), isBold: \(typingAttributes[AttributeKey.isBold] != nil)")
            }
        }
    }
    
    // 添加一个辅助方法来重置粗体状态
    func resetBoldState() {
        isBoldMode = false
        typingAttributes.removeValue(forKey: AttributeKey.isBold)
        typingAttributes[.font] = defaultFont
    }
    
    @objc func toggleList() {
        let currentRange = selectedRange
        guard currentRange.location != NSNotFound else { return }

        // 获取 textStorage 进行富文本操作
        let textStorage = self.textStorage
        let lineRange = (textStorage.string as NSString).lineRange(for: currentRange)

        // 决定插入的前缀和范围
        let listPrefixString: String
        let insertionRange: NSRange

        // 检查当前行是否已经是列表项 (以 "· " 开头)
        let lineText = textStorage.attributedSubstring(from: lineRange).string
        let trimmedLine = lineText.trimmingCharacters(in: .whitespacesAndNewlines)
        
        if trimmedLine.hasPrefix("· ") {
            // 如果已经是列表项，则移除 "· "
            let prefixRange = NSRange(location: lineRange.location, length: 2) // "· " 的长度为 2
            // 创建一个空的 AttributedString 来替换
            let replacement = NSAttributedString(string: "") 
            
            // 执行替换
            textStorage.replaceCharacters(in: prefixRange, with: replacement)
            
            // 调整光标位置 (向前移动 2)
            let newCursorLocation = max(0, currentRange.location - 2)
            self.selectedRange = NSRange(location: newCursorLocation, length: 0)

        } else {
            // 如果不是列表项，则添加 "· "
            listPrefixString = "· "
            insertionRange = NSRange(location: lineRange.location, length: 0) // 在行首插入

            // 创建带默认格式的前缀 AttributedString
            // 使用当前的 typingAttributes 来格式化插入的文本
            var attributes = typingAttributes
            if attributes[.font] == nil { // 确保有字体
                attributes[.font] = defaultFont
            }
            let listPrefixAttrString = NSAttributedString(string: listPrefixString, attributes: attributes)

            // 执行插入
            textStorage.insert(listPrefixAttrString, at: insertionRange.location)

            // 调整光标位置 (向后移动 2)
            self.selectedRange = NSRange(location: currentRange.location + listPrefixAttrString.length, length: 0)
        }
        
        // 手动触发 delegate 方法，以便更新绑定和计算高度等
        self.delegate?.textViewDidChange?(self)
    }
    
    private func findViewController() -> UIViewController? {
        var responder: UIResponder? = self
        while let nextResponder = responder?.next {
            if let viewController = nextResponder as? UIViewController {
                return viewController
            }
            responder = nextResponder
        }
        return nil
    }
    
    private func nsRange(from textRange: UITextRange) -> NSRange? {
        print("[BoldToggle] nsRange(from:) called with UITextRange: start=\(textRange.start), end=\(textRange.end)")
        let location = offset(from: beginningOfDocument, to: textRange.start)
        let length = offset(from: textRange.start, to: textRange.end)
        // Check for invalid location or length (e.g., happens with certain selections)
        if location == NSNotFound || location < 0 || length < 0 {
             print("[BoldToggle] nsRange(from:) failed. Invalid location (\(location)) or length (\(length)).")
             return nil
        }
        let resultRange = NSRange(location: location, length: length)
        print("[BoldToggle] nsRange(from:) success. Result: \(resultRange)")
        return resultRange
    }
    
    @objc func saveAction() {
        updateMarkdownFromAttributedText()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            print("NoteSaveDebug: 保存按钮点击")
            self?.onSave?()
            print("NoteSaveDebug: 保存回调执行完成")
        }
    }
}

// Add helper extension to UIView to find the sheet's gesture recognizer
extension UIView {
    func findSheetPanGestureRecognizer() -> UIPanGestureRecognizer? {
        var currentView: UIView? = self
        while let superview = currentView?.superview {
            // Look for the specific gesture recognizer used by sheets (might need adjustment)
            // Often associated with a view named like "_UIHostingView" or similar
            // This is fragile and might break with iOS updates.
            if let hostingViewGestureRecognizers = superview.gestureRecognizers?.filter({ $0 is UIPanGestureRecognizer }) {
                // Find the one most likely related to sheet dismissal (e.g., vertical pan)
                // This identification logic might need refinement.
                for recognizer in hostingViewGestureRecognizers {
                     if let panRecognizer = recognizer as? UIPanGestureRecognizer {
                         // Basic check: Does it primarily recognize vertical movement?
                         // You might need more specific checks if multiple pan recognizers exist.
                         let translation = panRecognizer.translation(in: superview)
                         if abs(translation.y) > abs(translation.x) || panRecognizer.state == .possible {
                             // This is a likely candidate
                              return panRecognizer
                         }
                     }
                 }
            }
            currentView = superview
            // Stop searching at the window level
            if currentView is UIWindow { break }
        }
        return nil
    }
} 