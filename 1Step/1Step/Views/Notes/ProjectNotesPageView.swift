import SwiftUI

/// 项目笔记页面 - 展示项目的所有笔记
struct ProjectNotesPageView: View {
    // MARK: - 依赖属性
    @ObservedObject var viewModel: ProjectDetailViewModel
    
    // MARK: - 状态
    @State private var editingNote: Task? = nil
    @State private var showingAddNote: Bool = false
    @State private var searchText: String = ""
    @State private var showOnlyFocused: Bool = false
    @State private var editingNoteContent: String = ""
    @State private var showingHalfScreenSheet: Bool = false
    @State private var newNote: String = ""
    
    // MARK: - 过滤笔记
    private var filteredNotes: [Task] {
        var notes = viewModel.notes
        
        // 按焦点过滤
        if showOnlyFocused {
            notes = notes.filter { $0.isFocused }
        }
        
        // 按搜索文本过滤
        if !searchText.isEmpty {
            notes = notes.filter { note in
                note.noteTitle.localizedCaseInsensitiveContains(searchText) ||
                note.notes.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        // 按时间排序
        return notes.sorted { $0.updatedAt > $1.updatedAt }
    }
    
    // MARK: - 主视图
    var body: some View {
        ZStack(alignment: .bottom) {
            // 背景色 - 更接近Flomo的淡灰色背景
            Color(UIColor(red: 0.96, green: 0.96, blue: 0.96, alpha: 1.0))
                .ignoresSafeArea()
            
            // 内容主体
            VStack(spacing: 0) {
                // 搜索栏
                searchBar
                
                if filteredNotes.isEmpty {
                    emptyNotesView
                } else {
                    // 笔记列表
                    ScrollView {
                        LazyVStack(spacing: 18) {
                            ForEach(filteredNotes) { note in
                                FlomoNoteCard(
                                    note: note,
                                    onEdit: { editingNote = note },
                                    onDelete: {
                                        viewModel.deleteNote(note)
                                    },
                                    onToggleFocus: {
                                        viewModel.toggleNoteFocus(note)
                                    },
                                    onConvertToTask: {
                                        if let _ = viewModel.convertNoteToTask(note) {
                                            DependencyContainer.toastManager().showSuperLightInfo("已转换为任务")
                                        }
                                    }
                                )
                                .padding(.horizontal, 10) // 减小水平内边距，让卡片更宽
                            }
                        }
                        .padding(.vertical, 16)
                        .padding(.bottom, 80) // 为底部的新建按钮留出空间
                    }
                }
            }
            
            // 底部新建按钮，真正悬浮在内容上方
            ZStack {
                // 圆形蓝色背景 - 使用应用主题色
                Circle()
                    .fill(Color.blue)
                    .frame(width: 52, height: 52)
                    .shadow(color: Color.black.opacity(0.15), radius: 3, x: 0, y: 1)
                
                // 加号图标
                Image(systemName: "plus")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white)
            }
            .padding(.bottom, 20)
            .onTapGesture {
                newNote = ""
                showingHalfScreenSheet = true
            }
        }
        .navigationTitle("项目笔记")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Menu {
                    Toggle(isOn: $showOnlyFocused) {
                        Label("仅显示重点关注", systemImage: "star.fill")
                    }
                    
                    Button {
                        // TODO: 导出笔记
                    } label: {
                        Label("导出笔记", systemImage: "square.and.arrow.up")
                    }
                } label: {
                    Image(systemName: "ellipsis.circle")
                }
            }
        }
        .id(viewModel.project.id) // 添加ID以确保视图状态与项目关联
        .onAppear {
            // 添加统一前缀的日志，便于调试导航状态问题
            //print("[1Step导航] 笔记页面出现，项目ID: \(viewModel.project.id)")
        }
        // REMOVE .fullScreenCover for half-sheet
        // .fullScreenCover(isPresented: $showingHalfScreenSheet) { ... }
        
        // RE-ADD .overlay implementation
        .overlay {
            if showingHalfScreenSheet {
                ZStack {
                    // Background covers everything including safe areas
                    Color.black.opacity(0.9)
                        .ignoresSafeArea()
                        .onTapGesture {
                            withAnimation(.easeInOut(duration: 0.25)) {
                                showingHalfScreenSheet = false
                            }
                        }
                    
                    // Simple VStack within safe area
                    VStack(spacing: 0) {
                        Spacer() // Pushes NoteInputView to the bottom of the safe area
                        
                        NoteInputView(
                            noteText: $newNote,
                            onSave: {
                                createNote()
                                withAnimation(.easeInOut(duration: 0.25)) {
                                    showingHalfScreenSheet = false
                                }
                            },
                            onCancel: {
                                withAnimation(.easeInOut(duration: 0.25)) {
                                    showingHalfScreenSheet = false
                                }
                            }
                        )
                        .transition(.move(edge: .bottom))
                    }
                    // VStack DOES NOT ignore safe area here
                }
                .transition(.opacity)
            }
        }
        // Keep the .fullScreenCover for editing existing notes
        .fullScreenCover(item: $editingNote) { note in
            NavigationView {
                VStack {
                    MarkdownNoteEditor(
                        text: $editingNoteContent,
                        placeholder: "记录你的想法...",
                        onSave: {
                            // 更新笔记内容
                            viewModel.updateNoteContent(note, newContent: editingNoteContent)
                            editingNote = nil
                        },
                        onCancel: {
                            editingNote = nil
                        }
                    )
                }
                .navigationBarHidden(true)
                .edgesIgnoringSafeArea(.bottom)
            }
            .onAppear {
                // 确保内容是空的
                editingNoteContent = note.notes
            }
        }
    }
    
    // MARK: - 搜索栏
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(Color(.systemGray))
            
            TextField("搜索笔记", text: $searchText)
                .font(.system(size: 16))
            
            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(Color(.systemGray3))
                }
            }
        }
        .padding(10)
        .background(Color(.systemBackground))
        .cornerRadius(10)
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
    }
    
    // MARK: - 空笔记视图
    private var emptyNotesView: some View {
        VStack(spacing: 16) {
            Spacer()
            
            Image(systemName: "doc.plaintext")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            Text(searchText.isEmpty ? "暂无笔记" : "没有匹配的笔记")
                .font(.headline)
                .foregroundColor(.primary)
            
            if searchText.isEmpty {
                Text("添加笔记来记录项目相关的想法和思考")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
                
                Button(action: {
                    showingHalfScreenSheet = true
                }) {
                    Text("添加笔记")
                        .font(.headline)
                        .foregroundColor(.white)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 10)
                        .background(Color.blue)
                        .cornerRadius(8)
                }
                .padding(.top, 16)
            }
            
            Spacer()
        }
        .frame(maxWidth: .infinity)
    }
    
    // MARK: - 笔记列表
    private var notesList: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(filteredNotes) { note in
                    FlomoNoteCard(
                        note: note,
                        onEdit: { editingNote = note },
                        onDelete: {
                            viewModel.deleteNote(note)
                        },
                        onToggleFocus: {
                            viewModel.toggleNoteFocus(note)
                        },
                        onConvertToTask: {
                            if let _ = viewModel.convertNoteToTask(note) {
                                DependencyContainer.toastManager().showSuperLightInfo("已转换为任务")
                            }
                        }
                    )
                }
            }
            .padding(.horizontal)
            .padding(.top, 8)
            .padding(.bottom, 16)
        }
    }
    
    // MARK: - 辅助方法
    
    /// 创建新笔记
    private func createNote() {
        guard !newNote.isEmpty else { return }
        
        // 提取第一行作为标题
        let lines = newNote.split(separator: "\n", maxSplits: 1)
        let title = lines.first.map(String.init) ?? "无标题笔记"
        let noteContent = newNote
        
        _ = viewModel.addNote(title: title, content: noteContent)
        
        // 重置输入内容
        newNote = ""
    }
}

/// Flomo风格的笔记卡片
struct FlomoNoteCard: View {
    let note: Task
    var onEdit: () -> Void
    var onDelete: () -> Void
    var onToggleFocus: () -> Void
    var onConvertToTask: () -> Void
    
    @State private var isExpanded = false
    @State private var showMenu = false
    @State private var shouldShowExpandButton = false
    
    // 内容预览最大行数
    private let previewLineLimit = 5
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 顶部信息栏：时间和操作按钮
            HStack {
                // 时间显示 - 优化字体大小和颜色
                Text(formatDate(note.createdAt))
                    .font(.system(size: 12))
                    .foregroundColor(Color(.systemGray3))
                
                Spacer()
                
                // 三点菜单
                Button {
                    showMenu = true
                } label: {
                    Image(systemName: "ellipsis")
                        .font(.system(size: 15))
                        .foregroundColor(Color(.systemGray3))
                }
                .confirmationDialog("", isPresented: $showMenu) {
                    Button("编辑", action: onEdit)
                    Button(note.isFocused ? "取消关注" : "关注", action: onToggleFocus)
                    Button("转为任务", action: onConvertToTask)
                    Button("删除", role: .destructive, action: onDelete)
                    Button("取消", role: .cancel) {}
                }
            }
            .padding(.horizontal, 16)
            .padding(.top, 13)
            .padding(.bottom, 10)
            
            // 笔记内容
            VStack(alignment: .leading, spacing: 12) {
                // Markdown渲染的内容 - 优化字体、颜色和行距
                Text(note.notes.markdownToAttributedString(
                    baseFontSize: 15,
                    textColor: Color(UIColor(red: 0.25, green: 0.25, blue: 0.25, alpha: 1.0)),
                    lineSpacing: 5
                ))
                    .lineLimit(isExpanded ? nil : previewLineLimit)
                    .fixedSize(horizontal: false, vertical: true)
                    .contentShape(Rectangle())
                    .background(
                        GeometryReader { geometry in
                            Color.clear.onAppear {
                                // 检测内容是否需要展开按钮
                                let size = geometry.size
                                let textHeight = size.height
                                let lineHeight: CGFloat = 22 // 调整估计行高
                                
                                // 调试日志
                                
                                shouldShowExpandButton = textHeight > lineHeight * CGFloat(previewLineLimit - 1)
                            }
                        }
                    )
                
                // 展开/收起按钮（仅当内容过长时显示）
                if shouldShowExpandButton {
                    Button(action: {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                            isExpanded.toggle()
                            // 调试日志
                        }
                    }) {
                        Text(isExpanded ? "收起" : "展开")
                            .font(.system(size: 13))
                            .foregroundColor(Color.blue.opacity(0.9))
                    }
                    .padding(.top, 2)
                }
                
                // 标签显示（如果有）
                if !note.tags.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 6) {
                            ForEach(note.tags, id: \.self) { tag in
                                Text("#\(tag)")
                                    .font(.system(size: 13))
                                    .foregroundColor(.blue)
                                    .padding(.horizontal, 7)
                                    .padding(.vertical, 2)
                                    .background(Color.blue.opacity(0.1))
                                    .cornerRadius(4)
                            }
                        }
                    }
                    .padding(.top, 6)
                }
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 14)
        }
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(color: Color.black.opacity(0.04), radius: 1, x: 0, y: 1)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemGray5), lineWidth: 0.5)
        )
        .padding(.vertical, 5) // 调整卡片垂直间距
        .padding(.horizontal, 1) // 轻微水平内边距
        .contentShape(Rectangle())
        .onTapGesture(count: 2) {
            // 双击编辑功能
            onEdit()
        }
    }
    
    // 格式化日期显示
    private func formatDate(_ date: Date) -> String {
        // 优化日期格式
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        return formatter.string(from: date)
    }
}

// 用UIViewRepresentable包装UITextView，以获取选择范围
struct TextViewWrapper: UIViewRepresentable {
    @Binding var text: String
    @Binding var selectedRange: NSRange?
    var attributedString: NSAttributedString?
    
    func makeUIView(context: Context) -> UITextView {
        let textView = UITextView()
        textView.font = UIFont.systemFont(ofSize: 17)
        textView.textColor = UIColor(red: 51/255, green: 51/255, blue: 51/255, alpha: 1.0)
        textView.backgroundColor = .clear
        textView.isScrollEnabled = true
        textView.autocorrectionType = .yes
        textView.autocapitalizationType = .sentences
        
        // 移除内边距使其更像flomo
        textView.textContainerInset = UIEdgeInsets(top: 8, left: 0, bottom: 8, right: 0)
        
        // 段落样式，行间距
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = 6 // flomo风格的紧凑行间距
        textView.typingAttributes[.paragraphStyle] = paragraphStyle
        
        textView.delegate = context.coordinator
        return textView
    }
    
    func updateUIView(_ uiView: UITextView, context: Context) {
        // 如果有富文本，优先使用富文本
        if let attributedString = attributedString {
            uiView.attributedText = attributedString
        } else if uiView.text != text {
            // 仅在文本变化时更新，避免光标位置重置
            uiView.text = text
            
            // 应用段落样式
            let paragraphStyle = NSMutableParagraphStyle()
            paragraphStyle.lineSpacing = 6
            uiView.typingAttributes[.paragraphStyle] = paragraphStyle
        }
        
        // 如果有需要设置的选择范围且不同于当前范围
        if let range = selectedRange, range != uiView.selectedRange {
            uiView.selectedRange = range
        }
        
        // 确保键盘始终可见
        if !text.isEmpty && uiView.isFirstResponder {
            uiView.scrollRangeToVisible(uiView.selectedRange)
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UITextViewDelegate {
        var parent: TextViewWrapper
        
        init(_ parent: TextViewWrapper) {
            self.parent = parent
        }
        
        func textViewDidChange(_ textView: UITextView) {
            if let attributedString = textView.attributedText {
                parent.text = attributedString.string
            } else {
                parent.text = textView.text
            }
            parent.selectedRange = textView.selectedRange
        }
        
        func textViewDidChangeSelection(_ textView: UITextView) {
            parent.selectedRange = textView.selectedRange
        }
    }
}

// MARK: - New View for Half-Screen Input Presentation
// struct HalfScreenInputView: View { ... }
// struct ClearBackgroundView: UIViewRepresentable { ... }
