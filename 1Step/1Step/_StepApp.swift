//
//  _StepApp.swift
//  1Step
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/3/24.
//

import SwiftData
import SwiftUI
import CloudKit
import UIKit // 导入UIKit

// MARK: - AppDelegate
class AppDelegate: NSObject, UIApplicationDelegate {
    func application(_ application: UIApplication, configurationForConnecting connectingSceneSession: UISceneSession, options: UIScene.ConnectionOptions) -> UISceneConfiguration {
        // 处理冷启动时的快捷方式
        if let shortcutItem = options.shortcutItem {
            // 使用通知中心传递事件，避免过早处理
             NotificationCenter.default.post(name: .processShortcutItem, object: shortcutItem)
            print("[AppDelegate] Cold launch shortcut: \\(shortcutItem.type)")
        }
        
        let sceneConfig = UISceneConfiguration(name: nil, sessionRole: connectingSceneSession.role)
        sceneConfig.delegateClass = SceneDelegate.self // 指定 SceneDelegate
        return sceneConfig
    }
}

// MARK: - SceneDelegate
class SceneDelegate: NSObject, UIWindowSceneDelegate {
    func windowScene(_ windowScene: UIWindowScene, performActionFor shortcutItem: UIApplicationShortcutItem, completionHandler: @escaping (Bool) -> Void) {
        // 处理应用在后台或已运行时激活的快捷方式
        NotificationCenter.default.post(name: .processShortcutItem, object: shortcutItem)
        print("[SceneDelegate] Background/Active shortcut: \\(shortcutItem.type)")
        completionHandler(true)
    }
}

// MARK: - AppShortcutType Enum
enum AppShortcutType: String, Hashable, Identifiable {
    case createTask = "com.absir.1Step.createTask" // 与Info.plist中的类型匹配
    
    var id: String { rawValue }
}

// MARK: - Notification Name Extension
extension Notification.Name {
    static let processShortcutItem = Notification.Name("processShortcutItem")
}

@main
struct StepApp: App {
    // 添加 App Delegate Adaptor
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    
    // 明确指定初始化配置
    let container: ModelContainer
    
    // 添加邀请状态检查
    @State private var isInviteVerified: Bool = false
    @State private var isCheckingInvite: Bool = true
    
    // 添加友盟服务和隐私政策状态
    @State private var isPrivacyPolicyAccepted: Bool = false
    @State private var isCheckingPrivacyPolicy: Bool = true
    @StateObject private var umengService = UMengService.shared
    
    // 添加快捷方式状态
    @State private var activeShortcut: AppShortcutType? = nil
    
    // 友盟AppKey - 替换为您的实际AppKey
    private let umengAppKey = "67ee083a65c707471a3da917"
    
    init() {
        // 确保 TaskFormCoordinator 初始化 (如果是单例)
        _ = TaskFormCoordinator.shared
        
        // 在使用self前检查TestFlight环境
        let isTestFlightBuild = Bundle.main.appStoreReceiptURL?.path.contains("sandboxReceipt") ?? false
        
        do {
            
            // 使用.automatic配置CloudKit，让系统自动选择合适的容器
            // 这是Apple推荐的最佳实践，更简洁可靠
            let cloudKitConfig = ModelConfiguration(
                isStoredInMemoryOnly: false,
                allowsSave: true,
                cloudKitDatabase: .automatic
            )
            
            let tempContainer = try ModelContainer(
                for: Task.self, Project.self, Tag.self, ChecklistItem.self, InviteCode.self, Thought.self,
                configurations: cloudKitConfig
            )
            
            // 先保存container到属性
            self.container = tempContainer
            
            // 立即同步配置依赖容器 - 避免使用异步
            DependencyContainer.shared.configureWith(container: tempContainer)
            print("[CloudKit] 已使用CloudKit容器配置依赖容器")
            
        } catch {
            
            if let nsError = error as NSError? {

                
                // 检查是否CloudKit错误
                if nsError.domain == CKErrorDomain {
                    if let errorDescription = nsError.userInfo[NSLocalizedDescriptionKey] as? String {
                        print("[CloudKit] 错误描述: \\(errorDescription)")
                    }
                }
            }
            
            // 尝试使用纯本地配置作为备选
            do {
                let fallbackConfig = ModelConfiguration(
                    isStoredInMemoryOnly: false,
                    allowsSave: true
                )
                let tempContainer = try ModelContainer(
                    for: Task.self, Project.self, Tag.self, ChecklistItem.self, InviteCode.self, Thought.self,
                    configurations: fallbackConfig
                )
                
                // 保存到属性
                self.container = tempContainer
                
                print("[CloudKit] 已切换到备选本地存储，不使用CloudKit")
                
                // 立即同步配置依赖容器
                DependencyContainer.shared.configureWith(container: tempContainer)
                print("[CloudKit] 已使用本地容器配置依赖容器")
                
            } catch {
                print("[CloudKit] 备选本地存储也失败: \\(error)")
                print("[CloudKit] 尝试最后的内存存储方案")
                
                do {
                    // 尝试内存存储作为最后手段
                    let memoryConfig = ModelConfiguration(isStoredInMemoryOnly: true)
                    let tempContainer = try ModelContainer(
                        for: Task.self, Project.self, Tag.self, ChecklistItem.self, InviteCode.self, Thought.self,
                        configurations: memoryConfig
                    )
                    
                    // 保存到属性
                    self.container = tempContainer
                    
                    // 立即同步配置依赖容器
                    DependencyContainer.shared.configureWith(container: tempContainer)
                    print("[CloudKit] 已使用内存容器配置依赖容器")
                    
                } catch {
                    print("[CloudKit] 无法初始化任何形式的数据库，严重错误: \\(error)")
                    fatalError("无法初始化任何形式的数据库: \\(error)")
                }
            }
        }
        
    }
    
    /// 检查应用版本
    func checkAppVersion() {
        // 先验证服务端版本要求
        APIVersionValidator.shared.validateAppVersion { isValid, message in
            if !isValid {
                // 版本不符合要求，显示强制更新提示
                DependencyContainer.versionManager().showForceUpdateAlert(message: message)
            } else {
                // 版本有效，延迟检查App Store更新
                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                    DependencyContainer.versionManager().checkForUpdates()
                }
            }
        }
    }
    
    /// 检查邀请状态
    func checkInviteStatus() {
        // 获取邀请状态
        let inviteStatus = DependencyContainer.inviteRepository().getLocalInviteStatus()
        isInviteVerified = inviteStatus.isVerified
        isCheckingInvite = false
    }
    
    /// 检查隐私政策状态
    func checkPrivacyPolicyStatus() {
        // 从UserDefaults读取隐私政策同意状态
        let hasAccepted = UserDefaults.standard.bool(forKey: "hasAcceptedPrivacyPolicy")
        isPrivacyPolicyAccepted = hasAccepted
        isCheckingPrivacyPolicy = false
        
        // 如果已同意，初始化友盟SDK
        if hasAccepted {
            umengService.initializeSDK(appKey: umengAppKey)
        }
    }
    
    /// 检查CloudKit可用性
    private func checkCloudKitAvailability() {
        
        // 使用默认容器
        let container = CKContainer.default()
        
        // 获取并显示容器ID用于诊断
        if let containerID = container.containerIdentifier {
        } else {
        }
        

        
        // 执行基本的连通性检查
        container.accountStatus { (accountStatus, error) in
            if error == nil {
                print("[CloudKit] ✅ CloudKit容器可访问")
            } else if let error = error {
                print("[CloudKit] ❌ CloudKit访问错误: \\(error.localizedDescription)")
            }
        }
    }
    
    /// 检查是否为TestFlight环境
    private func isTestFlight() -> Bool {
        // TestFlight构建通常包含特定的标志
        let isTestFlight = Bundle.main.appStoreReceiptURL?.path.contains("sandboxReceipt") ?? false
        return isTestFlight
    }
    
    /// 初始化任务管理器
    private func initializeTaskManager() {
        // 使用依赖容器获取TaskManager实例
        // 这会触发TaskManager的初始化，开始监听事件
        let _ = DependencyContainer.taskManager()
        print("[EventSystem] TaskManager已初始化并开始监听事件")
    }
    
    var body: some Scene {
        WindowGroup {
            ZStack {
                if isCheckingInvite || isCheckingPrivacyPolicy {
                    // 加载状态
                    ProgressView("正在检查...")
                        .onAppear {
                            // 检查邀请状态
                            checkInviteStatus()
                            // 检查隐私政策状态
                            checkPrivacyPolicyStatus()
                            // 执行完整的CloudKit检查
                            checkCloudKitAvailability()
                            // 初始化任务管理器
                            initializeTaskManager()
                        }
                } else if !isInviteVerified {
                    // 未通过邀请验证，显示验证页面
                    // 在口令验证页面集成隐私政策同意功能
                    InviteVerificationView()
                        .injectDefaultRepositories()
                        .withToast()
                        .onAppear {
                            // 定期检查邀请状态
                            Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
                                checkInviteStatus()
                            }
                        }
                } else {
                    // 已通过邀请验证，显示主界面
                    ContentView(activeShortcut: $activeShortcut) // 传递绑定
                        .withThemeMode()
                        .injectDefaultRepositories()
                        .withToast()  // 使用 AlertToast 的修饰器
                        .withConfetti()  // 添加烟花效果支持
                        .withActionSheet() // 添加ActionSheet支持，确保所有视图都能使用
                        .withUMengService() // 添加友盟服务
                        .withOnboarding() // 添加新手引导支持
                        .environment(\.eventBus, EventBus.shared) // 添加事件总线
                        .onAppear {
                            // 使用已存在的 container 获取 context
                            let modelContext = container.mainContext
                            // --- Demo 数据版本控制 ---
                            let currentDemoVersion = 2 // 每次有大变动递增
                            let demoVersionKey = "demoDataVersion"
                            let lastImportedVersion = UserDefaults.standard.integer(forKey: demoVersionKey)
                            if lastImportedVersion < currentDemoVersion {
                                DemoData.addDemoTasks(modelContext: modelContext, forceLoad: true)
                                UserDefaults.standard.set(currentDemoVersion, forKey: demoVersionKey)
                            } else {
                                DemoData.addDemoTasks(modelContext: modelContext, forceLoad: false)
                            }
                            // --- End ---
                            // 在视图出现时执行版本检查，避免init中的闭包捕获self
                            checkAppVersion()
                        }
                        // 监听来自 Delegate 的通知
                        .onReceive(NotificationCenter.default.publisher(for: .processShortcutItem)) { notification in
                            if let shortcutItem = notification.object as? UIApplicationShortcutItem {
                                handleShortcutItem(shortcutItem)
                            }
                        }
                }
            }
        }
        .modelContainer(container)
    }
    
    // 处理快捷方式项目
    private func handleShortcutItem(_ shortcutItem: UIApplicationShortcutItem) {
        guard let shortcutType = AppShortcutType(rawValue: shortcutItem.type) else {
            print("[Shortcut] Unknown shortcut type: \\(shortcutItem.type)")
            return
        }
        
        // 延迟设置状态，确保UI准备好响应
        DispatchQueue.main.async {
            self.activeShortcut = shortcutType
             print("[Shortcut] Handled: \\(shortcutType)")
        }
    }
}

