import SwiftUI

/// 浏览标签的导航协调器
class BrowseCoordinator: BaseCoordinator {
    
    /// 简化初始化方法
    override init(navigationStateManager: NavigationStateManaging, associatedTab: Tab = .main) {
        super.init(navigationStateManager: navigationStateManager, associatedTab: associatedTab)
    }
    
    /// 重写恢复导航方法，添加浏览特定的恢复逻辑
    override func restoreNavigation(to destination: NavigationDestination) {
        navigationStateManager.logNavigation(message: "正在恢复浏览导航状态: \(destination.description)")
        
        // 确保当前协调器为空路径
        if !path.isEmpty {
            navigationStateManager.logNavigation(message: "在恢复前清空路径")
            path = NavigationPath()
        }
        
        switch destination {
        case .projectList:
            // 直接导航到项目列表
            path.append(NavigationDestination.projectList)
            navigationStateManager.logNavigation(message: "恢复到项目列表")
        case .tagList:
            // 直接导航到标签列表
            path.append(NavigationDestination.tagList)
            navigationStateManager.logNavigation(message: "恢复到标签列表")
        case .projectDetail(let projectID):
            navigateToProject(projectID: projectID)
            navigationStateManager.logNavigation(message: "恢复到项目详情: \(projectID)")
        case .projectNotes(let projectID):
            navigateToProjectNotes(projectID: projectID)
            navigationStateManager.logNavigation(message: "恢复到项目笔记: \(projectID)")
        case .tagDetail(let tag):
            navigateToTag(tag: tag)
            navigationStateManager.logNavigation(message: "恢复到标签详情: \(tag)")
        default:
            // 其他目的地直接添加到路径
            path.append(destination)
            navigationStateManager.logNavigation(message: "恢复到其他目的地: \(destination.description)")
        }
        
        // 确保同步状态
        navigationStateManager.updateAndPersistNavigationState(for: associatedTab, destination: destination)
    }
    
    /// 导航到项目详情
    func navigateToProject(projectID: UUID) {
        let destination = NavigationDestination.projectDetail(projectID: projectID)
        navigateToDestination(destination)
    }
    
    /// 导航到项目笔记
    func navigateToProjectNotes(projectID: UUID) {
        // 先导航到项目详情（如果需要的话）
        // 注意：NavigationPath不支持直接检查内容，简化处理方式
        // 为避免潜在的路径问题，直接添加项目详情路径
        path.append(NavigationDestination.projectDetail(projectID: projectID))
        
        // 然后导航到项目笔记
        let destination = NavigationDestination.projectNotes(projectID: projectID)
        navigateToDestination(destination)
    }
    
    /// 导航到标签详情
    func navigateToTag(tag: String) {
        let destination = NavigationDestination.tagDetail(tag: tag)
        navigateToDestination(destination)
    }
} 