import SwiftUI

/// 基础导航协调器
// Conform to ObservableObject and NavigationCoordinator
open class BaseCoordinator: ObservableObject, NavigationCoordinator {
    // 符合协议：使用 NavigationPath
    @Published public var path = NavigationPath()
    // 符合协议：添加 isRestoringNavigation
    @Published public var isRestoringNavigation = false
    
    // 符合协议：已有
    public let navigationStateManager: NavigationStateManaging
    // 符合协议：已有
    public let associatedTab: Tab
    
    /// 初始化方法
    public init(navigationStateManager: NavigationStateManaging, associatedTab: Tab) {
        self.navigationStateManager = navigationStateManager
        self.associatedTab = associatedTab
        // No super.init() needed if not inheriting from NSObject
        navigationStateManager.logNavigation(message: "\(String(describing: type(of: self)))初始化 - 标签:\(associatedTab)")
        
        // 初始化时尝试恢复路径
        restoreNavigationIfNeeded()
    }
    
    /// 恢复导航状态的实现 - 子类可覆盖
    /// 注意：现在操作的是 NavigationPath
    open func restoreNavigation(to destination: NavigationDestination) {
        // 默认实现：将解码后的目标添加到 NavigationPath
        // 需要确保 NavigationDestination 符合 NavigationPath 的要求（通常是 Hashable）
        path.append(destination)
        navigationStateManager.logNavigation(message: "\(associatedTab)调用基础 restoreNavigation 到: \(destination.description)")
    }
    
    /// 处理导航事件 - 子类应该重写此方法以处理特定事件
    open func handleNavigationEvent(to destination: NavigationDestination, context: [String: Any] = [:]) {
        // 默认实现直接导航到目标
        navigateToDestination(destination)
    }
    
    /// 导航到目标 - 操作 NavigationPath
    public func navigateToDestination(_ destination: NavigationDestination) {
        path.append(destination)
        // 持久化最后一个目标（如果需要，或者持久化整个路径的表示）
        // 注意：NavigationPath 本身不易直接 Codable，通常保存其包含的值
        navigationStateManager.updateAndPersistNavigationState(for: associatedTab, destination: destination) 
        navigationStateManager.logNavigation(message: "\(associatedTab)导航到: \(destination.description)")
    }
    
    /// 返回上一页 - 操作 NavigationPath
    public func goBack() {
        if !path.isEmpty {
            // 在移除最后一个元素之前获取倒数第二个元素（即返回后的目的地）
            let nextDestination: NavigationDestination? = path.count > 1 ? 
                (Mirror(reflecting: path).descendant("root", "elements") as? [Any])?[path.count - 2] as? NavigationDestination : 
                nil
            
            // 移除最后一个路径元素
            path.removeLast()
            
            // 更新导航状态
            if path.isEmpty {
                // 如果路径为空，清除状态
                navigationStateManager.clearNavigationState(for: associatedTab)
                navigationStateManager.logNavigation(message: "\(associatedTab)返回到根页面，已清除导航状态")
            } else if let destination = nextDestination {
                // 如果有倒数第二个元素，更新状态
                navigationStateManager.updateAndPersistNavigationState(for: associatedTab, destination: destination)
                navigationStateManager.logNavigation(message: "\(associatedTab)返回上一页，更新导航状态为: \(destination.description)")
            } else {
                // 如果无法获取目的地但路径不为空，尝试使用getLastDestinationFromPath
                if let lastDestination = getLastDestinationFromPath() {
                    navigationStateManager.updateAndPersistNavigationState(for: associatedTab, destination: lastDestination)
                    navigationStateManager.logNavigation(message: "\(associatedTab)返回上一页，使用备选方法更新导航状态为: \(lastDestination.description)")
                }
            }
        }
    }

    // 符合协议：实现 restoreNavigationIfNeeded
    public func restoreNavigationIfNeeded() {
        guard !isRestoringNavigation else { return }
        
        if let restoredDestination = navigationStateManager.getNavigationState(for: associatedTab) {
            isRestoringNavigation = true
            navigationStateManager.logNavigation(message: "\(self.associatedTab)开始恢复路径到: \(restoredDestination.description)")
            
            // 使用短延迟确保视图加载
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
                guard let self = self else { return }
                
                // 调用（可能被覆盖的）restoreNavigation 方法来处理恢复
                self.restoreNavigation(to: restoredDestination)
                self.isRestoringNavigation = false
                self.navigationStateManager.logNavigation(message: "\(self.associatedTab)恢复路径完成")
            }
        } else {
            navigationStateManager.logNavigation(message: "\(self.associatedTab)未找到可恢复路径")
        }
    }
    
    /// 返回到根页面 - 操作 NavigationPath
    public func navigateToRoot() {
        path = NavigationPath() // 重置 NavigationPath
        navigationStateManager.clearNavigationState(for: associatedTab)
        navigationStateManager.logNavigation(message: "\(associatedTab)返回到根页面")
    }
    
    // 符合协议：实现 clearNavigation
    public func clearNavigation() {
        path = NavigationPath()
        navigationStateManager.clearNavigationState(for: associatedTab)
        navigationStateManager.logNavigation(message: "\(associatedTab)清除导航路径")
    }
    
    /// 实现获取最后导航目的地的方法
    public func getLastDestinationFromPath() -> NavigationDestination? {
        // 如果路径为空，返回nil
        if path.isEmpty {
            return nil
        }
        
        // 尝试获取最新的导航状态
        var lastDestination: NavigationDestination? = nil
        
        // 使用反射获取NavigationPath内部的值
        // 注意：这是一个变通方法，因为NavigationPath的内部结构不是公开的
        // 在实际项目中应该小心使用反射技术
        let mirror = Mirror(reflecting: path)
        if let elements = mirror.descendant("root", "elements") as? [Any] {
            if let lastElement = elements.last as? NavigationDestination {
                // 如果最后元素是NavigationDestination类型，直接使用
                lastDestination = lastElement
            } else {
                // 否则，使用已保存的状态或返回nil
                lastDestination = navigationStateManager.getNavigationState(for: associatedTab)
            }
        }
        
        if let lastDestination = lastDestination {
            navigationStateManager.logNavigation(message: "获取导航路径最后一个目的地: \(lastDestination.description)")
        } else {
            navigationStateManager.logNavigation(message: "无法获取导航路径最后一个目的地")
        }
        
        return lastDestination
    }
}
