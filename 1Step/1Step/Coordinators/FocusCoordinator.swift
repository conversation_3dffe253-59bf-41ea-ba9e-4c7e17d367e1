import SwiftUI

/// 一步(Focus)标签的导航协调器
class FocusCoordinator: BaseCoordinator {
    
    /// 初始化方法
    override init(navigationStateManager: NavigationStateManaging, associatedTab: Tab = .main) {
        super.init(navigationStateManager: navigationStateManager, associatedTab: associatedTab)
    }
    
    /// 重写恢复导航方法，添加一步视图特定的恢复逻辑
    override func restoreNavigation(to destination: NavigationDestination) {
        navigationStateManager.logNavigation(message: "正在恢复一步导航状态: \(destination.description)")
        
        switch destination {
        case .taskDetail(let taskID):
            navigateToTaskDetail(taskID: taskID)
        case .settingsPage:
            path.append(destination)
        default:
            // 其他目的地直接添加到路径
            path.append(destination)
        }
    }
    
    /// 导航到任务详情
    func navigateToTaskDetail(taskID: UUID) {
        let destination = NavigationDestination.taskDetail(taskID: taskID)
        navigateToDestination(destination)
    }
    
    /// 导航到设置页面
    func navigateToSettings() {
        let destination = NavigationDestination.settingsPage
        navigateToDestination(destination)
    }
    
    /// 处理导航事件
    override func handleNavigationEvent(to destination: NavigationDestination, context: [String: Any] = [:]) {
        // 实现Focus视图特定的导航事件处理
        // 例如，如果目标是任务详情，可能需要从context获取任务ID
        switch destination {
        case .taskDetail(let taskID):
            navigationStateManager.logNavigation(message: "Focus导航到任务详情: \(taskID)")
            navigateToDestination(destination) // 调用基类的导航方法
        // 处理其他特定于Focus的导航目的地...
        default:
            // 对于其他目的地，调用基类的默认处理
            super.handleNavigationEvent(to: destination, context: context)
        }
    }
} 