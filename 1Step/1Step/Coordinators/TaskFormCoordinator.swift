import SwiftUI
import Combine

/// 任务表单协调器
/// 统一管理任务表单的状态和行为
class TaskFormCoordinator: ObservableObject {
    // MARK: - Published 属性
    @Published private(set) var isShowing: Bool = false
    @Published private(set) var formId: UUID = UUID()
    
    // MARK: - 内部状态
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 单例
    static let shared = TaskFormCoordinator()
    
    private init() {
        print("[TaskFormCoordinator] 初始化")
        // 不需要在这里初始化仓库，在 showForm 时获取
    }
    
    // MARK: - 公共方法
    
    /// 显示任务表单 - 只负责设置 isShowing
    func showForm() {
        print("[TaskFormCoordinator] 显示表单")
        DispatchQueue.main.async {
            self.formId = UUID()
            withAnimation(.spring()) {
                self.isShowing = true
            }
            
            // 键盘焦点请求可以在 AddTaskFormView 内部处理，或者在这里保留
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                NotificationCenter.default.post(name: .requestKeyboardFocus, object: nil)
            }
        }
    }
    
    /// 隐藏任务表单 - 只负责设置 isShowing
    func hideForm() {
        print("[TaskFormCoordinator] 隐藏表单")
        // 先隐藏键盘（如果需要）
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
        
        DispatchQueue.main.async {
            withAnimation(.spring()) {
                self.isShowing = false
            }
        }
    }
    
    /// 发布任务添加完成的通知
    func notifyTaskAdded() {
        NotificationCenter.default.post(name: .taskAdded, object: nil)
    }
    
    // 移除旧的 onTaskAdded，由 TaskFormViewModel 处理
    // func onTaskAdded() { ... }
}

// MARK: - 通知扩展
extension Notification.Name {
    /// 请求键盘聚焦的通知
    static let requestKeyboardFocus = Notification.Name("RequestKeyboardFocus")
    
    /// 任务添加完成的通知
    static let taskAdded = Notification.Name("TaskAdded")
}
