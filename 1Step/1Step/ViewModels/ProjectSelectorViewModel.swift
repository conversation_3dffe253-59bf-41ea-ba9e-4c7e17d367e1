import Foundation
import SwiftData

/// 项目选择器视图模型
class ProjectSelectorViewModel: ObservableObject {
    // MARK: - 依赖
    
    private let projectRepository: ProjectRepository
    
    // MARK: - 发布属性
    
    @Published var projects: [Project] = []
    
    // MARK: - 初始化方法
    
    init(projectRepository: ProjectRepository) {
        self.projectRepository = projectRepository
        loadProjects()
    }
    
    // MARK: - 公共方法
    
    /// 加载所有项目
    func loadProjects() {
        projects = projectRepository.getAllProjects().filter { !$0.isArchived }
    }
    
    /// 创建新项目
    func createProject(name: String) -> Project? {
        guard !name.isEmpty else { return nil }
        
        let newProject = Project(name: name)
        projectRepository.saveProject(newProject)
        loadProjects()
        return newProject
    }
    
    /// 过滤项目
    func filteredProjects(searchText: String) -> [Project] {
        if searchText.isEmpty {
            return projects
        } else {
            return projects.filter { $0.name.localizedCaseInsensitiveContains(searchText) }
        }
    }
}
