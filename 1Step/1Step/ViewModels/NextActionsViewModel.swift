import Foundation
import SwiftUI
import SwiftData
import Combine
import UIKit

/// 下一步行动视图模型
class NextActionsViewModel: BaseTaskViewModel {
    // MARK: - 发布属性
    @Published var tasks: [Task] = []
    @Published var searchText: String = ""
    @Published var isSearchFocused: Bool = false
    @Published var showToast: Bool = false
    @Published var toastMessage: String = ""
    
    // MARK: - 初始化
    override init(taskManager: TaskManager = DependencyContainer.taskManager()) {
        super.init(taskManager: taskManager)
        
        loadTasks()
        
        $searchText
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                self?.loadTasks()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 公共方法
    
    /// 加载下一步任务
    override func loadTasks() {
        var fetchedResults: [Task] = []
        
        if searchText.isEmpty {
            // 获取所有下一步任务
            fetchedResults = taskManager.getTasksByStatus(TaskStatus.na.rawValue)
            .sorted { $0.updatedAt > $1.updatedAt }
        } else {
            // 搜索过滤 - 先获取所有下一步任务，然后在内存中过滤
            let allNextTasks = taskManager.getTasksByStatus(TaskStatus.na.rawValue)
            fetchedResults = allNextTasks.filter { task in
                task.title.localizedStandardContains(searchText) ||
                task.notes.localizedStandardContains(searchText)
            }.sorted { $0.updatedAt > $1.updatedAt }
        }
        
        // 过滤掉标记为待删除或待完成的任务
        self.tasks = fetchedResults.filter { task in
            !pendingDeletionTaskIDs.contains(task.id) && 
            !pendingCompletionTaskIDs.contains(task.id)
        }
    }
    
    /// 显示添加任务表单
    func showAddTaskForm() {
        TaskFormCoordinator.shared.showForm()
    }
    
    /// 隐藏添加任务表单
    func hideAddTaskForm() {
        TaskFormCoordinator.shared.hideForm()
    }
    
    /// 显示 Toast 消息
    func showToastMessage(_ message: String) {
        toastMessage = message
        showToast = true
        
        // 3秒后自动隐藏
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) { [weak self] in
            self?.showToast = false
        }
    }
    
    /// 隐藏键盘
    func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
    
    /// 刷新任务 - 用于下拉刷新
    @MainActor
    func refreshTasks() async {
        loadTasks()
        // 显示轻量级刷新提示
        DependencyContainer.toastManager().showSuperLightInfo("已刷新")
    }
    
    // Provide filteredTasks for the view, similar to how other ViewModels might do it if they don't filter directly in loadTasks.
    // In this version, loadTasks ALREADY filters by searchText, so filteredTasks simply returns tasks.
    var filteredTasks: [Task] {
        return tasks
    }
}
