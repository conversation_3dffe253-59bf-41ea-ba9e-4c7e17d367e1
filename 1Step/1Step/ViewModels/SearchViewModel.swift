import SwiftUI
import Combine
import Foundation

/// 负责处理搜索逻辑和状态的视图模型
class SearchViewModel: BaseTaskViewModel {

    // MARK: - 搜索状态
    @Published var searchText = ""
    @Published var selectedFilter: SearchFilter = .all
    @Published var isSearchFocused = false
    @Published var searchHistory: [String] = []
    @Published var searchResults: [Task] = []
    
    // --- Add a property to hold the current search task ---
    private var searchTask: _Concurrency.Task<Void, Never>? = nil

    // MARK: - 初始化
    override init(taskManager: TaskManager = DependencyContainer.taskManager()) {
        super.init(taskManager: taskManager)
        
        // 加载搜索历史
        loadSearchHistory()
        
        // 设置搜索文本和过滤器的观察者
        setupSearchObservers()
        
        // 初始加载一次（此时searchText为空，searchResults应为空）
        // Don't call loadTasks here initially as searchText is empty
        // searchResults are initialized as empty anyway.
    }

    // MARK: - 加载与搜索
    
    /// 重写 loadTasks，在搜索上下文中异步加载和过滤搜索结果
    override func loadTasks() {
        // --- Cancel the previous search task if it's still running ---
        searchTask?.cancel()
        
        // 如果搜索文本为空，清空结果并返回
        guard !searchText.isEmpty else {
            // Ensure immediate clearing on the main thread if search text becomes empty
             if !searchResults.isEmpty {
                 searchResults = []
             }
            return
        }

        // --- Start background task for searching ---
        searchTask = _Concurrency.Task { 
            do { // Start do-catch block
                // Capture searchText and selectedFilter for the background task
                let currentSearchText = self.searchText
                let currentFilter = self.selectedFilter

                // 在开始搜索前确保没有正在进行的删除操作
                // 捕获当前状态，避免在异步执行过程中发生变化
                let pendingDeletionIDs = self.pendingDeletionTaskIDs
                let pendingCompletionIDs = self.pendingCompletionTaskIDs

                // --- 获取基础数据 (在后台线程执行) ---
                var tasksToFilter: [Task] = []
                var smbTasks: [Task] = []
                
                do {
                    // 在数据访问时捕获可能的错误
                    // 确保每个getTasksByStatus调用都有错误处理
                    let inboxTasks = self.taskManager.getTasksByStatus(TaskStatus.inbox.rawValue)
                    try _Concurrency.Task.checkCancellation()
                    
                    let naTasks = self.taskManager.getTasksByStatus(TaskStatus.na.rawValue)
                    try _Concurrency.Task.checkCancellation()
                    
                    let waitingTasks = self.taskManager.getTasksByStatus(TaskStatus.waiting.rawValue)
                    try _Concurrency.Task.checkCancellation()
                    
                    // 安全地组合结果
                    tasksToFilter = inboxTasks
                    tasksToFilter += naTasks
                    tasksToFilter += waitingTasks
                    
                    if currentFilter == .all || currentFilter == .smb {
                        smbTasks = self.taskManager.getTasksByStatus(TaskStatus.smb.rawValue)
                        try _Concurrency.Task.checkCancellation()
                    }
                } catch is CancellationError {
                    // 任务被取消，直接返回
                    return
                } catch {
                    print("[SearchViewModel] 获取任务错误: \(error)")
                    // 错误处理：在主线程清空搜索结果
                    await MainActor.run { [weak self] in
                        self?.searchResults = []
                    }
                    return
                }
                // --- 结束获取基础数据 ---\

                // --- 开始过滤逻辑 (在后台线程执行) ---
                let filteredByScope: [Task]
                switch currentFilter {
                case .all:
                    let allTasks = tasksToFilter + smbTasks
                    filteredByScope = allTasks.filter { task in
                        task.title.localizedStandardContains(currentSearchText) ||
                        task.notes.localizedStandardContains(currentSearchText) ||
                        (task.project != nil && self.getProjectName(for: task.project).localizedStandardContains(currentSearchText)) ||
                        task.tags.contains { $0.localizedStandardContains(currentSearchText) }
                    }
                case .tasks:
                    filteredByScope = tasksToFilter.filter { $0.title.localizedStandardContains(currentSearchText) }
                case .projects:
                    filteredByScope = tasksToFilter.filter { task in
                        if let projectId = task.project {
                            // 使用try?包装可能失败的操作
                            return (try? self.getProjectName(for: projectId).localizedStandardContains(currentSearchText)) ?? false
                        }
                        return false
                    }
                case .smb:
                    filteredByScope = smbTasks.filter { $0.title.localizedStandardContains(currentSearchText)}
                case .tags:
                    filteredByScope = tasksToFilter.filter { $0.tags.contains { $0.localizedStandardContains(currentSearchText) } }
                case .notes:
                    filteredByScope = tasksToFilter.filter { $0.notes.localizedStandardContains(currentSearchText) }
                }
                
                try _Concurrency.Task.checkCancellation()

                // --- 应用挂起状态过滤和排序 (使用捕获的IDs) ---
                let finalResults = filteredByScope
                    .filter { !pendingDeletionIDs.contains($0.id) && !pendingCompletionIDs.contains($0.id) }
                    .sorted { $0.updatedAt > $1.updatedAt }

                try _Concurrency.Task.checkCancellation()

                // --- Switch back to the main thread to update UI ---
                await MainActor.run { [weak self] in
                     guard let self = self else { return }
                     // Only update if the search text hasn't changed again
                     if self.searchText == currentSearchText && self.selectedFilter == currentFilter {
                         self.searchResults = finalResults
                     }
                }
            } catch is CancellationError {
                 // Task was cancelled, possibly log or just ignore
            } catch {
                 // Handle other potential errors during filtering/data fetch
                 print("[SearchViewModel] Error during search task: \(error)")
                 await MainActor.run { [weak self] in
                    self?.searchResults = [] // Clear results on error
                 }
            }
        }
    }

    // MARK: - 搜索历史管理 (从 NextActionsViewModel 迁移)

    /// 加载搜索历史
    private func loadSearchHistory() {
        if let searchHistoryData = UserDefaults.standard.data(forKey: "searchHistory"),
           let decodedHistory = try? JSONDecoder().decode([String].self, from: searchHistoryData) {
            self.searchHistory = decodedHistory
        }
    }

    /// 保存搜索历史 (应在适当的时候调用，例如搜索框失焦或添加新历史时)
    func saveSearchHistory() {
        if let encodedData = try? JSONEncoder().encode(searchHistory) {
            UserDefaults.standard.set(encodedData, forKey: "searchHistory")
        }
    }

    /// 添加搜索词到历史记录
    override func addSearchHistory(_ term: String) {
        let cleanedTerm = term.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !cleanedTerm.isEmpty else { return }
        
        // 移除已存在的相同记录，并添加到最前面
        searchHistory.removeAll { $0 == cleanedTerm }
        searchHistory.insert(cleanedTerm, at: 0)
        
        // 限制历史记录数量 (例如，最多20条)
        if searchHistory.count > 20 {
            searchHistory = Array(searchHistory.prefix(20))
        }
        saveSearchHistory() // 添加后立即保存
    }

    /// 从历史记录中移除搜索词
    func removeSearchTerm(_ term: String) {
        searchHistory.removeAll { $0 == term }
        saveSearchHistory()
    }

    /// 清空搜索历史
    override func clearSearchHistory() {
        searchHistory = []
        saveSearchHistory()
    }

    // MARK: - 响应式搜索

    /// 设置搜索文本和过滤器的观察者，自动触发搜索
    private func setupSearchObservers() {
        // 当 searchText 改变时，等待一小段时间后触发搜索 (防抖)
        $searchText
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main) // Debounce on main thread
            .removeDuplicates()
            .sink { [weak self] query in
                // Trigger loadTasks regardless of whether query is empty or not.
                // loadTasks now handles the empty case internally.
                self?.loadTasks()
            }
            .store(in: &cancellables)

        // 当 selectedFilter 改变时，立即触发搜索
        $selectedFilter
            .removeDuplicates()
            .receive(on: RunLoop.main) // Ensure sink runs on main thread
            .sink { [weak self] _ in // No need to capture filterValue explicitly if just calling loadTasks
                self?.loadTasks() // Trigger search when filter changes
            }
            .store(in: &cancellables)
    }
    
    // Helper function (ensure thread safety if needed, assumed okay for now)
    // This likely reads from projectRepository which might need attention
     override func getProjectName(for projectId: UUID?) -> String {
         guard let projectId = projectId, let project = taskManager.getProjectById(projectId) else {
             return ""
         }
         return project.name
     }

    // 重写handleTaskDelete，确保在删除操作后取消并重置当前搜索任务
    override func handleTaskDelete(_ task: Task) {
        // 取消当前搜索任务防止访问错误数据
        searchTask?.cancel()
        
        // 调用父类实现
        super.handleTaskDelete(task)
        
        // 在主线程上立即移除搜索结果中的已删除任务
        DispatchQueue.main.async { [weak self] in 
            guard let self = self else { return }
            self.searchResults.removeAll { $0.id == task.id }
        }
    }
} 