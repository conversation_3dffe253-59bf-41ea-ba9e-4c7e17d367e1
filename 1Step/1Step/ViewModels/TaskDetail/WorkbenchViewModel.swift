import SwiftUI
import SwiftData

/// 行动台页面的视图模型
@MainActor // 确保所有 UI 更新都在主线程执行
class WorkbenchViewModel: ObservableObject {
    // MARK: - 属性

    /// 当前关联的 Task 对象
    private let task: Task

    /// 任务仓库 (虽然现在不用它直接操作Thought，但可能未来需要，先保留)
    private let taskRepository: TaskRepository
    
    /// 思绪仓库，用于数据操作
    private let thoughtRepository: ThoughtRepository

    /// 用于展示在列表中的思绪记录，已排序
    @Published var thoughts: [Thought] = []

    /// 新思绪的输入内容
    @Published var newThoughtContent: String = ""
    
    /// 任务标题 - 供视图使用
    var taskTitle: String {
        return task.title +  " 行动台"
    }

    /// 检查是否有历史思绪可以加载
    func hasHistoryThoughts() -> Bool {
        // 获取当前任务的历史思绪数量，而不加载具体内容
        let count = thoughtRepository.getWorkbenchThoughtsCount(taskId: task.id)
        return count > 0
    }

    // MARK: - 初始化方法

    init(task: Task, taskRepository: TaskRepository, thoughtRepository: ThoughtRepository) {
        self.task = task
        self.taskRepository = taskRepository
        self.thoughtRepository = thoughtRepository // 初始化 thoughtRepository
        // 可以在初始化时加载一次，或者依赖 View 的 onAppear
        // loadThoughts()
    }

    // MARK: - 公开方法

    /// 加载与当前 Task 关联的思绪记录
    func loadThoughts() {
        // 使用 ThoughtRepository 获取数据，确保按时间倒序
        self.thoughts = thoughtRepository.getWorkbenchThoughts(taskId: task.id, sortBy: SortDescriptor(\.createdAt, order: .reverse))
        
        // // --- 原来的实现，保留注释以供参考 --- 
        // // 直接访问 Task 的 thoughts 数组，并排序
        // // 注意：这里假设 Task 和 Thought 之间建立了正确的 SwiftData 关系
        // // 并且 task 对象是有效的，能够访问其关联的 thoughts
        // if let taskThoughts = task.thoughts {
        //     // 按创建时间倒序排列 (最新的在前面)
        //     self.thoughts = taskThoughts.sorted { $0.createdAt > $1.createdAt }
        // } else {
        //     // 如果 task.thoughts 是 nil，则清空列表
        //     self.thoughts = []
        //     // 可以在这里添加日志或错误处理
        //     print("Warning: Task.thoughts is nil for task ID: \(task.id)")
        // }
    }

    /// 添加新的思绪记录
    func addThought() {
        let trimmedContent = newThoughtContent.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedContent.isEmpty else { return } // 不添加空内容

        // 使用 ThoughtRepository 添加记录
        _ = thoughtRepository.addWorkbenchThought(content: trimmedContent, taskId: task.id)

        // 清空输入框
        newThoughtContent = ""

        // 重新加载并排序列表 (Repository 已经保存，这里只需刷新 UI)
        loadThoughts()
    }
    
    /// 添加新的思绪记录并返回添加的思绪对象
    func addThoughtAndReturn() -> Thought? {
        let trimmedContent = newThoughtContent.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedContent.isEmpty else { return nil } // 不添加空内容

        // 使用 ThoughtRepository 添加记录并获取返回值
        let newThought = thoughtRepository.addWorkbenchThought(content: trimmedContent, taskId: task.id)

        // 清空输入框
        newThoughtContent = ""

        return newThought
    }

    /// 删除指定的思绪记录
    func deleteThought(_ thought: Thought) {
        // 使用 ThoughtRepository 删除记录
        thoughtRepository.deleteThought(thought)
        
        // 重新加载数据以确保同步
        loadThoughts()
    }
} 