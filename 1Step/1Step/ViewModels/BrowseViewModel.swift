import Foundation
import SwiftUI
import SwiftData

/// 浏览视图的视图模型
class BrowseViewModel: ObservableObject {
    // MARK: - 依赖注入
    private let taskManager: TaskManager
    
    // MARK: - 发布属性
    @Published var tasks: [Task] = []
    @Published var projectCount: Int = 0
    @Published var tagCount: Int = 0
    
    // MARK: - 初始化方法
    init(
        taskManager: TaskManager = DependencyContainer.taskManager()
    ) {
        self.taskManager = taskManager
    }
    
    // MARK: - 公共方法
    
    /// 加载初始数据
    func loadInitialData() {
        tasks = taskManager.getAllTasks()
        loadProjectCount()
        loadTagCount()
    }
    
    /// 加载项目数量
    private func loadProjectCount() {
        let projects = taskManager.getAllProjects()
        projectCount = projects.filter { !$0.isArchived }.count
    }
    
    /// 加载标签数量
    private func loadTagCount() {
        let tags = taskManager.getAllTags()
        tagCount = tags.count
    }
    
    /// 获取特定状态的任务数量
    func getTaskCount(for status: TaskStatus) -> Int {
        return taskManager.getTasksByStatus(status.rawValue).count
    }
}
