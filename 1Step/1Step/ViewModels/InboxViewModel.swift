import Foundation
import SwiftUI
import SwiftData
import Combine
import UIKit

/// 收集箱视图模型
class InboxViewModel: BaseTaskViewModel {
    // MARK: - 发布属性
    @Published var inboxTasks: [Task] = []
    @Published var searchText: String = ""
    @Published var isSearchFocused: Bool = false
    @Published var showToast: Bool = false
    @Published var toastMessage: String = ""
    
    // 批量操作相关
    @Published var isBatchMode: Bool = false
    @Published var selectedTasks: Set<UUID> = []
    @Published var showingBatchActionSheet: Bool = false
    
    // 任务表单属性
    @Published var taskTitle: String = ""
    @Published var taskNotes: String = ""
    @Published var previousTaskTitle: String = "" // 用于跟踪上一次的标题值，用于检测退格操作
    
    // 项目相关
    @Published var taskProject: UUID? = nil
    
    // 标签相关
    @Published var taskTags: [String] = []
    
    // MARK: - 初始化
    override init(taskManager: TaskManager = DependencyContainer.taskManager()) {
        super.init(taskManager: taskManager)
        
        // 加载初始数据
        loadTasks()
        
        // 设置搜索文本变更监听
        $searchText
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                self?.loadTasks()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 公共方法
    
    /// 加载收集箱任务
    override func loadTasks() {
        var tasks: [Task] = []
        
        if searchText.isEmpty {
            // 获取所有收集箱任务
            tasks = taskManager.getTasksByStatus(TaskStatus.inbox.rawValue)
                .sorted { $0.updatedAt > $1.updatedAt }
        } else {
            // 搜索过滤 - 先获取所有收集箱任务，然后在内存中过滤
            let allInboxTasks = taskManager.getTasksByStatus(TaskStatus.inbox.rawValue)
            tasks = allInboxTasks.filter { task in
                task.title.localizedStandardContains(searchText) ||
                task.notes.localizedStandardContains(searchText)
            }.sorted { $0.updatedAt > $1.updatedAt }
        }
        
        // 过滤掉标记为待删除或待完成的任务
        inboxTasks = tasks.filter { task in
            !pendingDeletionTaskIDs.contains(task.id) && 
            !pendingCompletionTaskIDs.contains(task.id)
        }
    }
    
    /// 显示添加任务表单
    func showAddTaskForm() {
        TaskFormCoordinator.shared.showForm()
    }
    
    /// 隐藏添加任务表单
    func hideAddTaskForm() {
        TaskFormCoordinator.shared.hideForm()
    }
    
    /// 显示 Toast 消息
    func showToastMessage(_ message: String) {
        toastMessage = message
        showToast = true
        
        // 3秒后自动隐藏
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) { [weak self] in
            self?.showToast = false
        }
    }
    
    /// 隐藏键盘
    func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
    
    /// 重置任务字段
    func resetTaskFields() {
        taskTitle = ""
        taskNotes = ""
        taskProject = nil
        taskTags = []
    }
    
    /// 添加任务
    func addTask() {
        guard !taskTitle.isEmpty else { return }
        
        // 使用仓库添加任务
        _ = taskManager.addTask(
            title: taskTitle,
            status: TaskStatus.inbox.rawValue,
            notes: taskNotes,
            project: taskProject
        )
        
        // 重置字段并刷新数据
        resetTaskFields()
        loadTasks()
        
        // 显示Toast提示
        showToastMessage("已添加到收集箱")
        
        // 隐藏添加表单
        TaskFormCoordinator.shared.hideForm()
    }
    
    // MARK: - 批量操作方法
    
    /// 切换任务选中状态
    func toggleTaskSelection(_ taskId: UUID) {
        if selectedTasks.contains(taskId) {
            selectedTasks.remove(taskId)
        } else {
            selectedTasks.insert(taskId)
        }
    }
    
    /// 批量移动任务到下一步
    func moveSelectedTasksToNextActions() {
        for taskId in selectedTasks {
            if let task = taskManager.getTaskById(taskId) {
                task.status = TaskStatus.na.rawValue
                taskManager.updateTask(task)
            }
        }
        selectedTasks.removeAll()
        isBatchMode = false
        loadTasks()
        showToastMessage("已移动到下一步")
    }
    
    /// 批量移动任务到将来也许
    func moveSelectedTasksToSomeday() {
        for taskId in selectedTasks {
            if let task = taskManager.getTaskById(taskId) {
                task.status = TaskStatus.smb.rawValue
                taskManager.updateTask(task)
            }
        }
        selectedTasks.removeAll()
        isBatchMode = false
        loadTasks()
        showToastMessage("已移动到将来也许")
    }
    
    /// 取消批量操作
    func cancelBatchOperation() {
        selectedTasks.removeAll()
        isBatchMode = false
    }
    
    /// 刷新任务 - 用于下拉刷新
    @MainActor
    func refreshTasks() async {
        loadTasks()
        // 显示轻量级刷新提示
        DependencyContainer.toastManager().showSuperLightInfo("已刷新")
    }
}
