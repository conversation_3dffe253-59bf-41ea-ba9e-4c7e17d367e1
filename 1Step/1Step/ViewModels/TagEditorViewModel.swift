import Foundation
import SwiftData
import SwiftUI

/// 标签编辑器视图模型
class TagEditorViewModel: ObservableObject {
    // MARK: - 依赖
    
    private let tagRepository: TagRepository
    
    // MARK: - 发布属性
    
    @Published var allTags: [Tag] = []
    
    // MARK: - 初始化方法
    
    init(tagRepository: TagRepository) {
        self.tagRepository = tagRepository
        loadTags()
    }
    
    // MARK: - 公共方法
    
    /// 加载所有标签
    func loadTags() {
        allTags = tagRepository.getAllTags()
    }
    
    /// 检查标签是否已存在
    func tagExists(with name: String) -> Bool {
        return tagRepository.tagExists(name)
    }
    
    /// 创建新标签
    func createNewTag(name: String) -> Tag? {
        guard !name.isEmpty else { return nil }
        
        // 如果标签已存在，返回已存在的标签
        if let existingTag = tagRepository.getTagByName(name) {
            return existingTag
        }
        
        // 创建新标签
        if let newTag = tagRepository.createTag(name: name) {
            // 重新加载标签
            loadTags()
            return newTag
        }
        
        return nil
    }
    
    /// 根据搜索过滤标签
    func filteredTags(searchText: String) -> [Tag] {
        if searchText.isEmpty {
            return allTags
        } else {
            return allTags.filter { $0.name.localizedCaseInsensitiveContains(searchText) }
        }
    }
    
    /// 获取标签颜色
    func tagColor(for tag: Tag, colorScheme: ColorScheme) -> Color {
        return AppColors.UI.tagColor(for: colorScheme, tag: tag)
    }
}
