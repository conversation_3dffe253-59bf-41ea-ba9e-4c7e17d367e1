import Foundation
import SwiftUI
import AVFoundation

/// UI 状态管理器 - 专门处理界面相关状态
@MainActor
class UIStateManager: ObservableObject {
    // MARK: - 回声Drop相关状态
    @Published var showingEchoDrop = false
    @Published var echoText = ""
    
    // MARK: - 拖拽相关状态
    @Published var draggedTask: Task? = nil
    @Published var pendingCompletionTaskID: UUID? = nil
    
    // MARK: - 激励语相关
    @Published var currentQuote: String = ""
    
    // MARK: - 最近添加项目集合
    @Published var recentlyAddedItems: Set<UUID> = []
    
    // MARK: - 音频播放器
    private var audioPlayer: AVAudioPlayer?
    
    // MARK: - 激励语数据
    private let motivationalQuotes = [
        "一步 · 你正在专注的事，不需要太多 ",
        "先走这一步，剩下的事情之后再说",
        "你不是要做很多事，只是现在这一步",
        "此刻你只需关注这些",
        "这些行动不为完成，只为靠近",
        "如果太难开始，就只做一个小行动",
        "没关系，小行动不大，但已经很勇敢",
        "你正在的，就是你该在的这一步",
        "不需要计划太远，走好这一步就好",
        "你不是要变得更好，而是开始走路",
        "没有计划的日子，也可以往前走一点",
        "被打断也没关系，你还可以再迈出一小步",
        "专注不是做完全部，而是愿意留时间给这一件",
        "你没有掉队，只是以自己的速度在走",
        "有些时候，只是开始，也已经是一种完整",
        "你不需要向谁证明，只要和自己一起往前走",
        "今天不做也没关系，你还有你自己",
        "有时候，不动也是一种节奏",
        "你可以回来，也可以暂停，这里都会等你"
    ]
    
    private let taskManager: TaskManager
    
    init(taskManager: TaskManager) {
        self.taskManager = taskManager
        setupAudioPlayer()
        selectQuoteForToday()
    }
    
    // MARK: - 回声Drop功能
    
    /// 显示回声Drop
    func showEchoDrop() {
        DispatchQueue.main.async {
            withAnimation {
                self.showingEchoDrop = true
            }
        }
    }
    
    /// 隐藏回声Drop
    func hideEchoDrop() {
        withAnimation {
            showingEchoDrop = false
            echoText = ""
        }
    }
    
    /// 处理回声文本，将其转化为任务
    func handleEchoDropText() {
        guard !echoText.isEmpty else { return }
        
        // 创建新任务，状态改为 inbox
        let task = taskManager.addTask(
            title: echoText,
            status: TaskStatus.inbox.rawValue
        )
        
        // 清空文本
        echoText = ""
        
        // 更新 Toast 提示
        DependencyContainer.toastManager().showSuperLightInfo("已添加到收集箱")
        
        // 隐藏回声Drop
        showingEchoDrop = false
        
        // 触发触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()
    }
    
    /// 释放回声文本（不保存，只清空）
    func releaseEcho() {
        // 清空文本
        echoText = ""
        
        // 隐藏回声Drop
        showingEchoDrop = false
    }
    
    // MARK: - 音频播放
    
    /// 播放完成音效
    func playCompletionSound() {
        audioPlayer?.currentTime = 0
        audioPlayer?.play()
    }
    
    /// 设置音频播放器
    private func setupAudioPlayer() {
        guard let soundURL = Bundle.main.url(forResource: "task_completed", withExtension: "wav") else {
            print("无法找到音频文件")
            return
        }
        
        do {
            // 配置音频会话为混音模式，允许与其他应用的音频共存
            // 使用 .ambient 类别，这样不会中断其他应用的音频播放
            try AVAudioSession.sharedInstance().setCategory(.ambient, mode: .default)
            try AVAudioSession.sharedInstance().setActive(true, options: .notifyOthersOnDeactivation)
            
            audioPlayer = try AVAudioPlayer(contentsOf: soundURL)
            audioPlayer?.volume = 0.3 // 设置音量为 30%
            audioPlayer?.prepareToPlay()
        } catch {
            print("音频播放器初始化失败: \(error.localizedDescription)")
        }
    }
    
    // MARK: - 激励语管理
    
    /// 选择今天的激励语
    private func selectQuoteForToday() {
        let defaults = UserDefaults.standard
        let today = Calendar.current.startOfDay(for: Date())
        
        // 获取上次显示第一条引言的日期
        if let lastDate = defaults.object(forKey: "LastFirstQuoteDate") as? Date,
           !Calendar.current.isDate(today, inSameDayAs: lastDate) {
            // 如果是新的一天，显示第一条引言
            currentQuote = motivationalQuotes[0]
            
            // 保存今天的日期
            defaults.set(today, forKey: "LastFirstQuoteDate")
        } else {
            // 如果是同一天内再次打开，随机显示任意一条
            currentQuote = motivationalQuotes.randomElement() ?? ""
        }
    }
    
    /// 刷新激励语（手动调用）
    func refreshQuote() {
        currentQuote = motivationalQuotes.randomElement() ?? ""
    }
    
    // MARK: - 拖拽状态管理
    
    /// 设置拖拽任务
    func setDraggedTask(_ task: Task?) {
        draggedTask = task
    }
    
    /// 设置待完成任务ID
    func setPendingCompletionTaskID(_ taskID: UUID?) {
        pendingCompletionTaskID = taskID
    }
    
    /// 清除拖拽状态
    func clearDragState() {
        draggedTask = nil
        pendingCompletionTaskID = nil
    }
    
    // MARK: - 最近添加项目管理
    
    /// 添加最近添加的项目
    func addRecentlyAddedItem(_ itemId: UUID) {
        recentlyAddedItems.insert(itemId)
        
        // 3秒后自动移除
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) { [weak self] in
            self?.recentlyAddedItems.remove(itemId)
        }
    }
    
    /// 移除最近添加的项目
    func removeRecentlyAddedItem(_ itemId: UUID) {
        recentlyAddedItems.remove(itemId)
    }
    
    /// 清除所有最近添加的项目
    func clearRecentlyAddedItems() {
        recentlyAddedItems.removeAll()
    }
    
    /// 检查是否是最近添加的项目
    func isRecentlyAdded(_ itemId: UUID) -> Bool {
        return recentlyAddedItems.contains(itemId)
    }
} 