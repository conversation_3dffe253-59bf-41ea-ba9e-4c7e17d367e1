import Foundation
import SwiftUI
import Combine

/// 通用树形节点管理器 - 统一处理所有类型的节点操作
@MainActor
class FocusManager: ObservableObject {
    // MARK: - Publishers
    public let showToastSubject = PassthroughSubject<String, Never>()

    // MARK: - 核心状态
    @Published var focusPath: [UUID] = []           // 聚焦路径，表示当前层级
    @Published var expandedNodes: Set<UUID> = []    // 展开的节点
    @Published var animatingNodes: [UUID: Date] = [:]  // 正在动画的节点
    
    // 保存进入一步模式前的任务列表展开状态
    private var taskListExpandedState: Set<UUID> = []
    
    private let taskManager: TaskManager
    private var focusStateManager = ActionFocusStateManager()
    
    // MARK: - 计算属性
    var isFocused: Bool { !focusPath.isEmpty }
    var focusLevel: Int { focusPath.count }
    var isInActionFocusMode: Bool { focusLevel >= 2 }
    
    // MARK: - 初始化
    private let userDefaultsKey = "actionFocusStates"

    init(taskManager: TaskManager) {
        self.taskManager = taskManager
        loadFocusStates()
    }
    
    // MARK: - Focus Path Persistence (using ActionFocusStateManager)

    private func recordCurrentFocusPath() {
        focusStateManager.cleanExpiredStates() // Clean expired states first

        // Ensure there's a path to begin with
        guard !focusPath.isEmpty else {
            // No current focus path. We've cleaned expired states, so save any changes from that.
            saveFocusStates()
            return
        }

        let currentPathSnapshot = self.focusPath // Work with a snapshot of the path
        let taskId = currentPathSnapshot[0] // The first element is always the task ID

        // A meaningful focus path for saving must include at least a task and a checklist item.
        guard currentPathSnapshot.count >= 2 else {
            // The path is too shallow (only task ID). 
            // If there was a saved state for this task, it's now outdated because we're no longer focused on a specific checklist item within it.
            if focusStateManager.getState(for: taskId) != nil {
                focusStateManager.removeState(for: taskId)
            }
            saveFocusStates() // Save changes (either from cleaning or removal)
            return
        }

        let checklistItemId = currentPathSnapshot[1]
        let subStepPathArray = currentPathSnapshot.count > 2 ? Array(currentPathSnapshot.dropFirst(2)) : []

        // Save the valid, detailed focus path
        focusStateManager.saveState(
            taskId: taskId,
            checklistItemId: checklistItemId,
            subStepPath: subStepPathArray
        )
        saveFocusStates() // Persist the new or updated state
    }


    func setLastFocusPath(for taskId: UUID, checklistItemId: UUID, subStepPath: [UUID]) {
        focusStateManager.saveState(taskId: taskId, checklistItemId: checklistItemId, subStepPath: subStepPath)
        saveFocusStates()
    }

    func getLastFocusPath(for taskId: UUID) -> (checklistItemId: UUID, subStepPath: [UUID])? {
        guard let state = focusStateManager.getState(for: taskId) else {
            return nil
        }
        return (state.checklistItemId, state.subStepPath)
    }

    func clearLastFocusPath(for taskId: UUID) {
        if focusStateManager.getState(for: taskId) != nil {
            focusStateManager.removeState(for: taskId)
            saveFocusStates()
        }
    }

    private func saveFocusStates() {
        if let encoded = try? JSONEncoder().encode(focusStateManager) {
            UserDefaults.standard.set(encoded, forKey: userDefaultsKey)
        } else {
            print("[FocusManager ERROR] Failed to encode focusStateManager.")
        }
    }

    public func clearFocusMemoryForTask(taskId: UUID) {
        clearLastFocusPath(for: taskId)
        print("[FocusManager LOG] clearFocusMemoryForTask: Cleared focus memory for task \(taskId)")
    }

    private func loadFocusStates() {
        defer {
            focusStateManager.cleanExpiredStates()
            saveFocusStates() // Persist changes after cleaning expired states
        }

        guard let savedStatesData = UserDefaults.standard.data(forKey: userDefaultsKey) else {
            focusStateManager = ActionFocusStateManager()
            return
        }
        
        guard let decodedManager = try? JSONDecoder().decode(ActionFocusStateManager.self, from: savedStatesData) else {
            focusStateManager = ActionFocusStateManager()
            return
        }
        
        focusStateManager = decodedManager
    }

    // MARK: - 统一节点操作
    
    /// 聚焦到任意节点
    func focusTo(_ nodeId: UUID) {
        // 保存当前任务列表的展开状态（进入一步模式前）
        saveTaskListExpandedState()
        
        let initialNodeType = identifyNodeType(nodeId)

        // Attempt to restore focus path if nodeId represents a Task
        if case .task(let taskToFocusOn) = initialNodeType {
            if let (retrievedChecklistItemId, retrievedSubStepPath) = getLastFocusPath(for: taskToFocusOn.id) {
                // 验证恢复的路径是否仍然有效
                if isValidFocusPath(taskId: taskToFocusOn.id, checklistItemId: retrievedChecklistItemId, subStepPath: retrievedSubStepPath) {
                    // Successfully retrieved a stored focus path for this task
                    var restoredFocusPath = [taskToFocusOn.id, retrievedChecklistItemId]
                    restoredFocusPath.append(contentsOf: retrievedSubStepPath)

                    self.focusPath = restoredFocusPath
                    
                    // Expand all nodes in the restored path
                    for idInRestoredPath in self.focusPath {
                        expandedNodes.insert(idInRestoredPath)
                    }
                    
                    ToastManager.shared.showSuperLightInfo("已恢复到一步位置")
                    return // Focus restored, skip default focusing logic
                } else {
                    // 路径无效，清除该任务的记忆
                    clearLastFocusPath(for: taskToFocusOn.id)
                }
            }
        }

        // --- Default focusing logic (if no path was restored or nodeId is not a task/task without stored path) ---
        let newlyBuiltPath = buildPathTo(nodeId) // Build path from scratch to the target nodeId
        self.focusPath = newlyBuiltPath
        
        // Expand all nodes in the newly built focus path to ensure visibility
        for idInNewlyBuiltPath in self.focusPath {
            expandedNodes.insert(idInNewlyBuiltPath)
        }
    }
    
    /// 加深聚焦（在当前路径基础上添加）
    func focusDeeper(to nodeId: UUID) {
        recordCurrentFocusPath() // Record path before deepening

        let pathBeforeDeepening = self.focusPath
        
        // 保存当前的聚焦路径中的节点ID（用于保留展开状态）
        let oldPathIds = Set(focusPath)
        
        // 更新聚焦路径
        focusPath.append(nodeId)
        
        // 清理不在新聚焦路径上的展开状态
        let newPathIds = Set(focusPath)
        expandedNodes = expandedNodes.filter { oldPathIds.contains($0) || newPathIds.contains($0) || isTaskNode($0) }
        
        // 确保所有聚焦路径上的节点都处于展开状态
        for pathNodeId in focusPath {
            expandedNodes.insert(pathNodeId)
        }
        
        // 确保新聚焦的节点也自动展开（如果有子项）
        let nodeType = identifyNodeType(nodeId)
        switch nodeType {
        case .task(let task):
            if !task.children.isEmpty {
                expandedNodes.insert(task.id)
            }
        case .checklistItem(let task, let item):
            if !item.subStepsList.isEmpty {
                expandedNodes.insert(item.id)
            }
        case .subStep(_, _, let stepId):
            // 子步骤也自动展开
            expandedNodes.insert(stepId)
        case .unknown:
            break
        }

        // Record the path of the previous level, if it was at least a checklist item
        if let rootTaskId = pathBeforeDeepening.first, pathBeforeDeepening.count >= 2 {
            let checklistItemId = pathBeforeDeepening[1]
            let subStepPathArray = pathBeforeDeepening.count > 2 ? Array(pathBeforeDeepening.dropFirst(2)) : []
            setLastFocusPath(for: rootTaskId, checklistItemId: checklistItemId, subStepPath: subStepPathArray)
        }
        
    }
    
    /// 退出聚焦
    func exitFocus() {
        recordCurrentFocusPath() // Record path before exiting

        let pathBeforeExit = self.focusPath

        // 清空聚焦路径
        focusPath = []
        
        // 恢复到进入一步模式前的任务列表展开状态
        restoreTaskListExpandedState()

        // Record the full path before exiting, if it was at least a checklist item
        if let rootTaskId = pathBeforeExit.first, pathBeforeExit.count >= 2 {
            let checklistItemId = pathBeforeExit[1]
            let subStepPathArray = pathBeforeExit.count > 2 ? Array(pathBeforeExit.dropFirst(2)) : []
            setLastFocusPath(for: rootTaskId, checklistItemId: checklistItemId, subStepPath: subStepPathArray)
        }
    }
    
    /// 退出到指定层级
    func exitToLevel(_ level: Int) {
        recordCurrentFocusPath() // Record path before changing level

        guard level >= 0 else { 
            exitFocus() // This will handle saving the path before full exit
            return 
        }
        
        if level < focusPath.count {
            // 保存当前要保留的节点ID
            let preservedNodeIds = Set(focusPath.prefix(level + 1))
            
            // 更新路径
            focusPath = Array(focusPath.prefix(level + 1))
            
            // 清理不在当前路径上的展开状态，保留聚焦路径上的节点展开状态
            let newExpandedNodes = expandedNodes.filter { nodeId in
                preservedNodeIds.contains(nodeId) ||
                isTaskNode(nodeId) // 保留顶层任务节点的展开状态
            }
            expandedNodes = newExpandedNodes

            // Record the new target path, if it's at least a checklist item
            let newFocusPathAfterExit = self.focusPath
            if let rootTaskId = newFocusPathAfterExit.first, newFocusPathAfterExit.count >= 2 {
                let checklistItemId = newFocusPathAfterExit[1]
                let subStepPathArray = newFocusPathAfterExit.count > 2 ? Array(newFocusPathAfterExit.dropFirst(2)) : []
                setLastFocusPath(for: rootTaskId, checklistItemId: checklistItemId, subStepPath: subStepPathArray)
            }
        }
    }
    
    /// 判断是否为任务节点
    private func isTaskNode(_ nodeId: UUID) -> Bool {
        let nodeType = identifyNodeType(nodeId)
        if case .task(_) = nodeType {
            return true
        }
        return false
    }
    
    /// 处理节点完成状态 - 支持双向切换（完成/撤销）
    /// 注意：这个方法现在只处理小行动和子步骤的完成，任务完成应由ViewModel处理
    public func handleNodeCompletion(_ nodeId: UUID) {
        let nodeType = identifyNodeType(nodeId)
        
        switch nodeType {
        case .task(_):
            // 任务完成现在由ViewModel处理，这里不再直接修改任务数据
            break
            
        case .checklistItem(let task, let item):
            // 小行动支持双向切换：完成 ↔ 撤销
            let wasCompleted = item.isCompleted
            taskManager.toggleChecklistItemCompletion(task, itemId: item.id)
            
            if !wasCompleted {
                // 从未完成变为完成，处理完成动画
                handleCompletionAnimation(nodeId, wasCompleted: false)
            } else {
                // 从完成变为未完成，清除动画状态
                clearAnimationState(nodeId)
            }
            
        case .subStep(let task, let checklistItem, _):
            // 子步骤支持双向切换：完成 ↔ 撤销
            let wasCompleted = findSubStepCompleted(nodeId, in: checklistItem.subStepsList)
            taskManager.toggleSubStepCompletion(task, checklistItemId: checklistItem.id, subStepId: nodeId)
            
            if !wasCompleted {
                // 从未完成变为完成，处理完成动画
                handleCompletionAnimation(nodeId, wasCompleted: false)
            } else {
                // 从完成变为未完成，清除动画状态
                clearAnimationState(nodeId)
            }
            
        case .unknown:
            break
        }
    }
    
    /// 兼容旧代码的方法，调用handleNodeCompletion
    @available(*, deprecated, message: "请使用handleNodeCompletion方法")
    func toggleNodeCompletion(_ nodeId: UUID) {
        handleNodeCompletion(nodeId)
    }
    
    /// 展开/折叠节点
    func toggleNodeExpansion(_ nodeId: UUID) {
        let context = getCurrentDisplayContext()
        
        if expandedNodes.contains(nodeId) {
            // 如果已展开，则折叠
            expandedNodes.remove(nodeId)
        } else {
            // 如果未展开，则展开
            if context == .taskList {
                // 任务列表模式：只能展开一个任务，清空其他展开状态
                let nodeType = identifyNodeType(nodeId)
                if case .task(_) = nodeType {
                    expandedNodes.removeAll()
                }
            }
            expandedNodes.insert(nodeId)
        }
    }
    
    /// 检查节点是否展开
    func isNodeExpanded(_ nodeId: UUID) -> Bool {
        expandedNodes.contains(nodeId)
    }
    
    /// 检查节点是否在动画中
    func isNodeAnimating(_ nodeId: UUID) -> Bool {
        animatingNodes[nodeId] != nil
    }
    
    /// 清理动画状态
    public func clearAnimationState(_ nodeId: UUID) {
        animatingNodes.removeValue(forKey: nodeId)
    }
    
    /// 清理所有动画状态
    func clearAllAnimationStates() {
        animatingNodes.removeAll()
    }
    
    /// 清理超时的动画状态
    func clearTimeoutAnimations(timeout: TimeInterval = 5.0) -> [UUID] {
        let currentTime = Date()
        var removedNodes: [UUID] = []
        
        for (nodeId, startTime) in animatingNodes {
            if currentTime.timeIntervalSince(startTime) > timeout {
                animatingNodes.removeValue(forKey: nodeId)
                removedNodes.append(nodeId)
            }
        }
        
        return removedNodes
    }
    
    /// 检查节点是否在聚焦路径中
    func isInFocusPath(_ nodeId: UUID) -> Bool {
        focusPath.contains(nodeId)
    }
    
    /// 获取当前显示上下文
    func getCurrentDisplayContext() -> DisplayContext {
        if focusPath.isEmpty {
            return .taskList
        } else {
            return .focusMode
        }
    }
    
    // MARK: - 导航和面包屑
    
    /// 获取面包屑标题
    func getBreadcrumbTitles() -> [String] {
        var titles: [String] = []
        
        // 如果没有聚焦路径，返回空数组（不显示面包屑）
        guard !focusPath.isEmpty else { return titles }
        
        // 获取根任务（聚焦路径的第一个节点）
        guard let rootTaskId = focusPath.first,
              let rootTask = getTaskForNode(rootTaskId) else { return titles }
        
        // 添加项目名（如果有项目，限制8个字符）
        var hasProject = false
        if let projectId = rootTask.project,
           let project = taskManager.getProjectById(projectId) {
            let projectName = limitStringLength(project.name, maxLength: 8)
            titles.append(projectName)
            hasProject = true
        }
        
        // 只有在聚焦深度大于1时才添加路径层级
        // 面包屑显示"到达当前位置的路径"，不包含当前位置本身
        if focusPath.count > 1 {
            // 构建路径标题（排除最后一层当前位置）
            let pathToShow = Array(focusPath.dropLast()) // 去掉最后一层
            var fullPathTitles: [String] = []
            
            for (index, nodeId) in pathToShow.enumerated() {
                if let title = getNodeTitle(nodeId) {
                    let limitedTitle: String
                    if index == 0 {
                        // 行动名限制10个字符
                        limitedTitle = limitStringLength(title, maxLength: 10)
                    } else {
                        // 上级限制12个字符
                        limitedTitle = limitStringLength(title, maxLength: 12)
                    }
                    fullPathTitles.append(limitedTitle)
                }
            }
            
            // 计算最大层级数：项目名(1) + 路径层级(3) = 4层
            let maxLevels = 3
            let currentPathCount = fullPathTitles.count
            
            if currentPathCount <= maxLevels {
                // 层级不多，显示全部
                titles.append(contentsOf: fullPathTitles)
            } else {
                // 层级过多，省略中间部分
                // 显示格式：第一层 > ... > 倒数第二层
                titles.append(fullPathTitles[0]) // 第一层（行动名）
                if currentPathCount > 2 {
                    titles.append("...") // 省略号
                    titles.append(fullPathTitles[currentPathCount - 1]) // 最后一层（倒数第一层）
                } else if currentPathCount == 2 {
                    titles.append(fullPathTitles[1]) // 只有两层时，显示第二层
                }
            }
        }
        
        return titles
    }
    
    /// 限制字符串长度，超出显示省略号
    private func limitStringLength(_ text: String, maxLength: Int) -> String {
        if text.count <= maxLength {
            return text
        } else {
            let endIndex = text.index(text.startIndex, offsetBy: maxLength - 1)
            return String(text[..<endIndex]) + "…"
        }
    }
    
    /// 导航到面包屑层级
    func navigateToBreadcrumb(_ level: Int) {
        guard !focusPath.isEmpty else { return }
        
        // 获取根任务
        guard let rootTaskId = focusPath.first,
              let rootTask = getTaskForNode(rootTaskId) else { return }
        
        // 检查是否有项目
        let hasProject = rootTask.project != nil
        
        if hasProject {
            // 有项目的情况：项目名(level 0, 不可点击) > 任务名(level 1) > 小行动(level 2) > 子步骤(level 3+)
            if level == 0 {
                // 点击项目名 - 不响应（项目名不可点击）
                return
            } else {
                // 点击其他层级 - 调整索引（因为项目名占了level 0）
                let targetFocusLevel = level - 1
                if targetFocusLevel == 0 {
                    // 回到任务层级
                    exitToLevel(0)
                } else {
                    // 回到更深层级
                    exitToLevel(targetFocusLevel)
                }
            }
        } else {
            // 无项目的情况：任务名(level 0) > 小行动(level 1) > 子步骤(level 2+)
            if level == 0 {
                // 回到任务层级
                exitToLevel(0)
            } else {
                // 回到更深层级
                exitToLevel(level)
            }
        }
        
     
    }
    
    // MARK: - 私有辅助方法
    
    /// 识别节点类型
    private func identifyNodeType(_ nodeId: UUID) -> NodeType {
        // 先查找任务
        let allTasks = taskManager.getTasksByStatus(TaskStatus.doing.rawValue)
        
        for task in allTasks {
            if task.id == nodeId {
                return .task(task)
            }
            
            // 查找小行动
            if let checklist = task.checklist {
                for item in checklist {
                    if item.id == nodeId {
                        return .checklistItem(task, item)
                    }
                    
                    // 查找子步骤
                    if findSubStepExists(nodeId, in: item.subStepsList) {
                        return .subStep(task, item, nodeId)
                    }
                }
            }
        }
        
        return .unknown
    }
    
    /// 获取节点对应的任务对象
    public func getTaskForNode(_ nodeId: UUID) -> Task? {
        let nodeType = identifyNodeType(nodeId)
        switch nodeType {
        case .task(let task):
            return task
        case .checklistItem(let task, _):
            return task
        case .subStep(let task, _, _):
            return task
        case .unknown:
            return nil
        }
    }
    
    /// 构建到指定节点的路径
    private func buildPathTo(_ nodeId: UUID) -> [UUID] {
        let nodeType = identifyNodeType(nodeId)
        
        switch nodeType {
        case .task(_):
            return [nodeId]
            
        case .checklistItem(let task, _):
            return [task.id, nodeId]
            
        case .subStep(let task, let checklistItem, _):
            var path = [task.id, checklistItem.id]
            if let subPath = buildSubStepPath(to: nodeId, in: checklistItem.subStepsList) {
                path.append(contentsOf: subPath)
            }
            return path
            
        case .unknown:
            return []
        }
    }
    
    /// 构建子步骤路径
    private func buildSubStepPath(to targetId: UUID, in steps: [SubStep]) -> [UUID]? {
        for step in steps {
            if step.id == targetId {
                return [targetId]
            }
            
            if let subPath = buildSubStepPath(to: targetId, in: step.subSteps) {
                return [step.id] + subPath
            }
        }
        return nil
    }
    
    /// 获取节点标题
    public func getNodeTitle(_ nodeId: UUID) -> String? {
        let nodeType = identifyNodeType(nodeId)
        
        switch nodeType {
        case .task(let task):
            return task.title
        case .checklistItem(_, let item):
            return item.title
        case .subStep(_, _, let stepId):
            return findSubStepTitle(stepId)
        case .unknown:
            return nil
        }
    }
    
    /// 查找子步骤标题
    private func findSubStepTitle(_ stepId: UUID) -> String? {
        let allTasks = taskManager.getTasksByStatus(TaskStatus.doing.rawValue)
        
        for task in allTasks {
            if let checklist = task.checklist {
                for item in checklist {
                    if let title = findSubStepTitleRecursive(stepId, in: item.subStepsList) {
                        return title
                    }
                }
            }
        }
        return nil
    }
    
    private func findSubStepTitleRecursive(_ stepId: UUID, in steps: [SubStep]) -> String? {
        for step in steps {
            if step.id == stepId {
                return step.title
            }
            if let title = findSubStepTitleRecursive(stepId, in: step.subSteps) {
                return title
            }
        }
        return nil
    }
    
    /// 检查子步骤是否存在
    private func findSubStepExists(_ stepId: UUID, in steps: [SubStep]) -> Bool {
        for step in steps {
            if step.id == stepId {
                return true
            }
            if findSubStepExists(stepId, in: step.subSteps) {
                return true
            }
        }
        return false
    }
    
    /// 查找子步骤完成状态
    public func findSubStepCompleted(_ stepId: UUID, in steps: [SubStep]) -> Bool {
        for step in steps {
            if step.id == stepId {
                return step.isCompleted
            }
            if findSubStepExists(stepId, in: step.subSteps) {
                return findSubStepCompleted(stepId, in: step.subSteps)
            }
        }
        return false
    }
    
    /// 处理完成动画
    public func handleCompletionAnimation(_ nodeId: UUID, wasCompleted: Bool) {
        if !wasCompleted {
            // 从未完成变为完成，添加动画状态
            animatingNodes[nodeId] = Date()
            
            // 延迟移除动画状态
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) { [weak self] in
                self?.animatingNodes.removeValue(forKey: nodeId)
                
                // 如果是当前聚焦的节点，回退一级
                if self?.focusPath.last == nodeId {
                    self?.exitToLevel((self?.focusLevel ?? 1) - 2)
                }
            }
        }
    }
    
    // MARK: - 任务列表展开状态管理
    
    /// 保存当前任务列表的展开状态
    public func saveTaskListExpandedState() {
        // 只保存任务节点的展开状态
        taskListExpandedState = expandedNodes.filter { nodeId in
            let nodeType = identifyNodeType(nodeId)
            if case .task(_) = nodeType {
                return true
            }
            return false
        }
        

    }
    
    /// 恢复任务列表的展开状态
    private func restoreTaskListExpandedState() {
        // 恢复到保存的状态
        expandedNodes = taskListExpandedState
        
        
        // 清空保存的状态
        taskListExpandedState = []
    }
    
    // MARK: - Focus路径验证
    
    /// 验证focus路径是否仍然有效
    private func isValidFocusPath(taskId: UUID, checklistItemId: UUID, subStepPath: [UUID]) -> Bool {
        // 1. 检查任务是否存在且未完成
        guard let task = taskManager.getTask(withId: taskId),
              task.status != TaskStatus.done.rawValue else {
            return false
        }
        
        // 2. 检查小行动是否存在且未完成
        guard let checklist = task.checklist,
              let checklistItem = checklist.first(where: { $0.id == checklistItemId }),
              !checklistItem.isCompleted else {
            return false
        }
        
        // 3. 如果有子步骤路径，验证子步骤路径是否仍然有效
        if !subStepPath.isEmpty {
            return isValidSubStepPath(subStepPath, in: checklistItem.subStepsList)
        }
        
        return true
    }
    
    /// 递归验证子步骤路径是否有效
    private func isValidSubStepPath(_ path: [UUID], in steps: [SubStep]) -> Bool {
        guard !path.isEmpty else { return true }
        
        let currentStepId = path[0]
        let remainingPath = Array(path.dropFirst())
        
        // 查找当前步骤
        guard let currentStep = steps.first(where: { $0.id == currentStepId }),
              !currentStep.isCompleted else {
            return false
        }
        
        // 如果还有更深的路径，递归验证
        if !remainingPath.isEmpty {
            return isValidSubStepPath(remainingPath, in: currentStep.subSteps)
        }
        
        return true
    }

    /// 恢复ActionFocus状态
    /// 用于从任务详情页的"最近一步"功能恢复到上次的聚焦位置
    func restoreActionFocusState(taskId: UUID, checklistItemId: UUID, subStepPath: [UUID]) {
        // 构建完整的焦点路径
        var fullFocusPath = [taskId, checklistItemId]
        fullFocusPath.append(contentsOf: subStepPath)
        
        // 直接设置焦点路径
        focusPath = fullFocusPath
        
        // 清除所有展开状态，只保留当前路径上的节点
        expandedNodes.removeAll()
        
        // 展开所有路径上的节点
        for nodeId in fullFocusPath {
            expandedNodes.insert(nodeId)
        }
        
        // 更新 ActionFocusState 记录
        setLastFocusPath(for: taskId, checklistItemId: checklistItemId, subStepPath: subStepPath)
        
        // 加载并展开当前层级的直接子节点（只有目标节点的子节点）
        if let lastNodeId = fullFocusPath.last {
            // 不需要额外操作，因为已经将最后一个节点添加到展开节点集合中，
            // 这确保了它的子节点会被显示
            
            
        }
        
        // 显示恢复提示
        ToastManager.shared.showSuperLightInfo("已恢复到指定位置")
    }
}

// MARK: - 节点类型定义

private enum NodeType {
    case task(Task)
    case checklistItem(Task, ChecklistItem)  
    case subStep(Task, ChecklistItem, UUID)
    case unknown
} 