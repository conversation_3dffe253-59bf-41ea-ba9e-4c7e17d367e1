import SwiftUI
import Foundation

/// 邀请码验证视图模型
class InviteVerificationViewModel: ObservableObject {
    // MARK: - 发布属性
    
    /// 邀请码输入
    @Published var inviteCode: String = ""
    
    /// 验证状态
    @Published var verificationState: VerificationState = .idle
    
    /// 错误消息
    @Published var errorMessage: String = ""
    
    /// 剩余使用次数
    @Published var remainingUses: Int? = nil
    
    /// 验证成功回调
    var onVerificationSuccess: (() -> Void)?
    
    // MARK: - 类型定义
    
    /// 验证状态枚举
    enum VerificationState {
        case idle      // 空闲状态
        case verifying // 验证中
        case success   // 验证成功
        case failed    // 验证失败
    }
    
    // MARK: - 依赖
    
    private let inviteRepository: InviteRepository
    
    // MARK: - 初始化
    
    init(inviteRepository: InviteRepository = DependencyContainer.inviteRepository()) {
        self.inviteRepository = inviteRepository
    }
    
    // MARK: - 公共方法
    
    /// 检查设备是否已通过验证
    func checkVerificationStatus() -> Bool {
        let status = inviteRepository.getLocalInviteStatus()
        return status.isVerified
    }
    
    /// 验证邀请码
    @MainActor
    func verifyInviteCode() async {
        // 验证输入不为空
        let trimmedCode = inviteCode.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedCode.isEmpty else {
            errorMessage = "邀请码不能为空"
            verificationState = .failed
            return
        }
        
        // 开始验证
        verificationState = .verifying
        
        do {
            // 调用仓库验证邀请码
            let (isValid, remaining) = try await inviteRepository.verifyInviteCode(trimmedCode)
            
            // 更新剩余使用次数
            self.remainingUses = remaining
            
            if isValid {
                verificationState = .success
                
                // 调用成功回调
                onVerificationSuccess?()
                
                // 短暂延迟后更新UI
                try await delay(seconds: 1.0)
            } else {
                if let remaining = remaining, remaining <= 0 {
                    errorMessage = "邀请码已达到最大使用次数"
                } else {
                    errorMessage = "邀请码无效或已被使用"
                }
                verificationState = .failed
            }
        } catch {
            errorMessage = "验证失败: \(error.localizedDescription)"
            verificationState = .failed
        }
    }
    
    /// 自定义延迟函数
    private func delay(seconds: Double) async throws {
        try await withCheckedThrowingContinuation { continuation in
            DispatchQueue.main.asyncAfter(deadline: .now() + seconds) {
                continuation.resume(returning: ())
            }
        }
    }
}

/// 邀请码创建视图模型
class InviteCreationViewModel: ObservableObject {
    // MARK: - 发布属性
    
    /// 自定义邀请码输入
    @Published var customCode: String = ""
    
    /// 最大使用次数
    @Published var maxUses: Int = 1
    
    /// 创建状态
    @Published var creationState: CreationState = .idle
    
    /// 错误消息
    @Published var errorMessage: String = ""
    
    /// 已创建的邀请码列表
    @Published var ownCodes: [InviteCode] = []
    
    /// 创建成功回调
    var onCreationSuccess: (() -> Void)?
    
    // MARK: - 类型定义
    
    /// 创建状态枚举
    enum CreationState {
        case idle      // 空闲状态
        case creating  // 创建中
        case success   // 创建成功
        case failed    // 创建失败
    }
    
    // MARK: - 依赖
    
    private let inviteRepository: InviteRepository
    
    // MARK: - 初始化
    
    init(inviteRepository: InviteRepository = DependencyContainer.inviteRepository()) {
        self.inviteRepository = inviteRepository
        loadOwnCodes()
    }
    
    // MARK: - 公共方法
    
    /// 加载用户创建的邀请码
    func loadOwnCodes() {
        let status = inviteRepository.getLocalInviteStatus()
        ownCodes = inviteRepository.getInviteCodesByCreator(deviceId: status.deviceId)
    }
    
    /// 创建邀请码
    @MainActor
    func createInviteCode() async {
        // 验证输入
        let trimmedCode = customCode.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedCode.isEmpty else {
            errorMessage = "邀请码不能为空"
            creationState = .failed
            return
        }
        
        // 检查长度
        guard trimmedCode.count >= 3 else {
            errorMessage = "邀请码至少需要3个字符"
            creationState = .failed
            return
        }
        
        // 验证最大使用次数
        guard maxUses > 0 else {
            errorMessage = "最大使用次数必须大于0"
            creationState = .failed
            return
        }
        
        // 开始创建
        creationState = .creating
        
        do {
            // 先检查是否存在
            let exists = try await inviteRepository.checkInviteCodeExists(trimmedCode)
            if exists {
                errorMessage = "邀请码已存在，请使用其他邀请码"
                creationState = .failed
                return
            }
            
            // 调用仓库创建邀请码
            let _ = try await inviteRepository.createInviteCode(code: trimmedCode, maxUses: maxUses)
            
            // 更新状态
            creationState = .success
            customCode = ""
            
            // 调用成功回调
            onCreationSuccess?()
            
            // 更新列表
            loadOwnCodes()
        } catch let error as InviteError {
            // 处理特定的邀请错误
            errorMessage = error.localizedDescription
            creationState = .failed
        } catch {
            // 处理其他错误
            errorMessage = "创建失败: \(error.localizedDescription)"
            creationState = .failed
        }
    }
} 