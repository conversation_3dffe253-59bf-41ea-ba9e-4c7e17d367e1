import Foundation
import SwiftUI
import SwiftData
import Combine

/// 一步视图模型 - 重构后的轻量级版本
class FocusViewModel: BaseTaskViewModel {
    // MARK: - 核心数据
    @Published var doingTasks: [Task] = []
    
    // MARK: - 状态管理器
    let focusManager: FocusManager
    let uiStateManager: UIStateManager
    
    // MARK: - 计算属性
    
    /// 是否处于聚焦模式
    var isFocusMode: Bool {
        return focusManager.isFocused
    }
    
    /// 获取当前聚焦的任务
    func getFocusedTask() -> Task? {
        guard let taskId = focusManager.focusPath.first else { return nil }
        return doingTasks.first { $0.id == taskId }
    }
    
    // MARK: - 初始化
    override init(taskManager: TaskManager = DependencyContainer.taskManager()) {
        // 初始化状态管理器
        self.focusManager = FocusManager(taskManager: taskManager)
        self.uiStateManager = UIStateManager(taskManager: taskManager)
        
        super.init(taskManager: taskManager)
        
        // 在主线程上执行UI更新操作
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            // 加载初始数据
            self.loadTasks()
        }
    }
    
    // MARK: - 重写父类方法
    
    /// 加载一步任务
    override func loadTasks() {
        // 在主线程上修改UI绑定的属性
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            self.doingTasks = self.taskManager.getTasksByStatus(TaskStatus.doing.rawValue)
                .sorted { $0.sortOrder < $1.sortOrder }
                .filter { task in
                    !self.pendingDeletionTaskIDs.contains(task.id) && 
                    !self.pendingCompletionTaskIDs.contains(task.id)
                }
            
            // 验证聚焦状态是否仍然有效
            if let taskId = self.focusManager.focusPath.first,
               !self.doingTasks.contains(where: { $0.id == taskId }) {
                // 聚焦的任务不存在了，清除聚焦状态
                self.focusManager.exitFocus()
            }
        }
    }
    
    // MARK: - 聚焦管理
    
    /// 聚焦于某个任务
    func focusOnTask(_ task: Task, initialChecklistItemId: UUID? = nil) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            var path = [task.id]
            if let itemId = initialChecklistItemId {
                path.append(itemId)
            }
            
            self.focusManager.focus(to: path)
            self.loadTasks()
        }
    }
    
    /// 聚焦到小行动级别
    func focusOnChecklistItem(_ item: ChecklistItem, in task: Task) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            let path = [task.id, item.id]
            self.focusManager.focus(to: path)
            
            print("[FocusViewModel] 聚焦到小行动 - 任务: \(task.title), 小行动: \(item.title)")
        }
    }
    
    /// 退出聚焦模式
    func exitFocusMode() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            self.focusManager.exitFocus()
            self.loadTasks()
        }
    }
    
    /// 退出到指定层级
    func exitToLevel(_ level: Int) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            self.focusManager.focusTo(level: level)
        }
    }
    
    // 任务完成和删除后的处理
    func handleTaskCompletionForFocus(_ task: Task) {
        // 如果完成的是聚焦任务，退出聚焦
        if focusManager.focusPath.first == task.id {
            exitFocusMode()
        }
    }
    
    func handleTaskDeletionForFocus(_ task: Task) {
        // 如果删除的是聚焦任务，退出聚焦
        if focusManager.focusPath.first == task.id {
            exitFocusMode()
        }
    }
    
    // MARK: - 公共方法
    
    /// 播放完成音效（委托给 UIStateManager）
    func playCompletionSound() {
        uiStateManager.playCompletionSound()
    }
    
    /// 移动任务到下一步
    func moveTaskToNextActions(task: Task) {
        // 修改数据模型
        task.status = TaskStatus.na.rawValue
        task.updatedAt = Date()
        taskManager.updateTask(task)
        
        // 如果移动的是聚焦任务，退出聚焦模式
        if task.id == focusStateManager.focusedTaskId {
            exitFocusMode()
        }
        
        // 刷新UI
        loadTasks()
    }
    
    /// 清空所有一步任务（移回下一步）
    func clearAllDoingTasks() {
        // 修改数据模型
        let tasks = taskManager.getTasksByStatus(TaskStatus.doing.rawValue)
        for task in tasks {
            task.status = TaskStatus.na.rawValue
            task.updatedAt = Date()
            taskManager.updateTask(task)
        }
        
        // 清空时退出聚焦模式
        exitFocusMode()
        
        // 刷新UI
        loadTasks()
    }
    
    /// 更新任务排序
    func updateTaskOrder(tasks: [Task]) {
        // 修改数据模型
        for (index, task) in tasks.enumerated() {
            task.sortOrder = Int16(index)
            taskManager.updateTask(task)
        }
        
        // 刷新UI
        loadTasks()
    }
    
    // MARK: - 拖放相关方法
    
    /// 处理任务拖放操作
    func handleTaskDrop(draggedTask: Task?, toTask: Task) {
        guard let draggedTask = draggedTask else { return }
        
        // 获取源任务和目标任务的索引
        guard let fromIndex = doingTasks.firstIndex(where: { $0.id == draggedTask.id }),
              let toIndex = doingTasks.firstIndex(where: { $0.id == toTask.id }) else {
            return
        }
        
        // 如果源和目标相同，不执行操作
        if fromIndex == toIndex { return }
        
        // 创建任务数组的副本
        var updatedTasks = doingTasks
        
        // 移动任务
        let task = updatedTasks.remove(at: fromIndex)
        updatedTasks.insert(task, at: toIndex)
        
        // 更新排序
        updateTaskOrder(tasks: updatedTasks)
    }
    
    // MARK: - 回声Drop功能（委托给 UIStateManager）
    
    /// 显示回声Drop
    func showEchoDrop() {
        uiStateManager.showEchoDrop()
    }
    
    /// 隐藏回声Drop
    func hideEchoDrop() {
        uiStateManager.hideEchoDrop()
    }
    
    /// 处理回声文本，将其转化为任务
    func handleEchoDropText() {
        uiStateManager.handleEchoDropText()
        // 重新加载任务列表以反映变化
        loadTasks()
    }
    
    /// 释放回声文本（不保存，只清空）
    func releaseEcho() {
        uiStateManager.releaseEcho()
    }
    
    // MARK: - 私有方法（已移动到对应管理器）
    // 注：selectQuoteForToday 和 setupAudioPlayer 已移动到 UIStateManager
    
    // MARK: - ActionFocus 相关方法（委托给 ActionFocusManager）
    
    /// 聚焦到小行动，进入 ActionFocus 模式
    func focusOnChecklistItem(_ item: ChecklistItem, in task: Task) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            // 设置聚焦的任务
            self.focusStateManager.focusOnTask(task)
            
            // 委托给 ActionFocusManager 进入 ActionFocus 模式
            self.actionFocusManager.enterActionFocusMode(taskId: task.id, checklistItemId: item.id)
            
            print("[FocusViewModel] 进入 ActionFocus 模式 - 任务: \(task.title), 小行动: \(item.title)")
        }
    }
    
    /// 获取面包屑标题
    func getBreadcrumbTitles() -> [String] {
        return focusManager.getBreadcrumbTitles()
    }
    
    /// 导航到面包屑层级
    func navigateToBreadcrumbLevel(_ level: Int) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            if level == 0 {
                // 点击项目名，回到一步列表（完全退出聚焦模式）
                self.exitFocusMode()
            } else {
                // 其他层级，退出到对应层级
                self.focusManager.focusTo(level: level - 1) // 减1因为面包屑从项目名开始
            }
        }
    }
    
    /// 获取当前聚焦的小行动
    func getFocusedChecklistItem() -> ChecklistItem? {
        guard focusManager.focusLevel >= 2,
              let taskId = focusManager.focusPath.first,
              let itemId = focusManager.focusPath[safe: 1],
              let task = taskManager.getTaskById(taskId),
              let checklist = task.checklist else {
            return nil
        }
        
        return checklist.first { $0.id == itemId }
    }
    
    /// 切换小行动完成状态（使用统一的完成处理逻辑）
    func toggleChecklistItemCompletion(_ item: ChecklistItem) {
        guard let focusedTask = getFocusedTask() else { 
            print("[FocusViewModel] 错误：无法找到聚焦的任务")
            return 
        }
        
        let wasCompleted = item.isCompleted
        
        // 使用 taskManager 来处理小行动完成状态切换
        taskManager.toggleChecklistItemCompletion(focusedTask, itemId: item.id)
        
        // 使用统一的完成处理逻辑
        focusManager.handleNodeCompletion(item.id, wasCompleted: wasCompleted)
        
        // 强制刷新任务数据，确保获取最新状态
        loadTasks()
        
        // 如果从未完成变为完成，添加视觉反馈延迟
        if !wasCompleted {
            // 添加到动画状态，显示完成效果
            DispatchQueue.main.async { [weak self] in
                self?.animatingSubSteps[item.id] = Date()
            }
            
            // 延迟3.5秒后退出 ActionFocus 模式
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.5) { [weak self] in
                guard let self = self else { return }
                
                // 移除动画状态
                self.animatingSubSteps.removeValue(forKey: item.id)
                
                // 重新获取最新的小行动数据确认状态
                if let updatedItem = self.getFocusedChecklistItem() {
                    print("[FocusViewModel] 延迟检查后状态: \(updatedItem.isCompleted)")
                    
                    if updatedItem.isCompleted {
                        print("[FocusViewModel] 小行动已完成，准备退出 ActionFocus 模式")
                        
                        // 小行动完成，清除状态而不是保存
                        if let taskId = self.focusedTaskId {
                            self.clearActionFocusState(for: taskId)
                        }
                        
                        // 退出 ActionFocus 模式
                        self.isInActionFocusMode = false
                        self.focusedChecklistItemId = nil
                        self.focusedSubStepPath = []
                        
                        print("[FocusViewModel] 已退出 ActionFocus 模式")
                    }
                } else {
                    print("[FocusViewModel] 错误：无法获取更新后的小行动数据")
                }
            }
        } else {
            // 如果是从完成变为未完成，立即处理
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
                guard let self = self else { return }
                
                if let updatedItem = self.getFocusedChecklistItem() {
                    print("[FocusViewModel] 切换后状态: \(updatedItem.isCompleted)")
                    print("[FocusViewModel] 状态切换完成，但不需要退出 ActionFocus 模式")
                }
            }
        }
    }
    
    /// 切换子步骤完成状态
    func toggleSubStepCompletion(_ stepId: UUID) {
        guard let focusedTask = getFocusedTask(),
              let checklistItem = getFocusedChecklistItem(),
              let taskChecklist = focusedTask.checklist,
              let itemIndex = taskChecklist.firstIndex(where: { $0.id == checklistItem.id }) else {
            print("[FocusViewModel] 无法找到聚焦的任务或小行动")
            return
        }
        
        print("[FocusViewModel] 开始切换子步骤完成状态，stepId: \(stepId)")
        
        // 获取子步骤列表的可变副本
        var steps = focusedTask.checklist![itemIndex].subStepsList
        
        // 递归查找并获取当前状态
        var wasCompleted = false
        var stepTitle = ""
        let success = getSubStepStatus(stepId: stepId, in: steps, wasCompleted: &wasCompleted, stepTitle: &stepTitle)
        
        if success {
            print("[FocusViewModel] 子步骤当前状态: \(stepTitle) -> \(wasCompleted)")
            
            // 立即执行状态切换（立即勾选、变灰、划线）
            performSubStepToggle(stepId: stepId, wasCompleted: wasCompleted)
            
            // 如果从未完成变为完成，添加动画状态防止立即移动位置
            if !wasCompleted {
                    DispatchQueue.main.async { [weak self] in
                        self?.animatingSubSteps[stepId] = Date()
                    }
                    
                // 延迟3.5秒后移除动画状态，让项目沉下去
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3.5) { [weak self] in
                        guard let self = self else { return }
                        
                    // 移除动画状态，允许项目移动到已完成区域
                        self.animatingSubSteps.removeValue(forKey: stepId)
                        
                    // 如果这是当前聚焦的子步骤，延迟回退到上一级
                    if let lastStepId = self.focusedSubStepPath.last, lastStepId == stepId {
                        // 再延迟1秒后回退到上一级
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
                            guard let self = self else { return }
                        self.focusedSubStepPath.removeLast()
                            print("[FocusViewModel] 子步骤完成，回退到上一级")
                        }
                    }
                }
            }
        } else {
            print("[FocusViewModel] 错误：无法找到指定的子步骤")
        }
    }
    
    /// 获取子步骤状态（不修改状态）
    private func getSubStepStatus(stepId: UUID, in steps: [SubStep], wasCompleted: inout Bool, stepTitle: inout String) -> Bool {
        // 在当前层级查找
        for step in steps {
            if step.id == stepId {
                wasCompleted = step.isCompleted
                stepTitle = step.title
                return true
            }
            
            // 递归查找子步骤
            if getSubStepStatus(stepId: stepId, in: step.subSteps, wasCompleted: &wasCompleted, stepTitle: &stepTitle) {
                return true
            }
        }
        
        return false
    }
    
    /// 执行实际的子步骤状态切换
    private func performSubStepToggle(stepId: UUID, wasCompleted: Bool) {
        guard let focusedTask = getFocusedTask(),
              let checklistItem = getFocusedChecklistItem(),
              let taskChecklist = focusedTask.checklist,
              let itemIndex = taskChecklist.firstIndex(where: { $0.id == checklistItem.id }) else {
            print("[FocusViewModel] 无法找到聚焦的任务或小行动")
            return
        }
        
        // 获取子步骤列表的可变副本
        var steps = focusedTask.checklist![itemIndex].subStepsList
        
        // 递归查找并切换子步骤状态
        var actualWasCompleted = false
        var stepTitle = ""
        let success = toggleSubStepRecursively(stepId: stepId, in: &steps, wasCompleted: &actualWasCompleted, stepTitle: &stepTitle)
        
        if success {
            print("[FocusViewModel] 执行子步骤切换: \(stepTitle) -> \(actualWasCompleted) 变为 \(!actualWasCompleted)")
            
            // 更新小行动的子步骤列表
            focusedTask.checklist![itemIndex].subStepsList = steps
            
            // 保存到数据库
            taskManager.updateTask(focusedTask)
            
            // 刷新任务数据
            loadTasks()
        }
    }
    
    /// 递归查找并切换子步骤状态
    private func toggleSubStepRecursively(stepId: UUID, in steps: inout [SubStep], wasCompleted: inout Bool, stepTitle: inout String) -> Bool {
        // 在当前层级查找
        for i in 0..<steps.count {
            if steps[i].id == stepId {
                wasCompleted = steps[i].isCompleted
                stepTitle = steps[i].title
                steps[i].toggleCompletion()
                return true
            }
            
            // 递归查找子步骤
            var subSteps = steps[i].subSteps
            if toggleSubStepRecursively(stepId: stepId, in: &subSteps, wasCompleted: &wasCompleted, stepTitle: &stepTitle) {
                steps[i].subSteps = subSteps
                return true
            }
        }
        
        return false
    }
    
    /// 添加子步骤到聚焦的小行动
    func addSubStepToFocusedItem(title: String) {
        guard let focusedTask = getFocusedTask(),
              let checklistItem = getFocusedChecklistItem(),
              let taskChecklist = focusedTask.checklist,
              let itemIndex = taskChecklist.firstIndex(where: { $0.id == checklistItem.id }) else { 
            print("[FocusViewModel] 无法找到聚焦的任务或小行动")
            return 
        }
        
        // 创建新的子步骤
        let newSubStep = SubStep(title: title)
        
        // 添加到小行动的子步骤数组
        focusedTask.checklist?[itemIndex].addSubStep(newSubStep)
        
        // 保存到数据库
        taskManager.updateTask(focusedTask)
        
        print("[FocusViewModel] 添加子步骤成功: \(title) 到小行动: \(checklistItem.title)")
        
        // 刷新任务数据
        loadTasks()
    }
    
    /// 从聚焦的小行动中移除子步骤
    func removeSubStepFromFocusedItem(_ stepId: UUID) {
        guard let focusedTask = getFocusedTask(),
              let checklistItem = getFocusedChecklistItem(),
              let taskChecklist = focusedTask.checklist,
              let itemIndex = taskChecklist.firstIndex(where: { $0.id == checklistItem.id }) else {
            print("[FocusViewModel] 无法找到聚焦的任务或小行动")
            return
        }
        
        print("[FocusViewModel] 开始删除子步骤，stepId: \(stepId)")
        
        // 获取子步骤列表的可变副本
        var steps = focusedTask.checklist![itemIndex].subStepsList
        
        // 递归查找并删除子步骤
        var removedStepTitle = ""
        let success = removeSubStepRecursively(stepId: stepId, from: &steps, removedTitle: &removedStepTitle)
        
        if success {
            print("[FocusViewModel] 成功删除子步骤: \(removedStepTitle)")
            
            // 更新小行动的子步骤列表
            focusedTask.checklist![itemIndex].subStepsList = steps
            
            // 保存到数据库
            taskManager.updateTask(focusedTask)
            
            // 如果删除的是当前聚焦的子步骤，回退到上一级
            if focusedSubStepPath.contains(stepId) {
                DispatchQueue.main.async { [weak self] in
                    if let lastStepId = self?.focusedSubStepPath.last, lastStepId == stepId {
                        self?.focusedSubStepPath.removeLast()
                    }
                }
            }
            
            // 刷新任务数据
            loadTasks()
        } else {
            print("[FocusViewModel] 错误：无法找到要删除的子步骤")
        }
    }
    
    /// 递归查找并删除子步骤
    private func removeSubStepRecursively(stepId: UUID, from steps: inout [SubStep], removedTitle: inout String) -> Bool {
        // 在当前层级查找
        for i in 0..<steps.count {
            if steps[i].id == stepId {
                removedTitle = steps[i].title
                steps.remove(at: i)
                return true
            }
            
            // 递归查找子步骤
            var subSteps = steps[i].subSteps
            if removeSubStepRecursively(stepId: stepId, from: &subSteps, removedTitle: &removedTitle) {
                steps[i].subSteps = subSteps
                return true
            }
        }
        
        return false
    }
    
    /// 聚焦到子步骤
    func focusOnSubStep(_ stepId: UUID) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            // 添加到聚焦路径
            if !self.focusedSubStepPath.contains(stepId) {
                self.focusedSubStepPath.append(stepId)
            }
            
            // 进入 ActionFocus 模式
            self.isInActionFocusMode = true
            
            // 保存当前状态
            self.saveCurrentActionFocusState()
        }
    }
    
    // MARK: - ActionFocus 状态持久化
    
    private let actionFocusStatesKey = "ActionFocusStates"
    
    /// 保存当前ActionFocus状态
    func saveCurrentActionFocusState() {
        guard let taskId = focusedTaskId,
              let itemId = focusedChecklistItemId else { 
            print("[FocusViewModel] 无法保存ActionFocus状态：缺少必要信息")
            return 
        }
        
        var manager = loadActionFocusStateManager()
        manager.saveState(
            taskId: taskId,
            checklistItemId: itemId,
            subStepPath: focusedSubStepPath
        )
        saveActionFocusStateManager(manager)
        
        print("[FocusViewModel] 保存ActionFocus状态 - 任务: \(taskId), 小行动: \(itemId), 路径: \(focusedSubStepPath)")
    }
    
    /// 直接恢复ActionFocus状态（用于外部调用）
    func restoreActionFocusState(taskId: UUID, checklistItemId: UUID, subStepPath: [UUID]) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            self.focusedTaskId = taskId
            self.focusedChecklistItemId = checklistItemId
            self.focusedSubStepPath = subStepPath
            self.isInActionFocusMode = true
            
            // 设置自动展开当前聚焦的小行动
            self.autoExpandStepId = checklistItemId
            
            // 保存聚焦状态
            self.saveFocusState()
            
            // 显示恢复提示
            DependencyContainer.toastManager().showSuperLightInfo("已恢复到上次位置")
            
            print("[FocusViewModel] 直接恢复ActionFocus状态 - 任务: \(taskId), 小行动: \(checklistItemId), 路径: \(subStepPath)")
        }
    }
    
    /// 尝试恢复指定任务的ActionFocus状态
    func tryRestoreActionFocusState(for taskId: UUID) -> Bool {
        let manager = loadActionFocusStateManager()
        
        guard let state = manager.getState(for: taskId) else {
            print("[FocusViewModel] 没有找到任务 \(taskId) 的ActionFocus状态")
            return false
        }
        
        // 验证数据是否仍然有效
        guard let task = taskManager.getTaskById(taskId),
              let checklistItem = task.checklist?.first(where: { $0.id == state.checklistItemId }) else {
            print("[FocusViewModel] ActionFocus状态数据已失效 - 任务或小行动不存在")
            clearActionFocusState(for: taskId) // 清理失效状态
            return false
        }
        
        // 验证子步骤路径是否仍然有效
        if !validateSubStepPath(state.subStepPath, in: checklistItem) {
            print("[FocusViewModel] 子步骤路径已失效")
            clearActionFocusState(for: taskId) // 清理失效状态
            return false
        }
        
        // 恢复状态
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            self.focusedTaskId = taskId
            self.focusedChecklistItemId = state.checklistItemId
            self.focusedSubStepPath = state.subStepPath
            self.isInActionFocusMode = true
            
            // 显示恢复提示
            DependencyContainer.toastManager().showSuperLightInfo("已恢复到上次位置")
        }
        
        print("[FocusViewModel] 成功恢复ActionFocus状态 - 小行动: \(state.checklistItemId), 路径: \(state.subStepPath)")
        return true
    }
    
    /// 清除指定任务的ActionFocus状态
    func clearActionFocusState(for taskId: UUID) {
        var manager = loadActionFocusStateManager()
        manager.removeState(for: taskId)
        saveActionFocusStateManager(manager)
        print("[FocusViewModel] 清除任务 \(taskId) 的ActionFocus状态")
    }
    
    /// 退出ActionFocus模式并保存状态
    func exitActionFocusModeWithSave() {
        // 先保存当前状态
        saveCurrentActionFocusState()
        
        // 然后退出模式，回到一步列表
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            // 退出ActionFocus模式
            self.isInActionFocusMode = false
            self.focusedChecklistItemId = nil
            self.focusedSubStepPath = []
            
            // 同时退出任务聚焦模式，回到一步列表
            self.focusedTaskId = nil
            self.initiallyFocusedSubtaskID = nil
            self.saveFocusState()
            
            // 刷新任务列表
            self.loadTasks()
        }
        
        print("[FocusViewModel] 退出ActionFocus模式并保存状态，回到一步列表")
    }
    
    /// 验证子步骤路径是否仍然有效
    private func validateSubStepPath(_ path: [UUID], in checklistItem: ChecklistItem) -> Bool {
        var currentSteps = checklistItem.subStepsList
        
        for stepId in path {
            guard let step = currentSteps.first(where: { $0.id == stepId }) else {
                return false
            }
            currentSteps = step.subSteps
        }
        
        return true
    }
    
    /// 从UserDefaults加载状态管理器
    private func loadActionFocusStateManager() -> ActionFocusStateManager {
        guard let data = UserDefaults.standard.data(forKey: actionFocusStatesKey),
              let manager = try? JSONDecoder().decode(ActionFocusStateManager.self, from: data) else {
            return ActionFocusStateManager()
        }
        return manager
    }
    
    /// 保存状态管理器到UserDefaults
    private func saveActionFocusStateManager(_ manager: ActionFocusStateManager) {
        guard let data = try? JSONEncoder().encode(manager) else { 
            print("[FocusViewModel] 编码ActionFocusStateManager失败")
            return 
        }
        UserDefaults.standard.set(data, forKey: actionFocusStatesKey)
    }
} 