import Foundation
import SwiftUI
import SwiftData
import Combine
import UIKit

/// 未来也许视图模型
class SMBViewModel: BaseTaskViewModel {
    // MARK: - 依赖注入
    // 不再需要单独声明这些仓库，因为它们已在 BaseTaskViewModel 中
    // let taskRepository: TaskRepository
    // let projectRepository: ProjectRepository
    // let tagRepository: TagRepository
    
    // MARK: - 发布属性
    @Published var smbTasks: [Task] = []
    @Published var searchText: String = ""
    @Published var isSearchFocused: Bool = false
    @Published var showToast: Bool = false
    @Published var toastMessage: String = ""
    
    // 批量操作相关
    @Published var isBatchMode: Bool = false
    @Published var selectedTasks: Set<UUID> = []
    @Published var showingBatchActionSheet: Bool = false
    
    // MARK: - 初始化
    override init(taskManager: TaskManager = DependencyContainer.taskManager()) {
        // 调用父类的初始化方法
        super.init(taskManager: taskManager)
        
        // 加载初始数据
        loadTasks()
        
        // 设置搜索文本变更监听
        $searchText
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                self?.loadTasks()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 公共方法
    
    /// 加载未来也许任务 (重写父类方法)
    override func loadTasks() {
        // 标记为待删除或待完成的任务应该被过滤掉
        let excludedIDs = pendingDeletionTaskIDs.union(pendingCompletionTaskIDs)
        
        let baseQuery = taskManager.getTasksByStatus(TaskStatus.smb.rawValue)
            .filter { !excludedIDs.contains($0.id) }

        if searchText.isEmpty {
            // 获取所有未被标记排除的未来也许任务
            smbTasks = baseQuery
                .sorted { $0.updatedAt > $1.updatedAt }
        } else {
            // 搜索过滤 - 在未被标记排除的任务中过滤
            smbTasks = baseQuery.filter { task in
                task.title.localizedStandardContains(searchText) ||
                task.notes.localizedStandardContains(searchText)
            }.sorted { $0.updatedAt > $1.updatedAt }
        }
    }
    
    /// 显示 Toast 消息
    func showToastMessage(_ message: String) {
        toastMessage = message
        showToast = true
        
        // 3秒后自动隐藏
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) { [weak self] in
            self?.showToast = false
        }
    }
    
    /// 隐藏键盘
    func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
    
    // MARK: - 批量操作方法
    
    /// 进入批量操作模式
    func enterBatchMode() {
        isBatchMode = true
        selectedTasks.removeAll()
    }
    
    /// 退出批量操作模式
    func exitBatchMode() {
        isBatchMode = false
        selectedTasks.removeAll()
    }
    
    /// 切换任务选择状态
    func toggleTaskSelection(_ taskId: UUID) {
        if selectedTasks.contains(taskId) {
            selectedTasks.remove(taskId)
        } else {
            selectedTasks.insert(taskId)
        }
    }
    
    /// 全选/取消全选
    func toggleSelectAll() {
        if selectedTasks.count == smbTasks.count {
            // 如果已全选，则取消全选
            selectedTasks.removeAll()
        } else {
            // 否则全选
            selectedTasks = Set(smbTasks.map { $0.id })
        }
    }
    
    /// 移动选中的任务到收集箱
    func moveSelectedTasksToInbox() {
        for taskId in selectedTasks {
            if let task = taskManager.getTaskById(taskId) {
                task.status = TaskStatus.inbox.rawValue
                taskManager.updateTask(task)
            }
        }
        selectedTasks.removeAll()
        isBatchMode = false
        loadTasks()
        showToastMessage("已移动到收集箱")
    }
    
    /// 移动选中的任务到下一步
    func moveSelectedTasksToNextActions() {
        for taskId in selectedTasks {
            if let task = taskManager.getTaskById(taskId) {
                task.status = TaskStatus.na.rawValue
                taskManager.updateTask(task)
            }
        }
        selectedTasks.removeAll()
        isBatchMode = false
        loadTasks()
        showToastMessage("已移动到下一步")
    }
    
    /// 移动选中的任务到等待中
    func moveSelectedTasksToWaiting() {
        for taskId in selectedTasks {
            if let task = taskManager.getTaskById(taskId) {
                task.status = TaskStatus.waiting.rawValue
                taskManager.updateTask(task)
            }
        }
        selectedTasks.removeAll()
        isBatchMode = false
        loadTasks()
        showToastMessage("已移动到等待中")
    }
    
    /// 删除选中的任务
    func deleteSelectedTasks() {
        for taskId in selectedTasks {
            if let task = taskManager.getTaskById(taskId) {
                taskManager.requestPermanentTaskDeletion(taskID: task.id)
            }
        }
        selectedTasks.removeAll()
        isBatchMode = false
        loadTasks()
        showToastMessage("已删除所选任务")
    }
    
    /// 取消批量操作
    func cancelBatchOperation() {
        selectedTasks.removeAll()
        isBatchMode = false
    }
    
    /// 刷新任务 - 用于下拉刷新
    @MainActor
    func refreshTasks() async {
        loadTasks()
        // 显示轻量级刷新提示
        DependencyContainer.toastManager().showSuperLightInfo("已刷新")
    }
}
