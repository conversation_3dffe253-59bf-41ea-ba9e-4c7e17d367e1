import SwiftUI
import SwiftData
import Combine // 需要引入 Combine

/// 项目详情视图模型，负责管理项目相关的任务和状态
class ProjectDetailViewModel: BaseTaskViewModel { // 修改继承
    // MARK: - 项目和任务属性
    @Published var project: Project
    // 移除 tasks 数组，使用父类的或按状态分类
    // @Published var tasks: [Task] = []
    
    // 任务分类 (保留，视图直接使用)
    @Published var naTasks: [Task] = []
    @Published var waitingTasks: [Task] = []
    @Published var inboxTasks: [Task] = []
    @Published var smbTasks: [Task] = []
    @Published var doneTasks: [Task] = [] // 已完成任务
    
    // 笔记相关
    @Published var notes: [Task] = [] // 项目笔记
    @Published var notesExpanded: Bool = true // 笔记区域是否展开
    
    // 展开状态 (保留)
    @Published var naExpanded: Bool = true
    @Published var waitingExpanded: Bool = true
    @Published var inboxExpanded: Bool = true
    @Published var smbExpanded: Bool = true
    @Published var doneExpanded: Bool = false // 已完成任务默认折叠
    @Published var showCompletedTasks: Bool = true // 是否显示已完成任务
    
    // 分页相关
    private var allCompletedTasks: [Task] = [] // 所有已完成任务
    private let pageSize = 10 // 每页显示数量
    @Published var hasMoreCompletedTasks = false // 是否还有更多已完成任务
    @Published var isLoadingMoreTasks = false // 是否正在加载更多任务
    
    // 界面状态 (保留)
    @Published var showingAddTask: Bool = false
    @Published var showingEchoDrop: Bool = false
    @Published var echoText: String = ""
    @Published var showingEditProject: Bool = false
    
    // 计算属性
    var hasNoTasks: Bool {
        naTasks.isEmpty && waitingTasks.isEmpty && inboxTasks.isEmpty && smbTasks.isEmpty && doneTasks.isEmpty
    }
    
    var hasNoNotes: Bool {
        notes.isEmpty
    }
    
    // 仓库实例（从父类继承）
    // private(set) var taskRepository: TaskRepository
    // private(set) var projectRepository: ProjectRepository
    // private(set) var tagRepository: TagRepository
    
    // MARK: - 初始化
    init(
        project: Project,
        taskManager: TaskManager = DependencyContainer.taskManager()
    ) {
        self.project = project
        // 调用 super.init
        super.init(taskManager: taskManager)
        
        // 初始加载任务 (调用重写后的方法)
        loadTasks()
    }
    
    // MARK: - 任务管理 (重写)
    override func loadTasks() {
        // 获取待排除的任务ID集合
        let excludedIDs = pendingDeletionTaskIDs.union(pendingCompletionTaskIDs)
        
        // 加载普通任务并过滤掉待删除/完成的任务
        naTasks = taskManager.getTasksForProject(project.id, status: TaskStatus.na.rawValue)
            .filter { !$0.isNote && !excludedIDs.contains($0.id) }
        
        waitingTasks = taskManager.getTasksForProject(project.id, status: TaskStatus.waiting.rawValue)
            .filter { !$0.isNote && !excludedIDs.contains($0.id) }
        
        inboxTasks = taskManager.getTasksForProject(project.id, status: TaskStatus.inbox.rawValue)
            .filter { !$0.isNote && !excludedIDs.contains($0.id) }
        
        smbTasks = taskManager.getTasksForProject(project.id, status: TaskStatus.smb.rawValue)
            .filter { !$0.isNote && !excludedIDs.contains($0.id) }
        
        // 加载笔记
        loadNotes()
        
        // 加载完成任务（使用分页）并过滤掉待删除的任务
        allCompletedTasks = taskManager.getTasksForProject(project.id, status: TaskStatus.done.rawValue)
            .filter { !$0.isNote && !excludedIDs.contains($0.id) }
        
        loadMoreCompletedTasks()
        
        print("[ProjectDetailVM] Loaded \(taskManager.getTasksForProject(project.id).count) tasks for project '\(project.name)' (Excluded: \(excludedIDs.count))")
    }
    
    /// 加载项目笔记
    func loadNotes() {
        notes = taskManager.getNotesForProject(project.id)
    }
    
    /// 判断任务是否可恢复（7天内完成的任务）
    func isTaskRecoverable(_ task: Task) -> Bool {
        guard let completedAt = task.completedAt else { return false }
        let calendar = Calendar.current
        let now = Date()
        let components = calendar.dateComponents([.day], from: completedAt, to: now)
        return (components.day ?? 0) <= 7
    }
    
    /// 加载更多已完成任务
    func loadMoreCompletedTasks() {
        guard !isLoadingMoreTasks else { return }
        isLoadingMoreTasks = true
        
        let currentCount = doneTasks.count
        let endIndex = min(currentCount + pageSize, allCompletedTasks.count)
        if currentCount < endIndex {
            let nextBatch = Array(allCompletedTasks[currentCount..<endIndex])
            doneTasks.append(contentsOf: nextBatch)
        }
        
        hasMoreCompletedTasks = doneTasks.count < allCompletedTasks.count
        isLoadingMoreTasks = false
    }
    
    /// 恢复已完成任务到下一步
    func recoverTask(_ task: Task) {
        guard task.status == TaskStatus.done.rawValue else { return }
        
        // 保存原始状态用于事件发布
        let originalStatus = task.status
        
        // 更新任务状态为下一步
        task.status = TaskStatus.na.rawValue
        task.completedAt = nil
        taskManager.updateTask(task)
        
        // 发布状态变更事件
        EventBus.shared.publish(.taskStatusChanged(task, oldStatus: originalStatus, newStatus: TaskStatus.na.rawValue))
        
        // 刷新任务列表
        loadTasks()
    }
    
    // 重写 handleTaskIntent 处理来自 TaskSectionView 的事件
    override func handleTaskIntent(_ intent: TaskIntent) {
        switch intent {
        case .delete(let task):
            // 直接调用父类方法处理标记、刷新和 Toast
            super.handleTaskIntent(intent)
            
        case .complete(let task):
            // 保存原始状态
            let originalStatus = task.status
            
            // 直接调用父类方法处理标记、刷新和 Toast
            super.handleTaskIntent(intent)
            
            // 手动将完成的任务添加到已完成列表
            if task.status == TaskStatus.done.rawValue {
                // 移除任务从原来的列表中
                if let index = naTasks.firstIndex(where: { $0.id == task.id }) {
                    naTasks.remove(at: index)
                } else if let index = waitingTasks.firstIndex(where: { $0.id == task.id }) {
                    waitingTasks.remove(at: index)
                } else if let index = inboxTasks.firstIndex(where: { $0.id == task.id }) {
                    inboxTasks.remove(at: index)
                } else if let index = smbTasks.firstIndex(where: { $0.id == task.id }) {
                    smbTasks.remove(at: index)
                }
                
                // 将任务添加到已完成列表的最前面
                allCompletedTasks.insert(task, at: 0)
                if doneExpanded || doneTasks.count < pageSize {
                    doneTasks.insert(task, at: 0)
                }
            }
            
        case .changeStatus(let task, let newStatus):
            // 直接调用父类方法处理状态变更
            super.handleTaskIntent(intent)
        }
    }
    
    /// 重写更新任务列表的方法，以实现任务状态变更时的立即更新
    override func updateTaskInLists(task: Task, oldStatus: String, newStatus: String) {
        // 根据旧状态从原列表中移除
        switch oldStatus {
        case TaskStatus.na.rawValue:
            if let index = naTasks.firstIndex(where: { $0.id == task.id }) {
                naTasks.remove(at: index)
            }
        case TaskStatus.waiting.rawValue:
            if let index = waitingTasks.firstIndex(where: { $0.id == task.id }) {
                waitingTasks.remove(at: index)
            }
        case TaskStatus.inbox.rawValue:
            if let index = inboxTasks.firstIndex(where: { $0.id == task.id }) {
                inboxTasks.remove(at: index)
            }
        case TaskStatus.smb.rawValue:
            if let index = smbTasks.firstIndex(where: { $0.id == task.id }) {
                smbTasks.remove(at: index)
            }
        case TaskStatus.done.rawValue:
            if let index = doneTasks.firstIndex(where: { $0.id == task.id }) {
                doneTasks.remove(at: index)
            }
            if let index = allCompletedTasks.firstIndex(where: { $0.id == task.id }) {
                allCompletedTasks.remove(at: index)
            }
        default:
            break
        }
        
        // 根据新状态添加到对应列表
        switch newStatus {
        case TaskStatus.na.rawValue:
            naTasks.insert(task, at: 0)
        case TaskStatus.waiting.rawValue:
            waitingTasks.insert(task, at: 0)
        case TaskStatus.inbox.rawValue:
            inboxTasks.insert(task, at: 0)
        case TaskStatus.smb.rawValue:
            smbTasks.insert(task, at: 0)
        case TaskStatus.done.rawValue:
            allCompletedTasks.insert(task, at: 0)
            if doneExpanded || doneTasks.count < pageSize {
                doneTasks.insert(task, at: 0)
            }
        default:
            break
        }
    }
    
    // 移除 handleTaskStatusChange，由父类 handleTaskIntent 处理
    // func handleTaskStatusChange(task: Task, newStatus: String) { ... }
    
    // MARK: - 项目操作 (保留)
    func updateProject(_ project: Project) {
        taskManager.updateProject(project)
    }
    
    func toggleArchiveStatus() {
        project.isArchived.toggle()
        taskManager.updateProject(project)
    }
    
    func deleteProject(dismissAction: DismissAction) {
        // 删除项目及其下所有任务
        taskManager.deleteProject(project)
        dismissAction()
    }
    
    // MARK: - 回声Drop相关 (保留)
    func showEchoDrop() {
        showingEchoDrop = true
    }
    
    func hideEchoDrop() {
        showingEchoDrop = false
    }
    
    // MARK: - 项目编辑相关 (保留)
    func editProject() {
        showingEditProject = true
    }
    
    func dismissEditProject() {
        showingEditProject = false
    }
    
    // MARK: - 绑定 (保留)
    var echoDropBinding: Binding<Bool> {
        Binding(
            get: { self.showingEchoDrop },
            set: { self.showingEchoDrop = $0 }
        )
    }
    
    var echoDropTextBinding: Binding<String> {
        Binding(
            get: { self.echoText },
            set: { self.echoText = $0 }
        )
    }
    
    var editProjectBinding: Binding<Bool> {
        Binding(
            get: { self.showingEditProject },
            set: { self.showingEditProject = $0 }
        )
    }
    
    // MARK: - NextActionsViewModel 创建 (保留)
    func createNextActionsViewModel() -> NextActionsViewModel {
        NextActionsViewModel(
            taskManager: taskManager // 使用父类的 taskManager
        )
    }
    
    /// 刷新任务 - 用于下拉刷新
    @MainActor
    func refreshTasks() async {
        loadTasks()
        // 显示轻量级刷新提示
        DependencyContainer.toastManager().showSuperLightInfo("已刷新")
    }
    
    // MARK: - 笔记相关方法
    
    /// 添加笔记
    func addNote(title: String, content: String, tags: [String] = [], isFocused: Bool = false) -> Task {
        let note = taskManager.addNote(
            title: title,
            content: content,
            project: project.id,
            tags: tags,
            isFocused: isFocused
        )
        
        // 刷新笔记列表
        loadNotes()
        
        return note
    }
    
    /// 更新笔记标题
    func updateNoteTitle(_ note: Task, newTitle: String) {
        taskManager.updateNoteTitle(note, newTitle: newTitle)
        loadNotes()
    }
    
    /// 更新笔记内容
    func updateNoteContent(_ note: Task, newContent: String) {
        taskManager.updateNoteContent(note, newContent: newContent)
        loadNotes()
    }
    
    /// 更新笔记重点关注状态
    func toggleNoteFocus(_ note: Task) {
        taskManager.updateNoteFocusStatus(note, isFocused: !note.isFocused)
        loadNotes()
    }
    
    /// 删除笔记
    func deleteNote(_ note: Task) {
        taskManager.requestPermanentTaskDeletion(taskID: note.id)
        loadNotes()
    }
    
    /// 将笔记转换为任务
    func convertNoteToTask(_ note: Task) -> Task? {
        let task = taskManager.convertNoteToTask(note)
        
        // 刷新任务和笔记列表
        loadTasks()
        
        return task
    }
} 
