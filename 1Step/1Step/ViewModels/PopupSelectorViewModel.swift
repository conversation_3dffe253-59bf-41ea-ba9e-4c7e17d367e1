import Foundation
import SwiftData
import SwiftUI

/// 弹出选择器视图模型
class PopupSelectorViewModel: ObservableObject {
    // MARK: - 依赖
    
    private let taskManager: TaskManager
    
    // MARK: - 发布属性
    
    @Published var projects: [Project] = []
    @Published var tagNames: [String] = []
    
    // MARK: - 初始化方法
    
    init(taskManager: TaskManager = DependencyContainer.taskManager()) {
        self.taskManager = taskManager
    }
    
    // MARK: - 公共方法
    
    /// 加载项目或标签
    func loadItems(type: PopupSelectorView.SelectorType) {
        if type == .project {
            loadProjects()
        } else {
            loadTags()
        }
    }
    
    /// 加载项目
    private func loadProjects() {
        projects = taskManager.getAllProjects().filter { !$0.isArchived }
    }
    
    /// 加载标签
    private func loadTags() {
        let tags = taskManager.getAllTags()
        tagNames = tags.map { $0.name }
    }
    
    /// 创建新项目
    func createProject(name: String) -> Project? {
        let projectWithoutSpaces = name.trimmingCharacters(in: .whitespacesAndNewlines).replacingOccurrences(of: " ", with: "")
        
        guard !projectWithoutSpaces.isEmpty else { return nil }
        
        // 如果项目已存在，返回已存在的项目
        if let existingProject = taskManager.getProjectByName(projectWithoutSpaces) {
            return existingProject
        }
        
        // 创建新项目
        if let newProject = taskManager.createProject(name: projectWithoutSpaces, color: generateRandomColor()) {
            // 更新项目列表
            loadProjects()
            return newProject
        }
        
        return nil
    }
    
    /// 创建新标签
    func createTag(name: String) -> String? {
        let tagWithoutSpaces = name.trimmingCharacters(in: .whitespacesAndNewlines).replacingOccurrences(of: " ", with: "")
        
        guard !tagWithoutSpaces.isEmpty else { return nil }
        
        // 如果标签已存在，返回已存在的标签名
        if taskManager.getTagByName(tagWithoutSpaces) != nil {
            return tagWithoutSpaces
        }
        
        // 创建新标签
        if let _ = taskManager.createTag(name: tagWithoutSpaces, color: generateRandomColor()) {
            // 更新标签列表
            loadTags()
            return tagWithoutSpaces
        }
        
        return nil
    }
    
    /// 获取项目或标签的颜色
    func getItemColor(item: String, type: PopupSelectorView.SelectorType, colorScheme: ColorScheme) -> Color {
        if type == .project {
            // 获取项目颜色
            if let project = projects.first(where: { $0.name == item }) {
                return AppColors.UI.projectColor(for: colorScheme, project: project)
            }
        } else {
            // 获取标签颜色
            if let tag = taskManager.getTagByName(item) {
                return AppColors.UI.tagColor(for: colorScheme, tag: tag)
            }
        }
        return AppColors.UI.primaryText(for: colorScheme)
    }
    
    /// 生成随机颜色
    private func generateRandomColor() -> String {
        let colors = ["6366F1", "8B5CF6", "EC4899", "EF4444", "F59E0B", "10B981", "06B6D4", "3B82F6"]
        return colors.randomElement() ?? "6366F1"
    }
}
