import Foundation
import SwiftUI
import SwiftData
import Combine
import UIKit

/// 等待中视图模型
class WaitingViewModel: BaseTaskViewModel {
    // MARK: - 依赖注入
    // 使用父类的仓库
    // let taskRepository: TaskRepository
    // let projectRepository: ProjectRepository
    // let tagRepository: TagRepository
    
    // MARK: - 发布属性
    @Published var tasks: [Task] = [] // 保留用于存储当前视图的任务
    @Published var searchText: String = ""
    @Published var isSearchFocused: Bool = false
    @Published var showToast: Bool = false
    @Published var toastMessage: String = ""
    
    // MARK: - 私有属性
    // 使用父类的 cancellables
    // private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    override init(taskManager: TaskManager = DependencyContainer.taskManager()) {
        // 调用 super.init
        super.init(taskManager: taskManager)
        
        // 加载初始数据
        loadTasks()
        
        // 设置搜索文本变更监听
        $searchText
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                self?.loadTasks()
            }
            .store(in: &cancellables) // 使用父类的 cancellables
    }
    
    var filteredTasks: [Task] {
        if searchText.isEmpty {
            return tasks
        } else {
            return tasks.filter { task in
                task.title.localizedCaseInsensitiveContains(searchText)
            }
        }
    }
    
    // MARK: - 公共方法
    
    /// 加载等待中任务 (重写父类方法)
    override func loadTasks() {
        let excludedIDs = pendingDeletionTaskIDs.union(pendingCompletionTaskIDs)
        let baseQuery = taskManager.getTasksByStatus(TaskStatus.waiting.rawValue)
                          .filter { !excludedIDs.contains($0.id) }

        if searchText.isEmpty {
            tasks = baseQuery
                .sorted { $0.updatedAt > $1.updatedAt }
        } else {
            tasks = baseQuery.filter { task in
                task.title.localizedStandardContains(searchText) ||
                task.notes.localizedStandardContains(searchText)
            }.sorted { $0.updatedAt > $1.updatedAt }
        }
    }
    
    /// 显示 Toast 消息
    func showToastMessage(_ message: String) {
        toastMessage = message
        showToast = true
        
        // 3秒后自动隐藏
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) { [weak self] in
            self?.showToast = false
        }
    }
    
    /// 隐藏键盘
    func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
    
    /// 刷新任务 - 用于下拉刷新
    @MainActor
    func refreshTasks() async {
        loadTasks()
        // 显示轻量级刷新提示
        DependencyContainer.toastManager().showSuperLightInfo("已刷新")
    }
}   
