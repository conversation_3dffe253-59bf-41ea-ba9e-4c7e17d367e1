import Foundation
import SwiftUI
import Combine

/// 基础任务视图模型，封装任务操作的共享逻辑
@MainActor
class BaseTaskViewModel: ObservableObject {
    // MARK: - 服务依赖
    /// 任务管理器
    let taskManager: TaskManager
    
    // MARK: - 任务状态跟踪
    /// 待删除任务ID集合
    @Published var pendingDeletionTaskIDs: Set<UUID> = []
    /// 待完成任务ID集合
    @Published var pendingCompletionTaskIDs: Set<UUID> = []
    
    // MARK: - 事件系统集成
    var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    init(taskManager: TaskManager = DependencyContainer.taskManager()) {
        self.taskManager = taskManager
        
        // 设置事件订阅
        setupEventSubscriptions()
    }
    
    // MARK: - 事件系统集成
    
    /// 设置事件订阅
    func setupEventSubscriptions() {
        EventBus.shared.events
            .filter { [weak self] event in
                self?.shouldHandleEvent(event) ?? false
            }
            .sink { [weak self] event in
                self?.handleEvent(event)
            }
            .store(in: &cancellables)
    }
    
    /// 判断是否需要处理事件
    func shouldHandleEvent(_ event: AppEvent) -> Bool {
        switch event {
        case .taskDeleted, .taskCompleted, .taskStatusChanged:
            return true // 任务操作事件需要刷新UI
        default:
            return false
        }
    }
    
    /// 处理事件
    func handleEvent(_ event: AppEvent) {
        // 所有被监听的事件都需要刷新任务列表
        loadTasks()
    }
    
    // MARK: - 任务操作
    
    /// 加载任务列表（子类必须重写）
    func loadTasks() {
        fatalError("子类必须重写此方法")
    }
    
    /**
     更新任务状态时立即更新视图模型中的任务列表
     
     子类应重写此方法，实现任务状态变更时的列表立即更新。该方法用于在任务状态变更后立即从旧列表移除任务并添加到新列表，避免使用延迟刷新。
     
     示例实现:
     ```
     override func updateTaskInLists(task: Task, oldStatus: String, newStatus: String) {
         // 从原状态列表中移除
         switch oldStatus {
         case TaskStatus.na.rawValue:
             if let index = naTasks.firstIndex(where: { $0.id == task.id }) {
                 naTasks.remove(at: index)
             }
         // ... 其他状态
         }
         
         // 添加到新状态列表
         switch newStatus {
         case TaskStatus.na.rawValue:
             naTasks.insert(task, at: 0)
         // ... 其他状态
         }
     }
     ```
     
     - Parameters:
        - task: 发生状态变更的任务
        - oldStatus: 任务的原始状态
        - newStatus: 任务的新状态
     */
    func updateTaskInLists(task: Task, oldStatus: String, newStatus: String) {
        // 基类中提供空实现，让子类重写
        // 不需要在这里调用 loadTasks()，因为这只是提供子类重写的默认实现
    }
    
    /// 根据项目ID获取项目名称
    func getProjectName(for projectId: UUID?) -> String {
        guard let projectId = projectId else { return "无项目" }
        
        if let project = taskManager.getProjectById(projectId) {
            return project.name
        } else {
            return "无项目"
        }
    }
    
    /// 处理任务删除
    func handleTaskDelete(_ task: Task) {

        // 立即发布可撤销的删除事件，并提供回调以跟踪最终状态
        EventBus.shared.publish(.taskDeleted(task, allowUndo: true)) { [weak self] wasUndone in
            guard let self = self else { return }
            if !wasUndone {
                // 这个回调现在由 EventBus 在确认超时且未撤销后调用
                // ViewModel 可以在这里处理删除最终发生后的逻辑（如果需要）
                // 注意：实际的数据库删除由 TaskManager 处理，ViewModel 通常不需要在这里做额外的数据操作
            } else {
                // 如果 wasUndone 为 true，表示操作被撤销了
            }
        }
        
        // 标记为待删除并立即刷新UI（视觉效果）
        pendingDeletionTaskIDs.insert(task.id)
        // 先刷新UI，确保视觉效果立即生效
        loadTasks()
        
        // 显示删除Toast，提供撤销选项，移除 onDismiss 中的事件发布逻辑
        DependencyContainer.toastManager().showTaskDeleted("\"\(task.title)\" 已删除", 
            action: ToastAction(title: "撤销", action: {
                // 撤销删除
                self.pendingDeletionTaskIDs.remove(task.id)
                // 通知EventBus撤销删除
                EventBus.shared.undoTaskDeletion(task.id)
                // 刷新UI显示任务
                self.loadTasks()
                // 显示撤销成功提示（如果需要，可以取消注释）
                //DependencyContainer.toastManager().showUndoDeletionSuccess("已撤销删除")
            }),
            onDismiss: nil // 移除onDismiss的回调，不再由此处发布事件
        )
    }
    
    /// 处理任务完成
    func handleTaskComplete(_ task: Task) {
        
        // 保存原始状态用于撤销
        let originalStatus = task.status
        
        // 标记为待完成并立即刷新UI
        pendingCompletionTaskIDs.insert(task.id)
        // 先刷新UI，确保视觉效果立即生效
        loadTasks()
        
        // 显示完成Toast，提供撤销选项
        DependencyContainer.toastManager().showTaskCompleted("\"\(task.title)\" 已完成", 
            action: ToastAction(title: "撤销", action: {
                // 撤销完成
                self.pendingCompletionTaskIDs.remove(task.id)
                
                // 如果任务已被完成，恢复其状态（简单检查，不引入大量改变）
                if let existingTask = self.taskManager.getTaskById(task.id),
                   existingTask.status == TaskStatus.done.rawValue {
                    existingTask.status = originalStatus
                    existingTask.completedAt = nil
                    self.taskManager.updateTask(existingTask)
                }
                
                // 通知EventBus撤销完成
                EventBus.shared.undoTaskCompletion(task.id)
                // 刷新UI显示任务
                self.loadTasks()
                // 显示撤销成功提示
                //DependencyContainer.toastManager().showUndoDeletionSuccess("已撤销完成")
            }),
            onDismiss: {
                // Toast消失且未撤销，发布完成事件
                EventBus.shared.publish(.taskCompleted(task, allowUndo: true)) { [weak self] wasUndone in
                    guard let self = self else { return }
                    
                    if !wasUndone {
                        // 未被撤销，可以考虑执行最终完成（已由EventBus处理）
                    }
                }
            }
        )
    }
    
    /// 处理任务状态变更
    func handleTaskStatusChange(_ task: Task, newStatus: String) {
        
        // 保存原始状态用于后续处理
        let oldStatus = task.status
        
        // 更新任务状态
        task.status = newStatus
        taskManager.updateTask(task)
        
        // 立即更新视图模型中的任务列表
        updateTaskInLists(task: task, oldStatus: oldStatus, newStatus: newStatus)
        
        // 直接发布状态变更事件
        EventBus.shared.publish(.taskStatusChanged(task, oldStatus: oldStatus, newStatus: newStatus))
        
        // 根据新状态显示适当的Toast
        let statusDescription: String
        switch newStatus {
        case TaskStatus.na.rawValue:
            statusDescription = "下一步"
        case TaskStatus.waiting.rawValue:
            statusDescription = "等待中"
        case TaskStatus.inbox.rawValue:
            statusDescription = "收集箱"
        case TaskStatus.doing.rawValue:
            statusDescription = "一步"
        case TaskStatus.smb.rawValue:
            statusDescription = "未来也许"
        default:
            statusDescription = newStatus
        }
        
        DependencyContainer.toastManager().showSuperLightInfo("已移动到\(statusDescription)")
    }
    
    /// 处理任务意图
    func handleTaskIntent(_ intent: TaskIntent) {
        switch intent {
        case .delete(let task):
            handleTaskDelete(task)
            
        case .complete(let task):
            handleTaskComplete(task)
            
        case .changeStatus(let task, let newStatus):
            handleTaskStatusChange(task, newStatus: newStatus)
        }
    }
    
    // MARK: - 搜索相关
    
    /// 在搜索历史中保存搜索词
    func addSearchHistory(_ term: String) {
        saveSearchTerm(term)
    }
    
    /// 在搜索历史中保存搜索词（底层实现）
    func saveSearchTerm(_ term: String) {
        // 子类可根据需要重写此方法
    }
    
    /// 清空搜索历史
    func clearSearchHistory() {
        // 子类可根据需要重写此方法
    }
} 