import Foundation
import SwiftUI
import SwiftData

// MARK: - 辅助类型

/// 日期分组
enum DateGroup: Int, CaseIterable, Identifiable, Comparable {
    case today = 0
    case yesterday = 1
    case thisWeek = 2
    case lastWeek = 3
    case earlier = 4
    
    var id: Int { self.rawValue }
    
    var title: String {
        switch self {
        case .today: return "今天"
        case .yesterday: return "昨天"
        case .thisWeek: return "本周"
        case .lastWeek: return "上周"
        case .earlier: return "更早"
        }
    }
    
    static func < (lhs: DateGroup, rhs: DateGroup) -> Bool {
        return lhs.rawValue < rhs.rawValue
    }
}

/// 已完成任务视图模型
class CompletedTasksViewModel: BaseTaskViewModel {
    // MARK: - 发布属性
    
    /// 分组任务
    @Published var groupedTasks: [DateGroup: [Task]] = [:]
    
    /// 是否正在加载
    @Published var isLoading: Bool = false
    
    /// 是否可以加载更多
    @Published var canLoadMore: Bool = false
    
    /// 搜索文本
    @Published var searchText: String = ""
    
    /// 筛选项目
    @Published var filterProject: Project? = nil
    
    /// 筛选标签
    @Published var filterTags: [String] = []
    
    /// 筛选时间范围
    @Published var filterTimeRange: TaskTimeRange = .all
    
    // MARK: - 私有属性
    
    /// 当前页码
    private var currentPage: Int = 1
    
    /// 每页数量
    private let pageSize: Int = 20
    
    // MARK: - 初始化方法
    
    override init(taskManager: TaskManager = DependencyContainer.taskManager()) {
        super.init(taskManager: taskManager)
    }
    
    // MARK: - 公共方法
    
    /// 加载初始任务
    func loadInitialTasks() {
        currentPage = 1
        isLoading = true
        
        // 执行异步加载
        DispatchQueue.global().async { [weak self] in
            guard let self = self else { return }
            
            // 获取筛选后的任务
            let filteredTasks = self.getFilteredTasks(limit: self.pageSize, offset: 0)
            
            // 更新UI
            DispatchQueue.main.async {
                // 分组任务
                self.groupedTasks = self.groupTasksByTime(filteredTasks)
                
                // 更新状态
                self.canLoadMore = filteredTasks.count >= self.pageSize
                self.isLoading = false
            }
        }
    }
    
    // 由于 loadTasks 是 BaseTaskViewModel 的要求，我们需要实现它
    override func loadTasks() {
        loadInitialTasks()
    }
    
    /// 加载更多任务
    func loadMoreTasks() {
        if isLoading || !canLoadMore {
            return
        }
        
        isLoading = true
        currentPage += 1
        
        // 计算偏移量
        let offset = (currentPage - 1) * pageSize
        
        // 执行异步加载
        DispatchQueue.global().async { [weak self] in
            guard let self = self else { return }
            
            // 获取更多筛选后的任务
            let filteredTasks = self.getFilteredTasks(limit: self.pageSize, offset: offset)
            
            // 更新UI
            DispatchQueue.main.async {
                // 将新任务添加到现有分组中
                let newGroupedTasks = self.groupTasksByTime(filteredTasks)
                
                // 合并分组
                for (group, tasks) in newGroupedTasks {
                    if var existingTasks = self.groupedTasks[group] {
                        existingTasks.append(contentsOf: tasks)
                        self.groupedTasks[group] = existingTasks
                    } else {
                        self.groupedTasks[group] = tasks
                    }
                }
                
                // 更新状态
                self.canLoadMore = filteredTasks.count >= self.pageSize
                self.isLoading = false
            }
        }
    }
    
    /// 应用筛选器
    func applyFilters(project: Project?, tags: [String], timeRange: TaskTimeRange) {
        filterProject = project
        filterTags = tags
        filterTimeRange = timeRange
        
        // 重新加载任务
        loadInitialTasks()
    }
    
    /// 清除所有筛选条件
    func applyFilter() {
        // 重置所有筛选条件
        filterProject = nil
        filterTags = []
        filterTimeRange = .all
        
        // 重新加载任务
        loadInitialTasks()
    }
    
    /// 应用搜索
    func applySearch(text: String) {
        searchText = text
        
        // 重新加载任务
        loadInitialTasks()
    }
    
    /// 检查任务是否可恢复（仅完成时间在一小时内的任务可恢复）
    func isTaskRecoverable(task: Task) -> Bool {
        // 获取任务的完成时间
        guard let completedAt = task.completedAt else {
            return false
        }
        
        // 检查完成时间是否在一小时内
        let oneHourAgo = Date().addingTimeInterval(-3600) // 3600秒 = 1小时
        return completedAt > oneHourAgo
    }
    
    /// 恢复任务
    func restoreTask(_ task: Task) {
        // 检查任务是否可恢复
        if !isTaskRecoverable(task: task) {
            DependencyContainer.toastManager().showWarning("只能恢复一小时内完成的行动")
            return
        }
        
        // 更新任务状态
        task.status = TaskStatus.na.rawValue
        
        // 保存更改
        taskManager.updateTask(task)
        
        // 从分组中移除任务
        for (group, tasks) in groupedTasks {
            if let index = tasks.firstIndex(where: { $0.id == task.id }) {
                var updatedTasks = tasks
                updatedTasks.remove(at: index)
                groupedTasks[group] = updatedTasks
            }
        }
        
        // 清理空分组
        groupedTasks = groupedTasks.filter { !$0.value.isEmpty }
        
        // 发送通知以更新其他视图
        NotificationCenter.default.post(name: NSNotification.Name("TaskStatusChanged"), object: nil)
    }
    
    /// 删除任务
    func deleteTask(_ task: Task) {
        // 1. 乐观更新UI (从UI数据源 groupedTasks 中移除任务)
        let taskIDToDelete = task.id
        for (group, tasks) in groupedTasks {
            if let index = tasks.firstIndex(where: { $0.id == taskIDToDelete }) {
                var updatedTasks = tasks
                updatedTasks.remove(at: index)
                groupedTasks[group] = updatedTasks
            }
        }
        groupedTasks = groupedTasks.filter { !$0.value.isEmpty }

        // 2. 使用父类的删除机制
        handleTaskIntent(.delete(task))
    }
    
    // MARK: - 刷新功能
    
    /// 异步刷新任务
    @MainActor
    func refreshTasks() async {
        // 重新加载初始任务
        currentPage = 1
        isLoading = true
        
        // 获取筛选后的任务
        let filteredTasks = self.getFilteredTasks(limit: self.pageSize, offset: 0)
        
        // 更新UI
        self.groupedTasks = self.groupTasksByTime(filteredTasks)
        
        // 更新状态
        self.canLoadMore = filteredTasks.count >= self.pageSize
        self.isLoading = false
        
        // 显示轻量级刷新提示
        DependencyContainer.toastManager().showSuperLightInfo("已刷新")
    }
    
    // MARK: - 私有方法
    
    /// 获取筛选后的任务并应用分页
    private func getFilteredTasks(limit: Int, offset: Int) -> [Task] {
        // 获取所有已完成任务
        let allCompletedTasks = taskManager.getTasksByStatus(TaskStatus.done.rawValue)
        
        // 应用所有筛选条件
        let filteredTasks = allCompletedTasks.filter { task in
            // 项目筛选
            if let project = filterProject, task.project != project.id {
                return false
            }
            
            // 标签筛选
            if !filterTags.isEmpty {
                let taskTags = task.tags ?? []
                let hasAllTags = filterTags.allSatisfy { tag in
                    taskTags.contains(tag)
                }
                if !hasAllTags {
                    return false
                }
            }
            
            // 时间范围筛选
            if filterTimeRange != .all {
                let calendar = Calendar.current
                let now = Date()
                let today = calendar.startOfDay(for: now)
                let taskDate = task.completedAt ?? task.createdAt
                let taskDay = calendar.startOfDay(for: taskDate)
                
                switch filterTimeRange {
                case .today:
                    if !calendar.isDate(taskDay, inSameDayAs: today) {
                        return false
                    }
                    
                case .yesterday:
                    let yesterday = calendar.date(byAdding: .day, value: -1, to: today)!
                    if !calendar.isDate(taskDay, inSameDayAs: yesterday) {
                        return false
                    }
                    
                case .thisWeek:
                    let weekStart = calendar.date(from: calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: now))!
                    if taskDay < weekStart || calendar.isDate(taskDay, inSameDayAs: today) {
                        return false
                    }
                    
                case .lastWeek:
                    let thisWeekStart = calendar.date(from: calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: now))!
                    let lastWeekStart = calendar.date(byAdding: .weekOfYear, value: -1, to: thisWeekStart)!
                    if taskDay < lastWeekStart || taskDay >= thisWeekStart {
                        return false
                    }
                    
                case .thisMonth:
                    let components = calendar.dateComponents([.year, .month], from: now)
                    let monthStart = calendar.date(from: components)!
                    if taskDay < monthStart || calendar.isDate(taskDay, inSameDayAs: today) {
                        return false
                    }
                    
                case .earlier:
                    // 仅显示比当前条件更早的任务
                    let earlierThreshold = calendar.date(byAdding: .month, value: -1, to: today)!
                    if taskDay > earlierThreshold {
                        return false
                    }
                    
                case .all:
                    break // 不需要筛选
                }
            }
            
            // 搜索文本筛选
            if !searchText.isEmpty {
                if !task.title.localizedStandardContains(searchText) && 
                   !task.notes.localizedStandardContains(searchText) {
                    return false
                }
            }
            
            return true
        }
        
        // 按完成时间排序（降序）
        let sortedTasks = filteredTasks.sorted { task1, task2 in
            let date1 = task1.completedAt ?? task1.createdAt
            let date2 = task2.completedAt ?? task2.createdAt
            return date1 > date2
        }
        
        // 应用分页
        let startIndex = offset
        let endIndex = min(startIndex + limit, sortedTasks.count)
        
        if startIndex < sortedTasks.count {
            return Array(sortedTasks[startIndex..<endIndex])
        } else {
            return []
        }
    }
    
    /// 按时间分组任务
    private func groupTasksByTime(_ tasks: [Task]) -> [DateGroup: [Task]] {
        var groupedTasks: [DateGroup: [Task]] = [:]
        
        // 初始化所有分组
        for group in DateGroup.allCases {
            groupedTasks[group] = []
        }
        
        // 获取当前日期的开始时间
        let calendar = Calendar.current
        let now = Date()
        let today = calendar.startOfDay(for: now)
        let yesterday = calendar.date(byAdding: .day, value: -1, to: today)!
        let weekStart = calendar.date(from: calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: now))!
        let lastWeekStart = calendar.date(byAdding: .weekOfYear, value: -1, to: weekStart)!
        
        // 对任务进行分组
        for task in tasks {
            let completedDate = task.completedAt ?? task.createdAt
            let taskDay = calendar.startOfDay(for: completedDate)
            
            if calendar.isDate(taskDay, inSameDayAs: today) {
                groupedTasks[.today]?.append(task)
            } else if calendar.isDate(taskDay, inSameDayAs: yesterday) {
                groupedTasks[.yesterday]?.append(task)
            } else if taskDay >= weekStart {
                groupedTasks[.thisWeek]?.append(task)
            } else if taskDay >= lastWeekStart && taskDay < weekStart {
                groupedTasks[.lastWeek]?.append(task)
            } else {
                groupedTasks[.earlier]?.append(task)
            }
        }
        
        // 过滤掉空分组
        return groupedTasks.filter { !$0.value.isEmpty }
    }
}
