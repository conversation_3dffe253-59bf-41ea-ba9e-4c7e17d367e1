import Foundation
import SwiftUI
import SwiftData

/// 过滤任务列表的视图模型
class FilteredTaskListViewModel: BaseTaskViewModel {
    // MARK: - 发布属性
    @Published var tasks: [Task] = []
    @Published var searchText: String = ""
    
    // MARK: - 私有属性
    private let status: TaskStatus
    private let sortOrder: SortOrder
    
    // MARK: - 初始化方法
    init(
        status: TaskStatus,
        sortOrder: SortOrder,
        taskManager: TaskManager = DependencyContainer.taskManager()
    ) {
        self.status = status
        self.sortOrder = sortOrder
        
        // 这里不需要传递 taskManager 参数，让父类使用默认值
        super.init(taskManager: taskManager)
        
        // 加载初始数据
        loadTasks()
    }
    
    // MARK: - 公共方法
    
    /// 加载任务 (重写父类方法)
    override func loadTasks() {
        // 获取待排除的任务ID集合
        let excludedIDs = pendingDeletionTaskIDs.union(pendingCompletionTaskIDs)
        
        // 获取任务并应用排序
        var loadedTasks = taskManager.getTasksByStatus(status.rawValue)
            .filter { !excludedIDs.contains($0.id) }
        
        // 应用排序
        switch sortOrder {
        case .dateCreatedDesc:
            loadedTasks.sort { $0.createdAt > $1.createdAt }
        case .dateCreatedAsc:
            loadedTasks.sort { $0.createdAt < $1.createdAt }
        case .byProject:
            // 先按项目ID排序，再按创建时间倒序
            loadedTasks.sort { task1, task2 in
                if let project1 = task1.project, let project2 = task2.project {
                    if project1 == project2 {
                        return task1.createdAt > task2.createdAt
                    }
                    return project1.uuidString < project2.uuidString
                } else if task1.project != nil {
                    return true
                } else if task2.project != nil {
                    return false
                } else {
                    return task1.createdAt > task2.createdAt
                }
            }
        }
        
        // 应用搜索过滤
        if !searchText.isEmpty {
            loadedTasks = loadedTasks.filter { task in
                task.title.localizedStandardContains(searchText) ||
                task.notes.localizedStandardContains(searchText)
            }
        }
        
        // 更新发布属性
        tasks = loadedTasks
    }
    
    /// 搜索任务
    func searchTasks(query: String) {
        searchText = query
        loadTasks()
    }
    
    /// 删除任务 - 使用父类的 handleTaskDelete 方法
    func deleteTask(_ task: Task) {
        handleTaskIntent(.delete(task))
    }
    
    /// 更新任务状态 - 使用父类的 handleTaskStatusChange 方法
    func updateTaskStatus(_ task: Task, newStatus: String) {
        handleTaskIntent(.changeStatus(task, newStatus))
    }
    
    /// 获取项目名称
    override func getProjectName(for projectId: UUID?) -> String {
        guard let projectId = projectId else { return "无项目" }
        
        if let project = taskManager.getProjectById(projectId) {
            return project.name
        } else {
            return "无项目"
        }
    }
    
    /// 根据项目 ID 获取项目名称
    func getProjectNameById(projectId: UUID) -> String {
        if let project = taskManager.getProjectById(projectId) {
            return project.name
        } else {
            return "无项目"
        }
    }
}
