import Foundation
import SwiftUI
import SwiftData
import Combine

/// 一步视图模型 - 彻底简化版本
class FocusViewModel: BaseTaskViewModel {
    // MARK: - 核心数据
    @Published var doingTasks: [Task] = []

    // MARK: - 状态管理器（只需要两个）
    @ObservedObject var focusManager: FocusManager
    @ObservedObject var uiStateManager: UIStateManager

    // MARK: - 桥接属性 - 确保UI能观察到状态变化
    @Published var showingEchoDrop: Bool = false

    // MARK: - 计算属性
    var isFocusMode: Bool { focusManager.isFocused }
    var isInActionFocusMode: Bool { focusManager.isInActionFocusMode }

    // MARK: - 初始化
    override init(taskManager: TaskManager = DependencyContainer.taskManager()) {
        self.focusManager = FocusManager(taskManager: taskManager)
        self.uiStateManager = UIStateManager(taskManager: taskManager)

        super.init(taskManager: taskManager)

        // 设置状态桥接
        setupStateBridging()
        subscribeToTaskEvents()

        DispatchQueue.main.async { [weak self] in
            self?.loadTasks()
        }
    }

    // MARK: - 状态桥接设置
    private func setupStateBridging() {
        // 监听 uiStateManager 的 showingEchoDrop 变化并同步到本地 @Published 属性
        uiStateManager.$showingEchoDrop
            .receive(on: DispatchQueue.main)
            .assign(to: \.showingEchoDrop, on: self)
            .store(in: &cancellables)
    }

    // MARK: - Subscriptions

    private func subscribeToTaskEvents() {
        EventBus.shared.events
            .receive(on: DispatchQueue.main)
            .sink { [weak self] event in
                guard let self = self else { return }
                switch event {
                case .taskCompleted(let task, _):
                    print("[FocusViewModel] Received taskCompleted event for task: \(task.id), clearing focus memory.")
                    self.focusManager.clearFocusMemoryForTask(taskId: task.id)
                case .taskDeleted(let task, _): // Assuming we want to clear for both undoable and permanent deletions that reach this VM
                    print("[FocusViewModel] Received taskDeleted event for task: \(task.id), clearing focus memory.")
                    self.focusManager.clearFocusMemoryForTask(taskId: task.id)
                default:
                    break
                }
            }
            .store(in: &cancellables)
    }


    // MARK: - 数据加载
    override func loadTasks() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            // 获取所有正在进行中的任务
            let allDoingTasks = self.taskManager.getTasksByStatus(TaskStatus.doing.rawValue)
                .sorted { $0.sortOrder < $1.sortOrder }

            // 简化过滤逻辑：只过滤待删除的任务，完成任务交给BaseTaskViewModel处理
            self.doingTasks = allDoingTasks.filter { task in
                !self.pendingDeletionTaskIDs.contains(task.id) &&
                // 如果任务在待完成但没有动画，则隐藏（已实际完成）
                !(self.pendingCompletionTaskIDs.contains(task.id) && !self.focusManager.isNodeAnimating(task.id))
            }

            // 验证聚焦状态
            if let taskId = self.focusManager.focusPath.first,
               !allDoingTasks.contains(where: { $0.id == taskId }) {
                self.focusManager.exitFocus()
            }
        }
    }

    // MARK: - 统一操作接口

    /// 聚焦到任意节点（统一方法）
    func focusTo(_ nodeId: UUID) {
        focusManager.focusTo(nodeId)
        loadTasks()
    }

    /// 加深聚焦
    func focusDeeper(to nodeId: UUID) {
        focusManager.focusDeeper(to: nodeId)
    }

    /// 退出聚焦
    func exitFocus() {
        // 使用动画确保平滑过渡
        withAnimation(.easeInOut(duration: 0.2)) {
            focusManager.exitFocus()
        }

        // 立即刷新任务列表，确保数据一致性
        DispatchQueue.main.async { [weak self] in
            self?.loadTasks()
        }
    }

    /// 退出到指定层级
    func exitToLevel(_ level: Int) {
        focusManager.exitToLevel(level)
    }

    /// 恢复ActionFocus状态
    /// 用于从任务详情页的"最近一步"功能恢复到上次的聚焦位置
    func restoreActionFocusState(taskId: UUID, checklistItemId: UUID, subStepPath: [UUID]) {
        // 先保存当前任务列表的展开状态
        focusManager.saveTaskListExpandedState()

        // 调用 FocusManager 处理恢复焦点状态
        focusManager.restoreActionFocusState(taskId: taskId, checklistItemId: checklistItemId, subStepPath: subStepPath)

        // 刷新任务列表以反映聚焦状态
        loadTasks()
    }

    /// 处理任务完成（重写BaseTaskViewModel的方法）
    override func handleTaskComplete(_ task: Task) {
        // 如果正在聚焦的任务被完成，退出聚焦模式
        if focusManager.focusPath.first == task.id {
            focusManager.exitFocus()
        }

        // 保存原始完成状态
        let wasCompleted = task.isCompleted

        // 简化动画处理：只添加动画状态，让UI自然响应
        if !wasCompleted {
            focusManager.handleCompletionAnimation(task.id, wasCompleted: false)
        }

        // 调用父类方法处理完成逻辑（包含Toast的onDismiss回调）
        super.handleTaskComplete(task)

        // 播放完成音效
        playCompletionSound()
    }

    /// 处理节点完成
    func handleNodeCompletion(_ nodeId: UUID) {
        // 判断节点类型并获取任务
        if let task = focusManager.getTaskForNode(nodeId) {
            // 如果节点是任务本身，则处理任务完成
            if task.id == nodeId {
                handleTaskComplete(task)
            } else {
                // 对于小行动和子步骤，使用FocusManager
                focusManager.handleNodeCompletion(nodeId)

                // 播放完成音效
                playCompletionSound()

                // 延迟刷新UI，等待动画完成
                // 这里不需要立即调用loadTasks()，因为小行动完成后需要保持动画效果
                // 在动画结束后，FocusManager会自动移除动画状态
            }
        } else {
            // 未找到相关任务，也交给FocusManager处理
            focusManager.handleNodeCompletion(nodeId)

            // 播放完成音效
            playCompletionSound()

            // 刷新UI
            loadTasks()
        }
    }

    /// 切换节点展开状态
    func toggleExpansion(_ nodeId: UUID) {
        focusManager.toggleNodeExpansion(nodeId)
    }

    // MARK: - 任务操作

    /// 显示任务安排菜单
    func showTaskArrangementSheet(for task: Task, at position: CGPoint? = nil, onDismiss: (() -> Void)? = nil) {

        // 获取环境中的 ActionSheetManager
        let actionSheetManager = DependencyContainer.actionSheetManager()

        // 显示操作菜单
        actionSheetManager.showActionSheet(
            for: task,
            at: position, // 传递指定位置，如果有的话
            onChange: { [weak self] newStatus in
                // 处理状态变更
                self?.handleTaskStatusChange(task, newStatus: newStatus)
            },
            onDismiss: onDismiss
        )
    }

    /// 处理任务状态变更
    override func handleTaskStatusChange(_ task: Task, newStatus: String) {
        // 更新任务状态
        task.status = newStatus
        taskManager.updateTask(task)

        // 刷新任务列表
        loadTasks()

        // 显示提示
        let statusName = TaskStatus(rawValue: newStatus)?.description ?? "新状态"
        ToastManager.shared.show("已移动到"+statusName)
    }

    /// 移动任务到下一步
    func moveTaskToNextActions(task: Task) {
        task.status = TaskStatus.na.rawValue
        task.updatedAt = Date()
        taskManager.updateTask(task)

        if focusManager.focusPath.first == task.id {
            exitFocus()
        }

        loadTasks()
    }



    /// 更新任务排序
    func updateTaskOrder(tasks: [Task]) {
        for (index, task) in tasks.enumerated() {
            task.sortOrder = Int16(index)
            taskManager.updateTask(task)
        }
        loadTasks()
    }

    // MARK: - 拖放处理

    func handleTaskDrop(draggedTask: Task?, toTask: Task) {
        guard let draggedTask = draggedTask,
              let fromIndex = doingTasks.firstIndex(where: { $0.id == draggedTask.id }),
              let toIndex = doingTasks.firstIndex(where: { $0.id == toTask.id }),
              fromIndex != toIndex else { return }

        var updatedTasks = doingTasks
        let task = updatedTasks.remove(at: fromIndex)
        updatedTasks.insert(task, at: toIndex)
        updateTaskOrder(tasks: updatedTasks)
    }

    // MARK: - 回声Drop（委托给UIStateManager）

    func showEchoDrop() {
        uiStateManager.showEchoDrop()
    }
    func hideEchoDrop() { uiStateManager.hideEchoDrop() }
    func handleEchoDropText() {
        uiStateManager.handleEchoDropText()
        loadTasks()
    }
    func releaseEcho() { uiStateManager.releaseEcho() }
    func playCompletionSound() { uiStateManager.playCompletionSound() }

    // MARK: - 导航和获取数据

    func getFocusedTask() -> Task? {
        guard let taskId = focusManager.focusPath.first else { return nil }
        return doingTasks.first { $0.id == taskId }
    }

    func getBreadcrumbTitles() -> [String] {
        return focusManager.getBreadcrumbTitles()
                    }

    func navigateToBreadcrumbLevel(_ level: Int) {
        focusManager.navigateToBreadcrumb(level)
        loadTasks()
    }

    // MARK: - 任务状态处理

    func handleTaskCompletionForFocus(_ task: Task) {
        if focusManager.focusPath.first == task.id {
            exitFocus()
        }
    }

    func handleTaskDeletionForFocus(_ task: Task) {
        if focusManager.focusPath.first == task.id {
            exitFocus()
        }
    }

    // MARK: - 节点编辑方法

    /// 更新任务标题
    func updateTaskTitle(_ taskId: UUID, newTitle: String) {
        if let task = doingTasks.first(where: { $0.id == taskId }) {
            taskManager.updateTaskTitle(task, title: newTitle)
            loadTasks()
        }
    }

    /// 更新小行动标题
    func updateChecklistItemTitle(_ itemId: UUID, newTitle: String) {
        for task in doingTasks {
            if let checklist = task.checklist,
               let item = checklist.first(where: { $0.id == itemId }) {
                taskManager.updateChecklistItemTitle(task, itemId: itemId, newTitle: newTitle)
                loadTasks()
                return
            }
        }
    }

    /// 更新子步骤标题
    func updateSubStepTitle(_ stepId: UUID, newTitle: String) {
        // 遍历所有任务查找子步骤所在的任务和小行动
        for task in doingTasks {
            if let checklist = task.checklist {
                for item in checklist {
                    // 在子步骤列表中查找
                    if findSubStepInChecklistItem(stepId, item) {
                        // 调用TaskManager方法更新标题
                        taskManager.updateSubStepTitle(task, checklistItemId: item.id, subStepId: stepId, title: newTitle)
                        loadTasks()
                        return
                    }
                }
            }
        }

        print("[FocusViewModel] 未找到子步骤: \(stepId)")
    }

    // 检查子步骤是否存在于小行动的子步骤列表中
    private func findSubStepInChecklistItem(_ stepId: UUID, _ item: ChecklistItem) -> Bool {
        // 直接在当前层级查找
        if item.subStepsList.contains(where: { $0.id == stepId }) {
            return true
        }

        // 递归查找更深层级
        for subStep in item.subStepsList {
            if findSubStepInSubSteps(stepId, subStep.subSteps) {
                return true
            }
        }

        return false
    }

    // 递归查找子步骤
    private func findSubStepInSubSteps(_ stepId: UUID, _ subSteps: [SubStep]) -> Bool {
        // 直接在当前层级查找
        if subSteps.contains(where: { $0.id == stepId }) {
            return true
        }

        // 递归查找更深层级
        for subStep in subSteps {
            if findSubStepInSubSteps(stepId, subStep.subSteps) {
                return true
            }
        }

        return false
    }

    /// 删除任务
    func deleteTask(_ taskId: UUID) {
        if let task = doingTasks.first(where: { $0.id == taskId }) {
            // 如果删除的是聚焦的任务，退出聚焦
            if focusManager.focusPath.first == taskId {
                exitFocus()
            }

            // 使用BaseTaskViewModel的删除方法
            handleTaskDelete(task)
        }
    }

    /// 删除小行动
    func deleteChecklistItem(_ itemId: UUID) {
        for task in doingTasks {
            if let checklist = task.checklist,
               checklist.contains(where: { $0.id == itemId }) {
                taskManager.removeChecklistItemFromTask(task, itemId: itemId)
                loadTasks()
                return
            }
        }
    }

    /// 删除子步骤
    func deleteSubStep(_ stepId: UUID) {
        // 遍历所有任务查找子步骤所在的任务和小行动
        for task in doingTasks {
            if let checklist = task.checklist {
                for item in checklist {
                    // 在子步骤列表中查找
                    if findSubStepInChecklistItem(stepId, item) {
                        // 调用TaskManager方法删除子步骤
                        taskManager.removeSubStep(task, checklistItemId: item.id, subStepId: stepId)
                        loadTasks()
                        return
                    }
                }
            }
        }

        print("[FocusViewModel] 未找到子步骤: \(stepId)")
    }

    /// 添加小行动
    func addChecklistItem(to taskId: UUID, title: String) {
        if let task = doingTasks.first(where: { $0.id == taskId }) {
            let _ = taskManager.addChecklistItemToTask(task, title: title, createdAt: Date())
            loadTasks()
        }
    }

    /// 添加子步骤
    func addSubStep(to parentId: UUID, title: String) {
        // 遍历所有任务查找父节点
        for task in doingTasks {
            // 检查是否为小行动
            if let checklist = task.checklist {
                for item in checklist {
                    if item.id == parentId {
                        // 找到父小行动，添加子步骤
                        let _ = taskManager.addSubStepToChecklistItem(task, checklistItemId: parentId, title: title)
                        loadTasks()
                        return
                    }

                    // 检查是否为子步骤
                    if findSubStepInChecklistItem(parentId, item) {
                        // 找到父子步骤，添加子步骤
                        let _ = taskManager.addSubStepToSubStep(task, checklistItemId: item.id, parentStepId: parentId, title: title)
                        loadTasks()
                        return
                    }
                }
            }
        }

        print("[FocusViewModel] 未找到父节点: \(parentId)")
    }
}