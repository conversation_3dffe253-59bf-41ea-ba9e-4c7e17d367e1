import Foundation
import Combine
import SwiftUI // For UUID

// Renamed from oneStepCore? Assuming it's accessible or needs adjustment
// If TaskRepository is in oneStepCore, import might be needed
// import oneStepCore

@MainActor
class FocusTaskSelectorViewModel: ObservableObject {
    
    // MARK: - Dependencies
    let taskManager: TaskManager
    let currentDoingCount: Int
    let maxDoingTasks = 3
    
    // MARK: - Published Properties
    @Published var availableTasks: [Task] = []
    @Published var selectedTaskIDs = Set<UUID>()
    @Published var errorMessage: String? = nil // For potential errors
    
    // Computed property to determine if more tasks can be added
    var canSelectMore: Bool {
        currentDoingCount + selectedTaskIDs.count < maxDoingTasks
    }
    
    // Computed property for the add button title
    var addButtonTitle: String {
        selectedTaskIDs.isEmpty ? "选择行动" : "添加选中行动 (\(selectedTaskIDs.count))"
    }
    
    // MARK: - Initialization
    init(taskManager: TaskManager = DependencyContainer.taskManager(), currentDoingCount: Int) {
        self.taskManager = taskManager
        self.currentDoingCount = currentDoingCount
        loadAvailableTasks()
    }
    
    // MARK: - Public Methods
    
    /// Loads tasks with status 'NA'.
    func loadAvailableTasks() {
        // Fetch tasks that are in the 'Next Action' state
        availableTasks = taskManager.getTasksByStatus(TaskStatus.na.rawValue)
            .sorted { $0.updatedAt > $1.updatedAt } // Or sort as needed
    }
    
    /// Toggles the selection state of a task.
    func toggleSelection(task: Task) {
        let taskID = task.id
        if selectedTaskIDs.contains(taskID) {
            // Deselect
            selectedTaskIDs.remove(taskID)
        } else {
            // Attempt to select
            if canSelectMore {
                selectedTaskIDs.insert(taskID)
            } else {
                // Optionally show an error or provide feedback that the limit is reached
                errorMessage = "“一步”行动已达上限 (\(maxDoingTasks)个)"
                // Clear the error message after a delay
                 DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                     self.errorMessage = nil
                 }
            }
        }
        // Trigger UI update for button title and potentially row states
        objectWillChange.send()
    }
    
    /// Adds the selected tasks to the 'Doing' state.
    func addSelectedTasks() -> Bool {
        guard !selectedTaskIDs.isEmpty else { return false }
        
        for taskID in selectedTaskIDs {
            if let task = availableTasks.first(where: { $0.id == taskID }) {
                // Update task status to 'doing'
                taskManager.updateTaskStatus(task, newStatus: TaskStatus.doing.rawValue)
            }
        }
        
        // Clear selection and reload
        selectedTaskIDs.removeAll()
        loadAvailableTasks()
        
        return true
    }
    
    /// Adds the selected tasks to the 'Doing' state.
    func addSelectedTasksToDoing() {
        guard !selectedTaskIDs.isEmpty else { return }
        
        for taskID in selectedTaskIDs {
            if let task = availableTasks.first(where: { $0.id == taskID }) {
                // Update task status to 'doing'
                taskManager.updateTaskStatus(task, newStatus: TaskStatus.doing.rawValue)
            }
        }
        
        // Clear selection and reload
        selectedTaskIDs.removeAll()
        loadAvailableTasks()
    }
} 