import Foundation
import SwiftData

/// 标签管理视图模型
class TagViewModel: ObservableObject {
    // MARK: - 依赖
    
    private let tagRepository: TagRepository
    
    // MARK: - 发布属性
    
    @Published var tags: [Tag] = []
    
    // MARK: - 初始化方法
    
    init(tagRepository: TagRepository) {
        self.tagRepository = tagRepository
        loadTags()
    }
    
    // MARK: - 公共方法
    
    /// 加载所有标签
    func loadTags() {
        tags = tagRepository.getAllTags()
    }
    
    /// 添加新标签
    /// - Parameters:
    ///   - name: 标签名称
    ///   - color: 标签颜色
    /// - Returns: 如果标签名已存在则返回 false，否则返回 true
    @discardableResult
    func addTag(name: String, color: String) -> Bool {
        if let _ = tagRepository.createTag(name: name, color: color) {
            loadTags()
            return true
        }
        return false
    }
    
    /// 重命名标签
    /// - Parameters:
    ///   - tag: 要重命名的标签
    ///   - newName: 新的标签名
    /// - Returns: 如果新标签名已存在则返回 false，否则返回 true
    @discardableResult
    func renameTag(_ tag: Tag, newName: String) -> Bool {
        tag.name = newName
        if tagRepository.updateTag(tag) {
            loadTags()
            return true
        }
        return false
    }
    
    /// 删除标签
    func deleteTag(_ tag: Tag) {
        tagRepository.deleteTag(tag)
        loadTags()
    }
}
