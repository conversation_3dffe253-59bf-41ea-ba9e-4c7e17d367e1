import Foundation
import SwiftUI
import SwiftData
import Combine

/// 标签详情视图模型
class TagDetailViewModel: BaseTaskViewModel {
    // MARK: - 依赖
    
    let tag: Tag
    
    // MARK: - 发布属性
    
    @Published var naTasks: [Task] = []
    @Published var waitingTasks: [Task] = []
    @Published var inboxTasks: [Task] = []
    @Published var smbTasks: [Task] = []
    @Published var doneTasks: [Task] = [] // 已完成任务
    @Published var showCompletedTasks: Bool = true // 是否显示已完成任务
    
    // MARK: - 初始化方法
    
    init(
        tag: Tag,
        taskManager: TaskManager = DependencyContainer.taskManager()
    ) {
        self.tag = tag
        super.init(taskManager: taskManager)
        loadTasks()
    }
    
    // MARK: - 任务加载与处理
    
    /// 加载与标签关联的任务并分类
    override func loadTasks() {
        let excludedIDs = pendingDeletionTaskIDs.union(pendingCompletionTaskIDs)
        
        let allTasksWithTag = taskManager.getAllTasks()
            .filter { task in
                task.tags.contains(tag.name) && !excludedIDs.contains(task.id)
            }
            .sorted { $0.updatedAt > $1.updatedAt }
        
        naTasks = allTasksWithTag.filter { $0.status == TaskStatus.na.rawValue }
        waitingTasks = allTasksWithTag.filter { $0.status == TaskStatus.waiting.rawValue }
        inboxTasks = allTasksWithTag.filter { $0.status == TaskStatus.inbox.rawValue }
        smbTasks = allTasksWithTag.filter { $0.status == TaskStatus.smb.rawValue }
        
        // 获取已完成任务（按完成时间倒序排列）
        let completedTasks = allTasksWithTag.filter { $0.status == TaskStatus.done.rawValue }
            .sorted { ($0.completedAt ?? Date()) > ($1.completedAt ?? Date()) }
        doneTasks = completedTasks
        
    }
    
    /// 处理任务意图
    override func handleTaskIntent(_ intent: TaskIntent) {
        switch intent {
        case .delete(let task):
            super.handleTaskIntent(intent)
            
        case .complete(let task):
            super.handleTaskIntent(intent)
            
        case .changeStatus(let task, let newStatus):
            super.handleTaskIntent(intent)
        }
    }
    
    /// 重写更新任务列表的方法，以实现任务状态变更时的立即更新
    override func updateTaskInLists(task: Task, oldStatus: String, newStatus: String) {
        // 根据旧状态从原列表中移除
        switch oldStatus {
        case TaskStatus.na.rawValue:
            if let index = naTasks.firstIndex(where: { $0.id == task.id }) {
                naTasks.remove(at: index)
            }
        case TaskStatus.waiting.rawValue:
            if let index = waitingTasks.firstIndex(where: { $0.id == task.id }) {
                waitingTasks.remove(at: index)
            }
        case TaskStatus.inbox.rawValue:
            if let index = inboxTasks.firstIndex(where: { $0.id == task.id }) {
                inboxTasks.remove(at: index)
            }
        case TaskStatus.smb.rawValue:
            if let index = smbTasks.firstIndex(where: { $0.id == task.id }) {
                smbTasks.remove(at: index)
            }
        case TaskStatus.done.rawValue:
            if let index = doneTasks.firstIndex(where: { $0.id == task.id }) {
                doneTasks.remove(at: index)
            }
        default:
            break
        }
        
        // 如果任务不再包含当前标签，则不添加到任何列表
        if !task.tags.contains(tag.name) {
            return
        }
        
        // 根据新状态添加到对应列表
        switch newStatus {
        case TaskStatus.na.rawValue:
            naTasks.insert(task, at: 0)
        case TaskStatus.waiting.rawValue:
            waitingTasks.insert(task, at: 0)
        case TaskStatus.inbox.rawValue:
            inboxTasks.insert(task, at: 0)
        case TaskStatus.smb.rawValue:
            smbTasks.insert(task, at: 0)
        case TaskStatus.done.rawValue:
            doneTasks.insert(task, at: 0)
        default:
            break
        }
    }
    
    // MARK: - 标签特定方法
    
    /// 从标签中移除任务
    private func removeTaskFromTag(_ task: Task) {
        if let index = task.tags.firstIndex(of: tag.name) {
            var updatedTags = task.tags
            updatedTags.remove(at: index)
            task.tags = updatedTags
            taskManager.updateTask(task)
        }
    }
    
    /// 重命名标签
    func renameTag(newName: String) {
        let oldName = tag.name
        tag.name = newName
        taskManager.updateTag(tag)
        
        let tasksToUpdate = taskManager.getAllTasks().filter { $0.tags.contains(oldName) }
        for task in tasksToUpdate {
            if let index = task.tags.firstIndex(of: oldName) {
                var updatedTags = task.tags
                updatedTags[index] = newName
                task.tags = updatedTags
                taskManager.updateTask(task)
            }
        }
        
        loadTasks()
    }
    
    /// 删除标签
    func deleteTag(dismissAction: DismissAction) {
        let tasksToUpdate = taskManager.getAllTasks().filter { $0.tags.contains(tag.name) }
        for task in tasksToUpdate {
            removeTaskFromTag(task)
        }
        
        taskManager.deleteTag(tag)
        
        dismissAction()
    }
    
    /// 刷新任务 - 用于下拉刷新
    @MainActor
    func refreshTasks() async {
        loadTasks()
        // 显示轻量级刷新提示
        DependencyContainer.toastManager().showSuperLightInfo("已刷新")
    }
}
