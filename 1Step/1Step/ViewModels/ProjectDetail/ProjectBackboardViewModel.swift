import SwiftUI
import SwiftData

/// 项目背景板的视图模型
@MainActor
class ProjectBackboardViewModel: ObservableObject {
    // MARK: - 属性

    /// 当前关联的项目
    private let project: Project

    /// 项目仓库
    private let projectRepository: ProjectRepository
    
    /// 思绪仓库
    private let thoughtRepository: ThoughtRepository

    /// 用于展示的背景板思绪记录
    @Published var thoughts: [Thought] = []

    /// 新思绪的输入内容
    @Published var newThoughtContent: String = ""
    
    /// 项目名称 - 供视图使用
    var projectName: String {
        return project.name
    }

    /// 任务仓库引用，用于获取任务信息
    private let taskRepository: TaskRepository

    // MARK: - 初始化方法

    init(project: Project, projectRepository: ProjectRepository, thoughtRepository: ThoughtRepository) {
        self.project = project
        self.projectRepository = projectRepository
        self.thoughtRepository = thoughtRepository
        self.taskRepository = DependencyContainer.taskRepository() // 添加任务仓库引用
    }

    // MARK: - 公开方法

    /// 检查是否有历史思绪
    func hasHistoryThoughts() -> Bool {
        // 获取当前项目的历史背景板思绪数量
        let count = thoughtRepository.getBackboardThoughtsCount(projectId: project.id)
        return count > 0
    }
    
    /// 加载与当前项目关联的背景板思绪记录
    func loadThoughts() {
        // 使用正向时间排序（从早到晚），使对话流更自然
        self.thoughts = thoughtRepository.getBackboardThoughts(
            projectId: project.id,
            sortBy: SortDescriptor(\.createdAt, order: .forward)
        )
        
        // 调试输出系统消息
        let systemMessages = self.thoughts.filter { $0.isSystemMessage }

    }
    
    /// 获取今天的思绪记录
    func getTodaysMessages() -> [Thought] {
        // 获取今天的起始时间
        let calendar = Calendar.current
        let startOfToday = calendar.startOfDay(for: Date())
        
        // 获取项目所有背景板思绪（默认按时间正序排列）
        let allThoughts = thoughtRepository.getBackboardThoughts(projectId: project.id)
        
        // 过滤出今天的系统消息
        let todaysMessages = allThoughts.filter { thought in
            thought.isSystemMessage && thought.createdAt >= startOfToday
        }
        
        return todaysMessages
    }

    /// 添加新的思绪记录并返回
    func addThoughtAndReturn() -> Thought? {
        let trimmedContent = newThoughtContent.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedContent.isEmpty else { return nil }

        // 使用ThoughtRepository添加背景板思绪
        let thought = thoughtRepository.addBackboardThought(
            content: trimmedContent,
            projectId: project.id,
            isSystemMessage: false
        )
        
        // 清空输入框
        newThoughtContent = ""
        
        return thought
    }
    
    /// 删除思绪记录
    func deleteThought(_ thought: Thought) {
        thoughtRepository.deleteThought(thought)
        
        // 如果已加载历史思绪，则刷新列表
        if !thoughts.isEmpty {
            thoughts.removeAll(where: { $0.id == thought.id })
        }
    }

    /// 检查新完成的任务并生成系统消息
    func checkCompletedTasksAndAddMessages() {
        
        // 获取最近7天已完成的任务
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let sevenDaysAgo = calendar.date(byAdding: .day, value: -7, to: today) ?? today
        
        
        let completedTasks = taskRepository.getCompletedTasksForProject(
            projectId: project.id, 
            since: sevenDaysAgo
        )
        

        
        // 获取已有系统消息中提到的任务ID
        let existingMessageTaskIds = getExistingCompletionMessageTaskIds()
        
        // 过滤出需要创建消息的任务
        let tasksToRecord = completedTasks.filter { 
            !existingMessageTaskIds.contains($0.id) 
        }
        
        
        // 为每个未记录的已完成任务创建系统消息
        for task in tasksToRecord {
            let messageContent = "行动「\(task.title)」已完成 ✓"
            
            // 使用任务的完成时间作为消息时间
            _ = thoughtRepository.addBackboardSystemThought(
                content: messageContent,
                projectId: project.id,
                relatedTaskId: task.id,
                createdAt: task.completedAt ?? Date()
            )
        }
        
        // 不再自动重新加载历史列表，让视图决定是否加载
        /*
        // 如果有新消息，重新加载列表
        if !tasksToRecord.isEmpty {
            print("♻️ 有新消息，重新加载思绪列表")
            loadThoughts()
        }
        */
        
    }
    
    /// 从现有系统消息中提取任务ID
    private func getExistingCompletionMessageTaskIds() -> [UUID] {
        let systemMessages = thoughtRepository.getBackboardSystemThoughts(projectId: project.id)
        
        var taskIds: [UUID] = []
        
        for message in systemMessages {
            if let taskId = message.relatedTaskId {
                taskIds.append(taskId)
            } else {
            }
        }
        
        return taskIds
    }
} 