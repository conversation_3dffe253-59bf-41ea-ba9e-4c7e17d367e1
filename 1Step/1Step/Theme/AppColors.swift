import SwiftUI

// 颜色扩展 - 通用工具方法
extension Color {
    // 从十六进制字符串创建颜色
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
    
    // 系统颜色简写
    static let systemBackground = Color(UIColor.systemBackground)
    static let secondarySystemBackground = Color(UIColor.secondarySystemBackground)
    static let tertiarySystemBackground = Color(UIColor.tertiarySystemBackground)
    
    // 动态背景色
    static let dynamicBackground = Color(UIColor { trait in
        trait.userInterfaceStyle == .dark ? .systemGray5 : .systemGray6
    })
}

// 应用颜色主题管理
struct AppColors {
    // 任务状态颜色
    struct Status {
        // 收集箱颜色
        static func inbox(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color(red: 142/255, green: 142/255, blue: 147/255) : Color(red: 142/255, green: 142/255, blue: 147/255)
        }
        
        // 下一步颜色
        static func nextAction(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color(red: 0.2, green: 0.5, blue: 1.0) : Color(red: 0.2, green: 0.5, blue: 1.0)
        }
        
        // 一步颜色
        static func doing(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color(red: 0, green: 0.8, blue: 0.4) : Color(red: 0, green: 0.8, blue: 0.4)
        }
        
        // 等待中颜色
        static func waiting(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color(red: 1.0, green: 0.6, blue: 0.2) : Color(red: 1.0, green: 0.6, blue: 0.2)
        }
        
        // 将来也许颜色
        static func someday(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color(red: 0.7, green: 0.3, blue: 0.8) : Color(red: 0.7, green: 0.3, blue: 0.8)
        }
        
        // 已完成颜色
        static func done(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color(red: 142/255, green: 142/255, blue: 147/255) : Color(red: 142/255, green: 142/255, blue: 147/255)
        }
    }
    
    // UI元素颜色
    struct UI {
        // 主色调（替代.blue）
        static func primary(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color(red: 0.2, green: 0.5, blue: 1.0) : Color(red: 0.2, green: 0.5, blue: 1.0)
        }
        
        // 强调色（替代.accentColor）
        static func accent(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color(red: 0.2, green: 0.5, blue: 1.0) : Color(red: 0.2, green: 0.5, blue: 1.0)
        }
        
        // 强调色（静态版）
        static var accent: Color {
            Color.accentColor
        }
        
        // 主背景色 - 用于页面整体背景
        static func background(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color(red: 0.05, green: 0.05, blue: 0.05) : Color(red: 0.95, green: 0.95, blue: 0.97)
        }
        
        // 卡片背景色 - 用于卡片内容区域，与主背景形成对比
        static func card(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color(red: 0.12, green: 0.12, blue: 0.13) : Color(red: 1.0, green: 1.0, blue: 1.0)
        }
        
        // 添加卡片边框颜色
        static func cardBorder(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color.white.opacity(0.08) : Color.gray.opacity(0.1)
        }
        
        // 分割线颜色
        static func divider(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color.white.opacity(0.12) : Color(red: 0.9, green: 0.9, blue: 0.9)
        }
        
        // 成功色（替代.green）
        static func success(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color(red: 0, green: 0.8, blue: 0.4) : Color(red: 0, green: 0.8, blue: 0.4)
        }
        
        // 警告色（替代.orange）
        static func warning(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color(red: 1.0, green: 0.6, blue: 0.2) : Color(red: 1.0, green: 0.6, blue: 0.2)
        }
        
        // 错误色（替代.red）
        static func error(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color(red: 1.0, green: 0.3, blue: 0.3) : Color(red: 1.0, green: 0.3, blue: 0.3)
        }
        
        // 特色色（替代.purple）
        static func special(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color(red: 0.7, green: 0.3, blue: 0.8) : Color(red: 0.7, green: 0.3, blue: 0.8)
        }
        
        // 次要文本色（替代.secondary）
        static func secondaryText(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color(red: 142/255, green: 142/255, blue: 147/255) : Color(red: 142/255, green: 142/255, blue: 147/255)
        }
        
        // 次要文本色（静态版）
        static var secondaryText: Color {
            Color.secondary
        }
        
        // 灰色
        static func gray(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color(red: 142/255, green: 142/255, blue: 147/255) : Color(red: 142/255, green: 142/255, blue: 147/255)
        }
        
        // 灰色（静态版）
        static var gray: Color {
            Color(red: 142/255, green: 142/255, blue: 147/255)
        }
        
        // 主要文本色（替代.primary）
        static func primaryText(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color(red: 0.92, green: 0.92, blue: 0.96) : Color.black
        }
        
        // 主要文本色（静态版）
        static var primaryText: Color {
            Color.primary
        }
        
        // 标签背景色
        static func tagBackground(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color(red: 0.2, green: 0.2, blue: 0.21) : Color(red: 0.94, green: 0.94, blue: 0.96)
        }
        
        // 系统背景色 - 保持与系统一致
        static func systemBackground(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color(red: 0.0, green: 0.0, blue: 0.0) : Color(red: 1.0, green: 1.0, blue: 1.0)
        }
        
        // 系统背景色（静态版）
        static var systemBackground: Color {
            Color(.systemBackground)
        }
        
        // systemGray6 替代色 - 用于页面背景
        static func systemGray6(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color(red: 0.05, green: 0.05, blue: 0.05) : Color(red: 0.95, green: 0.95, blue: 0.97)
        }
        
        // systemGray6（静态版）
        static var systemGray6: Color {
            Color(.systemGray6)
        }
        
        // 蓝色(.blue 替代色)
        static func blue(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color(red: 0.0, green: 0.48, blue: 1.0) : Color(red: 0.0, green: 0.48, blue: 1.0)
        }
        
        // 蓝色(.blue 替代色)（静态版）
        static var blue: Color {
            Color.blue
        }
        
        // 项目颜色
        static func projectColor(for colorScheme: ColorScheme, project: Project) -> Color {
            if let colorName = project.color {
                switch colorName {
                case "red": return AppColors.UI.error(for: colorScheme)
                case "orange": return AppColors.UI.warning(for: colorScheme)
                case "yellow": return AppColors.UI.yellow(for: colorScheme)
                case "green": return AppColors.UI.success(for: colorScheme)
                case "blue": return AppColors.UI.blue(for: colorScheme)
                case "purple": return AppColors.UI.special(for: colorScheme)
                case "pink": return AppColors.UI.pink(for: colorScheme)
                default:
                    // 尝试解析十六进制颜色
                    if colorName.hasPrefix("#") {
                        let hex = String(colorName.dropFirst())
                        var rgbValue: UInt64 = 0
                        Scanner(string: hex).scanHexInt64(&rgbValue)
                        let r = Double((rgbValue & 0xFF0000) >> 16) / 255.0
                        let g = Double((rgbValue & 0x00FF00) >> 8) / 255.0
                        let b = Double(rgbValue & 0x0000FF) / 255.0
                        return Color(red: r, green: g, blue: b)
                    }
                    return AppColors.UI.blue(for: colorScheme)
                }
            } else {
                return AppColors.UI.blue(for: colorScheme)
            }
        }
        
        // 获取标签颜色
        static func tagColor(for colorScheme: ColorScheme, tag: Tag) -> Color {
            if let colorName = tag.color {
                switch colorName {
                case "red": return AppColors.UI.error(for: colorScheme)
                case "orange": return AppColors.UI.warning(for: colorScheme)
                case "yellow": return AppColors.UI.yellow(for: colorScheme)
                case "green": return AppColors.UI.success(for: colorScheme)
                case "blue": return AppColors.UI.blue(for: colorScheme)
                case "purple": return AppColors.UI.special(for: colorScheme)
                case "pink": return AppColors.UI.pink(for: colorScheme)
                default:
                    // 尝试解析十六进制颜色
                    if colorName.hasPrefix("#") {
                        let hex = String(colorName.dropFirst())
                        var rgbValue: UInt64 = 0
                        Scanner(string: hex).scanHexInt64(&rgbValue)
                        let r = Double((rgbValue & 0xFF0000) >> 16) / 255.0
                        let g = Double((rgbValue & 0x00FF00) >> 8) / 255.0
                        let b = Double(rgbValue & 0x0000FF) / 255.0
                        return Color(red: r, green: g, blue: b)
                    }
                    return AppColors.UI.blue(for: colorScheme)
                }
            } else {
                return AppColors.UI.blue(for: colorScheme)
            }
        }
        
        // 黄色（替代.yellow）
        static func yellow(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color(red: 1.0, green: 0.8, blue: 0.0) : Color(red: 1.0, green: 0.8, blue: 0.0)
        }
        
        // 粉色（替代.pink）
        static func pink(for colorScheme: ColorScheme) -> Color {
            colorScheme == .dark ? Color(red: 1.0, green: 0.4, blue: 0.7) : Color(red: 1.0, green: 0.4, blue: 0.7)
        }
    }
}

// 便捷扩展，根据任务状态获取对应颜色
extension TaskStatus {
    func color(for colorScheme: ColorScheme) -> Color {
        switch self {
        case .inbox: return AppColors.Status.inbox(for: colorScheme)
        case .na: return AppColors.Status.nextAction(for: colorScheme)
        case .doing: return AppColors.Status.doing(for: colorScheme)
        case .waiting: return AppColors.Status.waiting(for: colorScheme)
        case .smb: return AppColors.Status.someday(for: colorScheme)
        case .done: return AppColors.Status.done(for: colorScheme)
        }
    }
}

// 便捷扩展，在SwiftUI视图中使用
extension View {
    // 获取当前环境的colorScheme并应用颜色
    func withAppColor(_ colorProvider: @escaping (ColorScheme) -> Color) -> some View {
        self.modifier(AppColorModifier(colorProvider: colorProvider))
    }
}

// 色彩环境修饰符
struct AppColorModifier: ViewModifier {
    @Environment(\.colorScheme) private var colorScheme
    let colorProvider: (ColorScheme) -> Color
    
    func body(content: Content) -> some View {
        content
            .foregroundColor(colorProvider(colorScheme))
    }
}
