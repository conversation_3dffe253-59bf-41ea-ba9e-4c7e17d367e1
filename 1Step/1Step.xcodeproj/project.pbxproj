// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		DB143FC32D9DA69700FCB08C /* Siren in Frameworks */ = {isa = PBXBuildFile; productRef = DB143FC22D9DA69700FCB08C /* Siren */; };
		DB2887532D99287F00E1EA43 /* AlertToast in Frameworks */ = {isa = PBXBuildFile; productRef = DB2887522D99287F00E1EA43 /* AlertToast */; };
		DB6AA2572DBD6E7000A3802C /* Flow in Frameworks */ = {isa = PBXBuildFile; productRef = DB6AA2562DBD6E7000A3802C /* Flow */; };
		DB87FA262D96E1010020B046 /* SwipeActions in Frameworks */ = {isa = PBXBuildFile; productRef = DB87FA252D96E1010020B046 /* SwipeActions */; };
		DB87FA4B2D980EA30020B046 /* SwiftUIX in Frameworks */ = {isa = PBXBuildFile; productRef = DB87FA4A2D980EA30020B046 /* SwiftUIX */; };
		DB8D198B2DE3646D00DB211E /* Lottie in Frameworks */ = {isa = PBXBuildFile; productRef = DB8D198A2DE3646D00DB211E /* Lottie */; };
		E1198E340ACCF10AE45B3F93 /* Pods_1Step.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 71F3B765D55EDFAC102A5C33 /* Pods_1Step.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		09F9AAF60B402BFDEB26AEB0 /* Pods-1Step.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-1Step.debug.xcconfig"; path = "Target Support Files/Pods-1Step/Pods-1Step.debug.xcconfig"; sourceTree = "<group>"; };
		1060CA59980A9FFDA30A5474 /* Pods-1Step.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-1Step.release.xcconfig"; path = "Target Support Files/Pods-1Step/Pods-1Step.release.xcconfig"; sourceTree = "<group>"; };
		19926A11F89D48EC7DB8C86E /* Pods-1Step.testflight.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-1Step.testflight.xcconfig"; path = "Target Support Files/Pods-1Step/Pods-1Step.testflight.xcconfig"; sourceTree = "<group>"; };
		71F3B765D55EDFAC102A5C33 /* Pods_1Step.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_1Step.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DBF1096C2D9074B600BED622 /* 1Step Dev.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "1Step Dev.app"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		DBAB4DE62DA58F5700BD1D8A /* Configs */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Configs;
			sourceTree = "<group>";
		};
		DBF1096E2D9074B600BED622 /* 1Step */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = 1Step;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		DBF109692D9074B600BED622 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB2887532D99287F00E1EA43 /* AlertToast in Frameworks */,
				DB8D198B2DE3646D00DB211E /* Lottie in Frameworks */,
				DB87FA262D96E1010020B046 /* SwipeActions in Frameworks */,
				DB6AA2572DBD6E7000A3802C /* Flow in Frameworks */,
				DB143FC32D9DA69700FCB08C /* Siren in Frameworks */,
				DB87FA4B2D980EA30020B046 /* SwiftUIX in Frameworks */,
				E1198E340ACCF10AE45B3F93 /* Pods_1Step.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1B899489A4B5BF2072AA7638 /* Pods */ = {
			isa = PBXGroup;
			children = (
				09F9AAF60B402BFDEB26AEB0 /* Pods-1Step.debug.xcconfig */,
				1060CA59980A9FFDA30A5474 /* Pods-1Step.release.xcconfig */,
				19926A11F89D48EC7DB8C86E /* Pods-1Step.testflight.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		3BCD7BC4F76F8667FB55BE50 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				71F3B765D55EDFAC102A5C33 /* Pods_1Step.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		DBF109632D9074B600BED622 = {
			isa = PBXGroup;
			children = (
				DBAB4DE62DA58F5700BD1D8A /* Configs */,
				DBF1096E2D9074B600BED622 /* 1Step */,
				DBF1096D2D9074B600BED622 /* Products */,
				1B899489A4B5BF2072AA7638 /* Pods */,
				3BCD7BC4F76F8667FB55BE50 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		DBF1096D2D9074B600BED622 /* Products */ = {
			isa = PBXGroup;
			children = (
				DBF1096C2D9074B600BED622 /* 1Step Dev.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		DBF1096B2D9074B600BED622 /* 1Step */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DBF109922D9074B800BED622 /* Build configuration list for PBXNativeTarget "1Step" */;
			buildPhases = (
				74CFD8B63A5B6F6D49DAF3A4 /* [CP] Check Pods Manifest.lock */,
				DBF109682D9074B600BED622 /* Sources */,
				DBF109692D9074B600BED622 /* Frameworks */,
				DBF1096A2D9074B600BED622 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				DBF1096E2D9074B600BED622 /* 1Step */,
			);
			name = 1Step;
			packageProductDependencies = (
				DB87FA252D96E1010020B046 /* SwipeActions */,
				DB87FA4A2D980EA30020B046 /* SwiftUIX */,
				DB2887522D99287F00E1EA43 /* AlertToast */,
				DB143FC22D9DA69700FCB08C /* Siren */,
				DB6AA2562DBD6E7000A3802C /* Flow */,
				DB8D198A2DE3646D00DB211E /* Lottie */,
			);
			productName = 1Step;
			productReference = DBF1096C2D9074B600BED622 /* 1Step Dev.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		DBF109642D9074B600BED622 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					DBF1096B2D9074B600BED622 = {
						CreatedOnToolsVersion = 16.2;
					};
				};
			};
			buildConfigurationList = DBF109672D9074B600BED622 /* Build configuration list for PBXProject "1Step" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = DBF109632D9074B600BED622;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				DB87F96C2D94050A0020B046 /* XCRemoteSwiftPackageReference "SwiftUI-PullToRefresh" */,
				DB87F96D2D94080C0020B046 /* XCRemoteSwiftPackageReference "Refresh" */,
				DB87FA242D96E1010020B046 /* XCRemoteSwiftPackageReference "SwipeActions" */,
				DB87FA492D980EA30020B046 /* XCRemoteSwiftPackageReference "SwiftUIX" */,
				DB2887512D99287F00E1EA43 /* XCRemoteSwiftPackageReference "AlertToast" */,
				DB143FC12D9DA69700FCB08C /* XCRemoteSwiftPackageReference "Siren" */,
				DB6AA2552DBD6E7000A3802C /* XCRemoteSwiftPackageReference "Flow" */,
				DB8D19892DE3646D00DB211E /* XCRemoteSwiftPackageReference "lottie-ios" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = DBF1096D2D9074B600BED622 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				DBF1096B2D9074B600BED622 /* 1Step */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		DBF1096A2D9074B600BED622 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		74CFD8B63A5B6F6D49DAF3A4 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-1Step-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		DBF109682D9074B600BED622 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		DB1965DE2DA56F1500ABE743 /* TestFlight */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = DBAB4DE62DA58F5700BD1D8A /* Configs */;
			baseConfigurationReferenceRelativePath = xcconfig/TestFlight.xcconfig;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = TestFlight;
		};
		DB1965DF2DA56F1500ABE743 /* TestFlight */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 19926A11F89D48EC7DB8C86E /* Pods-1Step.testflight.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon.beta;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Configs/entitlements/TestFlight.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"1Step/Preview Content\"";
				DEVELOPMENT_TEAM = 9H74CW8759;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_KEY_CFBundleDisplayName = "";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.productivity";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.absir.1Step-Beta";
				PRODUCT_NAME = "1Step Beta";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = TestFlight;
		};
		DBF109902D9074B800BED622 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = DBAB4DE62DA58F5700BD1D8A /* Configs */;
			baseConfigurationReferenceRelativePath = xcconfig/Debug.xcconfig;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		DBF109912D9074B800BED622 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = DBAB4DE62DA58F5700BD1D8A /* Configs */;
			baseConfigurationReferenceRelativePath = xcconfig/Release.xcconfig;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		DBF109932D9074B800BED622 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 09F9AAF60B402BFDEB26AEB0 /* Pods-1Step.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon.dev;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Configs/entitlements/Debug.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"1Step/Preview Content\"";
				DEVELOPMENT_TEAM = 9H74CW8759;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_KEY_CFBundleDisplayName = "";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.productivity";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "";
				PRODUCT_NAME = "1Step Dev";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		DBF109942D9074B800BED622 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1060CA59980A9FFDA30A5474 /* Pods-1Step.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Configs/entitlements/Release.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"1Step/Preview Content\"";
				DEVELOPMENT_TEAM = 9H74CW8759;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_KEY_CFBundleDisplayName = "";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.productivity";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "";
				PRODUCT_NAME = 1Step;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		DBF109672D9074B600BED622 /* Build configuration list for PBXProject "1Step" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DBF109902D9074B800BED622 /* Debug */,
				DBF109912D9074B800BED622 /* Release */,
				DB1965DE2DA56F1500ABE743 /* TestFlight */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DBF109922D9074B800BED622 /* Build configuration list for PBXNativeTarget "1Step" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DBF109932D9074B800BED622 /* Debug */,
				DBF109942D9074B800BED622 /* Release */,
				DB1965DF2DA56F1500ABE743 /* TestFlight */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		DB143FC12D9DA69700FCB08C /* XCRemoteSwiftPackageReference "Siren" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/ArtSabintsev/Siren.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 6.1.3;
			};
		};
		DB2887512D99287F00E1EA43 /* XCRemoteSwiftPackageReference "AlertToast" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/elai950/AlertToast";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.3.9;
			};
		};
		DB6AA2552DBD6E7000A3802C /* XCRemoteSwiftPackageReference "Flow" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/toastersocks/Flow";
			requirement = {
				kind = upToNextMinorVersion;
				minimumVersion = 1.0.0;
			};
		};
		DB87F96C2D94050A0020B046 /* XCRemoteSwiftPackageReference "SwiftUI-PullToRefresh" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/AppPear/SwiftUI-PullToRefresh";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.0.0;
			};
		};
		DB87F96D2D94080C0020B046 /* XCRemoteSwiftPackageReference "Refresh" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/wxxsw/Refresh.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.2.0;
			};
		};
		DB87FA242D96E1010020B046 /* XCRemoteSwiftPackageReference "SwipeActions" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/aheze/SwipeActions";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.1.0;
			};
		};
		DB87FA492D980EA30020B046 /* XCRemoteSwiftPackageReference "SwiftUIX" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SwiftUIX/SwiftUIX";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.2.3;
			};
		};
		DB8D19892DE3646D00DB211E /* XCRemoteSwiftPackageReference "lottie-ios" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/airbnb/lottie-ios";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 4.5.1;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		DB143FC22D9DA69700FCB08C /* Siren */ = {
			isa = XCSwiftPackageProductDependency;
			package = DB143FC12D9DA69700FCB08C /* XCRemoteSwiftPackageReference "Siren" */;
			productName = Siren;
		};
		DB2887522D99287F00E1EA43 /* AlertToast */ = {
			isa = XCSwiftPackageProductDependency;
			package = DB2887512D99287F00E1EA43 /* XCRemoteSwiftPackageReference "AlertToast" */;
			productName = AlertToast;
		};
		DB6AA2562DBD6E7000A3802C /* Flow */ = {
			isa = XCSwiftPackageProductDependency;
			package = DB6AA2552DBD6E7000A3802C /* XCRemoteSwiftPackageReference "Flow" */;
			productName = Flow;
		};
		DB87FA252D96E1010020B046 /* SwipeActions */ = {
			isa = XCSwiftPackageProductDependency;
			package = DB87FA242D96E1010020B046 /* XCRemoteSwiftPackageReference "SwipeActions" */;
			productName = SwipeActions;
		};
		DB87FA4A2D980EA30020B046 /* SwiftUIX */ = {
			isa = XCSwiftPackageProductDependency;
			package = DB87FA492D980EA30020B046 /* XCRemoteSwiftPackageReference "SwiftUIX" */;
			productName = SwiftUIX;
		};
		DB8D198A2DE3646D00DB211E /* Lottie */ = {
			isa = XCSwiftPackageProductDependency;
			package = DB8D19892DE3646D00DB211E /* XCRemoteSwiftPackageReference "lottie-ios" */;
			productName = Lottie;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = DBF109642D9074B600BED622 /* Project object */;
}
