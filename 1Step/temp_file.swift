import SwiftUI
import SwiftData
import SwipeActions

// MARK: - 子步骤相关视图
extension FocusView {
    
    /// 子步骤内容视图
    var subStepContentView: some View {
        VStack(alignment: .leading, spacing: 12) {
            if let currentSubStep = getCurrentFocusedSubStep() {
                // 检查当前子步骤是否正在动画
                let isAnimating = viewModel.animatingSubSteps[currentSubStep.id] != nil
                let shouldShowAsCompleted = currentSubStep.isCompleted && !isAnimating
                
                // 当前子步骤标题区域 - 使用与任务标题相似的样式
                HStack(spacing: 10) {
                    Button(action: {
                        // 播放完成音效
                        viewModel.playCompletionSound()
                        
                        // 触发烟花效果（大反馈）
                        let screenSize = UIScreen.main.bounds.size
                        let position = CGPoint(x: screenSize.width / 2, y: screenSize.height / 2)
                        confettiManager.trigger(at: position)
                        
                        // 触发触感反馈（大反馈）
                        UIImpactFeedbackGenerator(style: .medium).impactOccurred()
                        
                        // 完成当前子步骤并处理回退
                        if let lastStepId = viewModel.focusedSubStepPath.last {
                            print("[FocusView] 点击大标题完成子步骤，ID: \(lastStepId)")
                            viewModel.toggleSubStepCompletion(lastStepId)
                        }
                    }) {
                        // 直接使用完成状态，不受动画状态影响
                        Image(systemName: currentSubStep.isCompleted ? "checkmark.circle.fill" : "circle")
                            .font(.system(size: 24)) // 增大到与任务勾选框相同大小
                            .foregroundColor(currentSubStep.isCompleted ? 
                                           Color.green : 
                                           Color.gray)
                    }
                    
                    // 使用ZStack来实现标题和展开图标的布局
                    ZStack(alignment: .trailing) {
                        // 标题内容
                        VStack(alignment: .leading, spacing: 4) {
                            // 根据编辑状态显示不同内容
                            if editingStepId == currentSubStep.id {
                                // 编辑模式：显示文本输入框
                                TextField("子步骤内容", text: $editingText)
                                    .font(.system(size: 15))
                                    .foregroundColor(.primary)
                                    .focused($isEditingFocused)
                                    .onSubmit {
                                        saveStepEdit()
                                    }
                                    .onAppear {
                                        editingText = currentSubStep.title
                                        isEditingFocused = true
                                    }
                            } else {
                                // 显示模式：显示文本 - 使用动画状态
                                Text(currentSubStep.title)
                                    .font(.system(size: 16)) // 与行动标题保持一致
                                    .foregroundColor(shouldShowAsCompleted ? 
                                                   Color.secondary : 
                                                   Color.primary.opacity(0.8)) // 与行动标题颜色保持一致
                                    .strikethrough(currentSubStep.isCompleted, color: Color.secondary)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                                    .contentShape(Rectangle())
                                    .onTapGesture {
                                        // 点击子步骤标题显示任务详情
                                        showingTaskDetailFromBreadcrumb = true
                                    }
                                    .onAppear {
                                        print("[FocusView] 子步骤大标题显示 - 标题: \(currentSubStep.title), 完成状态: \(currentSubStep.isCompleted)")
                                    }
                                    .padding(.trailing, 30) // 始终给展开按钮留出空间
                            }
                            
                            // 编辑模式下显示保存和取消按钮
                            if editingStepId == currentSubStep.id {
                                HStack(spacing: 8) {
                                    Button("取消") {
                                        cancelStepEdit()
                                    }
                                    .font(.system(size: 14))
                                    .foregroundColor(.secondary)
                                    
                                    Button("保存") {
                                        saveStepEdit()
                                    }
                                    .font(.system(size: 14))
                                    .foregroundColor(.accentColor)
                                }
                            }
                        }
                        
                        // 展开图标（始终显示，即使没有子步骤也可以添加）
                        if editingStepId != currentSubStep.id {
                            Button(action: {
                                toggleStepExpansion(currentSubStep.id)
                            }) {
                                Image(systemName: isStepExpanded(currentSubStep.id) ? "chevron.up" : "chevron.down")
                                    .font(.system(size: 13))
                                    .foregroundColor(Color.secondary.opacity(0.5))
                                    .frame(width: 24, height: 24)
                                    .background(Color.clear)
                                    .contentShape(Rectangle())
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                }
                .padding(.horizontal, 18) // 与任务标题相同的内边距
                .opacity(shouldShowAsCompleted ? 0.5 : 1.0) // 添加透明度动画
                .animation(.easeInOut(duration: 0.8), value: isAnimating) // 添加动画
                
                // 子子步骤列表区域（只在展开时显示）
                if isStepExpanded(currentSubStep.id) {
                    // 添加分隔线
                    Rectangle()
                        .frame(height: 0.3)
                        .foregroundColor(Color.gray.opacity(0.2))
                        .padding(.horizontal, 8)
                        .padding(.top, 2)
                        .padding(.bottom, 4)
                    
                    // 如果有子子步骤，显示列表
                    if currentSubStep.hasSubSteps {
                        subSubStepsListView(for: currentSubStep)
                    }
                    
                    // 添加子子步骤输入框
                    addSubSubStepInputView(for: currentSubStep)
                }
            }
        }
    }
    
    /// 获取当前聚焦的子步骤
    func getCurrentFocusedSubStep() -> SubStep? {
        // 确保从最新的任务数据中获取
        guard let focusedId = viewModel.focusedTaskId,
              let freshTask = viewModel.taskManager.getTaskById(focusedId),
              let freshItem = freshTask.checklist?.first(where: { $0.id == viewModel.focusedChecklistItemId }),
              !viewModel.focusedSubStepPath.isEmpty else { return nil }
        
        var currentSteps = freshItem.subStepsList
        var currentStep: SubStep?
        
        for stepId in viewModel.focusedSubStepPath {
            if let step = currentSteps.first(where: { $0.id == stepId }) {
                currentStep = step
                currentSteps = step.subSteps
            } else {
                return nil
            }
        }
        
        return currentStep
    }
    
    /// 子步骤列表视图（用于小行动聚焦模式）
    func subStepsListView(for item: ChecklistItem) -> some View {
        let sortedSteps = getSortedSubSteps(item.subStepsList)
        
        return VStack(alignment: .leading, spacing: 6) {
            SwipeViewGroup {
                ForEach(sortedSteps, id: \.id) { subStep in
                    subStepRowView(subStep, item: item)
                }
            }
        }
    }
    
    /// 单个子步骤行视图
    private func subStepRowView(_ subStep: SubStep, item: ChecklistItem) -> some View {
        let isAnimating = viewModel.animatingSubSteps[subStep.id] != nil
        let shouldShowAsCompleted = subStep.isCompleted && !isAnimating
        
        return SwipeView {
            subStepContent(subStep, shouldShowAsCompleted: shouldShowAsCompleted, isAnimating: isAnimating, item: item)
        } leadingActions: { _ in
            // 空实现，禁用从左往右滑动
        } trailingActions: { context in
            subStepTrailingActions(subStep, item: item)
        }
        .swipeActionsMaskCornerRadius(0)
        .swipeActionCornerRadius(0)
        .swipeActionsStyle(.equalWidths)
        .swipeMinimumDistance(60)
        .swipeActionsVisibleStartPoint(max(0, 60))
        .swipeActionsVisibleEndPoint(max(0, 90))
        .swipeSpacing(max(0, 1))
        .swipeOffsetCloseAnimation(stiffness: max(1, 120), damping: max(1, 50))
        .swipeActionWidth(max(0, 85))
    }
    
    /// 子步骤内容视图
    private func subStepContent(_ subStep: SubStep, shouldShowAsCompleted: Bool, isAnimating: Bool, item: ChecklistItem) -> some View {
        HStack(spacing: 10) {
            subStepCheckbox(subStep)
            subStepTextContainer(subStep, shouldShowAsCompleted: shouldShowAsCompleted, item: item)
        }
        .padding(.horizontal, 22)
        .contentShape(Rectangle())
        .opacity(shouldShowAsCompleted ? 0.5 : 1.0)
        .animation(.easeInOut(duration: 0.8), value: isAnimating)
    }
    
    /// 子步骤勾选框
    private func subStepCheckbox(_ subStep: SubStep) -> some View {
        Button(action: {
            // 播放完成音效
            viewModel.playCompletionSound()
            
            // 触发触感反馈（小反馈）
            UIImpactFeedbackGenerator(style: .light).impactOccurred()
            
            // 切换完成状态
            viewModel.toggleSubStepCompletion(subStep.id)
        }) {
            Image(systemName: subStep.isCompleted ? "checkmark.circle.fill" : "circle")
                .font(.system(size: 15))
                .foregroundColor(subStep.isCompleted ? 
                               Color.green.opacity(0.8) : 
                               Color.gray.opacity(0.7))
                .frame(width: 22, height: 22)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    /// 子步骤文本容器
    private func subStepTextContainer(_ subStep: SubStep, shouldShowAsCompleted: Bool, item: ChecklistItem) -> some View {
        HStack(spacing: 6) {
            subStepTextContent(subStep, shouldShowAsCompleted: shouldShowAsCompleted)
            subStepActions(subStep, item: item)
        }
        .padding(.horizontal, 8)
        .background(
            subStep.isCompleted ? Color.clear : Color.gray.opacity(0.03)
        )
        .cornerRadius(4)
    }
    
    /// 子步骤文本内容
    private func subStepTextContent(_ subStep: SubStep, shouldShowAsCompleted: Bool) -> some View {
        Group {
            if editingStepId == subStep.id {
                // 编辑模式：显示文本输入框
                TextField("子步骤内容", text: $editingText)
                    .font(.system(size: 15))
                    .foregroundColor(.primary)
                    .focused($isEditingFocused)
                    .onSubmit {
                        saveStepEdit()
                    }
                    .onAppear {
                        editingText = subStep.title
                        isEditingFocused = true
                    }
            } else {
                // 显示模式：显示文本
                Text(subStep.title)
                    .font(.system(size: shouldShowAsCompleted ? 13 : 15))
                    .foregroundColor(subStep.isCompleted ? 
                                   .secondary.opacity(shouldShowAsCompleted ? 0.6 : 0.8) : 
                                   .primary.opacity(0.75))
                    .strikethrough(subStep.isCompleted, color: Color.secondary)
                    .lineLimit(2)
                    .truncationMode(.tail)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .contentShape(Rectangle())
                    .onTapGesture {
                        // 单击进入编辑模式（仅未完成的项目）
                        if !subStep.isCompleted {
                            startEditingStep(stepId: subStep.id, currentTitle: subStep.title)
                        }
                    }
            }
        }
    }
    
    /// 子步骤操作按钮
    @ViewBuilder
    private func subStepActions(_ subStep: SubStep, item: ChecklistItem) -> some View {
        if editingStepId == subStep.id {
            // 编辑模式下显示保存和取消按钮
            HStack(spacing: 8) {
                Button("取消") {
                    cancelStepEdit()
                }
                .font(.system(size: 14))
                .foregroundColor(.secondary)
                
                Button("保存") {
                    saveStepEdit()
                }
                .font(.system(size: 14))
                .foregroundColor(.accentColor)
            }
        } else if !subStep.isCompleted {
            // 正常状态下，未完成的子步骤显示 target 图标
            Button(action: {
                // 聚焦到这个子步骤
                viewModel.focusedSubStepPath.append(subStep.id)
            }) {
                Image(systemName: "target")
                    .font(.system(size: 14))
                    .foregroundColor(.secondary.opacity(0.6))
                    .frame(width: 20, height: 20)
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    /// 子步骤滑动删除操作
    @ViewBuilder
    private func subStepTrailingActions(_ subStep: SubStep, item: ChecklistItem) -> some View {
        // 只为未完成的项目显示删除操作
        if !subStep.isCompleted {
            SwipeAction(
                action: { 
                    // 删除子步骤
                    deleteSubStep(subStep.id, from: item)
                },
                label: { _ in
                    Image(systemName: "xmark")
                        .font(.system(size: 16))
                        .foregroundColor(.secondary)
                },
                background: { _ in
                    Color.clear
                }
            )
        }
    }
    
    /// 获取排序后的子步骤列表
    func getSortedSubSteps(_ steps: [SubStep]) -> [SubStep] {
        // 定义临时列表，根据实际状态和动画状态进行分类
        var incomplete: [SubStep] = []
        var completed: [SubStep] = []
        
        // 遍历所有子步骤，根据动画状态决定它们应该在哪个分组中
        for step in steps {
            // 检查子步骤是否正在动画
            if viewModel.animatingSubSteps[step.id] != nil {
                // 如果正在动画，保持在未完成列表中（动画期间不移动）
                incomplete.append(step)
            } else {
                // 如果没有动画，正常分类
                if step.isCompleted {
                    completed.append(step)
                } else {
                    incomplete.append(step)
                }
            }
        }
        
        // 在未完成项目中，将新增项目放在最后
        let oldIncompleteSteps = incomplete.filter { !viewModel.recentlyAddedItems.contains($0.id) }
        let newIncompleteSteps = incomplete.filter { viewModel.recentlyAddedItems.contains($0.id) }
        
        // 排序：旧的未完成 + 新增的未完成 + 已完成
        return oldIncompleteSteps + newIncompleteSteps + completed
    }
    /// 删除子步骤
    func deleteSubStep(_ stepId: UUID, from item: ChecklistItem) {
