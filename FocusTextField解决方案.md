# FocusTextField 焦点问题解决方案

## 🎯 问题总结

**核心问题**: 在递归的 NodeListView 组件中，用户快速点击不同层级的"添加一步"按钮时，需要点击两次才能获得键盘焦点。

**根本原因**: 
1. 每个 NodeListView 实例都有自己的 `@FocusState private var isAddingFocused: Bool`
2. iOS 系统的键盘焦点是全局唯一资源，多个 `@FocusState` 同时竞争导致冲突
3. 快速点击时，状态更新和视图重建的时序问题导致焦点丢失

## 💡 解决方案架构

### 核心思想：集中式焦点管理

采用**单一职责原则**和**全局状态管理**模式，创建一个全局焦点管理器来协调所有 TextField 的焦点状态。

### 架构组件

#### 1. GlobalTextFieldFocusManager (全局焦点管理器)
```swift
@MainActor
class GlobalTextFieldFocusManager: ObservableObject {
    static let shared = GlobalTextFieldFocusManager()
    @Published var activeTextFieldId: String? = nil
    
    func requestFocus(for textFieldId: String)
    func clearFocus(for textFieldId: String) 
    func shouldBeFocused(_ textFieldId: String) -> Bool
}
```

**职责**:
- 维护全局唯一的活跃 TextField ID
- 确保同一时间只有一个 TextField 获得焦点
- 提供焦点请求和清除的统一接口

#### 2. ManagedTextField (受管理的文本输入框)
```swift
struct ManagedTextField: View {
    let textFieldId: String
    @StateObject private var globalFocusManager = GlobalTextFieldFocusManager.shared
    @FocusState private var isFocused: Bool
}
```

**职责**:
- 封装标准 TextField 并添加全局焦点管理逻辑
- 监听全局焦点状态变化，同步本地 `@FocusState`
- 在视图生命周期中正确管理焦点

### 工作流程

1. **焦点请求**: 用户点击"添加一步"按钮
   ```swift
   GlobalTextFieldFocusManager.shared.requestFocus(for: textFieldId)
   ```

2. **全局协调**: 管理器清除当前活跃焦点，设置新的活跃 TextField
   ```swift
   activeTextFieldId = textFieldId  // 触发 @Published 更新
   ```

3. **本地同步**: 所有 ManagedTextField 监听全局状态变化
   ```swift
   .onChange(of: globalFocusManager.activeTextFieldId) { _, newActiveId in
       isFocused = (newActiveId == textFieldId)
   }
   ```

4. **焦点生效**: 只有匹配的 TextField 获得焦点，其他自动失去焦点

## 🔧 实现细节

### 唯一标识符生成
```swift
// 添加节点的 TextField
let textFieldId = "adding_\(parentNodeId.uuidString)"

// 编辑节点的 TextField  
let textFieldId = "editing_\(nodeId.uuidString)"
```

### 便利方法
```swift
extension ManagedTextField {
    static func forAddingNode(to parentNodeId: UUID, text: Binding<String>) -> ManagedTextField
    static func forEditingNode(_ nodeId: UUID, text: Binding<String>) -> ManagedTextField
}
```

### 生命周期管理
- **onAppear**: 检查是否应该获得焦点
- **onDisappear**: 清除焦点状态
- **onChange**: 双向同步全局和本地焦点状态

## ✅ 解决的问题

1. **竞争条件消除**: 全局管理器确保焦点的原子性操作
2. **状态一致性**: 单一数据源避免状态不同步
3. **时序问题**: 通过 `@Published` 机制确保正确的更新顺序
4. **快速点击**: 每次点击都会立即清除旧焦点，设置新焦点

## 🎨 使用方式

### 在 NodeListView 中使用
```swift
// 替换原来的 TextField
ManagedTextField.forAddingNode(
    to: parentNode.id,
    text: $newNodeTitle,
    placeholder: "输入内容...",
    onSubmit: { saveNewNode(to: parentNode) }
)

// 请求焦点
private func startAddingNode(to parentNode: Node) {
    addingToNodeId = parentNode.id
    let textFieldId = "adding_\(parentNode.id.uuidString)"
    GlobalTextFieldFocusManager.shared.requestFocus(for: textFieldId)
}
```

## 🧪 测试验证

创建了 `FocusTestView` 来验证解决方案:
- 多个 ManagedTextField 实例
- 快速点击测试
- 焦点状态可视化
- 手动焦点控制

## 📈 优势

1. **符合最佳实践**: 
   - 单一职责原则
   - 全局状态管理
   - 组件封装

2. **可维护性强**:
   - 清晰的职责分离
   - 简单的 API 接口
   - 易于调试和测试

3. **性能优化**:
   - 避免多个 `@FocusState` 竞争
   - 减少不必要的状态更新
   - 优化用户体验

4. **扩展性好**:
   - 可以轻松添加新的 TextField 类型
   - 支持更复杂的焦点管理逻辑
   - 与现有架构无缝集成

## 🔮 未来改进

1. **焦点历史**: 记录焦点切换历史，支持撤销操作
2. **智能焦点**: 基于用户行为预测下一个焦点位置
3. **动画效果**: 添加焦点切换的视觉反馈
4. **键盘导航**: 支持 Tab 键在 TextField 间切换

---

这个解决方案彻底解决了递归组件中的焦点竞争问题，提供了稳定、可靠的用户体验。
