# Xcode
.DS_Store
*.xcworkspace
*.xcodeproj/project.xcworkspace
*.xcodeproj/xcuserdata
build/
DerivedData/
**/xcuserdata

# Swift Package Manager
.swiftpm
.checkouts
Packages/
.build/

# AppCode
.idea/

# CocoaPods
Pods/
Podfile.lock

# Carthage
Carthage/Build/

# Fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output

# Environment
.env
.env.local
.env.development.local
.env.test.local
*.secret

# Logs
*.log
logs/

# Editors
.vscode/
.history/

# System
*.swp
*.swo
*~
*.orig

# Localized
.localized

# Dependencies
Carthage/Checkouts
Carthage/Build

# App specific
*.xcodeproj/project.xcworkspace
*.xcodeproj/xcuserdata



.history
.vscode
.idea
