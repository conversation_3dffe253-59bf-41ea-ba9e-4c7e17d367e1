commit c06651a6e33777146497e98043d8aa387569dcb2
Author: absir1949 <<EMAIL>>
Date:   Wed May 28 20:42:28 2025 +0800

    修复点击添加一步焦点要点两下的问题

diff --git a/1Step/1Step/ViewModels/Managers/FocusManager.swift b/1Step/1Step/ViewModels/Managers/FocusManager.swift
index b3b9231..efdd528 100644
--- a/1Step/1Step/ViewModels/Managers/FocusManager.swift
+++ b/1Step/1Step/ViewModels/Managers/FocusManager.swift
@@ -215,11 +215,8 @@ class FocusManager: ObservableObject {
             let checklistItemId = pathBeforeDeepening[1]
             let subStepPathArray = pathBeforeDeepening.count > 2 ? Array(pathBeforeDeepening.dropFirst(2)) : []
             setLastFocusPath(for: rootTaskId, checklistItemId: checklistItemId, subStepPath: subStepPathArray)
-            print("[FocusManager LOG] focusDeeper: Recorded last focus path for task \(rootTaskId) - checklistItem: \(checklistItemId), subSteps: \(subStepPathArray)")
         }
         
-        print("[FocusManager] 更新后focusPath: \(focusPath)")
-        print("[FocusManager] 当前展开节点: \(expandedNodes)")
     }
     
     /// 退出聚焦
@@ -239,7 +236,6 @@ class FocusManager: ObservableObject {
             let checklistItemId = pathBeforeExit[1]
             let subStepPathArray = pathBeforeExit.count > 2 ? Array(pathBeforeExit.dropFirst(2)) : []
             setLastFocusPath(for: rootTaskId, checklistItemId: checklistItemId, subStepPath: subStepPathArray)
-            print("[FocusManager LOG] exitFocus: Recorded last focus path for task \(rootTaskId) - checklistItem: \(checklistItemId), subSteps: \(subStepPathArray)")
         }
     }
     
@@ -272,7 +268,6 @@ class FocusManager: ObservableObject {
                 let checklistItemId = newFocusPathAfterExit[1]
                 let subStepPathArray = newFocusPathAfterExit.count > 2 ? Array(newFocusPathAfterExit.dropFirst(2)) : []
                 setLastFocusPath(for: rootTaskId, checklistItemId: checklistItemId, subStepPath: subStepPathArray)
-                print("[FocusManager LOG] exitToLevel: Recorded last focus path for task \(rootTaskId) - checklistItem: \(checklistItemId), subSteps: \(subStepPathArray)")
             }
         }
     }
diff --git a/1Step/1Step/Views/FocusView/Components/NodeListView.swift b/1Step/1Step/Views/FocusView/Components/NodeListView.swift
index 9d5136b..d826181 100644
--- a/1Step/1Step/Views/FocusView/Components/NodeListView.swift
+++ b/1Step/1Step/Views/FocusView/Components/NodeListView.swift
@@ -156,11 +156,8 @@ struct NodeListView: View {
                         .onSubmit {
                             saveNewNode(to: parentNode)
                         }
-                        .onAppear {
-                            isAddingFocused = true
-                        }
                         .padding(.vertical, 8)
-                        .padding(.leading, 8) // 与NodeView中文本容器的左边距保持一致
+                        .padding(.leading, 8)
                         .padding(.trailing, 8)
                     
                     // 操作按钮 - 只在有内容时显示
@@ -283,7 +280,11 @@ struct NodeListView: View {
     private func startAddingNode(to parentNode: Node) {
         addingToNodeId = parentNode.id
         newNodeTitle = ""
-        // 焦点将在TextField的onAppear中设置
+        
+        // 确保焦点设置在渲染后执行
+        DispatchQueue.main.async {
+            isAddingFocused = true
+        }
         
         // 触感反馈
         UIImpactFeedbackGenerator(style: .light).impactOccurred()
@@ -455,14 +456,10 @@ struct FocusedNodeTreeView: View {
         .scrollIndicators(.hidden)
         .keyboardAdaptive()
         .onAppear {
-            print("[FocusedNodeTreeView] 显示节点: \(currentNodeId?.uuidString ?? "nil")")
-            print("[FocusedNodeTreeView] 当前focusPath: \(focusManager.focusPath)")
+
         }
         .onChange(of: focusManager.focusPath) { oldValue, newValue in
-            print("[FocusedNodeTreeView] focusPath 变化: \(oldValue) -> \(newValue)")
-            if let currentId = newValue.last {
-                print("[FocusedNodeTreeView] 当前节点ID: \(currentId)")
-            }
+
         }
     }
     
