
⸻

📄 任务快速输入语法规范 · v1.4

⸻

🎯 目标

在自然语言式输入中，自动识别任务的各类属性（标签、项目、状态、耗时、重要性、备注），用一行文字完成复杂任务创建，最大限度降低操作心智负担。

⸻

🧱 结构概览

每条任务输入可包含以下元素：

任务内容 + 状态标记 + 项目标记 + 标签标记 + 耗时标记 + 重要性标记 + 备注



⸻

🔤 输入语法一览

类型	符号	示例	说明
标签	#标签	#写作 #晨间任务	可多个，顺序不限
项目	!项目	!1Step !产品开发	每任务仅一个，建议放后
状态	> = *	> 下一步、= 等待中、* 将来也许	默认无标记为收集箱 Inbox
耗时	~时间	~30 ~1.5h	单位分钟或小时
重要性	^	^ 放在任意位置	标记任务为重要
备注	//备注	// 备注内容	// 后的所有内容作为备注，建议写在句尾



⸻

✅ 示例解析

基本示例

写产品文档 ~45 > !工作 #深度工作 ^ // 中午后写，注意格式

	•	标题：写产品文档
	•	耗时：45 分钟
	•	状态：下一步
	•	项目：工作
	•	标签：深度工作
	•	重要：是
	•	备注：中午后写，注意格式

⸻

📌 语法规则说明

项	说明
顺序	所有标记顺序可自由排列，系统自动解析
容错	中文 ～ 等同于英文 ~，均识别为耗时标记
标签 / 项目	不可重复，系统自动去重
备注	使用 //，为任务的扩展描述，不参与标题解析
多余文本	无标记部分为标题主体



⸻

🧰 存储字段建议

@Model class Task {
  var title: String
  var tags: [String]
  var project: String?
  var status: TaskStatus // Inbox / Next / Waiting / SMB
  var estimatedTime: Int? // 单位：分钟
  var isImportant: Bool
  var note: String? // ✅ 备注内容
}



⸻

🌱 产品理念匹配

一句话生成结构化任务，极简、灵活、向下兼容。
高阶用户自愿使用进阶语法，初阶用户也无需学习即可上手。

⸻

✅ 最终语法标记表

类型	符号	示例
标签	#	#设计 #晨间任务
项目	!	!1Step !工作日志
状态	> = *	> 下一步，= 等待中，* 将来也许
耗时	~	~30 → 30 分钟，~2h → 2 小时
重要性	^	表示重要任务
备注	//	// 这是一个备注



