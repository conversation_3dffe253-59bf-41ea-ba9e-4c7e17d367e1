# 1Step · 核心术语表（glossary.md）

> 所有的词，都是我们系统的一部分思维方式。

---

## 🧠 状态模型相关

| 术语 | 含义 | 常驻位置 |
|------|------|------------|
| Inbox（收集箱） | 所有初始添加的任务默认进入此处，等待判断 | 下一步页底部 / 浏览页 |
| NA（下一步） | 可执行任务池，是行动推进的主列表 | 下一步页 |
| Doing（一步） | 当前专注推进的 1~3 个任务 | 一步页 |
| Waiting（等待中） | 暂无法推进，等待事件/他人完成的任务 | 下一步页（黄色）/ 浏览页 |
| SMB（将来也许） | 并非当下要做，但未来可能重要的任务 | 浏览页 |
| Done（已完成） | 已完成的任务归档区 | 浏览页 |

---

## 📄 任务字段相关

| 字段名 | 说明 |
|---------|------|
| title | 任务标题，简洁明了 |
| notes | 任务笔记，可补充细节说明、链接、描述等 |
| checklist | 子任务清单，仅在详情页中展示 |
| tags | 自定义标签，用于筛选、归类上下文 |
| project | 所属项目，简单归类用，无层级结构 |
| status | 当前任务状态（见上方） |
| created_at / updated_at | 任务的创建与修改时间 |
| attachments（预留） | 附件字段，可扩展用于文件、图片等资源链接 |

---

## 🧩 页面术语

| 页面名 | 功能描述 |
|---------|----------|
| 一步页（1StepTab） | 展示当前一步的任务，是行动的舞台 |
| 下一步页（NextActionsPage） | 展示可推进任务（NA），以及 Inbox / Waiting |
| 浏览页（BrowsePage） | 浏览其他状态清单 + 标签项目管理入口 |
| 任务详情页（TaskDetailView） | 查看/编辑任务的完整信息 |
| 添加任务弹窗（TaskCreateModal） | 快速添加任务入口 |
| 设置页（SettingsPage） | 数据导出/主题切换/未来账号登录等设置 |

---

## 🧭 核心理念词

| 术语 | 定义 |
|------|------|
| 只做一步（1Step） | 不设今天/本周/计划，只专注于下一步行动 |
| 弱结构 | 不强制分类、不做层级，全部以状态推进为主 |
| 非暴力任务管理 | 不打卡、不提醒、不制造启动压力 |
| 表演区 / 候场区 | 一步页是表演区，下一步页是候场准备区 |

---

> 语言即系统，概念即界面。
> 让我们的话语，成为行动的催化剂。