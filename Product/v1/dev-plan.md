# 1Step · MVP 开发计划（dev-plan.md）

> 我们不是为了构建一个庞大的系统，而是为了打造一个“能用起来”的第一步。

---

## 🎯 开发目标（v0.1）

- 实现一个移动端可用、功能闭环的 GTD 工具（React Native App）
- ✅ MVP 阶段仅支持 iOS（使用 Expo 测试与发布）
- 聚焦“一步 + 下一步 + 浏览”三大页面
- 所有数据保存在本地（AsyncStorage）
- 使用 AI 协作完成界面渲染与组件生成
- 用户可完整添加任务、管理状态、使用基本筛选

---

## 🧩 技术架构（React Native 方案）

- **开发框架**：React Native（使用 Expo 启动）
- **数据方案**：AsyncStorage（或后期切换 SQLite）
- **组件库**：NativeBase / Tamagui / 自定义组件
- **AI 协作工具**：Cursor、ChatGPT、Claude 用于代码生成、逻辑推演

---

## ✅ MVP 功能清单

### ✅ 必做功能（v0.1）
| 模块 | 功能 |
|------|------|
| 添加任务 | 支持快速添加，默认进入 Inbox |
| 状态管理 | 可从 Inbox → 下一步、下一步 → 一步、完成 |
| 一步页 | 展示最多 3 个任务，支持勾选完成、移出 |
| 下一步页 | 展示 NA、Inbox、Waiting 三种状态 |
| 浏览页 | 展示状态卡片、项目、标签列表 |
| 筛选功能 | 标签 / 项目筛选任务 |
| 任务详情 | 编辑标题、笔记、checklist、切换状态 |

### 🕗 可延后功能
| 模块 | 描述 |
|------|------|
| 云端同步 | 登录账号、跨设备同步 |
| 重复任务 | 每日 / 每周任务设定 |
| 附件上传 | 文件拖拽或关联 |
| AI 推荐 | Copilot / 智能聚焦建议 |
| 设置页 | 数据导出 / 导入、主题切换 |

---

## 🚧 开发阶段规划

### 阶段一：界面搭建（Day 1~3）
- 页面结构：底部导航栏 + 三大页面框架
- 完成任务列表展示逻辑（使用 FlatList 等）

### 阶段二：状态流转逻辑（Day 4~6）
- 实现任务状态切换 / 拖动 / 勾选行为
- 一步页限制为 3 项，规则完整

### 阶段三：详情页 + 筛选功能（Day 7~9）
- 添加任务详情页（可编辑笔记、项目、标签）
- 实现筛选与搜索

### 阶段四：数据结构固化与导出（Day 10）
- 封装 AsyncStorage 数据管理逻辑
- 提供导出 JSON 功能（用于迁移）

---

## 🧠 AI 协作方式

- 页面原型：通过 AI 帮助生成 UI 草图（基于 React Native）
- 代码结构：使用 Cursor / GPT 辅助组件编码
- 数据建模：状态逻辑 / 存储行为 / 架构归因均交给 AI 提案

---

> 1Step 的第一步，就是走到你手机上的那一步。
> 极简，轻盈，真正落地，从 iOS 开始。