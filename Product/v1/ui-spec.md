# 1Step · UI 设计规范（ui-spec.md）

> 极简但不冷漠，克制但不压迫 —— 所有设计皆为“推进一小步”而生。

---

## 🎨 基础配色系统

| 元素 | 颜色说明 |
|------|----------|
| 主色 | #3B6EF6（行动蓝）|
| 一步背景 | 纯白，保持专注感 |
| 下一步任务（NA） | 黑色文字 + 默认白底 |
| 等待中任务（Waiting） | 浅黄色背景（#FFF9E6）+ 灰色文字 |
| 收集箱任务（Inbox） | 浅灰背景（#F5F5F5）+ 浅灰文字 |
| 将来也许（SMB） | 隐藏，单独浏览页查看 |
| 已完成任务 | 文字浅灰 + 勾选框置灰 |

---

## ✅ 通用交互行为

### 勾选完成
- 一步页任务支持勾选 ✅，打勾即进入 DONE
- 下一步页不展示勾选框，防止压力
- Inbox、Waiting 等状态任务不支持直接打勾，需切换状态后完成

### 左滑操作
- 所有任务列表支持左滑：
  - Inbox → 移入 NA / SMB / 删除
  - NA → 完成 / 移入一步
  - 一步页 → 移出一步 / 设为等待中

### 添加任务按钮（➕）
- 固定在「下一步页」底部中央
- 点击打开 `TaskCreateModal`
- 默认创建任务状态为 Inbox

### 页面滑动逻辑
- 下一步页：往下滑动展示 Inbox 内容
- 浏览页：默认只展示状态卡片，项目 / 标签折叠展开

---

## 📦 任务卡片布局

- 主体：标题（加粗） + 项目 / 标签列表
- 状态标记：以背景色表达，不使用 icon 堆叠
- 重要任务：可置顶，置顶任务始终显示在最上方

---

## 🧭 状态可视性逻辑

| 状态 | 主界面可见性 | 显示颜色 | 可否勾选 |
|------|----------------|------------|------------|
| Doing | ✅ 一步页 | 白底 | ✅ |
| NA | ✅ 下一步页 | 白底 | ❌ |
| Waiting | ✅ 下一步页 | 浅黄 | ❌ |
| Inbox | ✅ 下一步页 | 浅灰 | ❌ |
| SMB | ❌ 一步页隐藏，仅浏览页可见 | 无 | ❌ |
| Done | ❌ 默认隐藏，仅浏览页可见 | 浅灰 | ❌ |

---

## ✨ 动效建议（可选）
- 勾选任务时：轻微缩放 + 打钩动效，提供「完成感」
- 左滑菜单弹出：流畅滑入 + 阴影浮层
- 切换一步任务：淡入淡出，强化“进入状态”的仪式感

---

> 1Step 的 UI 要让人感觉：任务不多，行动不重，永远可以再走一步。
> 极简不是为了好看，而是让行动更轻。