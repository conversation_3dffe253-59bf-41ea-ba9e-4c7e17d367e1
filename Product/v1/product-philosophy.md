# 1Step · 产品哲学

> Just the next 1Step.

---

## 我们为什么要做 1Step

现在的任务管理工具太多了。
但我们常常遇到这样的情况：

- 任务越空越多，却越条理不清
- 系统越用越累，我们不想再打开
- 几天没管，任务就崩溃了，心理压力越来越大

为什么？因为那些系统是基于“计划”进行的，但人类的生活本身就是混乱的。
一旦计划碎裂，整个任务系统就失效了。

而我们真正需要的，不是管理任务，而是——把任务做掉。

---

## 我们想解决的问题
🎯 核心目标：

在最小认知负担下，持续推进真正重要的行动

🧠 要解决的本质问题：

行动阻力过大：任务太多、不清晰、不知从哪开始

行动焦虑严重：今天做不完、过期任务堆积、计划失败

结构负担沉重：项目层级复杂、分类冗余、维护成本高



1Step 就是一个缓冲区，让你随时回到“作业状态”。

---

## 和其他工具的根本不同

| 维度 | 常见任务工具 | 1Step 的做法 |
|------|----------------|----------------|
| 时间维度 | 以“今天 / 本周”为中心 | 完全放弃时间，转为状态流 |
| 列表压力 | 强提醒、过期任务焦虑 | 不提醒、不压迫、不崩溃 |
| 管理方式 | 多层项目 + 多标签 + 分区 | 极简结构 + 单层判断 |
| 动作节奏 | 清空清单为目标 | 推进一小步为目标 |
| 产品哲学 | “完成更多” | “一步一事” |

---

## 我们的设计思路

### “下一步”，是唯一需要装备的东西

- 不必看全部，不必想明天，只做下一步
- 用户在系统里观看到的，是“我当前在做什么”和“我等你做什么”

### 强调行动，弱化管理

- 系统不能成为重启的阴影
- 所有功能最终仅有一个目的：让你把任务做掉

### 抽离时间和日程，保持轻量进入

- 不再分什么“今天、明天、过期”
- 不再定时、不提醒，完全让用户自主进入

### 设计不是为了“办事”，而是让行动更容易发生

- 系统不提醒你，但始终在那里等你
- 任何一个时刻回到，一点即照，同样可用
- 别让系统控制你，要让你用得动、用得轻

---

## 一句话总结

> 你不需要管理任务，你只需要做掉下一步。