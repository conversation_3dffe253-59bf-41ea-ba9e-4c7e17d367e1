# 1Step · 任务模型定义（task-model.md）

> 这是整个系统的“最小认知单元”定义。任务是如何存在的、如何流转的，决定了系统是否轻盈、可控。

---

## 🧱 任务核心结构（Task Schema）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `id` | string | 唯一任务标识符 |
| `title` | string | 任务标题，建议简洁明了 |
| `status` | enum | 当前状态：inbox / na / doing / waiting / smb / done |
| `project` | string? | 所属项目（可选） |
| `tags` | list[string] | 标签数组，用于上下文筛选（可为空） |
| `checklist` | list[string]? | 子步骤列表（仅任务详情页展示） |
| `created_at` | datetime | 创建时间 |
| `updated_at` | datetime | 最后修改时间 |
| `archived` | boolean | 是否已归档（仅 done 状态任务可归档） |
| `notes` | string? | 备注，用于记录思考、灵感、备注 |
| `attachments` | list[string]? | 附件列表，用于记录图片、视频、音频等 |
| `history` | list[string]? | 历史记录，用于记录任务的流转过程 |

---

## 🔁 状态模型定义（Task State Machine）

### 六个主状态：
- `inbox`：收集箱，所有新任务默认进入此处
- `na`（next action）：下一步，可直接推进的任务列表
- `doing`：一步，当前正在推进的 1~3 个任务
- `waiting`：等待他人 / 事件，不可立即推进
- `smb`（someday/maybe）：将来也许，未来可能做的事
- `done`：已完成任务

---

## 🔄 状态流转逻辑

```mermaid
flowchart LR
    INBOX[Inbox]
    NA[下一步 NA]
    DOING[一步 Doing]
    WAITING[等待中]
    SMB[将来也许 SMB]
    DONE[已完成 Done]

    INBOX --> NA
    INBOX --> SMB
    NA --> DOING
    NA --> SMB
    NA --> DONE
    DOING --> DONE
    DOING --> NA
    WAITING --> NA
    SMB --> NA
```

---

## ✅ 状态说明及规则

### Inbox
- 任务默认入口，用户快速捕捉想法
- 推荐尽快处理：移入 NA / SMB / 删除

### NA（下一步）
- 可执行任务池，支持筛选、标签、项目
- 支持左滑完成（进入 DONE）
- 可拉入 DOING（一步）

### Doing（一步）
- 最多 3 个任务
- 完成后自动进入 DONE
- 可主动移出回 NA

### Waiting
- 等待中，不提醒
- 不进入主列表，仅在下一步/浏览中查看

### SMB（将来也许）
- 永不提醒，不显示在主界面
- 可随时浏览，随时恢复为 NA

### Done
- 已完成任务，可归档
- 可通过搜索检索，记录行动历史

---

## 📌 补充说明

- **没有“今天”、“本周”等时间字段**，避免时间焦虑
- **不支持任务开始/截止时间、优先级**，一律用状态表达紧急性
- **不支持子任务嵌套**，仅 checklist 提供简单拆解
- **所有任务由用户主观驱动，不自动流转，不推送提醒**

---

> 1Step 的任务模型，不是为了管住你，而是为了让你能轻松推进下一步。

