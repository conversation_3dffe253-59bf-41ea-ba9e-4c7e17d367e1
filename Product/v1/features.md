# 1Step · 功能边界定义（features.md）

> 明确我们要做什么，也更重要地，明确我们不做什么。

---

## ✅ 支持的功能（Must Have）

| 功能 | 说明 |
|------|------|
| ✅ 添加任务 | 通过收集箱快速添加，不要求结构 |
| ✅ 任务状态划分 | 支持 Inbox、NA、Doing、Waiting、SMB、Done |
| ✅ 状态流转 | 用户可手动切换任务状态（从 Inbox → NA、NA → Doing 等） |
| ✅ 任务完成勾选 | 支持直接完成任务（Doing / NA 可勾选） |
| ✅ 标签系统 | 支持自定义标签，作为上下文筛选依据 |
| ✅ 项目支持 | 项目为轻量结构，无嵌套，仅用于组织任务 |
| ✅ 一步上限限制 | Doing 限制最多 3 个，支持添加/清空机制 |
| ✅ 搜索与筛选 | 支持按关键词、标签、项目筛选任务 |
| ✅ 浏览页结构 | 明确展示任务的五大状态、项目和标签分类 |
| ✅ 本地数据存储 | MVP 阶段使用本地存储（可切换 DB） |

---

## ❌ 明确不做（Not Doing）

| 功能 | 理由 |
|------|------|
| ❌ 日历/时间调度 | 会引入时间压力，不符合“无时间焦虑”理念 |
| ❌ 到期提醒 / 通知 | 不制造压力，不进行系统性催促 |
| ❌ 自动任务推荐 | 用户是唯一判断者，AI 不参与推荐 |
| ❌ 多层级项目结构 | 层级越多，越难维护，增加管理负担 |
| ❌ 重复任务 | 依赖时间结构，后续如有需与日历一并考虑 |
| ❌ 今日计划/每日清单 | 不设“今天”概念，避免计划破产焦虑 |
| ❌ 习惯追踪 / 打卡 | 不制造任务系统之外的成就负担 |
| ❌ 任务积分 / 激励机制 | 不 gamify，不引导“为完成而完成” |

---

## 🟡 可选/未来考虑功能（Future Maybe）

| 功能 | 条件或说明 |
|------|------------|
| 🟡 轻提醒 / 温和 nudges | 可通过“等待中”任务轻微提示用户（非 push） |
| 🟡 重复任务逻辑 | 仅在后续支持日历模块后统一考虑 |
| 🟡 AI 协助判断 | 仅作为提示或助手，不直接决策任务状态 |
| 🟡 多设备同步 / 云端 | MVP 阶段为本地，后续可接入云服务 |

---

## 🧭 功能设计原则（回顾）

- 所有功能必须服务于“推进行动”
- 能去掉的，就去掉；能晚加的，就晚加
- 用户心智必须极低 —— 无需维护、无需解释、无需计划

---

> 我们不是做一款功能多的工具，而是一款你能一直用下去的系统。