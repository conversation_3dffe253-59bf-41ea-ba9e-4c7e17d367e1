# 1Step · 市场分析文档

> 我们不是再做一个工具。而是做一个你不会想放弃的行动系统。

---

## 🌍 目前主流工具概览

| 工具 | 特点 | 用户病点 |
|--------|--------|--------|
| Todoist | 功能强大、支持分级、同步好 | 过期任务增加压力、重启难 |
| 滴答清单 | 做时计、打卡、成就感强 | 系统越来越重、体系化过度 |
| Notion | 自由度极高，可组合 | 入门成本高、行动链路长 |
| Apple Reminders | 原生、简洁 | 无结构，无经营深度 |

---

## ⚡️ 常见问题 / 常见弃用原因

- 计划系统一断崩溃，任务再也不想打开
- “今天、本周、必须完成” 的设计制造了自我启动压力
- 越做越重，系统成本越来越高
- 行动力与系统综合指数成倒闭关

---

## 🌟 1Step 的差异化位置

| 维度 | 主流工具 | 1Step 的做法 |
|------|---------------|------------------|
| 时间维度 | “今天、本周” | 完全放弃，用状态分组 |
| 行动入口 | 今日任务 + 提醒 | 唯一“一步”推进模型 |
| 系统规划 | 分类、分项、分级 | 单层状态、非分类经营 |
| 行为模式 | 打卡、规划、提醒 | 自我进入，非强迫行为 |
| 心态压力 | 有性能怪、任务重置压力 | 无迫作，只做一步，无心理形成 |

---

## ᾞd 我们的方向：一个不会崩溃的系统

- 不用定时、不用分级、不用统计
- 不用给出日程，你也知道这件事很重要
- 不用总结、不用打卡，你只需要继续

> 1Step 不是一个更好的系统，是一个你能一直用下去的系统。

