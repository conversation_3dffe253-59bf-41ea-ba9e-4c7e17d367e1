# 1Step · 用户分析文档

> 我们的系统，是做给那些真正想行动、却被系统抽离的人。

---

## Ἷ 目标用户人像

### ᾝ1‍💻 用户类型 01：独立工作者 / 运营者 / 创作者

- 年龄：25-40岁
- 职业：知识型、自由职业者、创业者、单飞企业高效率人士
- 功养：INTJ / INTP / 无闲人 / 负责任感强

### 需求特徴

| 需求 | 说明 |
|--------|------|
| 行动系统 | 需要一个轻量、稳定、可信的进入工作状态的系统 |
| 独立决策权 | 不喜欢被 AI / 提醒控制行动，更希望系统是“被我使用的” |
| 抽象观念 | 喜欢 GTD / flomo / Zettelkasten 这类有哲学性的系统 |
| 行动阶段性 | 每天不想完成全部事，只想做一段的事 |

🧑‍🎨 用户 02：感性创作者 / 文艺型执行者（次目标用户）
	•	年龄：20~35，女性为主，MBTI 倾向 INFP / ISFP
	•	职业：自由职业写作者、插画师、内容创作者
	•	行为特征：
	•	任务不多，但进入状态很难
	•	对“轻提醒”和“温柔界面”反应良好
	•	不接受压力型工具，但又渴望一点结构辅助
	•	偏好：
	•	喜欢页面干净简约
	•	不喜欢太多术语，希望界面像朋友说话一样

她们不是 GTD 重度用户，但也不是完全无结构 ——
1Step 能成为她们“灵感与行动”的过渡带。

⸻

🧑‍🔬 用户 03：理性知识工作者 / 轻重度切换者（拓展型用户）
	•	年龄：30~45，产品经理 / 科研人员 / 独立开发者
	•	行为特征：
	•	使用 Notion/Todoist多年，但疲于维护结构
	•	对系统性认知强，但在行动上常常掉队
	•	任务多，但需要一个轻起步的地方
	•	痛点：
	•	常陷入“维护系统”和“真正推进”的冲突中
	•	想从 GTD 再进化为“只做能做的事”的方法论
---

## ᾞ0 行为特征分析

### 有 GTD 经历，但系统常废
- 用过多个工具：Todoist / Notion / 笔记应用
- 常因任务崩溃、重启无望而放弃

### 不喜欢“强推动系统”
- 非成就型，对成就感、打卡、统计不感兴趣
- 更关注“环境、节奏、行动本身”

### 喜欢有力的“一步”
- 不愿忙于数据、任务重置、设计
- 更喜欢一种无压力、可以往前的小步奏

---

## ❌ 非目标用户

- 刚接触任务管理的初级用户
- 依赖日程、提醒、成就感系统的用户
- 写了就必须完成、不容迟迟延延的用户
- 事务型群体（如团队成员、合作组织）

---

## Ὂc 展望：我们最合适的用户，可能没有在用任何系统

- 他们可能用纸和笔进行行动划分
- 他们有一种专注的精神，但定心系统不好用
- 他们不喜欢任何需要被教育、规划、配置、调教的系统

> 他们需要的，是一个无声带行的系统。

