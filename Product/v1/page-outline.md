# 1Step · 页面结构文档（page-outline.md）

> 明确每一个页面存在的意义，决定我们如何让任务“推进而不是管理”。

---

## 🧭 页面总览

1. 一步页（1Step / Doing/ 1Step）
2. 下一步页（Next Actions / NA + Waiting + Inbox）
3. 浏览页（Browse / 状态分类 + 项目 + 标签）
4. 任务详情页（Task Detail）
5. 新建任务弹窗（Quick Add）
6. 设置页（Settings）

---

## ✅ 页面详情

### 1. 一步页（1StepTab）
- 展示一步的 1~3 个任务
- 支持勾选完成
- 支持左滑移除出一步
- 若不足 3 个，展示“添加一步任务”按钮（从 NA 中选）
- 最顶部文案传达“你现在正在专注的事”

### 2. 下一步页（NextActionsPage）
- 最前面列表展示所有 `NA` 状态任务（正常色）
- 接着列表展示所有 `Waiting` 状态任务（浅黄色）
- 接着列表展示所有 `Inbox` 状态任务（浅灰色）
- 支持搜索、筛选（项目、标签）
- 支持置顶任务（提升重要感）
- 页面底部中央为「➕」按钮，添加任务（默认进入 Inbox）

### 3. 浏览页（BrowsePage）
- 顶部搜索框：支持搜索任务、项目、标签
- 四个状态模块（卡片显示）
  - 收集箱 Inbox
  - 将来也许 SMB
  - 等待中 Waiting
  - 已完成 Done
- 下方为项目模块、标签模块：
  - 默认收起，点击展开
  - 可点进查看该项目/标签下任务

### 4. 任务详情页（TaskDetailView）
- 展示任务标题、项目、标签
- 支持编辑任务标题 / notes / checklist
- 支持切换状态、移动项目 / 标签
- 支持删除任务

### 5. 新建任务弹窗（TaskCreateModal）
- 快速输入任务标题（必填）
- 可填写任务备注（notes）
- 可指定项目、标签、状态（默认 Inbox）

### 6. 设置页（SettingsPage）
- 数据导出 / 备份 / 恢复（本地）
- 应用版本信息
- 未来支持：登录账号、云同步、主题切换等

---

## 🧩 页面命名建议（用于开发）

| 页面 | 建议命名 |
|------|-----------|
| 一步页 | `1StepTab` / `1StepPage` |
| 下一步页 | `NextActionsPage` |
| 浏览页 | `BrowsePage` |
| 任务详情页 | `TaskDetailView` |
| 添加任务 | `TaskCreateModal` |
| 设置页 | `SettingsPage` |

---

> 每个页面都必须为“推进行动”服务。任何不推行动的页面，都不该存在。

