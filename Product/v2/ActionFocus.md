

**PRD Section: 行动焦点 (Action Focus) - 核心交互与界面需求 (1Step V2)**

**1. 核心概念与目标 (Core Concept & Objective)**

* **1.1. 行动焦点定义：** “行动焦点”是用户在1Step中进行深度、分层行动处理的核心交互机制。它允许用户从“一步 (1Step)”列表（主界面）选择一个父行动进入其专属的“一步模式 (1Step Mode)”，并在该模式下，通过动态移动“焦点”，理论上可以无限下钻和拆解子步骤，界面始终清晰地显示“当前焦点行动”及其“直接下一级子行动/子步骤”。
* **1.2. 核心目标：**
    * 实现极致的“只迈出一小步”和“活在当下这一步”的体验。
    * 支持用户对复杂行动进行无限的、即时的、上下文清晰的“逐步求精”和拆解。
    * 在提供强大拆解能力的同时，通过面包屑导航保持用户的方向感，避免迷失。
    * 创造一种独特的、流动的、高度专注的行动执行环境。

**2. 进入“父行动的一步模式 (1Step Mode)” (第一层聚焦)**

* **2.1. 唯一触发入口：**
    * 在“一步 (1Step)”列表（主界面）中，对任一行动项执行**左滑操作**，然后点击出现的**“聚焦 (`🎯`)”按钮**。
* **2.2. 界面转换与呈现：**
    * **动态聚焦效果：** 当前“一步 (1Step)”列表发生“原地转换”或“动态聚焦”。被选中的父行动项在列表中的位置“扩展”或“放大”，成为屏幕的视觉和交互中心。列表中的其他所有父行动项则通过平滑的动画“淡出”、“缩小并移出视野”或以极度弱化的形式隐藏。（已实现）
    * **顶部导航栏变化：**
        * 原顶部导航栏（左上角`🔍`搜索，中间标题“1Step”，右上角`㗊`菜单）**整体被隐藏**。（已实现）
        * 新的顶部区域**只显示一个简洁的“1Step”应用标题/Logo**（或完全留空，以实现极致专注）。（已实现）
    * **主内容区（以复杂父行动为例）：**
        * **面包屑导航路径 (Breadcrumb Path)：** 位于“当前聚焦的行动标题”**下方**，即原来在“一步”列表项中可能显示“项目”提示的那个位置。使用 **`/` (斜杠)** 作为分隔符（例如：`父行动X / 小行动A1 / 更小一步A1.1`）。路径上的每一级都是可点击的，用于恢复焦点到该层级。（需实现）
        * **当前聚焦的行动标题：** 在面包屑路径的**上方**，作为当前视图最主要的标题，动态显示当前焦点所在的行动/小行动/更小一步的标题。（需实现）
        * **子行动/子步骤列表区：** 展示当前聚焦的这一“步”的下一级“子行动/子步骤”列表。每个子项前有完成勾选框，右侧有一个清晰的**“设为焦点/1Step (`◎` 靶心)”图标**。
        * 允许在此模式下为当前聚焦的这一“步”快速添加新的“子一步/子步骤”（这些“子一步”形式为简单文本+勾选框，不具备独立行动的复杂属性，直接命名为“一步”）。
    * **底部FAB按钮变化：** 原底部`✦` FAB图标和功能**转变为一个清晰的“返回 (`←` 或 `X`)”按钮。**（已实现）

**3. 在“一步模式”中移动“行动焦点”至更细层级 (第二层及后续聚焦)**

* **3.1. 触发方式：** 在当前父行动（或已聚焦的小行动）的“一步模式”界面中，用户点击其下属“子行动/子步骤列表”里某个具体子项旁边的**“设为焦点/1Step (`◎`)”按钮**。
* **3.2. 界面动态更新：**
    * **顶部主标题区域：** 从显示上一级焦点的标题，**动态更新为当前这个被聚焦的子项的标题。**
    * **面包屑导航路径更新：** 路径自动增加一级。
    * **主内容区更新：** 原先展示上一级焦点的“子行动/子步骤列表”的区域，现在变成了展示**当前这个被聚焦子项的再下一级“子步骤”列表**的区域。如果当前被聚焦的子项没有预设的“子步骤”，则这里可以提示“为此一步添加更细步骤...”或允许用户直接输入新的“一步”。
* **3.3. 理论上的“无限下钻”：** 此“聚焦子级 -> 标题更新 -> 面包屑更新 -> 内容区更新为子级的子级列表”的过程，理论上可以无限进行下去。

**4. 导航与退出机制**

* **4.1. 通过面包屑导航恢复上级焦点：** 用户可以点击面包屑路径上的任意上级名称，界面将平滑地将“行动焦点”恢复到那个层级（顶部标题、面包屑、内容区相应更新）。
* **4.2. 退出整个“一步模式”返回“一步 (1Step)”列表：**
    * **唯一的主要退出方式：** 点击界面底部由`✦` FAB转变而来的“返回 (`←` 或 `X`)”按钮。
    * 点击此底部返回按钮，**直接、完全地退出整个“一步模式”**（无论当前聚焦在哪一个层级），平滑地返回到完整的“一步 (1Step)”列表视图。
    * 支持标准的系统级返回手势，效果同上。

**5. 小行动/子步骤的“完成”对焦点的影响**

* 当用户完成“当前聚焦小行动”展示区内的行动（或其下的所有“子一步”）时，焦点**不自动**移动到下一个未完成的同级或上移。
* “当前聚焦小行动”展示区可以清空或提示完成，引导用户主动从下方列表选择下一个焦点，或通过面包屑/返回按钮调整焦点或退出。

**6. 视觉与动画**

* 所有焦点的移动、层级的转换、列表项的隐藏与恢复，都必须伴有**极其流畅、自然、有意义的过渡动画**，帮助用户理解上下文变化，避免迷失。

---

补充说明

PRD Section: 行动焦点 (Action Focus) - 核心交互与界面需求 (1Step V2)

1. 核心概念与目标 (Core Concept & Objective)

1.1. 行动焦点定义： “行动焦点”是用户在1Step中进行深度、分层行动处理的核心交互机制。它允许用户从“一步 (1Step)”列表（主界面）选择一个Task进入其专属的“一步模式 (1Step Mode)”。在该模式下，通过动态移动“焦点”，焦点可以从Task下移到其ChecklistItem，再从ChecklistItem下移到其通过JSON内嵌的“子步骤”，理论上可以无限下钻。界面始终清晰地显示“当前焦点”的标题及其“直接下一级子项”列表。
1.2. 核心目标：
实现极致的“只迈出一小步”和“活在当下这一步”的体验。
支持用户对复杂Task及其ChecklistItem进行无限的、即时的、上下文清晰的“逐步求精”和拆解。
在提供强大拆解能力的同时，通过面包屑导航保持用户的方向感，避免迷失。
创造一种独特的、流动的、高度专注的行动执行环境。
2. 进入Task的“一步模式 (1Step Mode)” (第一层聚焦)

2.1. 唯一触发入口：
在“一步 (1Step)”列表（主界面）中，对任一Task项执行左滑操作，然后点击出现的**“聚焦 (🎯)”按钮**。
2.2. 界面转换与呈现：
动态聚焦效果： 当前“一步 (1Step)”列表其他Task隐藏，被选中的Task在原地“扩展/放大”成为屏幕的视觉和交互中心。
顶部导航栏变化： 原顶部导航栏（左上角🔍搜索，中间标题“1Step”，右上角㗊菜单）整体被隐藏。新的顶部区域只显示一个简洁的“1Step”应用标题/Logo（或完全留空）。
主内容区（聚焦在Task时）：
面包屑导航路径 (Breadcrumb Path)： 位于“当前聚焦的Task标题”下方。使用 / (斜杠) 作为分隔符（例如：Task标题）。路径上的每一级都是可点击的。
当前聚焦的标题： 在面包屑路径的上方，作为当前视图最主要的标题，显示当前这个Task的标题。
ChecklistItem列表区： 展示该Task下属的所有ChecklistItem。每个ChecklistItem前有完成勾选框，右侧有一个清晰的**“设为焦点/1Step (◎ 靶心)”图标**。
允许在此模式下快速添加新的ChecklistItem到当前Task下。
底部FAB按钮变化： 原底部✦ FAB图标和功能转变为一个清晰的“返回 (← 或 X)”按钮。
3. 在“一步模式”中移动“行动焦点”至ChecklistItem及其“子步骤” (第二层及后续聚焦)

3.1. 聚焦到ChecklistItem：
触发方式： 在当前Task的“一步模式”界面中，用户点击其下属ChecklistItem列表里某个ChecklistItem A旁边的**“设为焦点/1Step (◎)”按钮**。
界面动态更新：
顶部主标题区域： 从显示Task的标题，动态更新为当前这个被聚焦的“ChecklistItem A”的标题。
面包屑导航路径更新： 路径增加一级，例如变为：Task标题 / ChecklistItem A标题。
主内容区更新： 原先展示Task的ChecklistItem列表的区域，现在变成了解析并展示“ChecklistItem A”的executionChecklist JSON字段中的“子步骤”列表的区域。每个“子步骤”前有勾选框，右侧有其自己的“设为焦点 (◎)”按钮（如果其JSON结构中的subSteps可以不为空，即允许再下一层）。
3.2. 进一步下钻到“子步骤”：
如果用户再点击某个“子步骤A.1”（来自ChecklistItem A的executionChecklist）旁边的“设为焦点 (◎)”按钮：
顶部主标题变为“子步骤A.1”的标题。
面包屑路径更新为 Task标题 / ChecklistItem A标题 / 子步骤A.1标题。
主内容区则渲染“子步骤A.1”在其JSON结构中的subSteps（即其下一级“子子步骤”列表）。
此过程理论上可以无限进行，只要JSON结构支持。
3.3. 添加新的“子步骤”：
在任何已聚焦的层级（无论是聚焦在ChecklistItem还是其下的某个“子步骤”），界面都应提供入口，允许用户为当前焦点添加新的“直接子级步骤”。这些新步骤会更新到其所属上级（ChecklistItem或某个“子步骤”）的executionChecklist JSON结构中。
3.4. “子步骤”的轻量化： 这些存储在JSON中的“子步骤”只有最核心的属性（如id, title, isCompleted, subSteps），不具备ChecklistItem或Task的完整独立属性。
4. 导航与退出机制

4.1. 通过面包屑导航恢复上级焦点： 用户可以点击面包屑路径上的任意上级名称，界面将平滑地将“行动焦点”恢复到那个层级（顶部标题、面包屑、内容区相应更新）。
4.2. 退出整个“一步模式”返回“一步 (1Step)”列表：
唯一的主要退出方式： 点击界面底部由✦ FAB转变而来的“返回 (← 或 X)”按钮。
点击此底部返回按钮，直接、完全地退出整个“一步模式”（无论当前焦点在哪一个层级），平滑地返回到完整的“一步 (1Step)”列表视图。
支持标准的系统级返回手势，效果同上。
5. ChecklistItem或其“子步骤”的“完成”对焦点的影响

当用户完成“当前聚焦的ChecklistItem”（或其下的所有“子一步”）时，焦点不自动移动到下一个未完成的同级或上移。
当前聚焦的项完成后（比如ChecklistItem A在界面上被勾选完成），其在“一步模式”中的呈现会更新（比如标题加删除线，或整个区域提示完成），引导用户主动通过面包屑或返回按钮调整焦点或退出。
6. 视觉与动画

所有焦点的移动、层级的转换、列表项的隐藏与恢复，都必须伴有极其流畅、自然、有意义的过渡动画，帮助用户理解上下文变化，避免迷失。