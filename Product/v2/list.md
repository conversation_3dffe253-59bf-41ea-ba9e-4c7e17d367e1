
**PRD：“一步 (1Step)”列表核心功能与交互需求 (V_最终校准版)**

**1. 概述 (Overview)**

* **1.1. 视图名称：** “一步 (1Step)”列表。
* **1.2. 定位：** App启动后的核心默认视图。
* **1.3. 核心价值：** 为用户提供一个动态的、无压力的、聚焦的界面，用于管理和执行其**当前所有明确定义为“一步 (1Step)”状态的行动**，帮助用户“只迈出一小步”并保持行动流动。

**2. 列表内容与呈现 (List Content & Presentation)**

* **2.1. 行动来源（核心定义修正）：**
    * **“一步 (1Step)”列表，只显示所有当前状态被用户明确设置为“一步 (1Step)”的行动。**
    * 这些行动可以来源于：
        * 用户从“收集箱 (Inbox)”或“下一步 (Next Step)”等其他状态列表，主动将其状态更改/移动到“一步 (1Step)”。
        * 用户通过主界面底部`+` FAB快速创建新行动时，直接将其初始状态设定为“一步 (1Step)”。
* **2.2. 数量：** 不设上限。
* **2.3. 排序：** 支持用户通过长按拖拽进行手动排序。默认排序为用户上次手动排序结果或按进入“一步 (1Step)”状态的时间。
* **2.4. 列表项显示元素 (极致简洁)：**
    * **2.4.1. 必需：**
        * **行动标题 (Action Title)。**
        * **完成勾选框 (`✔️`)。**

    * **2.4.2. 条件显示（当且仅当该行动包含“未完成小行动 (Micro Steps)”时）：**
        * 在行动项的右侧（或其他不突兀的固定位置）显示一个清晰的**“展开 (`∨`)”图标**。没有小行动的行动项则不显示此图标。
        * 显示未完成小行动数量
    * **2.4.3. 项目**
        * 若行动关联项目，可用极小色点/色条在不易察觉处提示（此项需确保不破坏极致简洁，若视觉效果不佳可舍弃）。

**3. 列表项交互操作 (List Item Interactions)**

* **3.1. 点击行动项主体（如标题文本区）：**
    * **无论行动是否有小行动：** 统一导航至该行动的**“全屏行动详情页”**。

* **3.2. 点击“展开 (`∨`)”图标 (仅限有小行动的行动项)：**
    * 在“一步 (1Step)”列表**原地展开**该父行动下方的小行动列表。
    * 用户可在此直接勾选完成小行动，或添加新的小行动到此父行动下。
* **3.3. 右滑行动项：**
    * **快速完成 (`✔️`)。** 状态从“一步 (1Step)”变为“已完成 (Done)”。
    * 伴有即时反馈。
* **3.4. 左滑行动项 (精简菜单)：**
    * 出现操作菜单，**仅包含以下两个选项：**
        * **聚焦 (`🎯`)：** 点击后，进入相应的“全屏专注模式”：
            * 若行动有小行动，进入其“父行动聚焦执行模式”（顶部父行动标题，下方“当前聚焦小行动”展示区和小行动列表）。
            * 若行动无小行动，进入为此简单行动定制的“极简专注视图”（显示标题、大完成按钮）。
        * **移动 (`➡️`)：** 点击后，允许用户将此行动的**状态**从当前的“一步 (1Step)”更改/移动到其他状态（如：“下一步”、“等待中”、“将来也许”）。行动将从“一步”列表移除，并出现在目标状态列表。
* **3.5. 长按行动项：**
    * 触发**排序 (拖拽 `↕️`)** 功能，仅调整在“一步 (1Step)”列表中的显示顺序。

**4. 列表整体交互与管理 (Overall List Interactions & Management)**

* **4.1. 主界面底部唯一按钮 (`+` FAB)：**
    * **点击：快速添加行动。**
        * 弹出极简输入界面。
        * **允许用户在添加时直接指定其初始状态，包括“一步 (1Step)”。如果用户选择将新行动直接设为“一步 (1Step)”状态，则该行动出现在“一步”列表中。** 如果不指定或选择其他状态，则进入相应列表。
        * 提供可选方式关联项目。
    * **长按：触发“回声Drop (Echo Drop)”功能。**
* **4.2. 空状态：** 当“一步 (1Step)”列表（即没有任何行动处于“一步”状态）为空时，显示友好的引导文字和操作建议（如“从‘下一步’挑选一些行动设为‘一步’吧”、“创建一个新的‘一步’行动”）。
* **4.3. 动画与反馈：** 所有核心操作应伴有平滑、自然的过渡动画和即时反馈。

**5. 导航上下文 (Navigational Context)**

* **5.1. 顶部导航栏：**
    * 左上角：菜单入口 (通往全屏“管理中心”，可访问“收集箱”、“下一步”、“等待中”、“将来也许”、“项目”、“标签”、“已完成/足迹”、“设置”等全局列表和功能区)。
    * 右上角 (从右至左)：`🔍` 搜索入口，`[ырͰ]` 节奏回廊入口。
* **5.2. 标题：** 主界面固定标题为“1Step”。

**6. 核心概念澄清：**

* **简单行动：** 指在创建或后续编辑中，**没有任何“未完成小行动 (Micro Steps)”** 的行动。
* **复杂行动：** 指在创建或后续编辑中，有未完成小行动的行动。界面上会因此显示“展开 (`∨`)”图标。




## 展开功能

1. 条件展示:

- 非聚焦状态下：只有已有小行动的行动才显示展开图标(∨)

- 聚焦状态下：所有行动（无论是否有小行动）都显示展开按钮

- 图标位于行动项的第三行右侧

1. 交互与行为:

- 点击展开图标时，在行动项下方原地展开小行动列表

- 展开状态下，图标变为(∧)

- 再次点击图标时，折叠小行动列表

1. 展开区域内容:

- 已有小行动的行动：显示现有小行动列表

- 无小行动的行动（聚焦状态下）：显示添加小行动的界面

- 用户可以在展开区域直接勾选完成小行动

- 用户可以在展开区域直接添加新的小行动

1. 视觉表现:

- 展开区域应有明确的视觉边界

- 展开/折叠过程应有流畅的动画效果

## 一步功能

1. 触发方式:

- 左滑行动项(从屏幕右边往左滑)显示操作菜单

- 菜单中包含"聚焦"(🎯)选项

1. 功能效果:

- 点击"聚焦"后，列表转换为只显示该行动的状态

- 不是导航到新页面，而是列表的一种特殊过滤模式

- 聚焦模式下，用户可以专注于单个行动及其小行动

1. 界面变化:

- 底部的添加按钮(+ FAB)变为返回按钮

- 点击返回按钮退出聚焦模式，恢复完整行动列表

- 可能有视觉提示表明当前处于聚焦模式

1. 特殊行为:

- 聚焦模式下，无论行动是否有小行动，都显示展开按钮

- 这允许用户在聚焦状态下方便地添加小行动

- 聚焦模式下执行完小行动后保持聚焦状态