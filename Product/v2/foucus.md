焦点路径的定义与存储：
焦点路径 (focusPath): 一个由节点 UUID 组成的数组，表示用户在“一步模式”中从根任务逐级下钻的路径。例如 [rootTaskId, childItemId, subChildItemId]。
存储机制:
在 FocusManager 中，会维护一个字典 lastFocusPaths: [UUID: FocusPathEntry]。
FocusPathEntry 是一个结构体或元组，包含：
path: [UUID] (实际的焦点路径)
timestamp: Date (该路径最后被更新或使用的时间戳)
这个 lastFocusPaths 字典会在 FocusManager 初始化时从 UserDefaults 加载，并在路径更新时保存回 UserDefaults。
记录最后焦点：
当用户在“一步模式”中进行以下操作时，当前的 focusPath 连同当前时间戳会被记录为对应根任务的“最后焦点路径”：
向下钻取 (drillDown): 每当用户点击一个子项进入更深层级时，更新后的完整 focusPath 和时间戳会被保存。
向上返回 (drillUp): 每当用户返回上一层级时，更新后的（缩短的）focusPath 和时间戳会被保存。
退出一步模式 (exitFocusMode):
无论是通过左滑根任务的“退出”按钮，还是通过导航栏的返回按钮（如果适用）退出整个“一步模式”时，退出前的那个 focusPath 和时间戳会被保存。
恢复焦点：
进入一步模式时 (enterFocusMode):
当用户通过左滑任务列表中的某个任务，选择“一步模式”进入时：
FocusManager 会检查 lastFocusPaths 中是否存在该根任务的 FocusPathEntry。
7天有效期检查: 如果存在，会检查其 timestamp。如果该时间戳距离当前时间已超过7天，则该条目被视为无效，并应从 lastFocusPaths 中移除。
如果存在有效的（未过期的）路径条目，并且该路径经过验证仍然有效（路径中的所有节点ID依然存在于当前任务的层级结构中），则 FocusManager 会将当前的 focusPath 设置为这个已保存的路径。
Toast提示: 成功恢复到上次焦点时，会显示一个Toast提示，例如：“已恢复到上次聚焦位置：[子步骤标题]”。
如果不存在有效路径（或已过期），则默认聚焦到根任务本身（即 focusPath 为 [rootTaskId]）。
路径有效性验证: 需要一个机制来验证存储的路径是否仍然有效，防止因节点被删除或结构变更导致尝试聚焦到不存在的节点。如果路径无效，应清除该任务的已存路径记录。
任务详情页入口：
在 TaskDetailView 页面：
当页面出现时，会检查当前任务在 FocusManager 中是否有已保存的、且未过期的“最后焦点路径”。
如果存在一个有效的（未过期的）、且层级大于1（即不仅仅是根任务本身）的路径，则会在详情页的某个位置显示一个按钮，如“回到上次聚焦: [子步骤标题]”。
点击此按钮会：
关闭 TaskDetailView。
触发 FocusManager 进入该任务的“一步模式”，并明确指示其恢复上次的焦点路径 (enterFocusMode(for: task, restoreLastPath: true))。恢复时同样应有Toast提示。
清除焦点路径：
除了7天自动过期外，当任务被标记为完成或被删除时，应主动清除该任务的已存“最后焦点路径”。