1. 行动聚焦
入口方式：
通过任务行的左滑操作，显示聚焦按钮(🎯图标)
点击聚焦按钮进入聚焦模式
UI展示：
聚焦的行动直接显示在主列表位置，保持原有UI风格
非聚焦行动隐藏，界面只显示聚焦的单个行动
不创建新页面，而是通过过滤实现
交互体验：
行动聚焦后仍可点击展开按钮查看小行动
无论是否有小行动，都显示展开按钮
可以查看现有小行动并添加新小行动
2. 小行动聚焦
入口方式：
通过点击小行动旁的target图标(🎯)
在展开区域里的小行动行末尾都有target图标(已完成的除外)
UI展示：
聚焦的小行动显示在任务卡片内，位于主任务标题下方
有较大的勾选框，比普通小行动的勾选框更大
主任务标题降低不透明度，视觉上弱化
聚焦状态下，展开区域自动折叠(小行动列表不显示)
交互特性：
聚焦的小行动从展开区域的列表中消失，避免重复显示
已聚焦小行动时，点击另一个小行动的target图标会切换聚焦
3. 数据管理
存储方式：
在FocusViewModel中维护聚焦状态
使用UserDefaults持久化存储focusedTaskId
小行动聚焦状态在UI层维护，不修改数据库
4. 底部按钮
切换逻辑：
在聚焦模式下，右下角的魔法棒按钮变为返回按钮
直接复用现有按钮，改变图标和功能，不额外添加新按钮
点击返回按钮退出聚焦模式，恢复完整行动列表
5. 完成与退出条件
自动退出条件：
完成聚焦任务时自动退出聚焦模式
完成聚焦小行动时自动退出小行动聚焦状态(但仍保持在行动聚焦模式)
任务被移回下一步或删除时自动退出聚焦
6. 特殊交互
完成行为：
小行动完成有与主任务相同的体验(烟花效果、声音、Toast带撤销功能)
聚焦模式下执行完小行动后保持聚焦状态(只退出小行动聚焦)
统一体验：
行动和小行动聚焦保持统一的视觉风格和交互模式
两种聚焦模式遵循相同的设计语言，提供一致的用户体验
