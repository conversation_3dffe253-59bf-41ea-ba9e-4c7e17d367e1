1. 完成任务的需求（修正版）
操作流程：
用户点击任务的勾选框
任务立即标记为完成
任务状态更新为"已完成"
任务完成时间（completedAt）设置为当前时间
视觉反馈：
勾选框显示为已勾选状态
任务文本变灰并添加删除线
播放完成音效
触发触觉反馈
可能有烟花/动画效果
状态变化：
任务不会立即消失，而是保持在原位置约3.5秒
显示Toast提示，带有撤销选项（Toast只显示几秒）
如果用户在这段时间内点击勾选框，可以将任务标记回未完成状态
3.5秒后，任务从列表中消失
时间表现：
0秒：视觉上标记为完成，但保持在原位置
显示Toast提示几秒钟
3.5秒后：任务从列表中消失
2. 完成展开项（小行动和子步骤）的需求
操作流程：
用户点击小行动/子步骤的勾选框
小行动/子步骤应该立即在视觉上标记为完成
但不应该立即从列表中消失
视觉反馈：
勾选框显示为已勾选状态
文本应该变灰并添加删除线
播放完成音效
触发触觉反馈
可能有烟花/动画效果
状态变化：
完成后，小行动/子步骤应该保持在原位置一段时间（约3.5秒）
然后移动到列表末尾或从列表中消失
如果用户在这段时间内点击勾选框，可以将小行动/子步骤标记回未完成状态
时间表现：
0秒：视觉上标记为完成，但保持在原位置
3.5秒后：从列表中消失或移动到列表末尾