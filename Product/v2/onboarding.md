**第一阶段：初见倾心——极简欢迎与核心导航认知 (首次启动，1-3屏)**

* **目标：** 快速传递核心理念，告知最重要的几个按钮在哪里。
* **形式：** 1-3屏可滑动的欢迎卡片，设计风格与“休息模式”插画保持一致，极简、美观、宁静。
* **内容示例：**
    * **屏1:** Logo + “欢迎来到1Step” + “为真实人类设计的行动系统。”
    * **屏2:** 核心理念 + “不求完美，只需迈出一步。” + “与你的节奏共处。”
    * **屏3:** 核心导航示意图 + 极简文字标注：
        * 指向左上角 `🔍`: “在这里**搜索**你的行动。”
        * 指向右上角 `[ырͰ]`+`☰`: “从这里进入**节奏回廊**与**管理中心**(收集箱/下一步等)。”
        * 指向底部 `+`/`✦`: “轻点**添加**新一步，长按**回声**释放想法。”
* **交互：** 用户可快速左滑跳过，提供醒目的“**开始使用**”按钮进入主界面。

**第二阶段：迈出第一步——核心流程互动引导 (进入主界面后)**

* **目标：** 让用户亲手完成一次最基础的“添加->执行/聚焦”流程。
* **形式：** 使用Coach Marks（教练标记）、高亮提示、小气泡等非阻塞式引导，一步步提示用户操作。
* **核心引导流程：**
    1.  **引导添加行动：** (当“一步”列表为空时)
        * 高亮底部`+`/`✦`按钮，提示：“轻点这里，记录你的第一步”。
        * （可选）用户点击后，在快速添加界面提示：“输入行动，按完成即可加入‘一步’列表”。
    2.  **引导完成简单行动：** (当列表有第一个行动后)
        * 高亮该行动项的`✔️`勾选框或整个行动项，提示：“完成了吗？向右滑动或点这里标记完成”。（用户完成后给予愉悦反馈）
    3.  **引导认识复杂行动与原地展开：** (引导用户添加第二个行动，并提示/辅助其添加一个小行动，或直接显示一个带有`∨`的示例复杂行动)
        * 高亮复杂行动右侧的`∨`图标，提示：“点这里展开/收起小行动”。
    4.  **引导核心手势操作 (左滑)：**
        * 用动画或箭头示意左滑操作，提示：“向左滑动试试？有惊喜！”
        * 用户左滑后，高亮出现的`🎯`聚焦和`➡️`移动按钮，简述功能：“`🎯`聚焦：进入专注模式；`➡️`移动：改变行动状态”。
    5.  **引导认识管理入口：**
        * 高亮左上角`☰`菜单，提示：“你的收集箱、下一步等空间，从这里进入管理”。
    * **设计原则：** 每个引导点只出现一次，用户完成操作或点击空白处即消失，提供“跳过引导”选项。