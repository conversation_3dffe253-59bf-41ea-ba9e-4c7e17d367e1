# 页面说明：一步页（FocusTab）

## 📌 页面目标
提供一个高度聚焦的任务区域，用户每天只需要完成这最多 3 个任务，减少认知负担，推动明确行动。

---

## 🧱 页面结构

1. **顶部标题区**
   - 标题：「一步」
   - 动态文案提示（每次进入页面随机一条）：
     - 一步 · 你正在专注的事，不需要太多 🧠
     - 先走这一步，剩下的事情之后再说
     - 你不是要做很多事，只是现在这一步
     - 此刻你只需关注这些
     - 专注，是最强大的行动力

2. **任务展示区**
   - 最多展示 3 个任务（状态 = Doing）
   - 每条任务包含：
     - ✅ 勾选框（勾选后设为已完成 Done）
     - 标题（加粗显示）
     - 所属项目 + 标签（小字体灰色）
     - 标题为一行，标签和项目为一行，标签靠左，项目靠右对齐，如果任务有 checklist，则中间增加一行，一个子任务的图标，然后一个5，5 表示未完成的子任务数量，如果全部完成，则不显示，图标参考 todoist 的子任务图标
     - 如果有备注，那么显示一行备注，如果有多行，也只显示一行（仅当任务有 notes）
     - 这里的设计参照 todoist 的任务设计
   - 支持左滑：移除出一步/删除 → 状态变回 NA/删除
   - 支持按住拖动排序（无拖动图标，直接长按卡片本身拖动）
     - 任务卡片轻浮起 + 阴影，增加“控制感”。
   - 支持长按：弹出底部操作菜单（移回下一步、设为等待中、查看详情、删除）

3. **空任务状态时的引导区域**
   - 若当前无任务：中间居中展示提示语 + icon
   - 示例文案：
     - 🛌 今天的一步还没定下来？现在选一个就好
     - ☁️ 现在是清空状态，你可以点击右下角魔法按钮添加
   - 无任何“必须添加”压力，仅温和引导

4. **魔法按钮（右下浮动）**
   - 图标：🧠 / ✨ / 自动
   - 逻辑：
     - 当前任务数 = 3 → 弹窗「确认清空一步？全部移入下一步」
     - 当前任务数 < 3 → 打开从 NA 中选择任务的界面
     - 点击时可加个旋转 or 微跳动，强化它的“召唤感”。
5. **问号按钮**
   - 位置：页面底部空白区域左侧（与魔法按钮平衡）
   - 小而不抢眼，建议使用浅灰色问号圆角 icon（？）
   - 点击后打开「一步引导说明页」或底部抽屉
6. **底部导航栏**（固定）
   - 一步（高亮）｜下一步｜浏览
   - 1Step(Highlight)/NextStep/Browse

---

## ✅ 特别交互规则

- 勾选完成：
  - 立即移入 Done
  - 卡片淡出动画
  - 播放轻快的完成音效
  - 局部烟花动画（confetti 效果，轻量、可选）
  - 展示 toast：“任务完成 🎉” + 可撤销按钮

- 左滑：
  - 显示操作：「移回下一步」

- 长按：
  - 弹出底部操作菜单（移动状态、删除、查看详情等）

- 拖动排序：
  - 长按任意任务卡片即可拖动排序（最多 3 项）
  - 拖动不影响其他状态列表排序

---

## 🧠 设计理念提醒
- 「一步」是用户的行动主舞台，应始终鼓励维持 1~3 个任务
- 完成任务不是为了清空，而是为了继续流动
- 顶部文案是情绪锚点，引导思维进入专注模式
- 魔法按钮是全局调度器，引导添加 / 清空操作
- 空状态提示语温和、可爱，不制造任务压力

