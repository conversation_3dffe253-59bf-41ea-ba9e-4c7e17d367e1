好的，以下是导入任务页面的完整产品需求文档 v1.0 草案：

⸻

📥 导入任务页面（ImportTasksPage）产品需求文档

⸻

🎯 页面定位

导入任务页面用于从其他状态（收集箱、等待中、未来也许）中挑选可推进的行动，移动至“下一步”，帮助用户快速补充下一步清单，保持行动可持续而不过载。

此页面强调轻操作、低心智负担、快速导入，避免复杂任务处理操作。

⸻

🧱 页面结构
	1.	顶部导航栏
        •	标题：「导入任务到下一步」
        •	关闭按钮（右上角 ✕）
	2.	搜索栏
        •	单行搜索框，支持关键词模糊匹配
        •	支持 #标签 和 !项目 快捷语法
        •	不主动唤起选择器，仅联想补全
	3.	状态页签（Tab）
        •	三个状态页签：📥 收集箱 / ⏳ 等待中 / 🌌 未来也许
        •	默认选中“收集箱”
        •	支持左右滑动切换
	4.	任务列表
        •	展示当前状态页签下所有可导入任务
        •	每项任务包含：
        •	标题
        •	所属项目（右对齐，灰字），同下一步列表，尾部用！表示颜色
        •	标签（左对齐，灰字），同下一步列表标签样式
        •	点击任务卡片：选中 / 取消选中
        •	已选任务自动置顶展示
        •	不支持左/右滑，不支持勾选完成任务
	5.	底部操作栏
        •	显示当前已选数量：「已选 3 项」
        •	「导入到下一步」按钮（选中 ≥1 项后高亮可点击）
        •	导入后显示 toast：「成功导入 3 项」

⸻

🔁 交互行为说明

用户行为	系统反馈
切换状态页签	列表内容更新，保留当前搜索关键词
搜索关键词	实时匹配当前状态下任务，支持 # 和 ! 快捷语法
点击任务卡片	切换选中状态，任务移至顶部区域展示
点击导入按钮	所有选中任务移入“下一步”，从原状态中移除
导入完成	弹出 toast 提示，不支持撤销



⸻

🚫 明确不支持功能
	•	❌ 批量全选任务
	•	❌ 多状态组合筛选
	•	❌ 撤销导入
	•	❌ Checklist/备注显示与匹配
	•	❌ 编辑任务内容

⸻

💡 设计风格与语气
	•	极简白底设计，文字为主
	•	标签、项目为浅灰色，不打扰主视觉
	•	操作按钮风格轻盈，避免强调“确认”“提交”等高压用语

⸻
好的，以下是《导入任务页面》完整的 UI / UX 需求文档：

⸻

🎨 导入任务页面 · UI/UX 设计需求（ImportTasksPage）

⸻

1. 页面布局结构

+--------------------------------------+
| ⬅︎        导入任务到下一步          ✕ |
+--------------------------------------+
| 🔍 搜索框（placeholder：搜索任务、#标签、!项目） |
+--------------------------------------+
| 📥 收集箱   ⏳ 等待中   🌌 未来也许       |
|（页签，支持横滑切换，单选状态）         |
+--------------------------------------+
| 已选任务（若有）                      |
| ▪︎ [✔︎] 阅读《深度工作》     !阅读  #成长   |
| ▪︎ [✔︎] 跑步计划                !健康  #习惯   |
+--------------------------------------+
| 可导入任务列表（当前状态）            |
| ▫︎ [ ] 记录灵感             !写作  #创意     |
| ▫︎ [ ] 归还图书             !生活  #待办     |
| ▫︎ [ ] 备份照片             !生活  #整理     |
+--------------------------------------+
| [导入到下一步]（浮动按钮，仅选中项高亮）   |
+--------------------------------------+



⸻

2. 元素说明与交互逻辑

🔍 搜索框
	•	始终固定在顶部
	•	输入 #标签 或 !项目 自动识别为筛选关键字
	•	无弹窗，仅下方列表动态展示匹配结果
	•	联想提示展示于列表顶部，不遮挡主内容
	•	清除搜索时自动恢复当前状态下全部任务

⸻

📥 状态页签（单选 Tab）
	•	三个固定项：收集箱、等待中、未来也许
	•	支持横滑切换，也可点击切换
	•	当前选中项高亮显示（建议使用浅色底 +粗体）

⸻

📋 任务卡片样式

区域	内容
主体	任务标题（加粗）
辅助	标签（左对齐，灰色字）+ 项目（右对齐，灰色字）
行为	点击整卡选中 / 取消选中
选中态	显示选中高亮边框或勾选图标；置顶显示
未选中态	无边框，常规灰白背景
禁止左右滑动	保持卡片交互统一，仅支持点击



⸻

✅ 已选任务逻辑
	•	一旦选中，任务移至卡片列表顶部，持续高亮显示
	•	最多显示 10 项（其余滚动）
	•	取消选中后恢复到原位

⸻

🧭 底部操作栏

元素	行为
已选数提示	动态更新：如「已选 3 项」
导入按钮	选中任务 ≥ 1 时高亮；否则禁用
点击导入	执行导入动作，弹出 Toast 提示
Toast 内容	成功导入 3 项到下一步 ✅
Toast 风格	顶部浮出、自动消失、轻提示感



⸻

3. 空状态设计
	•	当前状态无任务：展示简洁插画 + 文案提示（如“收集箱是空的 🎉”）
	•	搜索无结果：展示文案「没有找到符合条件的任务」

⸻

4. 风格细节

设计元素	建议样式
字体	系统默认 San Francisco
标签 / 项目	灰色细字，不强调色彩
页签切换	有过渡动效，切换自然
导入按钮	使用品牌主色，带阴影提升层级感
整体观感	极简、有序、专注、轻盈



⸻

5. 响应动效

操作	动效建议
点击任务	小范围轻震 / 缩放反馈
导入成功	Toast 滑入滑出、顶部轻轻飘出
页面切换	Tab 横滑带轻微动效


