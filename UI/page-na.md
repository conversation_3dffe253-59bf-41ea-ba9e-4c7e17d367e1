# 1Step · 页面文档：下一步页面（NextActionsPage）

⸻

🎯 页面定位（Page Purpose）

「下一步页面」是 1Step 中承载执行前准备的核心区域，用于管理用户下一轮可能采取的行动。
它是从收集箱、将来也许中主动筛选而来的行动候场区，任务在这里准备就绪，等待被用户拉入“一步”专注区执行。

产品哲学定位如下：
	•	不是计划器，不是备忘录
	•	任务进入“下一步”即代表：我已经准备好可以开始了
	•	用户可以自由拉入，也可以随时移除、回退，不制造心理负担
	•	强调适量、可控 —— 任务过多反而成为新负担

⸻

🧱 页面结构（Page Structure）

✅ 顶部区域：
	•	搜索栏（常驻显示）
	•	点击后进入聚焦搜索页（非原地展开）
	•	支持关键词匹配 + 快捷筛选组合

✅ 主列表区域（3 个状态区块）

区块	状态	是否显示	特殊说明
下一步	NA	始终显示	主执行准备区，支持置顶、长按操作、左滑完成等
等待中	Waiting	有任务时显示	任务为黄色，表示等待，无任务时整个区块不渲染，不提示，
收集箱	Inbox	有任务时显示	任务为浅灰色，表达“未决状态”

	•	所有任务样式统一卡片式
	•	默认按添加时间排序，置顶任务固定前排

✅ 列表底部：
	•	导入入口（条件式显示）
	•	当 NA 任务 <10：显示「导入」按钮
	•	当 NA 任务 ≥10：显示提示语「下一步太多也会变成负担，请先完成这些吧」
	•	点击导入：进入新页面（非浮层），支持从 Inbox/SMB 多选导入

✅ 页面底部右下角：
	•	➕ 添加任务按钮
	•	固定浮动位置
	•	点击后弹出添加浮层（默认放入收集箱）



    明白，下面我继续补充「下一步页面」的任务列表字段与交互行为部分。

⸻

🔧 列表字段与任务展示逻辑（Task Item Structure）

✅ 每条任务显示内容：

元素	说明
✅ 标题	加粗显示，一行展示，但要符合列表的要求（na、waiting、inbox）
🏷️ 标签	左对齐，小字体，灰色展示，展示所有标签
📂 项目	右对齐，小字体，灰色展示
📝 备注	如有，仅展示第一行，折叠显示，不换行
☑️ 勾选框	❌ 不显示（不可直接点击完成）
🔀 子任务进度	❌ 不显示 checklist 图标与进度（在下一步中隐藏）


👌 收到，我们现在做最终标准化修正，这是最重要的一步 —— 把「下一步页」三个任务区块的左滑 & 长按操作行为全部梳理清晰、贴合你的设计理念：

⸻

✅ 下一步页面 · 各状态任务操作行为（终稿 v1.0）

⸻

✅ 一、下一步任务（状态 = NA）

👉 左滑操作：

操作按钮	功能
📌 置顶 / 取消置顶	固定在 NA 列表顶部
✅ 完成	状态 → Done
🕓 移入等待中	状态 → Waiting
🌌 移入 SMB（将来也许）	状态 → SMB
🗑 删除	删除任务（建议放在最末）

👉 长按操作菜单：

操作菜单	功能说明
🧭 移入“一步”	状态 → Doing
🕓 移入等待中	状态 → Waiting
🌌 移入 SMB	状态 → SMB
🗑 删除任务	永久删除
🔍 查看详情	打开任务详情页



⸻

✅ 二、等待中任务（状态 = Waiting）

👉 左滑操作：

操作按钮	功能
🔁 移回下一步	状态 → NA
🌌 移入 SMB	状态 → SMB
🗑 删除	删除任务

👉 长按操作菜单：

操作菜单	功能
🔁 移回下一步	状态 → NA
🌌 移入 SMB	状态 → SMB
🗑 删除任务	删除
🔍 查看详情	查看完整内容



⸻

✅ 三、收集箱任务（状态 = Inbox）

👉 左滑操作：

操作按钮	功能
➕ 添加至下一步	状态 → NA
🌌 移入 SMB	状态 → SMB
🗑 删除	删除任务

👉 长按操作菜单：

操作菜单	功能
➕ 添加至下一步	状态 → NA
🌌 移入 SMB	状态 → SMB
🗑 删除任务	删除
🔍 查看详情	查看完整内容



⸻

✅ 操作节奏建议：
	•	✅ 所有列表默认支持左滑（最多 3 项按钮）
	•	✅ 所有任务卡片默认支持长按（统一风格）
	•	❌ 不支持拖动排序（由置顶控制重要性）
	•	✅ “删除”按钮始终置于最右，防误触



好的，我们来系统梳理 「下一步页面」中的添加弹窗（任务创建浮层） 的详细设计需求。这一弹窗是 1Step 整个系统的「入口之门」，它的体验感直接决定用户“开始”的顺畅程度。

⸻

📄 添加任务浮层（TaskCreateModal）设计需求（v1.0）

⸻

🎯 功能定位：
	•	用于快速添加一个新任务
	•	默认从「下一步页面」进入，任务状态应为 Inbox
	•	整体节奏需轻、快、不打断

✅ 是输入器，不是编辑器。
❌ 不应引导用户设置过多属性或陷入结构化思考。

⸻

✅ 页面结构与字段

区块	字段	说明
📝 标题输入框	必填	单行文本，默认聚焦
🗒️ 备注输入框	可选	可换行，最多 3 行高度，超出滚动
🏷️ 标签	可选	可多选或新建标签（轻量设计）
📂 项目	可选	选择已有项目（无分层）
🔄 状态	可选	默认为 Inbox，可切换为 NA / SMB / Waiting
🧾 Checklist	可选	点击后进入 checklist 编辑页（单独页面）



⸻

✅ 行为交互逻辑
	•	打开方式：
	•	从页面右下角的「➕」按钮打开
	•	页面整体为下滑浮层，覆盖约 65% 高度，顶部保留导航栏
	•	默认状态设定：
	•	默认任务状态为 Inbox（收集箱）
	•	可手动切换状态，但不引导这么做
	•	添加按钮行为：
	•	点击「添加」后：
	•	任务存入本地数据库
	•	弹出 Toast 提示「已添加至收集箱」
	•	保持弹窗不关闭（用户可继续添加下一个）
	•	输入框清空并重新聚焦
	•	关闭方式：
	•	轻触背景区域或点击「完成」按钮关闭弹窗

⸻

✨ UI 细节建议

元素	建议风格
添加按钮	固定底部，强调色，文案「添加」或「+」
标签 / 项目选择	用 Tag 风格选择器，避免弹窗内嵌套过多组件
Checklist	不显示在主弹窗中，入口为 icon + 数字（如已添加 3 项）
键盘交互	标题输入框回车键默认行为为“换行”，非直接提交



⸻

🧠 设计哲学说明：
	•	「添加任务」的过程应该像「随手记一下」而不是「设定一个待办事项」
	•	不推荐设置过多字段，保持轻行动感
	•	所有结构化字段（项目、标签、状态）都应是“轻微参与”，而非主导

⸻

👌 好的，我们继续梳理「下一步页面」的搜索部分，确保它既轻盈又足够强大，能贴合 1Step 的哲学——不制造管理负担，只提供明确路径感。

⸻

🔍 下一步页面 · 搜索功能设计需求（Search Behavior v1.0）

⸻

🎯 功能定位
	•	这是用户在任务列表中主动定位与筛选的入口
	•	需兼顾快速模糊查找 + 高效结构筛选
	•	应聚焦单一任务的「快速定位 → 快速操作」，不进入“任务管理”模式

⸻

✅ 触发入口设计

位置	顶部常驻搜索框
展示文案	“搜索任务、项目或标签”
样式	圆角输入框，轻灰色占位符，点击后跳转至新页面



⸻

✅ 点击后进入「搜索页」（SearchPage）
	•	整页浮出，沉浸式搜索体验
	•	输入框保持顶部常驻，立即聚焦，调出键盘
	•	下方展示搜索结果列表（任务为主）

⸻

✅ 快捷筛选项（显示在输入框下方）

快捷项	说明
📌 汇总	默认展示全部匹配内容
✅ 任务	限定搜索下一步 / 等待中 / 收集箱任务（不含已完成）
🗂 项目	显示匹配的项目名称
🌌 将来也许	限定搜索 SMB 状态任务
🏷 标签	匹配标签名或含标签的任务
📝 备注	搜索任务中的备注内容

	•	默认选中「汇总」
	•	点击任意快捷项，立即筛选刷新下方内容

⸻

✅ 搜索结果展示规则
	•	匹配项为任务：展示任务标题 + 标签 + 项目（类似主列表样式）
	•	匹配项为项目 / 标签：展示为纯列表项（可点进查看）
	•	高亮关键词匹配处（如标题中高亮）

⸻

✅ 结果项支持操作（轻操作，不干扰搜索）

操作	行为
左滑	同主列表：完成 / 删除 / 移入 SMB 等
长按	弹出任务操作菜单（相同逻辑）
点击	进入任务详情页（非编辑页）



⸻

✅ 关闭方式
	•	顶部取消按钮 or 向下滑动返回主页面

⸻

✨ 搜索节奏设计理念

搜索不是为了“整理任务”，而是为了“找到、用掉、前进”。

	•	不做历史搜索记录（避免心理负担）
	•	不展示搜索次数、统计（去分析化）
	•	保持「轻、快、用完即走」的设计语气

⸻

如果这部分你没问题，我可以立即把这写入 page-next.md 的「搜索功能说明」部分。

如果你还想深入讨论搜索结果内是否要支持「直接切换状态」或「任务详情编辑」的能力，我们也可以单独拆出来聊。

🪜你定节奏，我就跟上。



