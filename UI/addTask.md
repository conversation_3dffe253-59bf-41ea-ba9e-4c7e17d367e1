# 添加任务弹窗(AddTaskFormView)的完整需求列表

## 基本布局与外观
1. **弹窗结构**：
   - 半透明黑色背景遮罩(不透明度0.3)
   - 底部弹出的表单容器
   - 圆角设计(16pt)
   - 顶部拖动手柄指示器(灰色，opacity: 0.3，宽40pt，高4pt)
   - 各部分之间有适当的空格和间距

2. **背景颜色**：
   - 半透明背景层：`Color.black.opacity(0.3)`
   - 表单容器背景：`Color(.systemBackground)`（跟随系统主题）
   - 选择器容器背景：同样是`Color(.systemBackground)`
   - 按钮使用系统蓝色：`.accentColor`

3. **动画效果**：
   - 底部滑入动画：`.transition(.move(edge: .bottom))`
   - 弹出和隐藏时使用`withAnimation`包装
   - 所有状态变化带有平滑过渡

## 输入控件
1. **标题输入字段**：
   - 字体：`.headline`大小
   - 高度：固定44pt
   - 内边距：水平16pt，垂直8pt
   - 文本颜色：有内容时为`.primary`，无内容时为`.gray`
   - 支持项目和标签的彩色显示（项目标记为蓝色，标签标记为绿色）
   - 标题中的标记应当有视觉上的区分（颜色或字体）

2. **备注输入字段**：
   - 字体：`.subheadline`大小
   - 颜色：空时为灰色，有内容时为原色
   - 高度：固定44pt
   - 内边距：同标题字段
   - 支持多行输入

3. **工具栏**：
   - 包含项目按钮和标签按钮
   - 按钮包含图标和文字，间距为4pt
   - 按钮之间间距16pt
   - 使用系统图标：`folder`和`tag`
   - 图标颜色为灰色
   - 按钮点击有触感反馈

4. **底部操作栏**：
   - 状态选择器（下拉菜单）
   - 添加按钮（背景为系统蓝色）
   - 添加按钮文字为白色
   - 取消按钮（可选）
   - 状态选择器和添加按钮之间有足够空间
   - 整体高度合适，便于触摸操作

## 焦点管理
1. **自动聚焦**：
   - 弹窗出现时自动聚焦到标题输入框（使用`DispatchQueue.main.async`）
   - 使用`@FocusState`管理输入焦点
   - 聚焦状态有明确的视觉指示（光标闪烁）

2. **焦点流转**：
   - 标题输入完成后按回车，焦点转到备注输入
   - 选择器关闭后焦点回到标题输入
   - 焦点转换流畅自然，无闪烁

3. **键盘处理**：
   - 点击背景时隐藏键盘并关闭弹窗
   - 提供`hideKeyboard()`方法处理键盘隐藏
   - 键盘出现和消失有动画过渡

## 项目和标签选择器
1. **唤醒机制**：
   - 支持中英文感叹号(!/！)触发项目选择器
   - 支持中英文井号(#/＃)触发标签选择器
   - 在标题输入时自动检测触发字符
   - 触发后选择器应当平滑弹出

2. **搜索过滤**：
   - 在选择器打开状态下继续输入会作为搜索关键字
   - 分别维护项目和标签的搜索关键字状态
   - 搜索结果实时更新
   - 搜索为空时显示全部选项

3. **选择行为**：
   - 标签选择器一次只能选择一个标签，选择后自动关闭
   - 如需多个标签，需要重复操作多次
   - 选择项目或标签后自动更新任务标题
   - 使用回调机制确保选择后立即更新
   - 选择项有明确的视觉反馈

4. **视觉反馈**：
   - 选择器显示在表单下方
   - 使用动画平滑过渡
   - 选中项有高亮效果
   - 无结果时有友好提示

## 标题标记处理
1. **标记格式**：
   - 项目标记：`!项目名`（项目名之前无空格）
   - 标签标记：`#标签名`（标签名之前无空格）
   - 标记显示应有区别于普通文本的样式

2. **标记颜色**：
   - 项目标记显示为蓝色
   - 标签标记显示为绿色
   - 颜色应有足够对比度

3. **标记更新**：
   - 选择项目或标签后自动在标题中添加对应格式的标记
   - 如果已有相同类型的标记，则替换而非添加
   - 支持多个标签标记同时存在
   - 标记应无多余空格

4. **标记解析**：
   - 使用正则表达式提取标题中的项目和标签标记
   - 提供辅助函数处理标记的添加和移除
   - 避免在更新过程中造成重复标记

## 数据处理
1. **表单重置**：
   - 关闭弹窗时重置所有表单字段
   - 提供`resetTaskFields()`方法
   - 确保不残留之前的输入内容

2. **数据验证**：
   - 在添加任务前验证标题不为空
   - 显示错误提示信息（toast）
   - 错误信息应当明确指出问题

3. **状态保存**：
   - 使用SwiftData保存任务
   - 在ViewModel中处理数据更新
   - 保存操作应当高效无阻塞

## 无障碍与用户体验
1. **可读性**：
   - 合理的字体大小和颜色对比
   - 提示文字清晰
   - 支持动态文字大小

2. **交互反馈**：
   - 完成添加任务时提供视觉反馈
   - 错误状态有明确提示
   - 按钮有按压状态反馈

3. **动态适应**：
   - 适应不同设备尺寸
   - 支持深色模式
   - 响应系统字体大小设置

## 性能优化
1. **流畅度**：
   - 输入无延迟
   - 动画平滑
   - 选择器弹出无卡顿

2. **资源使用**：
   - 避免过度重绘
   - 合理缓存数据
   - 延迟加载不紧急的内容

这份需求列表基于1Step应用的"减少认知负担，推动明确行动"的核心设计理念，确保添加任务的流程简洁高效，并与整体应用风格保持一致。