# 项目详情页面需求文档

## 设计理念

「项目详情」页面秉承简洁性和减少认知负担的设计理念。 本页面设计应特别注重：

- **纯粹聚焦**：提供项目视角下的任务聚焦，减少一切干扰元素
- **轻盈呈现**：保持界面简洁，不展示任何统计数字，避免制造压力
- **行动导向**：强调任务执行，弱化项目分析和规划
- **一致体验**：与应用其他页面保持一致的交互模式和视觉风格

## 核心功能需求

### 项目信息区

- **项目标识**：显示项目名称、emoji(如有)和颜色
- **项目描述**：可选的简短项目说明（单行，可折叠）
- **编辑入口**：项目编辑按钮，点击后进入编辑模式
- **不显示数量**：刻意不显示任务数量，避免制造数字压力

### 任务分组与展示

- **状态分组**：按任务状态分组展示（下一步、等待中、收集箱、已完成）
- **默认聚焦**：默认展开"下一步"组，其他组可折叠/展开
- **空状态处理**：为空分组提供友好的空状态提示和添加入口
- **任务排序**：仅支持按日期或系统默认排序，不支持手动排序
- **任务搜索**：支持在项目内搜索任务

### 任务操作

- **添加任务**：快速添加任务到当前项目（自动填充项目属性）
- **状态转换**：支持任务在不同状态间流转（如将收集箱任务移至下一步）
- **任务编辑**：点击任务进入编辑，但保留在项目上下文中
- **左滑操作**：与主页面一致的左滑操作（完成/删除等）

### 项目管理功能

- **归档切换**：提供归档/取消归档项目的功能
- **删除功能**：删除项目（需确认，并说明对任务的影响）

## 用户场景支持

- **专注处理**：用户希望专注处理某个项目的所有相关任务
- **项目筛选**：用户想在项目内按状态查看任务
- **快速添加**：用户希望快速添加任务到特定项目

## 交互设计要点

- **状态过滤器**：顶部水平滚动的状态过滤器，点击切换显示不同状态任务
- **任务操作**：
  - 左滑支持：完成/删除/更改状态等操作
  - 点击：进入任务详情
- **添加按钮**：在页面底部或右下角放置醒目的添加按钮
- **返回导航**：清晰的返回到项目列表的导航元素
- **刷新机制**：下拉刷新更新项目内容

## 视觉设计考量

- **项目标识**：在页面顶部使用项目颜色作为强调色
- **分组视觉**：不同状态分组使用轻微的视觉分隔
- **任务展示**：保持与主页面一致的任务卡片样式，但可提示属于当前项目
- **空状态设计**：为空项目或空分组提供友好的视觉提示
- **去数字化**：刻意避免展示任何数字统计，保持页面纯净

## 避免的设计陷阱

- **数字压力**：不显示任务数量、完成率等数字统计
- **过度分析**：不添加项目统计和数据分析功能
- **复杂管理**：不引入复杂的项目管理概念
- **手动排序**：不支持任务的手动排序，避免增加用户决策负担
- **繁重设置**：避免过多的项目设置和配置选项

## 实现建议

- 使用类似`FilteredTaskListView`的组件支持项目内的任务筛选
- 项目详情应独立于主导航，通过标准iOS导航栈呈现
- 项目编辑功能应使用模态弹出而非页面跳转
- 使用系统原生组件和交互模式，确保操作流畅自然

## UX交互详细规范

### 页面布局与结构

1. **导航区域**：
   - 顶部导航栏高度：44pt
   - 导航标题：项目名称（字体大小18pt，加粗）
   - 项目emoji（如有）显示在名称前，间距6pt
   - 导航栏右侧编辑按钮使用系统图标`pencil`
   - 导航栏左侧返回按钮使用标准iOS返回样式
   - 导航栏使用半透明材质，支持滚动时的模糊效果变化

2. **状态过滤区域**：
   - 位于导航栏下方
   - 高度：40pt
   - 背景色：`Color(.systemBackground)`，略微区别于内容区
   - 包含水平滚动的状态过滤按钮
   - 按钮间距：12pt
   - 左侧边距：16pt
   - 指示当前选中状态的指示器：高2pt，宽度匹配文字，颜色使用项目颜色
   - 支持水平滚动时的惯性和回弹效果

3. **内容区域**：
   - 任务列表占据页面主体
   - 列表项高度：64pt（与应用其他任务列表保持一致）
   - 列表项水平内边距：16pt
   - 组与组之间的间距：24pt
   - 底部预留安全区域，确保底部操作按钮不遮挡最后一项

4. **底部操作区**：
   - 添加按钮直径：56pt
   - 位置：距离右下角16pt(水平)，24pt(垂直)
   - 添加按钮阴影：radius 4pt，opacity 0.2
   - 添加按钮使用项目颜色作为背景色

### 动画与过渡效果

1. **页面转场动画**：
   - 进入项目详情：标准右侧滑入转场，持续时间0.3秒
   - 退出项目详情：标准左侧滑出转场，持续时间0.3秒
   - 使用系统默认的`push`和`pop`动画

2. **状态切换动画**：
   - 状态过滤器选中态切换：下划线滑动动画，持续时间0.25秒，使用`.animation(.easeInOut(duration: 0.25))`
   - 列表内容切换：交叉淡入淡出效果，持续时间0.2秒
   - 使用`.animation(.easeOut(duration: 0.2))`确保平滑过渡

3. **任务操作动画**：
   - 左滑操作出现：弹性滑出效果，持续时间0.2秒
   - 任务完成动画：轻微上浮并淡出，持续时间0.3秒
   - 折叠/展开分组：平滑展开折叠效果，持续时间0.3秒
   - 使用`.animation(.spring())`实现自然的弹性感

4. **交互反馈动画**：
   - 按钮点击：轻微缩放效果（scale to 0.97），持续时间0.1秒
   - 状态过滤切换：状态文字加粗过渡，持续时间0.2秒
   - 添加任务成功：任务列表顶部轻微闪光效果，表示新任务插入

### 手势与操作体验

1. **基本手势**：
   - 左滑任务项：露出操作按钮（完成/删除/更改状态）
   - 点击任务项：进入任务详情编辑
   - 下拉列表：触发刷新
   - 轻点状态过滤器：切换显示状态
   - 长按项目名称：复制项目名称到剪贴板（带有触感反馈）

2. **手势反馈**：
   - 所有按钮点击提供触感反馈（使用`.sensoryFeedback(.impact, trigger: true)`）
   - 左滑时达到阈值提供轻微触感反馈
   - 状态切换时提供轻微触感反馈
   - 任务完成提供成功触感反馈（使用`.sensoryFeedback(.success, trigger: isCompleted)`）

3. **空状态交互**：
   - 空项目点击空白区域：弹出添加任务表单
   - 空状态下有明显的视觉引导（虚线边框或柔和提示图标）
   - 空状态提示文本优先鼓励行动而非提示空状态

4. **快捷添加**：
   - 全局添加按钮点击：弹出与主页一致的添加任务表单
   - 表单出现使用底部弹出动画（`.transition(.move(edge: .bottom))`）
   - 表单中自动填充当前项目标记（`!项目名`）
   - 表单弹出时自动聚焦标题输入框

### 状态与模式切换

1. **状态过滤机制**：
   - 状态过滤器默认选中"下一步"状态
   - 切换状态时保持列表位置在顶部（自动滚动）
   - 状态间切换时保持当前编辑或操作状态
   - 过滤状态在切换页面后保持，再次进入相同项目时恢复上次查看的状态

2. **编辑模式**：
   - 点击编辑按钮：弹出底部模态表单
   - 模态表单背景半透明（`Color.black.opacity(0.3)`）
   - 表单高度：屏幕高度的40%
   - 表单顶部包含拖动指示器（宽40pt，高4pt，`Color.gray.opacity(0.3)`）
   - 表单支持向下拖动关闭
   - 关闭时使用`.transition(.move(edge: .bottom))`动画

3. **确认对话框**：
   - 项目删除操作弹出确认对话框
   - 对话框使用系统标准alert样式
   - 危险操作（如删除）按钮使用红色强调
   - 安全操作按钮（如取消）使用普通强调色
   - 提示文本清晰说明操作后果

### 视觉反馈与状态指示

1. **加载状态**：
   - 数据加载时显示系统标准加载指示器
   - 位置：屏幕中央
   - 大小：系统默认（适中）
   - 透明度：0.7，避免过于抢眼

2. **选中状态**：
   - 状态过滤器选中效果：文字加粗、底部高亮条
   - 任务项选中效果：背景色变为`Color(.secondarySystemBackground)`
   - 选中态持续时间：点击反馈0.2秒

3. **错误状态**：
   - 操作失败时显示轻量级toast提示
   - 位置：页面顶部，导航栏下方
   - 背景色：淡红色（`Color.red.opacity(0.1)`）
   - 文字颜色：红色
   - 显示时间：2秒后自动消失
   - 提示内容简洁明了，指出错误原因

4. **空状态提示**：
   - 空项目提示文字："还没有任务，点击添加"
   - 文字颜色：`Color.secondary`
   - 字体大小：16pt
   - 位置：页面中央
   - 配合柔和的图标（`plus.circle`，尺寸30pt，透明度0.6）

### 无障碍与易用性

1. **可读性设计**：
   - 文本对比度符合WCAG AA标准
   - 最小可点击区域尺寸：44pt×44pt
   - 支持动态文字大小（`.dynamicTypeSize(.medium)`）
   - 项目色彩有备选文本描述

2. **VoiceOver支持**：
   - 所有操作元素添加`.accessibilityLabel`
   - 任务列表项添加自定义`.accessibilityValue`描述任务状态
   - 添加按钮标记为辅助功能重要控件（`.accessibilityAddTraits(.isButton)`）
   - 状态过滤器支持辅助功能旋转器导航

3. **减少认知负担**：
   - 使用熟悉的iOS交互模式
   - 避免多级嵌套的操作路径
   - 重要操作最多两步可达
   - 保持界面简洁，一次只显示必要信息
   - 文本提示友好简短，避免技术术语

### 响应式布局与适配

1. **设备适配**：
   - iPhone SE（小屏）：简化状态过滤器，保持核心功能
   - 标准iPhone：完整展示所有元素
   - 大屏iPhone/iPad：优化利用更大屏幕，增加内容密度
   - 横屏模式：支持，但非优先设计场景

2. **暗色模式**：
   - 适配系统暗色模式
   - 项目颜色在暗色模式下有适当调整（亮度提高10%）
   - 背景使用系统`Color(.systemBackground)`自动适配
   - 确保所有文本在暗色模式下保持良好可读性

3. **动态布局调整**：
   - 使用`GeometryReader`感知屏幕尺寸变化
   - 根据可用空间动态调整元素大小和间距
   - 支持SafeArea自动适应不同设备的安全区域
   - 在小屏设备上适当缩减边距（从16pt减至12pt）

---
项目详情页应保持简洁和专注，让用户能够在项目上下文中管理任务，同时刻意避免数字统计和复杂管理功能。