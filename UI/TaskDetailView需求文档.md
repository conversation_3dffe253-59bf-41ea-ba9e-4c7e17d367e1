# 1Step 任务详情页需求文档

## 概述

任务详情页是用户查看和编辑任务信息的核心界面，采用两阶段弹窗模式（半屏预览→全屏编辑），符合1Step"减少认知负担，推动明确行动"的核心理念。

## 交互模式

### 两阶段弹窗模式

| 模式 | 行为说明 |
| --- | --- |
| 半屏预览模式 | 点击任务后默认进入，显示标题、状态、子任务数、项目、标签（不可编辑） |
| 全屏编辑模式 | 往上滑或点击内容→切换为可编辑状态 |

- UI为同一个弹窗组件，逐渐过渡，不跳页面
- 最终操作完成后，用户点击右上角×或下滑两次返回主页面，不影响当前滚动和上下文

### 三阶段过渡流程

1. 点击任务 → 打开半屏模式
2. 上滑或点击 → 从半屏切换到全屏模式
3. 关闭方式：
   - 全屏模式下滑 → 切换到半屏模式
   - 半屏模式继续下滑 → 关闭详情页
   - 任意状态点击右上角关闭按钮 → 直接关闭详情页

## 页面结构（全屏编辑模式）

层级清晰、块之间有留白、不拥挤

### 1. 标题区
- 任务标题（可编辑，2行内自动换行）
- 视觉上突出，顶部位置
- 右侧可显示当前状态的小型指示器

### 2. Checklist区（如有）
- 显示所有子任务（带勾选）
- 支持添加子项、删除子项
- 完成的子项置底、灰色淡出
- 简洁的勾选交互（点击即完成）

### 3. Notes区
- 多行文本输入框（可折叠）
- 无边框样式，仿写作区块
- 内容为空时显示适当的占位提示
- 支持基本文本格式

### 4. 标签 & 项目
- 横向标签卡片（可添加/删除）
- 项目选择：当前项目 + 修改入口（弹出项目选择器）
- 视觉上与标题区分开来，但保持一致性

### 5. 状态切换
- 状态按钮组（显示当前状态）
- 支持变更为NA、SMB、Waiting等
- 视觉明确，易于区分不同状态

### 6. 删除任务（底部）
- 文字按钮样式（红色或灰色）
- 放置在整个页面底部
- 点击后需确认以防误操作

## 动效与体验细节

| 动效/细节 | 说明 |
| --- | --- |
| 拖动手势 | 从半屏拖至全屏（可仿Apple Calendar详情页） |
| 自动保存 | 所有字段编辑时自动保存，不需显式保存按钮 |
| 删除需确认 | 弹出确认对话框「确定删除该任务吗？」 |
| 声音反馈 | 可考虑完成checklist项时的轻声音效 |
| 关闭按钮× | 固定右上角，任意时候可一键退出弹窗 |

## 半屏预览模式细节

- 高度：屏幕高度的45-50%
- 内容：只展示最关键信息（标题、状态、项目、标签）
- 视觉上明确表达"可上滑查看更多"
- 顶部有圆角设计，底部为半透明遮罩

## 全屏编辑模式细节

- 覆盖整个屏幕，但保留状态栏
- 所有内容均可编辑
- 支持键盘弹出时内容自动上移
- 可通过下滑手势返回半屏模式

## 设计理念契合点

| 1Step系统理念 | 匹配的交互 |
| --- | --- |
| 极简不打扰 | 不跳页面、不强迫编辑 |
| 推行动作，不管理 | 默认只读，想修改再进入编辑态 |
| 可扩展性高 | 为后续AI功能预留了结构空间 |
| 直观交互 | 拖拽手势自然流畅，符合用户习惯 |

## 视觉风格要求

- 与1Step整体风格保持一致
- 简洁、轻量、现代
- 留白充足，不拥挤
- 关注点层次清晰
- 编辑状态与只读状态有明确区分
