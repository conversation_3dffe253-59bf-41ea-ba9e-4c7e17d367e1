好的，以下是 「收集箱页面」产品需求文档，已结合你前述全部交互逻辑整理：

⸻

📄 收集箱页面产品需求文档（1Step）

一、页面定位

收集箱是用户快速捕捉一切思绪的入口，所有新建任务若未指定状态，均默认进入收集箱。此页面的设计目标是轻量、无压、易整理，支持用户快速浏览并进一步处理内容。

⸻

二、主要功能模块

1. 任务列表展示
	•	展示当前所有处于“收集箱”状态的任务。
	•	每条任务卡片支持：
	•	✅ 点击查看详情页
	•	✅ 左滑（从右向左）操作，显示以下按钮：
	•	👉 添加到「下一步」
	•	👉 添加到「将来也许」
	•	👉 删除任务
	•	✅ 右滑（从左向右）操作：完成任务

⸻

2. 添加任务按钮（右下角 + 号）
	•	点击后打开「添加任务弹窗」
	•	任务状态默认为“收集箱”
	•	支持输入标题、项目（!）、标签（#）、备注（无必填）

⸻

3. 搜索功能（右上角🔍）
	•	点击后直接在当前页顶部出现搜索框（轻量弹出，不跳转页面）
	•	搜索支持：
	•	关键词模糊搜索（任务标题 / 备注）
	•	输入 # 触发标签联想选择器
	•	输入 ! 触发项目联想选择器
	•	支持按状态筛选（可选）

⸻

4. 整理功能（右上角按钮 ✨）
	•	点击后进入「批量整理模式」：
	•	✅ 支持多选任务
	•	✅ 底部弹出操作栏：
	    •	「取消」
        •	「移入下一步」
        •	「移入将来也许」
        •	「添加项目」
        •	「添加标签」

⸻

三、设计原则与约束
	•	📦 所有任务不论是否已添加项目 / 标签，只要状态为收集箱，都应显示在本页面。
	•	💡 左滑动作中不展示“移入项目”操作，因为任务是否已有项目不影响其处于收集箱状态。
	•	🧠 搜索 / 整理功能需保持极简，防止用户因“整理负担”而抗拒打开页面。
	•	✅ 所有操作均应轻交互完成，避免进入二级页面干扰收集节奏。

⸻

以下是「收集箱页面」的交互需求文档，与你已定好的产品逻辑完全一致，重点关注用户操作路径、动作反馈与轻交互体验：

⸻

🎯 收集箱页面 · 交互需求文档（Interaction Spec）

⸻

🧱 1. 页面结构
	•	顶部导航栏：返回按钮、页面标题「收集箱」、右上角按钮：🔍搜索、✨整理
	•	任务列表区域：展示所有收集箱状态的任务（支持点击 / 滑动）
	•	右下角悬浮按钮：➕ 添加任务（状态默认：收集箱）

⸻

✅ 2. 列表交互行为

📌 单条任务交互

手势/操作	动作	行为说明
▶ 点击任务	打开任务详情	半屏弹窗显示，支持编辑
◀ 从右向左滑动	显示操作栏	操作项包括：移入「下一步」、移入「将来也许」、删除
◀ 左滑后点击“移入下一步”	状态改为“下一步”，任务从列表中消失	
◀ 左滑后点击“移入将来也许”	状态改为“将来也许”，任务从列表中消失	
◀ 左滑后点击“删除”	弹出二次确认后删除任务	
▶ 从左向右滑动	标记完成	改为完成状态，并从当前页面移除



⸻

➕ 3. 添加任务（点击右下角按钮）

步骤	描述
1. 点击按钮 ➕	弹出添加任务输入框（支持键盘输入）
2. 输入内容	支持识别 !项目 和 #标签
3. 点击“保存”	创建新任务，状态为“收集箱”，插入列表顶部



⸻

🔍 4. 搜索框交互（右上角）

行为	描述
点击🔍搜索	在本页面顶部弹出搜索栏（非新页面）
输入关键词	实时筛选收集箱内的任务
输入 # / !	唤起联想选择器（轻量弹窗，不跳页）
清空搜索	列表回到初始展示状态



⸻

✨ 5. 整理模式（右上角按钮）

行为	描述
点击整理按钮✨	进入批量整理模式：每条任务左侧出现复选框
选中任务	页面底部弹出整理操作栏
操作栏内容	包含：「下一步」、「将来也许」、「添加项目」、「添加标签」、「取消」
任务执行操作后	状态更新，任务移出列表，弹出轻量 Toast「已添加至××」



⸻

🔚 6. 页面状态反馈
	•	所有操作均使用 Toast 反馈，不弹出确认框（除删除）
	•	操作完成后任务立即从当前列表中消失，界面自动更新
	•	空列表时展示空状态文案 + 引导添加任务提示

⸻

