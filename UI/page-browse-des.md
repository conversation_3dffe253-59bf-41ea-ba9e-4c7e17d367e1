# 1Step · 浏览页设计文档（page-browse.md）

> 浏览页是 1Step 的结构视图中心，不承载操作，只作为任务系统的「结构性入口」。

---

## 🎯 页面定位

浏览页的使命是：

- 提供任务在不同状态、结构维度下的统一入口
- 不制造管理负担，不强调任务数量
- 保持极简、克制的呈现方式
- 支持访问项目、标签与设置，完成结构管理的低频行为

该页面设计风格应：
- **安静 / 留白 / 抽象 / 非情绪化**
- 让用户以最小认知成本“扫一眼”，不需处理任何事情

---

## 🧱 页面结构

### 1️⃣ 状态视图区域（四个卡片）

以 2×2 网格形式展示以下四个状态入口：

| 状态名称 | 图标建议 | 说明 |
|----------|----------|------|
| 收集箱 Inbox | 📥（极简盒子） | 未分类任务收集处 |
| 将来也许 SMB | ⭐ / ✨ | 非主力任务收容区 |
| 等待中 Waiting | ⏳ / … | 受阻任务，暂不可动 |
| 已完成 Done | ✅ / ✔ | 已完成任务归档视图 |

- ❌ 不显示任务数量
- ❌ 不显示说明文案（如“还有 x 项任务”）
- ✅ 图标风格抽象、线性、无色块，图标为唯一视觉重点
- ✅ 卡片尺寸统一、无阴影、柔和圆角
- ✅ 卡片为纯点击区域，点击进入对应状态任务页

---

### 2️⃣ 列表项导航区（单行列表项）

以下结构模块以单行展示（非卡片）：

#### 📁 项目（Project）
- 左侧图标：文件夹 📁
- 文案：项目
- 右侧：右箭头 `>`
- 点击：进入「项目管理页」

#### 🏷️ 标签（Tag）
- 左侧图标：🏷️
- 文案：标签
- 右侧箭头 `>`
- 点击进入「标签管理页」

#### ⚙️ 设置（Settings）
- 图标：齿轮 ⚙️
- 文案：设置
- 点击进入设置页

---

## 🧭 风格指引（UI 规范）

| 属性 | 设计要求 |
|------|-----------|
| 色彩 | 主体灰蓝 / 白，低对比度，干净、柔和 |
| 图标 | 抽象化、线条风、尽量避免填充色 |
| 字体 | 无加粗、不突出、保持视觉平衡 |
| 布局 | 上下分区明显，模块间留白充足 |
| 动效 | 无动态提醒，无提示语，无计数气泡 |

---

## ✅ 使用频率与交互倾向

- 平均使用频率：每周 1~3 次（非高频页）
- 不用于“处理任务”，仅用于“访问结构”
- 鼓励的行为：
  - 清理收集箱
  - 回顾将来也许
  - 管理项目/标签
  - 找回遗忘任务

> 浏览页 = 思维地图 + 结构抽屉 + 安静港口