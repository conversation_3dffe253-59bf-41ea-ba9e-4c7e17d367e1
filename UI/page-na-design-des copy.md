# 下一步页面设计说明

✍️ 用于记录我们设计思路、权衡点与哲学取向，是团队对齐产品思维的内核文档。

⸻

🎯 设计定位：行动的候场区，不是待办收纳箱

我们将「下一步页面」定义为 1Step 的第二张舞台 —— 它不承担所有任务的收集义务，只承接「你已准备好开始行动的事」。
	•	不做时间驱动，不制造截止日焦虑
	•	不做任务密度管理，不提示“今日任务数”
	•	专注“当我准备好了，我就可以开始”

⸻

🧠 核心设计思路：

✅ 一、任务分区：从结构简化中制造节奏感

将页面划分为 3 个自然流动区块：

区块	含义	行为建议
下一步（NA）	你已准备好行动的任务	保持 5~10 项以内为宜
等待中（Waiting）	外部事件等待状态	自动折叠，非主导行为
收集箱（Inbox）	未决任务，刚捕获	灰色淡显，表达未被纳入行动路径

✅ 不通过显性限制约束用户
✅ 而是通过结构区块与视觉节奏制造“该行动了”的心理暗示

⸻

✅ 二、交互哲学：快速处理、深度整理二分法
	•	左滑 = 快操作（完成、置顶、状态迁移）
	•	长按 = 深度整理（移入一步、等待中、SMB、删除）

所有动作都强调“向前走”而非“归类好看”

⸻

✅ 三、任务添加行为：保持轻盈，不陷入结构思维
	•	默认进入 Inbox（不要求一开始就分类）
	•	多次添加时不关闭弹窗 → 支持用户捕捉节奏
	•	Checklist、标签、项目为可选项，但 UI 层级不扰主节奏

⸻

✅ 四、导入逻辑：从 Inbox / SMB 召唤而非收纳
	•	导入功能仅在「下一步任务数 ≤ 9 项」时显示
	•	当任务 ≥ 10 项，导入入口隐藏，提示语替代：
「下一步太多也会变成负担，请先完成一些吧」

✅ 强化“行动池不可膨胀”这一认知
✅ 提升用户对「下一步」的心理敬畏感

⸻

✅ 五、搜索体验：沉浸式任务定位，而非数据分析
	•	聚焦搜索页，不原地展开
	•	快捷筛选项目：任务、标签、项目、将来也许、备注
	•	左滑 / 长按行为一致，强化系统一致性

⸻

✅ 最终：不是帮助用户“管理任务”，而是“决定下一步”

下一步页面的设计，强调 适量、流动、自主感。
我们相信，把行动放在用户手里，胜过任何自动化推荐。

