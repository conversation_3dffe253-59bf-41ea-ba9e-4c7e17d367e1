# 1Step 应用「已完成」页面需求文档

## 1. 产品需求

### 1.1 设计理念

「已完成」页面的设计应遵循 1Step 应用的核心理念：「减少认知负担，推动明确行动」。在此基础上，我们需要：

- **弱化回顾**：提供已完成任务的查看功能，但不过度强调或分析历史
- **轻量呈现**：使用简洁的视觉语言，降低已完成任务的视觉权重
- **保持专注**：确保用户注意力仍然集中在当前和未来的任务上
- **与现有风格一致**：保持与应用其他部分的视觉和交互一致性

### 1.2 功能需求

#### 1.2.1 核心功能

- **时间分组展示**：按照时间段自动分组（今天、昨天、本周、上周、更早）
- **分页加载机制**：初始加载最近 20 条任务，支持「加载更多」
- **基础任务操作**：支持左滑恢复任务、删除任务、查看任务详情
- **筛选与搜索**：支持按时间、项目、标签筛选，支持文本搜索
- **与现有组件集成**：利用现有的 FilteredTaskListView 和 TaskRowView 组件

#### 1.2.2 用户场景

1. **回顾完成情况**：用户想查看最近完成了哪些任务
2. **查找特定任务**：用户需要找回之前完成的某个特定任务
3. **恢复误操作**：用户误将任务标记为完成，需要恢复
4. **清理旧任务**：用户希望删除不再需要的已完成任务

### 1.3 非功能需求

- **性能**：即使存在大量历史任务，页面加载和滚动也应保持流畅
- **可用性**：操作应直观易懂，不需要额外学习成本
- **一致性**：与应用其他部分保持视觉和交互一致性

## 2. 交互设计

### 2.1 页面结构

- **顶部区域**：搜索栏和筛选选项
- **内容区域**：按时间分组的任务列表
- **底部区域**：「加载更多」按钮（仅在有更多内容时显示）

### 2.2 列表交互

#### 2.2.1 分组展示

- 默认按时间自动分组：今天、昨天、本周、上周、更早
- 每个时间组显示组标题和任务数量
- 支持点击组标题折叠/展开该组（默认全部展开）

#### 2.2.2 分页加载

- 初始加载最近 20 条已完成任务
- 滚动到底部时显示「加载更多」按钮
- 点击按钮加载额外 20 条任务
- 加载过程中显示简洁的加载指示器
- 全部加载完毕后显示「已显示全部任务」提示

#### 2.2.3 任务项交互

- **左滑操作**：
  - 左滑显示「恢复」和「删除」操作
  - 「恢复」将任务移回收集箱
  - 「删除」彻底移除任务（需确认）
- **点击操作**：
  - 点击任务项打开只读详情视图
  - 详情视图中提供恢复选项
  - 不支持直接编辑已完成任务

### 2.3 筛选与搜索

#### 2.3.1 筛选交互

- **筛选控件**：
  - 位于页面顶部，默认收起
  - 点击筛选图标展开筛选选项
  - 使用水平滚动的标签式设计
- **筛选类别**：
  - 时间：本周、上周、本月、上月
  - 项目：显示最常用的 5-7 个项目
  - 标签：显示最常用的 5-7 个标签
- **筛选行为**：
  - 支持多选组合筛选
  - 选择即应用，无需确认
  - 提供「清除筛选」选项

#### 2.3.2 搜索交互

- **搜索框**：
  - 位于页面顶部
  - 点击展开，支持即时搜索
  - 输入停顿 0.5 秒后开始搜索
- **搜索结果**：
  - 保持时间分组结构
  - 高亮显示匹配文本
  - 空结果时显示友好提示

### 2.4 状态反馈

- **加载状态**：
  - 使用简洁的加载指示器
  - 加载时间超过 2 秒显示加载提示
- **操作反馈**：
  - 恢复/删除操作后显示简洁的成功提示
  - 操作失败时提供错误信息和重试选项
- **空状态**：
  - 无已完成任务时显示友好提示
  - 筛选/搜索无结果时显示相关提示

## 3. UI 设计

### 3.1 视觉风格

- **整体基调**：
  - 保持简洁、轻量的视觉语言，与现有的 FocusView 和 NextActionsView 风格一致
  - 降低已完成任务的视觉权重，使用次要颜色（.secondary）
  - 避免使用过于鲜艳的颜色，保持简洁清爽
- **色彩应用**：
  - 使用与应用其他部分一致的色调
  - 已完成任务卡片使用较浅的背景色，降低不透明度（约 85%）
  - 操作按钮使用标准系统颜色（恢复使用蓝色，删除使用红色）

### 3.2 组件设计

#### 3.2.1 任务卡片

- **视觉层级**：
  - 比未完成任务卡片更轻量，使用 .secondary 颜色
  - 降低不透明度（约 85%）
  - 保留项目和标签标识，但视觉上弱化
- **内容布局**：
  - 左侧显示完成勾选标记（已选中状态）
  - 主体显示任务标题（单行或双行）
  - 右侧显示完成日期（小字号）
  - 底部显示项目和标签标识（如有）
- **复用现有组件**：
  - 利用现有的 TaskRowView 组件并进行适当调整
  - 保持与应用其他部分的交互一致性

#### 3.2.2 分组标题

- **视觉设计**：
  - 使用 subheadline 字体
  - 左对齐，适当留白
  - 包含组名称和任务数量
  - 右侧显示折叠/展开图标

#### 3.2.3 筛选组件

- **标签式设计**：
  - 使用圆角胶囊形状
  - 未选中状态使用浅色背景
  - 选中状态使用深色背景和勾选标记
  - 标签内显示图标和文本

#### 3.2.4 加载更多按钮

- **视觉设计**：
  - 宽度与列表相同
  - 高度适中（44pt）
  - 使用浅色背景，不抢夺注意力
  - 加载中状态显示简洁的旋转指示器

### 3.3 排版与间距

- **字体选择**：
  - 任务标题：system 16pt regular
  - 完成日期：system 12pt light
  - 分组标题：system 14pt semibold
  - 筛选标签：system 14pt regular
- **间距规范**：
  - 卡片内边距：水平 16pt，垂直 12pt
  - 卡片间距：8pt
  - 分组间距：16pt
  - 分组标题与内容间距：8pt

### 3.4 动效设计

- **过渡动效**：
  - 筛选展开/收起使用平滑过渡
  - 分组展开/折叠使用自然动效
  - 加载新内容时使用淡入效果
- **操作反馈**：
  - 恢复任务时使用轻微的移出动效
  - 删除任务时使用淡出效果
  - 避免使用过于夸张的动画

## 4. 技术考量

### 4.1 性能优化

- **分页加载**：避免一次性加载全部历史数据
- **延迟加载**：使用 LazyVStack 确保只加载可见区域的任务
- **缓存机制**：利用 SwiftData 的缓存机制提高性能
- **预加载**：在用户接近底部时预加载下一页
- **与现有架构集成**：利用现有的 MVVM 架构和 SwiftData 模型

### 4.2 数据处理

- **时间分组**：服务端或客户端计算时间分组
- **搜索优化**：优化搜索算法，支持部分匹配
- **筛选逻辑**：支持多条件组合筛选

### 4.3 可访问性

- **动态字体**：支持系统动态字体大小
- **颜色对比**：确保文本与背景有足够对比度
- **VoiceOver**：提供适当的辅助功能标签

---

## 附录：实现路径

基于已有的代码结构，实现「已完成」页面的路径如下：

1. **利用现有组件**：
   - 已有 CompletedTasksView 组件在 DoneView.swift 中
   - 已有 FilteredTaskListView 组件支持基本功能

2. **需要实现的部分**：
   - 完善 doneTasksGroupedView 方法，实现时间分组
   - 添加分页加载机制
   - 实现筛选与搜索功能

3. **导航图标设计**：
   - 使用 "checkmark.circle" 或类似的系统图标
   - 保持与其他导航图标的风格一致
   - 支持选中/未选中状态的视觉区分
   - 适配暗色/亮色模式