好的，以下是**「小行动」模块的完整产品需求文档（v1.0）**，围绕「简洁呈现」「轻量拆解」「不增加管理负担」三大核心设计理念制定：

⸻

小行动（子任务）产品需求文档（v1.0）

一、定义
	•	小行动是对一个行动（任务）内部进一步拆解的具体步骤，用于帮助用户分阶段推进复杂行动。
	•	仅存在于任务内部，不作为独立对象存在。

⸻

二、出现位置与字段顺序

出现在任务详情页
	•	展示顺序如下：
	1.	任务标题
	2.	备注（notes）
	3.	项目（!project）
	4.	标签（#tag）
	5.	小行动

⸻

三、功能设计

1. 添加 / 编辑
	•	小行动为文本形式，支持连续添加
	•	添加方式：点击「➕ 添加小行动」输入框，输入后回车即添加
	•	每一项默认未完成
	•	不支持子层级，不支持设置标签 / 项目 / 备注等属性

2. 展示规则
	•	初始状态折叠，仅显示：「还有 x 项未完成」
	•	点击可展开显示完整小行动列表
	•	每项展示为：勾选框 + 文本
	•	已完成项置灰 + 删除线

3. 排序规则
	•	状态优先排序：
	•	未完成项置顶
	•	已完成项靠下
	•	各组内按创建时间正序
	•	不支持手动拖动排序

4. 完成逻辑
	•	用户可单独勾选小行动完成 / 取消完成
	•	若主任务被设为完成，则所有小行动自动标记为已完成
	•	小行动全部完成，不自动完成主任务（由用户决定）

5. 删除 / 清除
	•	每一项右滑可删除，或进入编辑模式点删除图标
	•	无批量删除功能（保持极简）

⸻

四、视觉与交互细节
	•	折叠时：只显示小行动模块标题 + 剩余数量提示（如「还有 3 项未完成」）
	•	展开时：完整展示全部小行动，已完成项视觉收缩
	•	添加区域永远显示在底部，输入即创建
	•	长按或左滑某一项 → 删除按钮
	•	无拖动排序功能
	•	全部完成后模块自动折叠，显示简洁提示：「✅ 已完成全部小行动」

⸻

五、其他约束
	•	单层结构，不支持嵌套子任务
	•	小行动不会出现在任何主列表（聚焦页 / 下一步页）中
	•	小行动的内容不可独立搜索，只能在任务详情页查看
	•	移动主任务时，小行动随之移动

⸻


⸻

小行动（子任务）交互需求文档

⸻

一、模块入口
	•	所在页面：任务详情页（全屏视图）
	•	展示位置：位于「项目」下方
	•	默认折叠，仅展示简要信息（如：“还有 3 项未完成”）

⸻

二、交互行为

1. 展开 / 折叠
	•	默认折叠
	•	点击模块标题区域 → 展开完整小行动列表
	•	所有小行动完成后，自动收起为一行提示：「✅ 已完成全部小行动」

⸻

2. 添加小行动
	•	展开状态下，在列表底部显示输入框
	•	输入内容后按回车即可创建新小行动，自动聚焦下一行
	•	每添加一项自动保存
	•	添加项默认为“未完成”

⸻

3. 勾选完成状态
	•	小行动前带勾选框
	•	勾选后：置灰文字 + 加删除线，状态设为“已完成”
	•	取消勾选：恢复为普通文本 + 勾选框

⸻

4. 删除小行动
	•	支持两种方式：
	•	长按小行动 → 弹出操作菜单 → 删除
	•	或：左滑该项 → 显示删除按钮
	•	删除不影响其他小行动和主任务状态

⸻

5. 自动行为
	•	当主任务设为「已完成」时：
	•	所有小行动自动设为完成
	•	显示为置灰 + 删除线
	•	当用户新建小行动时，仍可添加，无限制

⸻

6. 排序规则（自动）
	•	未完成项排上方，已完成项排下方
	•	每组内按创建时间正序
	•	不支持手动拖动排序

⸻

7. 限制与提示
	•	无最大数量限制，但建议 UI 显示前 10 项后自动滚动
	•	一旦展开，支持上下滑动浏览
	•	小行动不具备标签 / 项目等附属属性
	•	不支持小行动单独进入详情页（永远附属于主任务）
