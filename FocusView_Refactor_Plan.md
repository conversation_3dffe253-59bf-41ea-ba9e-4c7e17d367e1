# FocusView 重构计划

## 📋 项目概述

### 重构目标
将 FocusView 相关的视图层从 **1600+ 行重复代码、26个散落状态变量、12个文件** 重构为 **统一的Node架构、集中状态管理、3-4个核心文件**。

### 核心理念
> "小行动、步骤、这些其实都是相对的，所有展开、聚焦、滑动都是一样的"

实现真正的统一：
- **一切皆节点**: Task、ChecklistItem、SubStep 统一处理
- **操作统一**: 聚焦、完成、展开等操作完全一致
- **样式相对化**: 样式由在聚焦层级中的位置决定，而非节点类型

## 🎯 Phase 1: 核心架构设计 (1-2天)

### 1.1 数据层统一

#### 任务
- [ ] 设计Node协议
- [ ] 为Task/ChecklistItem/SubStep添加Node扩展
- [ ] 确保现有数据读写不受影响
- [ ] 测试数据层兼容性

#### 设计目标
```swift
protocol Node {
    var id: UUID { get }
    var title: String { get set }
    var isCompleted: Bool { get set }
    var children: [Node] { get }
    var canHaveChildren: Bool { get }
}

extension Task: Node { ... }
extension ChecklistItem: Node { ... }  
extension SubStep: Node { ... }
```

#### 风险评估
- **低风险**: 主要是添加接口，不修改现有数据结构
- **兼容性**: 确保SwiftData模型不受影响

### 1.2 状态管理重构

#### 任务
- [ ] 整理现有26个@State变量的职责
- [ ] 设计统一的状态管理接口
- [ ] 更新FocusManager承担所有状态
- [ ] 确保状态变更的响应式更新

#### 现状分析
```
FocusView.swift:      13个@State变量
TaskRowView.swift:    11个@State变量  
FocusEchoDropView:    多个动画状态
BottomButtonsView:    2个UI状态
```

#### 目标架构
```swift
@MainActor
class FocusManager: ObservableObject {
    // 核心状态
    @Published var focusPath: [UUID] = []
    @Published var expandedNodes: Set<UUID> = []
    @Published var editingNode: UUID? = nil
    @Published var animatingNodes: Set<UUID> = []
    
    // 统一操作
    func focusTo(_ nodeId: UUID)
    func toggleCompletion(_ nodeId: UUID)
    func toggleExpansion(_ nodeId: UUID)
    func startEditing(_ nodeId: UUID)
}
```

### 1.3 样式系统设计

#### 任务
- [ ] 定义NodeStyle枚举（primary/secondary/tertiary）
- [ ] 定义DisplayContext枚举（list/focused/actionFocus）
- [ ] 设计样式计算逻辑
- [ ] 确保不同组合下的视觉效果

#### 样式架构
```swift
enum NodeStyle {
    case primary    // 当前聚焦的主节点（大勾选框、粗体）
    case secondary  // 子节点（中等大小、target图标）
    case tertiary   // 深层子节点（小巧精致、缩进显示）
}

enum DisplayContext {
    case taskList       // 非聚焦模式
    case focused        // 任务聚焦模式  
    case actionFocus    // 小行动聚焦模式
}

func getNodeStyle(nodeId: UUID, context: DisplayContext) -> NodeStyle
```

## 🏗️ Phase 2: 核心组件开发 (2-3天)

### 2.1 NodeView组件

#### 任务
- [ ] 创建通用NodeView
- [ ] 实现统一的勾选框逻辑
- [ ] 实现统一的编辑模式
- [ ] 实现统一的展开/折叠
- [ ] 实现统一的滑动操作

#### 组件设计
```swift
struct NodeView: View {
    let node: Node
    let style: NodeStyle
    let context: DisplayContext
    let viewModel: FocusViewModel
    
    var body: some View {
        SwipeView {
            HStack(spacing: 10) {
                // 统一勾选框
                CompletionButton(node: node, style: style)
                
                // 统一内容区
                NodeContentView(node: node, style: style, context: context)
                
                // 统一操作区
                NodeActionsView(node: node, style: style, context: context)
            }
        } leadingActions: { context in
            // 统一左滑操作
        } trailingActions: { context in
            // 统一右滑操作
        }
    }
}
```

#### 统一的交互模式
- **勾选框**: 统一的完成逻辑和动画
- **编辑模式**: 统一的TextField + 保存/取消
- **滑动操作**: 统一的SwipeView配置
- **展开折叠**: 统一的chevron + 动画

### 2.2 NodeListView组件

#### 任务
- [ ] 创建通用列表组件
- [ ] 支持递归子节点显示
- [ ] 实现拖拽排序
- [ ] 性能优化（虚拟化等）

#### 组件设计
```swift
struct NodeListView: View {
    let nodes: [Node]
    let context: DisplayContext
    let viewModel: FocusViewModel
    
    var body: some View {
        LazyVStack {
            ForEach(nodes, id: \.id) { node in
                NodeView(
                    node: node,
                    style: getStyle(for: node),
                    context: context,
                    viewModel: viewModel
                )
                
                // 递归显示子节点
                if viewModel.focusManager.isExpanded(node.id) {
                    NodeListView(
                        nodes: node.children,
                        context: context,
                        viewModel: viewModel
                    )
                    .padding(.leading, 20)
                }
            }
        }
    }
}
```

### 2.3 测试单个组件

#### 任务
- [ ] 在隔离环境测试NodeView
- [ ] 测试各种状态变化
- [ ] 测试动画和交互
- [ ] 确保组件稳定

#### 测试用例
- 不同样式下的显示效果
- 状态变化的响应性
- 动画的流畅度
- 交互的准确性

## 🔄 Phase 3: 视图层重构 (2-3天)

### 3.1 FocusView重构

#### 任务
- [ ] 简化FocusView主逻辑
- [ ] 替换contentBasedOnMode分支
- [ ] 统一三种模式的显示逻辑
- [ ] 保留导航和工具栏

#### 新的FocusView架构
```swift
struct FocusView: View {
    @StateObject var viewModel: FocusViewModel
    
    var body: some View {
        NavigationStack {
            ZStack {
                // 主内容区
                if viewModel.doingTasks.isEmpty {
                    EmptyStateView()
                } else {
                    mainContentView
                }
                
                // 浮层
                overlayViews
            }
            .toolbar { toolbarContent }
        }
    }
    
    private var mainContentView: some View {
        if viewModel.focusManager.isFocused {
            // 聚焦模式：显示聚焦的节点树
            FocusedNodeTreeView(
                rootNodeId: viewModel.focusManager.focusPath.first!,
                viewModel: viewModel
            )
        } else {
            // 列表模式：显示所有任务
            NodeListView(
                nodes: viewModel.doingTasks.map { .task($0) },
                context: .taskList,
                viewModel: viewModel
            )
        }
    }
}
```

### 3.2 删除旧组件

#### 任务
- [ ] 备份现有TaskRowView等文件
- [ ] 逐步移除TaskRowView.swift (482行)
- [ ] 移除TaskRowView+ChecklistItem.swift (462行)
- [ ] 移除FocusView+SubSteps.swift (651行)
- [ ] 移除FocusView+ActionFocus.swift (292行)
- [ ] 移除其他冗余文件

#### 删除列表
```
📁 待删除文件 (总计1887行)
├── TaskRowView.swift (482行)
├── TaskRowView+ChecklistItem.swift (462行)
├── FocusView+SubSteps.swift (651行)
├── FocusView+ActionFocus.swift (292行)
├── TaskRowView+Utils.swift (55行)
├── TaskRowView+Swipe.swift (179行)
└── FocusView+StepComponents.swift (61行)

📁 保留文件 (简化后)
├── FocusView.swift (简化到<200行)
├── NodeView.swift (新增)
├── NodeListView.swift (新增)
└── FocusedNodeTreeView.swift (新增)
```

### 3.3 集成测试

#### 任务
- [ ] 测试任务列表模式
- [ ] 测试任务聚焦模式
- [ ] 测试ActionFocus模式
- [ ] 测试模式切换

#### 测试场景
- 空状态显示
- 任务列表展开/折叠
- 任务聚焦进入/退出
- 小行动聚焦深入
- 子步骤递归显示
- 面包屑导航

## 🔧 Phase 4: 边缘功能适配 (1-2天)

### 4.1 特殊功能

#### 任务
- [ ] 面包屑导航
- [ ] 回声Drop功能
- [ ] 音效和触感反馈
- [ ] 烟花动画集成

#### 集成策略
- 保持现有的特殊功能接口
- 将它们适配到新的NodeView架构
- 确保用户体验不降级

### 4.2 错误处理

#### 任务
- [ ] 处理数据不一致情况
- [ ] 处理网络/存储错误
- [ ] 添加错误边界
- [ ] 用户体验降级方案

#### 错误场景
- 节点ID找不到对应数据
- 聚焦路径失效
- 状态同步失败
- 动画中断处理

## ⚡ Phase 5: 性能优化和测试 (1天)

### 5.1 性能优化

#### 任务
- [ ] 减少不必要的重绘
- [ ] 优化递归查找算法
- [ ] 内存泄漏检查
- [ ] 滚动性能优化

#### 优化目标
- 深层嵌套的递归查找：`O(n)` → `O(1)` (使用缓存)
- 视图重绘：减少不必要的`body`计算
- 内存使用：避免循环引用和状态泄漏

### 5.2 全面测试

#### 任务
- [ ] 所有交互路径测试
- [ ] 状态持久化测试
- [ ] 数据同步测试
- [ ] 用户体验回归测试

#### 测试覆盖
- ✅ 基础功能：完成、编辑、删除
- ✅ 导航功能：聚焦、退出、面包屑
- ✅ 高级功能：拖拽、滑动、动画
- ✅ 边缘情况：空数据、错误状态、中断恢复

## 🚫 风险点预警

### 高风险
1. **状态同步风险**: 26个状态变量的迁移可能有遗漏
2. **性能风险**: 统一组件可能比专用组件慢
3. **回滚风险**: 如果失败，需要有完整的回滚方案

### 中风险
4. **数据层风险**: Node协议可能与现有SwiftData模型冲突
5. **测试覆盖风险**: 重写后可能遗漏边缘案例

### 风险缓解策略
- **备份策略**: 完整备份现有代码
- **渐进验证**: 每个Phase都要充分测试
- **回滚预案**: 保持现有ViewModel接口兼容
- **用户测试**: 在真实场景下验证体验

## 📊 成功指标

### 代码质量指标
- **代码行数**: 目标减少60%以上 (1887行 → ~700行)
- **文件数量**: FocusView相关文件从12个减少到4个
- **状态变量**: 从26个@State减少到<5个
- **重复代码**: 完全消除1600+行的重复逻辑

### 性能指标
- **滚动性能**: 60fps流畅滚动
- **交互响应**: <100ms响应时间
- **内存使用**: 不超过现有水平
- **启动时间**: 不降级

### 用户体验指标
- **功能完整性**: 100%功能保持
- **视觉一致性**: 统一的交互体验
- **操作流畅性**: 无卡顿、无闪烁
- **学习成本**: 降低用户认知负担

## 📅 时间安排

| Phase | 时间 | 关键里程碑 |
|-------|------|------------|
| Phase 1 | 1-2天 | 架构设计完成，协议定义清晰 |
| Phase 2 | 2-3天 | 核心组件稳定，单元测试通过 |
| Phase 3 | 2-3天 | 视图层替换完成，功能验证通过 |
| Phase 4 | 1-2天 | 边缘功能适配，错误处理完善 |
| Phase 5 | 1天 | 性能优化，全面测试 |
| **总计** | **7-11天** | **重构完成，达到成功指标** |

## 🎯 最终愿景

重构完成后，实现真正的"一切皆节点"架构：

```swift
// 统一的操作接口
viewModel.focusManager.focusTo(nodeId)        // 聚焦任意节点
viewModel.focusManager.toggleCompletion(nodeId) // 完成任意节点
viewModel.focusManager.toggleExpansion(nodeId)  // 展开任意节点

// 统一的视图组件
NodeView(node: anyNode, style: .primary, context: .focused)

// 简洁的状态管理
@Published var focusPath: [UUID] = []     // 替代所有聚焦状态
@Published var expandedNodes: Set<UUID>   // 替代所有展开状态
@Published var editingNode: UUID?         // 替代所有编辑状态
```

**结果**: 从复杂分散的组件体系 → 简洁统一的节点架构，真正实现"小行动、步骤都是相对的"设计理念。 